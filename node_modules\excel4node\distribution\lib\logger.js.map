{"version": 3, "file": "logger.js", "names": ["SimpleLogger", "opts", "_classCallCheck", "logLevel", "_createClass", "key", "value", "debug", "_console", "console", "apply", "arguments", "log", "_console2", "inspect", "_console3", "info", "_console4", "warn", "_console5", "error", "_console6", "module", "exports"], "sources": ["../../source/lib/logger.js"], "sourcesContent": ["class SimpleLogger {\n  constructor(opts) {\n    this.logLevel = opts.logLevel || 5;\n  }\n\n  debug() {\n    if (this.logLevel >= 5) {\n      console.debug(...arguments);\n    }\n  }\n\n  log() {\n    if (this.logLevel >= 4) {\n      console.log(...arguments);\n    }\n  }\n\n  inspect() {\n    if (this.logLevel >= 4) {\n      console.log(...arguments);\n    }\n  }\n\n  info() {\n    if (this.logLevel >= 3) {\n      console.info(...arguments);\n    }\n  }\n\n  warn() {\n    if (this.logLevel >= 2) {\n      console.warn(...arguments);\n    }\n  }\n\n  error() {\n    if (this.logLevel >= 1) {\n      console.error(...arguments);\n    }\n  }\n\n}\n\nmodule.exports = SimpleLogger;"], "mappings": ";;;;;IAAMA,YAAY;EAChB,SAAAA,aAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,YAAA;IAChB,IAAI,CAACG,QAAQ,GAAGF,IAAI,CAACE,QAAQ,IAAI,CAAC;EACpC;EAACC,YAAA,CAAAJ,YAAA;IAAAK,GAAA;IAAAC,KAAA,EAED,SAAAC,MAAA,EAAQ;MACN,IAAI,IAAI,CAACJ,QAAQ,IAAI,CAAC,EAAE;QAAA,IAAAK,QAAA;QACtB,CAAAA,QAAA,GAAAC,OAAO,EAACF,KAAK,CAAAG,KAAA,CAAAF,QAAA,EAAIG,SAAS,CAAC;MAC7B;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAM,IAAA,EAAM;MACJ,IAAI,IAAI,CAACT,QAAQ,IAAI,CAAC,EAAE;QAAA,IAAAU,SAAA;QACtB,CAAAA,SAAA,GAAAJ,OAAO,EAACG,GAAG,CAAAF,KAAA,CAAAG,SAAA,EAAIF,SAAS,CAAC;MAC3B;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAQ,QAAA,EAAU;MACR,IAAI,IAAI,CAACX,QAAQ,IAAI,CAAC,EAAE;QAAA,IAAAY,SAAA;QACtB,CAAAA,SAAA,GAAAN,OAAO,EAACG,GAAG,CAAAF,KAAA,CAAAK,SAAA,EAAIJ,SAAS,CAAC;MAC3B;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAU,KAAA,EAAO;MACL,IAAI,IAAI,CAACb,QAAQ,IAAI,CAAC,EAAE;QAAA,IAAAc,SAAA;QACtB,CAAAA,SAAA,GAAAR,OAAO,EAACO,IAAI,CAAAN,KAAA,CAAAO,SAAA,EAAIN,SAAS,CAAC;MAC5B;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAY,KAAA,EAAO;MACL,IAAI,IAAI,CAACf,QAAQ,IAAI,CAAC,EAAE;QAAA,IAAAgB,SAAA;QACtB,CAAAA,SAAA,GAAAV,OAAO,EAACS,IAAI,CAAAR,KAAA,CAAAS,SAAA,EAAIR,SAAS,CAAC;MAC5B;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAc,MAAA,EAAQ;MACN,IAAI,IAAI,CAACjB,QAAQ,IAAI,CAAC,EAAE;QAAA,IAAAkB,SAAA;QACtB,CAAAA,SAAA,GAAAZ,OAAO,EAACW,KAAK,CAAAV,KAAA,CAAAW,SAAA,EAAIV,SAAS,CAAC;MAC7B;IACF;EAAC;EAAA,OAAAX,YAAA;AAAA;AAIHsB,MAAM,CAACC,OAAO,GAAGvB,YAAY"}