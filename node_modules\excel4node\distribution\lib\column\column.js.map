{"version": 3, "file": "column.js", "names": ["utils", "require", "Column", "col", "ws", "_classCallCheck", "collapsed", "customWidth", "hidden", "max", "min", "outlineLevel", "style", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "key", "get", "set", "w", "TypeError", "value", "<PERSON><PERSON><PERSON><PERSON>", "width", "hide", "group", "level", "parseInt", "undefined", "freeze", "jumpTo", "o", "opts", "sheetView", "pane", "state", "xSplit", "activePane", "ySplit", "topLeftCell", "getExcelCellRef", "getExcelRowCol", "row", "module", "exports"], "sources": ["../../../source/lib/column/column.js"], "sourcesContent": ["const utils = require('../utils.js');\n\nclass Column {\n    /**\n     * Element representing an Excel Column\n     * @param {Number} col Column of cell\n     * @param {Worksheet} Worksheet that contains column\n     * @property {Worksheet} ws Worksheet that contains the specified Column\n     * @property {Boolean} collapsed States whether the column is collapsed if part of a group\n     * @property {Boolean} customWidth States whether or not the column as a width that is not default\n     * @property {Boolean} hidden States whether or not the specified column is hiddent\n     * @property {Number} max The greatest column if part of a range\n     * @property {Number} min The least column if part of a range\n     * @property {Number} outlineLevel The grouping leve of the Column\n     * @property {Number} style ID of style\n     * @property {Number} width Width of the Column\n     */\n    constructor(col, ws) {\n        this.ws = ws;\n        this.collapsed = null;\n        this.customWidth = null;\n        this.hidden = null;\n        this.max = col;\n        this.min = col;\n        this.outlineLevel = null;\n        this.style = null;\n        this.colWidth = null;\n    }\n\n    get width() {\n        return this.colWidth;\n    }\n\n    set width(w) {\n        if (typeof w === 'number') {\n            this.colWidth = w;\n            this.customWidth = true;\n        } else {\n            throw new TypeError('Column width must be a number');\n        }\n        return this.colWidth;\n    }\n\n    /**\n     * @alias Column.setWidth\n     * @desc Sets teh width of a column\n     * @func Column.setWidth\n     * @param {Number} val New Width of column\n     * @returns {Column} Excel Column with attached methods\n     */\n    setWidth(w) {\n        this.width = w;\n        return this;\n    }\n\n    /**\n     * @alias Column.hide\n     * @desc Sets a Column to be hidden\n     * @func Column.hide\n     * @returns {Column} Excel Column with attached methods\n     */\n    hide() {\n        this.hidden = true;\n        return this;\n    }\n\n    /**\n     * @alias Column.group\n     * @desc Adds column to the specified group\n     * @func Column.group\n     * @param {Number} level Level of excel grouping\n     * @param {Boolean} collapsed States wheter column grouping level should be collapsed by default\n     * @returns {Column} Excel Column with attached methods\n     */\n    group(level, collapsed) {\n        if (parseInt(level) === level) {\n            this.outlineLevel = level;\n        } else {\n            throw new TypeError('Column group level must be a positive integer');\n        }\n\n        if (collapsed === undefined) {\n            return this;\n        }\n\n        if (typeof collapsed === 'boolean') {\n            this.collapsed = collapsed;\n            this.hidden = collapsed;\n        } else {\n            throw new TypeError('Column group collapse flag must be a boolean');\n        }\n\n        return this;\n    }\n\n    /**\n     * @alias Column.freeze\n     * @desc Creates an Excel pane at the specificed column and Freezes that column from scolling\n     * @func Column.freeze\n     * @param {Number} jumptTo Specifies the column that the active pane will be scrolled to by default\n     * @returns {Column} Excel Column with attached methods\n     */\n    freeze(jumpTo) {\n        let o = this.ws.opts.sheetView.pane;\n        jumpTo = typeof jumpTo === 'number' && jumpTo > this.min ? jumpTo : this.min + 1;\n        o.state = 'frozen';\n        o.xSplit = this.min;\n        o.activePane = 'bottomRight';\n        o.ySplit === null ?\n            o.topLeftCell = utils.getExcelCellRef(1, jumpTo) :\n            o.topLeftCell = utils.getExcelCellRef(utils.getExcelRowCol(o.topLeftCell).row, jumpTo);\n        return this;\n    }\n}\n\nmodule.exports = Column;"], "mappings": ";;;;;AAAA,IAAMA,KAAK,GAAGC,OAAO,CAAC,aAAa,CAAC;AAAC,IAE/BC,MAAM;EACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,OAAYC,GAAG,EAAEC,EAAE,EAAE;IAAAC,eAAA,OAAAH,MAAA;IACjB,IAAI,CAACE,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACE,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,GAAG,GAAGN,GAAG;IACd,IAAI,CAACO,GAAG,GAAGP,GAAG;IACd,IAAI,CAACQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACxB;EAACC,YAAA,CAAAZ,MAAA;IAAAa,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAY;MACR,OAAO,IAAI,CAACH,QAAQ;IACxB,CAAC;IAAAI,GAAA,EAED,SAAAA,IAAUC,CAAC,EAAE;MACT,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACvB,IAAI,CAACL,QAAQ,GAAGK,CAAC;QACjB,IAAI,CAACX,WAAW,GAAG,IAAI;MAC3B,CAAC,MAAM;QACH,MAAM,IAAIY,SAAS,CAAC,+BAA+B,CAAC;MACxD;MACA,OAAO,IAAI,CAACN,QAAQ;IACxB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAE,GAAA;IAAAK,KAAA,EAOA,SAAAC,SAASH,CAAC,EAAE;MACR,IAAI,CAACI,KAAK,GAAGJ,CAAC;MACd,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAH,GAAA;IAAAK,KAAA,EAMA,SAAAG,KAAA,EAAO;MACH,IAAI,CAACf,MAAM,GAAG,IAAI;MAClB,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAO,GAAA;IAAAK,KAAA,EAQA,SAAAI,MAAMC,KAAK,EAAEnB,SAAS,EAAE;MACpB,IAAIoB,QAAQ,CAACD,KAAK,CAAC,KAAKA,KAAK,EAAE;QAC3B,IAAI,CAACd,YAAY,GAAGc,KAAK;MAC7B,CAAC,MAAM;QACH,MAAM,IAAIN,SAAS,CAAC,+CAA+C,CAAC;MACxE;MAEA,IAAIb,SAAS,KAAKqB,SAAS,EAAE;QACzB,OAAO,IAAI;MACf;MAEA,IAAI,OAAOrB,SAAS,KAAK,SAAS,EAAE;QAChC,IAAI,CAACA,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACE,MAAM,GAAGF,SAAS;MAC3B,CAAC,MAAM;QACH,MAAM,IAAIa,SAAS,CAAC,8CAA8C,CAAC;MACvE;MAEA,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAJ,GAAA;IAAAK,KAAA,EAOA,SAAAQ,OAAOC,MAAM,EAAE;MACX,IAAIC,CAAC,GAAG,IAAI,CAAC1B,EAAE,CAAC2B,IAAI,CAACC,SAAS,CAACC,IAAI;MACnCJ,MAAM,GAAG,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,GAAG,IAAI,CAACnB,GAAG,GAAGmB,MAAM,GAAG,IAAI,CAACnB,GAAG,GAAG,CAAC;MAChFoB,CAAC,CAACI,KAAK,GAAG,QAAQ;MAClBJ,CAAC,CAACK,MAAM,GAAG,IAAI,CAACzB,GAAG;MACnBoB,CAAC,CAACM,UAAU,GAAG,aAAa;MAC5BN,CAAC,CAACO,MAAM,KAAK,IAAI,GACbP,CAAC,CAACQ,WAAW,GAAGtC,KAAK,CAACuC,eAAe,CAAC,CAAC,EAAEV,MAAM,CAAC,GAChDC,CAAC,CAACQ,WAAW,GAAGtC,KAAK,CAACuC,eAAe,CAACvC,KAAK,CAACwC,cAAc,CAACV,CAAC,CAACQ,WAAW,CAAC,CAACG,GAAG,EAAEZ,MAAM,CAAC;MAC1F,OAAO,IAAI;IACf;EAAC;EAAA,OAAA3B,MAAA;AAAA;AAGLwC,MAAM,CAACC,OAAO,GAAGzC,MAAM"}