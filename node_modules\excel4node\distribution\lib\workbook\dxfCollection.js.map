{"version": 3, "file": "dxfCollection.js", "names": ["_isEqual", "require", "Style", "util", "DXFItem", "style", "wb", "_classCallCheck", "id", "_createClass", "key", "get", "value", "addToXMLele", "ele", "addDXFtoXMLele", "DXFCollection", "items", "add", "thisItem", "for<PERSON>ach", "item", "toObject", "push", "length", "dxfXML", "att", "module", "exports"], "sources": ["../../../source/lib/workbook/dxfCollection.js"], "sourcesContent": ["const _isEqual = require('lodash.isequal');\nconst Style = require('../style');\nconst util = require('util');\n\nclass DXFItem { // §18.8.14 dxf (Formatting)\n    constructor(style, wb) {\n        this.wb = wb;\n        this.style = style;\n        this.id;\n    }\n    get dxfId() {\n        return this.id;\n    }\n\n    addToXMLele(ele) {\n        this.style.addDXFtoXMLele(ele);\n    }\n}\n\nclass DXFCollection { // §18.8.15 dxfs (Formats)\n    constructor(wb) {\n        this.wb = wb;\n        this.items = [];\n    }\n\n    add(style) {\n        if (!(style instanceof Style)) {\n            style = this.wb.Style(style);\n        }\n\n        let thisItem;\n        this.items.forEach((item) => {\n            if (_isEqual(item.style.toObject(), style.toObject())) {\n                return thisItem = item;\n            }\n        });\n        if (!thisItem) {\n            thisItem = new DXFItem(style, this.wb);\n            this.items.push(thisItem);\n            thisItem.id = this.items.length - 1;\n        }\n        return thisItem;\n    }\n\n    get length() {\n        return this.items.length;\n    }\n\n    addToXMLele(ele) {\n        let dxfXML = ele\n            .ele('dxfs')\n            .att('count', this.length);\n\n        this.items.forEach((item) => {\n            item.addToXMLele(dxfXML);\n        });\n    }\n}\n\nmodule.exports = DXFCollection;"], "mappings": ";;;;;AAAA,IAAMA,QAAQ,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAC1C,IAAMC,KAAK,GAAGD,OAAO,CAAC,UAAU,CAAC;AACjC,IAAME,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAAC,IAEvBG,OAAO;EAAG;EACZ,SAAAA,QAAYC,KAAK,EAAEC,EAAE,EAAE;IAAAC,eAAA,OAAAH,OAAA;IACnB,IAAI,CAACE,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,EAAE;EACX;EAACC,YAAA,CAAAL,OAAA;IAAAM,GAAA;IAAAC,GAAA,EACD,SAAAA,IAAA,EAAY;MACR,OAAO,IAAI,CAACH,EAAE;IAClB;EAAC;IAAAE,GAAA;IAAAE,KAAA,EAED,SAAAC,YAAYC,GAAG,EAAE;MACb,IAAI,CAACT,KAAK,CAACU,cAAc,CAACD,GAAG,CAAC;IAClC;EAAC;EAAA,OAAAV,OAAA;AAAA;AAAA,IAGCY,aAAa;EAAG;EAClB,SAAAA,cAAYV,EAAE,EAAE;IAAAC,eAAA,OAAAS,aAAA;IACZ,IAAI,CAACV,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACW,KAAK,GAAG,EAAE;EACnB;EAACR,YAAA,CAAAO,aAAA;IAAAN,GAAA;IAAAE,KAAA,EAED,SAAAM,IAAIb,KAAK,EAAE;MACP,IAAI,EAAEA,KAAK,YAAYH,KAAK,CAAC,EAAE;QAC3BG,KAAK,GAAG,IAAI,CAACC,EAAE,CAACJ,KAAK,CAACG,KAAK,CAAC;MAChC;MAEA,IAAIc,QAAQ;MACZ,IAAI,CAACF,KAAK,CAACG,OAAO,CAAC,UAACC,IAAI,EAAK;QACzB,IAAIrB,QAAQ,CAACqB,IAAI,CAAChB,KAAK,CAACiB,QAAQ,CAAC,CAAC,EAAEjB,KAAK,CAACiB,QAAQ,CAAC,CAAC,CAAC,EAAE;UACnD,OAAOH,QAAQ,GAAGE,IAAI;QAC1B;MACJ,CAAC,CAAC;MACF,IAAI,CAACF,QAAQ,EAAE;QACXA,QAAQ,GAAG,IAAIf,OAAO,CAACC,KAAK,EAAE,IAAI,CAACC,EAAE,CAAC;QACtC,IAAI,CAACW,KAAK,CAACM,IAAI,CAACJ,QAAQ,CAAC;QACzBA,QAAQ,CAACX,EAAE,GAAG,IAAI,CAACS,KAAK,CAACO,MAAM,GAAG,CAAC;MACvC;MACA,OAAOL,QAAQ;IACnB;EAAC;IAAAT,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,OAAO,IAAI,CAACM,KAAK,CAACO,MAAM;IAC5B;EAAC;IAAAd,GAAA;IAAAE,KAAA,EAED,SAAAC,YAAYC,GAAG,EAAE;MACb,IAAIW,MAAM,GAAGX,GAAG,CACXA,GAAG,CAAC,MAAM,CAAC,CACXY,GAAG,CAAC,OAAO,EAAE,IAAI,CAACF,MAAM,CAAC;MAE9B,IAAI,CAACP,KAAK,CAACG,OAAO,CAAC,UAACC,IAAI,EAAK;QACzBA,IAAI,CAACR,WAAW,CAACY,MAAM,CAAC;MAC5B,CAAC,CAAC;IACN;EAAC;EAAA,OAAAT,aAAA;AAAA;AAGLW,MAAM,CAACC,OAAO,GAAGZ,aAAa"}