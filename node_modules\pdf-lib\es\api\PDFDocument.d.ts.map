{"version": 3, "file": "PDFDocument.d.ts", "sourceRoot": "", "sources": ["../../src/api/PDFDocument.ts"], "names": [], "mappings": "AAOA,OAAO,eAAe,0BAAgC;AACtD,OAAO,OAAO,kBAAwB;AACtC,OAAO,QAAQ,mBAAyB;AACxC,OAAO,OAAO,kBAAwB;AACtC,OAAO,OAAO,uBAA6B;AAE3C,OAAO,EAAE,aAAa,EAAE,wBAA8B;AACtD,OAAO,EAIL,eAAe,EAEf,UAAU,EACV,UAAU,EAeX,gBAAiB;AAClB,OAAO,EAEL,iBAAiB,EACjB,WAAW,EACX,iBAAiB,EACjB,WAAW,EACX,aAAa,EACb,gBAAgB,EAChB,eAAe,EAChB,6BAAmC;AAEpC,OAAO,MAAM,+BAAgC;AAC7C,OAAO,EAAE,OAAO,EAAE,yBAA0B;AAC5C,OAAO,EAAE,oBAAoB,EAAE,wBAAyB;AAmBxD;;GAEG;AACH,MAAM,CAAC,OAAO,OAAO,WAAW;IAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmDG;WACU,IAAI,CACf,GAAG,EAAE,MAAM,GAAG,UAAU,GAAG,WAAW,EACtC,OAAO,GAAE,WAAgB;IAyB3B;;;OAGG;WACU,MAAM,CAAC,OAAO,GAAE,aAAkB;IAY/C,8CAA8C;IAC9C,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;IAE7B,oCAAoC;IACpC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;IAE7B,iDAAiD;IACjD,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;IAE9B,uDAAuD;IACvD,iBAAiB,EAAE,MAAM,EAAE,CAAS;IAEpC,OAAO,CAAC,OAAO,CAAC,CAAU;IAC1B,OAAO,CAAC,SAAS,CAAqB;IACtC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAmB;IAC7C,OAAO,CAAC,QAAQ,CAAC,OAAO,CAA4B;IACpD,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAiB;IAC3C,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAY;IAClC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAa;IACpC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAoB;IAClD,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAoB;IAClD,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAkB;IAE9C,OAAO;IA0BP;;;;;;;;;;;;;;;;;OAiBG;IACH,eAAe,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAIvC;;;;;;;;;;;;;OAaG;IACH,OAAO,IAAI,OAAO;IAWlB;;;;;;;OAOG;IACH,QAAQ,IAAI,MAAM,GAAG,SAAS;IAO9B;;;;;;;OAOG;IACH,SAAS,IAAI,MAAM,GAAG,SAAS;IAO/B;;;;;;;OAOG;IACH,UAAU,IAAI,MAAM,GAAG,SAAS;IAOhC;;;;;;;OAOG;IACH,WAAW,IAAI,MAAM,GAAG,SAAS;IAOjC;;;;;;;OAOG;IACH,UAAU,IAAI,MAAM,GAAG,SAAS;IAOhC;;;;;;;OAOG;IACH,WAAW,IAAI,MAAM,GAAG,SAAS;IAOjC;;;;;;;;OAQG;IACH,eAAe,IAAI,IAAI,GAAG,SAAS;IAOnC;;;;;;;;;OASG;IACH,mBAAmB,IAAI,IAAI,GAAG,SAAS;IAOvC;;;;;;;;;;;;;;;;OAgBG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,eAAe,GAAG,IAAI;IAYxD;;;;;;;OAOG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAM/B;;;;;;;OAOG;IACH,UAAU,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAMjC;;;;;;;OAOG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI;IAMrC;;;;;;;OAOG;IACH,UAAU,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAMjC;;;;;;;OAOG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAMnC;;;;;;;;;OASG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAMnC;;;;;;;OAOG;IACH,eAAe,CAAC,YAAY,EAAE,IAAI,GAAG,IAAI;IAMzC;;;;;;;;OAQG;IACH,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,GAAG,IAAI;IAMjD;;;;;;OAMG;IACH,YAAY,IAAI,MAAM;IAKtB;;;;;;;;;;;OAWG;IACH,QAAQ,IAAI,OAAO,EAAE;IAIrB;;;;;;;;OAQG;IACH,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO;IAM/B;;;;;;;;;;;;;;OAcG;IACH,cAAc,IAAI,MAAM,EAAE;IAI1B;;;;;;;;;;OAUG;IACH,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAQ/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACH,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,OAAO;IAKnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,OAAO,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,OAAO;IAuBrE;;;;;;;;;;;;;;;;;OAiBG;IACG,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAgB3E;;;;;;;;;;;;;OAaG;IACG,IAAI,IAAI,OAAO,CAAC,WAAW,CAAC;IAkClC;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAW1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsDG;IACG,MAAM,CACV,UAAU,EAAE,MAAM,GAAG,UAAU,GAAG,WAAW,EAC7C,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,iBAAsB,GAC9B,OAAO,CAAC,IAAI,CAAC;IAuBhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACG,SAAS,CACb,IAAI,EAAE,aAAa,GAAG,MAAM,GAAG,UAAU,GAAG,WAAW,EACvD,OAAO,GAAE,gBAAqB,GAC7B,OAAO,CAAC,OAAO,CAAC;IAiCnB;;;;;;;;;;OAUG;IACH,iBAAiB,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO;IAepE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACG,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,UAAU,GAAG,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC;IAUzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACG,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,UAAU,GAAG,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC;IAUzE;;;;;;;;;;;;;;;;;;;OAmBG;IACG,QAAQ,CACZ,GAAG,EAAE,MAAM,GAAG,UAAU,GAAG,WAAW,GAAG,WAAW,EACpD,OAAO,GAAE,MAAM,EAAQ,GACtB,OAAO,CAAC,eAAe,EAAE,CAAC;IAiB7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACG,SAAS,CACb,IAAI,EAAE,OAAO,EACb,WAAW,CAAC,EAAE,eAAe,EAC7B,oBAAoB,CAAC,EAAE,oBAAoB,GAC1C,OAAO,CAAC,eAAe,CAAC;IAU3B;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACG,UAAU,CACd,KAAK,EAAE,OAAO,EAAE,EAChB,aAAa,GAAE,CAAC,eAAe,GAAG,SAAS,CAAC,EAAO,EACnD,sBAAsB,GAAE,CAAC,oBAAoB,GAAG,SAAS,CAAC,EAAO;IAoCnE;;;;;;;;;OASG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAQ5B;;;;;;;;;;;;;;;OAeG;IACG,IAAI,CAAC,OAAO,GAAE,WAAgB,GAAG,OAAO,CAAC,UAAU,CAAC;IA0B1D;;;;;;;;;;;;;;OAcG;IACG,YAAY,CAAC,OAAO,GAAE,iBAAsB,GAAG,OAAO,CAAC,MAAM,CAAC;IAQpE,wBAAwB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,GAAG,SAAS;YAc5C,QAAQ;IAMtB,OAAO,CAAC,cAAc;IAatB,OAAO,CAAC,WAAW;IAUnB,OAAO,CAAC,aAAa;IAKrB,OAAO,CAAC,YAAY,CAalB;IAEF,OAAO,CAAC,eAAe,CAGrB;CACH"}