import{u as e,j as s,B as a,h as r,d as i,f as n,G as t,F as l,w as d,x as c,M as x,l as o,i as h,a5 as m,W as u,a6 as j,a7 as g,a8 as p,a9 as v,aa as y,A as k,m as b,s as f}from"./mui-core-BBO2DoRL.js";import{r as M}from"./vendor-CeOqOr8o.js";import{u as S}from"./routing-B6PnZiBG.js";import{m as C}from"./animation-BJm6nf7i.js";import{a4 as w,m as A,T,q as W,a8 as G}from"./mui-icons-BXhTkfAe.js";import"./utils-misc-NFmYzBmQ.js";const B=[{id:1,name:"<PERSON><PERSON> <PERSON>",rollNumber:1,previousGrade:"A1",currentMarks:95,maxMarks:100},{id:2,name:"<PERSON><PERSON><PERSON><PERSON>",rollNumber:2,previousGrade:"A2",currentMarks:88,maxMarks:100},{id:3,name:"<PERSON><PERSON><PERSON> <PERSON>",rollNumber:3,previousGrade:"B1",currentMarks:82,maxMarks:100},{id:4,name:"Ravi Teja Sharma",rollNumber:4,previousGrade:"A1",currentMarks:92,maxMarks:100},{id:5,name:"Ankitha Patel",rollNumber:5,previousGrade:"A2",currentMarks:85,maxMarks:100},{id:6,name:"Sirisha Nair",rollNumber:6,previousGrade:"A1",currentMarks:96,maxMarks:100},{id:7,name:"Priya Agarwal",rollNumber:7,previousGrade:"B2",currentMarks:78,maxMarks:100}],E=e=>{switch(e){case"A1":case"A2":return"success";case"B1":case"B2":return"info";case"C1":case"C2":return"warning";case"D":case"E":return"error";default:return"default"}},N=()=>{const N=e();S();const[$,I]=M.useState("10-A"),[P,z]=M.useState("Mathematics"),[F,U]=M.useState("Unit Test 1"),[R,H]=M.useState({}),[D,O]=M.useState(!1);M.useEffect((()=>{const e={};B.forEach((s=>{e[s.id]={marks:s.currentMarks,maxMarks:s.maxMarks}})),H(e)}),[]);const q=(()=>{const e=Object.values(R).map((e=>e.marks/e.maxMarks*100)),s=e.reduce(((e,s)=>e+s),0)/e.length,a=Math.max(...e),r=Math.min(...e);return{average:s.toFixed(1),highest:a.toFixed(1),lowest:r.toFixed(1),totalStudents:e.length}})();return s.jsxs(a,{sx:{maxWidth:1200,mx:"auto",p:3},children:[s.jsx(C.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(a,{sx:{mb:4},children:[s.jsx(r,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${N.palette.primary.main} 0%, ${N.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Grade Entry"}),s.jsx(r,{variant:"body1",color:"text.secondary",children:"Enter and manage student grades with Indian grading system"})]})}),s.jsx(i,{sx:{mb:4},children:s.jsx(n,{children:s.jsxs(t,{container:!0,spacing:3,alignItems:"center",children:[s.jsx(t,{item:!0,xs:12,md:3,children:s.jsxs(l,{fullWidth:!0,children:[s.jsx(d,{children:"Class"}),s.jsxs(c,{value:$,onChange:e=>I(e.target.value),label:"Class",children:[s.jsx(x,{value:"9-A",children:"Class 9-A"}),s.jsx(x,{value:"9-B",children:"Class 9-B"}),s.jsx(x,{value:"10-A",children:"Class 10-A"}),s.jsx(x,{value:"10-B",children:"Class 10-B"})]})]})}),s.jsx(t,{item:!0,xs:12,md:3,children:s.jsxs(l,{fullWidth:!0,children:[s.jsx(d,{children:"Subject"}),s.jsxs(c,{value:P,onChange:e=>z(e.target.value),label:"Subject",children:[s.jsx(x,{value:"Mathematics",children:"Mathematics"}),s.jsx(x,{value:"Science",children:"Science"}),s.jsx(x,{value:"English",children:"English"}),s.jsx(x,{value:"Hindi",children:"Hindi"}),s.jsx(x,{value:"Social Studies",children:"Social Studies"}),s.jsx(x,{value:"Telugu",children:"Telugu"})]})]})}),s.jsx(t,{item:!0,xs:12,md:3,children:s.jsxs(l,{fullWidth:!0,children:[s.jsx(d,{children:"Test/Assessment"}),s.jsxs(c,{value:F,onChange:e=>U(e.target.value),label:"Test/Assessment",children:[s.jsx(x,{value:"Unit Test 1",children:"Unit Test 1"}),s.jsx(x,{value:"Unit Test 2",children:"Unit Test 2"}),s.jsx(x,{value:"Mid Term",children:"Mid Term Exam"}),s.jsx(x,{value:"Final Term",children:"Final Term Exam"}),s.jsx(x,{value:"Assignment",children:"Assignment"}),s.jsx(x,{value:"Project",children:"Project Work"})]})]})}),s.jsx(t,{item:!0,xs:12,md:3,children:s.jsx(o,{direction:"row",spacing:2,children:s.jsx(h,{variant:"contained",startIcon:s.jsx(w,{}),onClick:async()=>{O(!0);try{await new Promise((e=>setTimeout(e,1e3))),alert("Grades saved successfully!")}catch(e){}finally{O(!1)}},loading:D,sx:{background:`linear-gradient(135deg, ${N.palette.primary.main} 0%, ${N.palette.secondary.main} 100%)`},children:"Save Grades"})})})]})})}),s.jsxs(t,{container:!0,spacing:3,sx:{mb:4},children:[s.jsx(t,{item:!0,xs:12,md:3,children:s.jsx(i,{sx:{background:`linear-gradient(135deg, ${N.palette.primary.main} 0%, ${N.palette.primary.dark} 100%)`,color:"white"},children:s.jsx(n,{children:s.jsxs(a,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(a,{children:[s.jsxs(r,{variant:"h4",sx:{fontWeight:600},children:[q.average,"%"]}),s.jsx(r,{variant:"body2",sx:{opacity:.9},children:"Class Average"})]}),s.jsx(A,{sx:{fontSize:40,opacity:.8}})]})})})}),s.jsx(t,{item:!0,xs:12,md:3,children:s.jsx(i,{sx:{background:`linear-gradient(135deg, ${N.palette.success.main} 0%, ${N.palette.success.dark} 100%)`,color:"white"},children:s.jsx(n,{children:s.jsxs(a,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(a,{children:[s.jsxs(r,{variant:"h4",sx:{fontWeight:600},children:[q.highest,"%"]}),s.jsx(r,{variant:"body2",sx:{opacity:.9},children:"Highest Score"})]}),s.jsx(T,{sx:{fontSize:40,opacity:.8}})]})})})}),s.jsx(t,{item:!0,xs:12,md:3,children:s.jsx(i,{sx:{background:`linear-gradient(135deg, ${N.palette.warning.main} 0%, ${N.palette.warning.dark} 100%)`,color:"white"},children:s.jsx(n,{children:s.jsxs(a,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(a,{children:[s.jsxs(r,{variant:"h4",sx:{fontWeight:600},children:[q.lowest,"%"]}),s.jsx(r,{variant:"body2",sx:{opacity:.9},children:"Lowest Score"})]}),s.jsx(W,{sx:{fontSize:40,opacity:.8}})]})})})}),s.jsx(t,{item:!0,xs:12,md:3,children:s.jsx(i,{sx:{background:`linear-gradient(135deg, ${N.palette.info.main} 0%, ${N.palette.info.dark} 100%)`,color:"white"},children:s.jsx(n,{children:s.jsxs(a,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(a,{children:[s.jsx(r,{variant:"h4",sx:{fontWeight:600},children:q.totalStudents}),s.jsx(r,{variant:"body2",sx:{opacity:.9},children:"Total Students"})]}),s.jsx(G,{sx:{fontSize:40,opacity:.8}})]})})})})]}),s.jsx(i,{children:s.jsxs(n,{children:[s.jsxs(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:["Grade Entry - ",$," | ",P," | ",F]}),s.jsx(m,{component:u,variant:"outlined",children:s.jsxs(j,{children:[s.jsx(g,{children:s.jsxs(p,{children:[s.jsx(v,{children:"Roll No."}),s.jsx(v,{children:"Student Name"}),s.jsx(v,{children:"Previous Grade"}),s.jsx(v,{children:"Marks Obtained"}),s.jsx(v,{children:"Max Marks"}),s.jsx(v,{children:"Percentage"}),s.jsx(v,{children:"Current Grade"})]})}),s.jsx(y,{children:B.map((e=>{const i=R[e.id],n=i?i.marks/i.maxMarks*100:0,t=i?((e,s)=>{const a=e/s*100;return a>=91?"A1":a>=81?"A2":a>=71?"B1":a>=61?"B2":a>=51?"C1":a>=41?"C2":a>=33?"D":"E"})(i.marks,i.maxMarks):"E";return s.jsxs(p,{hover:!0,children:[s.jsx(v,{children:s.jsx(r,{variant:"body2",sx:{fontWeight:600},children:e.rollNumber})}),s.jsx(v,{children:s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:2},children:[s.jsx(k,{sx:{width:32,height:32},children:e.name.charAt(0)}),s.jsx(r,{variant:"body2",sx:{fontWeight:500},children:e.name})]})}),s.jsx(v,{children:s.jsx(b,{label:e.previousGrade,color:E(e.previousGrade),size:"small"})}),s.jsx(v,{children:s.jsx(f,{size:"small",type:"number",value:(null==i?void 0:i.marks)||"",onChange:s=>{return a=e.id,r=s.target.value,void H((e=>({...e,[a]:{...e[a],marks:parseInt(r)||0}})));var a,r},sx:{width:80},inputProps:{min:0,max:(null==i?void 0:i.maxMarks)||100}})}),s.jsx(v,{children:s.jsx(f,{size:"small",type:"number",value:(null==i?void 0:i.maxMarks)||100,onChange:s=>{return a=e.id,r=s.target.value,void H((e=>({...e,[a]:{...e[a],maxMarks:parseInt(r)||100}})));var a,r},sx:{width:80},inputProps:{min:1}})}),s.jsx(v,{children:s.jsxs(r,{variant:"body2",sx:{fontWeight:500},children:[n.toFixed(1),"%"]})}),s.jsx(v,{children:s.jsx(b,{label:t,color:E(t),size:"small"})})]},e.id)}))})]})})]})})]})};export{N as default};
//# sourceMappingURL=GradeEntry-C2wSA142.js.map
