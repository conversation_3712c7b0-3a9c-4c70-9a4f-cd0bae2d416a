{"version": 3, "file": "PDFContext.js", "sourceRoot": "", "sources": ["../../src/core/PDFContext.ts"], "names": [], "mappings": ";;;AAAA,sDAAwB;AAExB,2EAAoD;AACpD,mCAA4D;AAC5D,wEAAiD;AACjD,sEAA+C;AAC/C,sEAA+C;AAE/C,sEAA+C;AAC/C,sEAA+C;AAC/C,0EAAmD;AACnD,0EAAmD;AACnD,gFAAyD;AACzD,oEAA6C;AAG7C,gFAAyD;AACzD,0FAAsD;AACtD,2FAAoE;AACpE,kCAA0C;AAC1C,oCAA0C;AAqB1C,IAAM,uBAAuB,GAAG,UAC9B,EAAwB,EACxB,EAAwB;QADvB,CAAC,QAAA;QACD,CAAC,QAAA;IACC,OAAA,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY;AAA/B,CAA+B,CAAC;AAErC;IAkBE;QACE,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,mBAAS,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QAEtB,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,GAAG,GAAG,eAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,2BAAM,GAAN,UAAO,GAAW,EAAE,MAAiB;QACnC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACtC,IAAI,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE;YAC/C,IAAI,CAAC,mBAAmB,GAAG,GAAG,CAAC,YAAY,CAAC;SAC7C;IACH,CAAC;IAED,4BAAO,GAAP;QACE,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC;QAC9B,OAAO,gBAAM,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC7C,CAAC;IAED,6BAAQ,GAAR,UAAS,MAAiB;QACxB,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACzB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,2BAAM,GAAN,UAAO,GAAW;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1C,CAAC;IAqBD,gCAAW,GAAX,UAAY,GAAc;QAAE,eAAe;aAAf,UAAe,EAAf,qBAAe,EAAf,IAAe;YAAf,8BAAe;;QACzC,oEAAoE;QACpE,uCAAuC;QACvC,IAAM,eAAe,GAAG,KAAK,CAAC,QAAQ,CAAC,iBAAO,CAAC,CAAC;QAEhD,IAAM,MAAM,GAAG,GAAG,YAAY,gBAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAE3E,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,KAAK,iBAAO,IAAI,CAAC,eAAe,CAAC;YAAE,OAAO,SAAS,CAAC;QAE1E,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACtD,IAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,IAAI,KAAK,iBAAO,EAAE;gBACpB,IAAI,MAAM,KAAK,iBAAO;oBAAE,OAAO,MAAM,CAAC;aACvC;iBAAM;gBACL,IAAI,MAAM,YAAY,IAAI;oBAAE,OAAO,MAAM,CAAC;aAC3C;SACF;QACD,MAAM,IAAI,kCAAyB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;IAmBD,2BAAM,GAAN,UAAO,GAAc;QAAE,eAAe;aAAf,UAAe,EAAf,qBAAe,EAAf,IAAe;YAAf,8BAAe;;QACpC,IAAM,MAAM,GAAG,GAAG,YAAY,gBAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAE3E,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,MAAM,CAAC;QAEtC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACtD,IAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,IAAI,KAAK,iBAAO,EAAE;gBACpB,IAAI,MAAM,KAAK,iBAAO;oBAAE,OAAO,MAAM,CAAC;aACvC;iBAAM;gBACL,IAAI,MAAM,YAAY,IAAI;oBAAE,OAAO,MAAM,CAAC;aAC3C;SACF;QAED,MAAM,IAAI,kCAAyB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;IAED,iCAAY,GAAZ,UAAa,SAAoB;QAC/B,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAA,KAAgB,OAAO,CAAC,GAAG,CAAC,EAA3B,GAAG,QAAA,EAAE,MAAM,QAAgB,CAAC;YACnC,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,OAAO,GAAG,CAAC;aACZ;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,6CAAwB,GAAxB;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CACpD,uBAAuB,CACxB,CAAC;IACJ,CAAC;IASD,wBAAG,GAAH,UAAI,OAAgB;QAClB,IAAI,OAAO,YAAY,mBAAS,EAAE;YAChC,OAAO,OAAO,CAAC;SAChB;aAAM,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE;YACpD,OAAO,iBAAO,CAAC;SAChB;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACtC,OAAO,iBAAO,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;SAC5B;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACtC,OAAO,mBAAS,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;SAC9B;aAAM,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YACvC,OAAO,OAAO,CAAC,CAAC,CAAC,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAO,CAAC,KAAK,CAAC;SAC/C;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACjC,IAAM,KAAK,GAAG,kBAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACzC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBACxD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACpC;YACD,OAAO,KAAK,CAAC;SACd;aAAM;YACL,IAAM,IAAI,GAAG,iBAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACvC,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBACrD,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBACtB,IAAM,KAAK,GAAI,OAAyB,CAAC,GAAG,CAAQ,CAAC;gBACrD,IAAI,KAAK,KAAK,SAAS;oBAAE,IAAI,CAAC,GAAG,CAAC,iBAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;aACrE;YACD,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED,2BAAM,GAAN,UACE,QAA6B,EAC7B,IAAwB;QAAxB,qBAAA,EAAA,SAAwB;QAExB,OAAO,sBAAY,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,qBAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,gCAAW,GAAX,UACE,QAA6B,EAC7B,IAAwB;QAAxB,qBAAA,EAAA,SAAwB;QAExB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAI,CAAC,OAAO,CAAC,qBAAa,CAAC,QAAQ,CAAC,CAAC,wCACnD,IAAI,KACP,MAAM,EAAE,aAAa,IACrB,CAAC;IACL,CAAC;IAED,kCAAa,GAAb,UACE,SAAwB,EACxB,IAAwB;QAAxB,qBAAA,EAAA,SAAwB;QAExB,OAAO,0BAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC;IACxD,CAAC;IAED,gCAAW,GAAX,UACE,SAAwB,EACxB,IAAwB;QAAxB,qBAAA,EAAA,SAAwB;QAExB,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,sCACjC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC5B,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,IACjC,IAAI,KACP,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,IACf,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,sDAAiC,GAAjC;QACE,IAAI,IAAI,CAAC,iCAAiC,EAAE;YAC1C,OAAO,IAAI,CAAC,iCAAiC,CAAC;SAC/C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1B,IAAM,EAAE,GAAG,qBAAW,CAAC,EAAE,CAAC,0BAAG,CAAC,iBAAiB,CAAC,CAAC;QACjD,IAAM,MAAM,GAAG,0BAAgB,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC,iCAAiC,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACH,qDAAgC,GAAhC;QACE,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACzC,OAAO,IAAI,CAAC,gCAAgC,CAAC;SAC9C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1B,IAAM,EAAE,GAAG,qBAAW,CAAC,EAAE,CAAC,0BAAG,CAAC,gBAAgB,CAAC,CAAC;QAChD,IAAM,MAAM,GAAG,0BAAgB,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC,gCAAgC,CAAC;IAC/C,CAAC;IAED,oCAAe,GAAf,UAAgB,MAAc,EAAE,YAAgB;QAAhB,6BAAA,EAAA,gBAAgB;QAC9C,OAAU,MAAM,SAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,SAAA,EAAE,EAAI,YAAY,CAAA,CAAG,CAAC;IAC5E,CAAC;IAxPM,iBAAM,GAAG,cAAM,OAAA,IAAI,UAAU,EAAE,EAAhB,CAAgB,CAAC;IAyPzC,iBAAC;CAAA,AA1PD,IA0PC;AAED,kBAAe,UAAU,CAAC"}