{"version": 3, "file": "index.js", "names": ["deepmerge", "require", "Cell", "Row", "Comment", "Column", "Style", "utils", "util", "validXmlRegex", "removeInvalidXml", "str", "Array", "from", "map", "c", "cp", "codePointAt", "match", "join", "stringSetter", "val", "_this", "logger", "ws", "wb", "warn", "JSON", "stringify", "excelRefs", "_typeof", "merged", "cells", "for<PERSON>ach", "string", "getStringIndex", "complexStringSetter", "_this2", "numberSetter", "undefined", "parseFloat", "TypeError", "format", "i", "number", "booleanSetter", "valString", "toString", "toLowerCase", "bool", "formulaSetter", "formula", "dateSetter", "thisDate", "Date", "isNaN", "getTime", "date", "dtStyle", "numberFormat", "opts", "dateFormat", "styleSetter", "bind", "_this3", "thisStyle", "toObject", "Object", "borderEdges", "border", "outline", "left", "firstCol", "right", "lastCol", "top", "firstRow", "bottom", "lastRow", "thisCellsBorder", "row", "col", "s", "thisCellStyle", "createStyle", "style", "ids", "cellXfs", "curStyle", "styles", "newStyleOpts", "mergedStyle", "hyperlinkSetter", "url", "displayStr", "tooltip", "_this4", "ref", "hyperlinkCollection", "add", "location", "display", "font", "color", "underline", "commentSetter", "comment", "options", "_this5", "comments", "r", "mergeCells", "cellBlock", "length", "sort", "sortCellRefs", "cellRange", "rangeCells", "okToMerge", "mergedCells", "cr", "cur<PERSON><PERSON>s", "getAllCellsInExcelRange", "intersection", "arrayIntersectSafe", "error", "concat", "push", "_classCallCheck", "_createClass", "key", "get", "matrix", "tmpObj", "rows", "keys", "a", "b", "cellAccessor", "row1", "col1", "row2", "col2", "isMerged", "theseCells", "lastUsedRow", "lastUsedCol", "getExcelAlpha", "cellRefs", "indexOf", "prototype", "link", "module", "exports"], "sources": ["../../../source/lib/cell/index.js"], "sourcesContent": ["const deepmerge = require('deepmerge');\nconst Cell = require('./cell.js');\nconst Row = require('../row/row.js');\nconst Comment = require('../classes/comment');\nconst Column = require('../column/column.js');\nconst Style = require('../style/style.js');\nconst utils = require('../utils.js');\nconst util = require('util');\n\nconst validXmlRegex = /[\\u0009\\u000a\\u000d\\u0020-\\uD7FF\\uE000-\\uFFFD]/u;\n\n/**\n * The list of valid characters is\n * #x9 | #xA | #xD | [#x20-#xD7FF] | [#xE000-#xFFFD] | [#x10000-#x10FFFF]\n *\n * We need to test codepoints numerically, instead of regex characters above 65536 (0x10000),\n */\nfunction removeInvalidXml(str) {\n    return Array.from(str).map(c => {\n        const cp = c.codePointAt(0);\n        if (cp >= 65536 && cp <= 1114111) {\n            return c\n        } else if (c.match(validXmlRegex)) {\n            return c;\n        } else {\n            return '';\n        }\n    }).join('');\n}\n\nfunction stringSetter(val) {\n    let logger = this.ws.wb.logger;\n\n    if (typeof (val) !== 'string') {\n        logger.warn('Value sent to String function of cells %s was not a string, it has type of %s',\n            JSON.stringify(this.excelRefs),\n            typeof (val));\n        val = '';\n    }\n    val = removeInvalidXml(val);\n\n    if (!this.merged) {\n        this.cells.forEach((c) => {\n            c.string(this.ws.wb.getStringIndex(val));\n        });\n    } else {\n        let c = this.cells[0];\n        c.string(this.ws.wb.getStringIndex(val));\n    }\n    return this;\n}\n\nfunction complexStringSetter(val) {\n    if (!this.merged) {\n        this.cells.forEach((c) => {\n            c.string(this.ws.wb.getStringIndex(val));\n        });\n    } else {\n        let c = this.cells[0];\n        c.string(this.ws.wb.getStringIndex(val));\n    }\n    return this;\n}\n\nfunction numberSetter(val) {\n    if (val === undefined || parseFloat(val) !== val) {\n        throw new TypeError(util.format('Value sent to Number function of cells %s was not a number, it has type of %s and value of %s',\n            JSON.stringify(this.excelRefs),\n            typeof (val),\n            val\n        ));\n    }\n    val = parseFloat(val);\n\n    if (!this.merged) {\n        this.cells.forEach((c, i) => {\n            c.number(val);\n        });\n    } else {\n        var c = this.cells[0];\n        c.number(val);\n    }\n    return this;\n}\n\nfunction booleanSetter(val) {\n    if (val !== true && val !== false) {\n        let valString = val.toString().toLowerCase();\n        if (valString === \"true\") {\n            val = true;\n        } else if (valString === \"false\") {\n            val = false;\n        } else {\n            throw new TypeError(util.format('Value sent to Bool function of cells %s was not a bool, it has type of %s and value of %s',\n                JSON.stringify(this.excelRefs),\n                typeof (val),\n                val\n            ));\n        }\n    }\n\n    if (!this.merged) {\n        this.cells.forEach((c, i) => {\n            c.bool(val ? '1' : '0');\n        });\n    } else {\n        var c = this.cells[0];\n        c.bool(val ? '1' : '0');\n    }\n    return this;\n}\n\nfunction formulaSetter(val) {\n    if (typeof (val) !== 'string') {\n        throw new TypeError(util.format('Value sent to Formula function of cells %s was not a string, it has type of %s', JSON.stringify(this.excelRefs), typeof (val)));\n    }\n    if (this.merged !== true) {\n        this.cells.forEach((c, i) => {\n            c.formula(val);\n        });\n    } else {\n        var c = this.cells[0];\n        c.formula(val);\n    }\n\n    return this;\n}\n\nfunction dateSetter(val) {\n    let thisDate = new Date(val);\n    if (isNaN(thisDate.getTime())) {\n        throw new TypeError(util.format('Invalid date sent to date function of cells. %s could not be converted to a date.', val));\n    }\n    if (this.merged !== true) {\n        this.cells.forEach((c, i) => {\n            c.date(thisDate);\n        });\n    } else {\n        var c = this.cells[0];\n        c.date(thisDate);\n    }\n    const dtStyle = new Style(this.ws.wb, {\n        numberFormat: '[$-409]' + this.ws.wb.opts.dateFormat\n    });\n    return styleSetter.bind(this)(dtStyle);\n}\n\nfunction styleSetter(val) {\n    let thisStyle;\n    if (val instanceof Style) {\n        thisStyle = val.toObject();\n    } else if (val instanceof Object) {\n        thisStyle = val;\n    } else {\n        throw new TypeError(util.format('Parameter sent to Style function must be an instance of a Style or a style configuration object'));\n    }\n\n    let borderEdges = {};\n    if (thisStyle.border && thisStyle.border.outline) {\n        borderEdges.left = this.firstCol;\n        borderEdges.right = this.lastCol;\n        borderEdges.top = this.firstRow;\n        borderEdges.bottom = this.lastRow;\n    }\n\n    this.cells.forEach((c) => {\n        if (thisStyle.border && thisStyle.border.outline) {\n            let thisCellsBorder = {};\n            if (c.row === borderEdges.top && thisStyle.border.top) {\n                thisCellsBorder.top = thisStyle.border.top;\n            }\n            if (c.row === borderEdges.bottom && thisStyle.border.bottom) {\n                thisCellsBorder.bottom = thisStyle.border.bottom;\n            }\n            if (c.col === borderEdges.left && thisStyle.border.left) {\n                thisCellsBorder.left = thisStyle.border.left;\n            }\n            if (c.col === borderEdges.right && thisStyle.border.right) {\n                thisCellsBorder.right = thisStyle.border.right;\n            }\n            thisStyle.border = thisCellsBorder;\n        }\n\n        if (c.s === 0) {\n            let thisCellStyle = this.ws.wb.createStyle(thisStyle);\n            c.style(thisCellStyle.ids.cellXfs);\n        } else {\n            let curStyle = this.ws.wb.styles[c.s];\n            let newStyleOpts = deepmerge(curStyle.toObject(), thisStyle);\n            let mergedStyle = this.ws.wb.createStyle(newStyleOpts);\n            c.style(mergedStyle.ids.cellXfs);\n        }\n    });\n    return this;\n}\n\nfunction hyperlinkSetter(url, displayStr, tooltip) {\n    this.excelRefs.forEach((ref) => {\n        displayStr = typeof displayStr === 'string' ? displayStr : url;\n        this.ws.hyperlinkCollection.add({\n            location: url,\n            display: displayStr,\n            tooltip: tooltip,\n            ref: ref\n        });\n    });\n    stringSetter.bind(this)(displayStr);\n    return styleSetter.bind(this)({\n        font: {\n            color: 'Blue',\n            underline: true\n        }\n    });\n}\n\nfunction commentSetter(comment, options) {\n    if (this.merged !== true) {\n        this.cells.forEach((c, i) => {\n            this.ws.comments[c.r] = new Comment(c.r, comment, options)\n        });\n    } else {\n        var c = this.cells[0];\n        this.ws.comments[c.r] = new Comment(c.r, comment, options)\n    }\n    return this;\n}\n\nfunction mergeCells(cellBlock) {\n    let excelRefs = cellBlock.excelRefs;\n    if (excelRefs instanceof Array && excelRefs.length > 0) {\n        excelRefs.sort(utils.sortCellRefs);\n\n        let cellRange = excelRefs[0] + ':' + excelRefs[excelRefs.length - 1];\n        let rangeCells = excelRefs;\n\n        let okToMerge = true;\n        cellBlock.ws.mergedCells.forEach((cr) => {\n            // Check to see if currently merged cells contain cells in new merge request\n            let curCells = utils.getAllCellsInExcelRange(cr);\n            let intersection = utils.arrayIntersectSafe(rangeCells, curCells);\n            if (intersection.length > 0) {\n                okToMerge = false;\n                cellBlock.ws.wb.logger.error(`Invalid Range for: ${cellRange}. Some cells in this range are already included in another merged cell range: ${cr}.`);\n            }\n        });\n        if (okToMerge) {\n            cellBlock.ws.mergedCells.push(cellRange);\n        }\n    } else {\n        throw new TypeError(util.format('excelRefs variable sent to mergeCells function must be an array with length > 0'));\n    }\n}\n\n/**\n * @class cellBlock\n */\nclass cellBlock {\n\n    constructor() {\n        this.ws;\n        this.cells = [];\n        this.excelRefs = [];\n        this.merged = false;\n    }\n\n    get matrix() {\n        let matrix = [];\n        let tmpObj = {};\n        this.cells.forEach((c) => {\n            if (!tmpObj[c.row]) {\n                tmpObj[c.row] = [];\n            }\n            tmpObj[c.row].push(c);\n        });\n        let rows = Object.keys(tmpObj);\n        rows.forEach((r) => {\n            tmpObj[r].sort((a, b) => {\n                return a.col - b.col;\n            });\n            matrix.push(tmpObj[r]);\n        });\n        return matrix;\n    }\n\n    get firstRow() {\n        let firstRow;\n        this.cells.forEach((c) => {\n            if (c.row < firstRow || firstRow === undefined) {\n                firstRow = c.row;\n            }\n        });\n        return firstRow;\n    }\n\n    get lastRow() {\n        let lastRow;\n        this.cells.forEach((c) => {\n            if (c.row > lastRow || lastRow === undefined) {\n                lastRow = c.row;\n            }\n        });\n        return lastRow;\n    }\n\n    get firstCol() {\n        let firstCol;\n        this.cells.forEach((c) => {\n            if (c.col < firstCol || firstCol === undefined) {\n                firstCol = c.col;\n            }\n        });\n        return firstCol;\n    }\n\n    get lastCol() {\n        let lastCol;\n        this.cells.forEach((c) => {\n            if (c.col > lastCol || lastCol === undefined) {\n                lastCol = c.col;\n            }\n        });\n        return lastCol;\n    }\n}\n\n/**\n * Module repesenting a Cell Accessor\n * @alias Worksheet.cell\n * @namespace\n * @func Worksheet.cell\n * @desc Access a range of cells in order to manipulate values\n * @param {Number} row1 Row of top left cell\n * @param {Number} col1 Column of top left cell\n * @param {Number} row2 Row of bottom right cell (optional)\n * @param {Number} col2 Column of bottom right cell (optional)\n * @param {Boolean} isMerged Merged the cell range into a single cell\n * @returns {cellBlock}\n */\nfunction cellAccessor(row1, col1, row2, col2, isMerged) {\n    let theseCells = new cellBlock();\n    theseCells.ws = this;\n\n    row2 = row2 ? row2 : row1;\n    col2 = col2 ? col2 : col1;\n\n    if (row2 > this.lastUsedRow) {\n        this.lastUsedRow = row2;\n    }\n\n    if (col2 > this.lastUsedCol) {\n        this.lastUsedCol = col2;\n    }\n\n    for (let r = row1; r <= row2; r++) {\n        for (let c = col1; c <= col2; c++) {\n            let ref = `${utils.getExcelAlpha(c)}${r}`;\n            if (!this.cells[ref]) {\n                this.cells[ref] = new Cell(r, c);\n            }\n            if (!this.rows[r]) {\n                this.rows[r] = new Row(r, this);\n            }\n            if (this.rows[r].cellRefs.indexOf(ref) < 0) {\n                this.rows[r].cellRefs.push(ref);\n            }\n\n            theseCells.cells.push(this.cells[ref]);\n            theseCells.excelRefs.push(ref);\n        }\n    }\n    if (isMerged) {\n        theseCells.merged = true;\n        mergeCells(theseCells);\n    }\n\n    return theseCells;\n}\n\n/**\n * @alias cellBlock.string\n * @func cellBlock.string\n * @param {String} val Value of String\n * @returns {cellBlock} Block of cells with attached methods\n */\ncellBlock.prototype.string = function (val) {\n    if (val instanceof Array) {\n        return complexStringSetter.bind(this)(val);\n    } else {\n        return stringSetter.bind(this)(val);\n    }\n};\n\n/**\n * @alias cellBlock.style\n * @func cellBlock.style\n * @param {Object} style One of a Style instance or an object with Style parameters\n * @returns {cellBlock} Block of cells with attached methods\n */\ncellBlock.prototype.style = styleSetter;\n\n/**\n * @alias cellBlock.number\n * @func cellBlock.number\n * @param {Number} val Value of Number\n * @returns {cellBlock} Block of cells with attached methods\n */\ncellBlock.prototype.number = numberSetter;\n\n/**\n * @alias cellBlock.bool\n * @func cellBlock.bool\n * @param {Boolean} val Value of Boolean\n * @returns {cellBlock} Block of cells with attached methods\n */\ncellBlock.prototype.bool = booleanSetter;\n\n/**\n * @alias cellBlock.formula\n * @func cellBlock.formula\n * @param {String} val Excel style formula as string\n * @returns {cellBlock} Block of cells with attached methods\n */\ncellBlock.prototype.formula = formulaSetter;\n\n/**\n * @alias cellBlock.date\n * @func cellBlock.date\n * @param {Date} val Value of Date\n * @returns {cellBlock} Block of cells with attached methods\n */\ncellBlock.prototype.date = dateSetter;\n\n/**\n * @alias cellBlock.link\n * @func cellBlock.link\n * @param {String} url Value of Hyperlink URL\n * @param {String} displayStr Value of String representation of URL\n * @param {String} tooltip Value of text to display as hover\n * @returns {cellBlock} Block of cells with attached methods\n */\ncellBlock.prototype.link = hyperlinkSetter;\n\ncellBlock.prototype.comment = commentSetter;\n\nmodule.exports = cellAccessor;"], "mappings": ";;;;;;AAAA,IAAMA,SAAS,GAAGC,OAAO,CAAC,WAAW,CAAC;AACtC,IAAMC,IAAI,GAAGD,OAAO,CAAC,WAAW,CAAC;AACjC,IAAME,GAAG,GAAGF,OAAO,CAAC,eAAe,CAAC;AACpC,IAAMG,OAAO,GAAGH,OAAO,CAAC,oBAAoB,CAAC;AAC7C,IAAMI,MAAM,GAAGJ,OAAO,CAAC,qBAAqB,CAAC;AAC7C,IAAMK,KAAK,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AAC1C,IAAMM,KAAK,GAAGN,OAAO,CAAC,aAAa,CAAC;AACpC,IAAMO,IAAI,GAAGP,OAAO,CAAC,MAAM,CAAC;AAE5B,IAAMQ,aAAa,GAAG,+BAAiD;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,OAAOC,KAAK,CAACC,IAAI,CAACF,GAAG,CAAC,CAACG,GAAG,CAAC,UAAAC,CAAC,EAAI;IAC5B,IAAMC,EAAE,GAAGD,CAAC,CAACE,WAAW,CAAC,CAAC,CAAC;IAC3B,IAAID,EAAE,IAAI,KAAK,IAAIA,EAAE,IAAI,OAAO,EAAE;MAC9B,OAAOD,CAAC;IACZ,CAAC,MAAM,IAAIA,CAAC,CAACG,KAAK,CAACT,aAAa,CAAC,EAAE;MAC/B,OAAOM,CAAC;IACZ,CAAC,MAAM;MACH,OAAO,EAAE;IACb;EACJ,CAAC,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;AACf;AAEA,SAASC,YAAYA,CAACC,GAAG,EAAE;EAAA,IAAAC,KAAA;EACvB,IAAIC,MAAM,GAAG,IAAI,CAACC,EAAE,CAACC,EAAE,CAACF,MAAM;EAE9B,IAAI,OAAQF,GAAI,KAAK,QAAQ,EAAE;IAC3BE,MAAM,CAACG,IAAI,CAAC,+EAA+E,EACvFC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,SAAS,CAAC,EAAAC,OAAA,CACtBT,GAAG,CAAC,CAAC;IACjBA,GAAG,GAAG,EAAE;EACZ;EACAA,GAAG,GAAGX,gBAAgB,CAACW,GAAG,CAAC;EAE3B,IAAI,CAAC,IAAI,CAACU,MAAM,EAAE;IACd,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAK;MACtBA,CAAC,CAACmB,MAAM,CAACZ,KAAI,CAACE,EAAE,CAACC,EAAE,CAACU,cAAc,CAACd,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC;EACN,CAAC,MAAM;IACH,IAAIN,CAAC,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC,CAAC;IACrBjB,CAAC,CAACmB,MAAM,CAAC,IAAI,CAACV,EAAE,CAACC,EAAE,CAACU,cAAc,CAACd,GAAG,CAAC,CAAC;EAC5C;EACA,OAAO,IAAI;AACf;AAEA,SAASe,mBAAmBA,CAACf,GAAG,EAAE;EAAA,IAAAgB,MAAA;EAC9B,IAAI,CAAC,IAAI,CAACN,MAAM,EAAE;IACd,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAK;MACtBA,CAAC,CAACmB,MAAM,CAACG,MAAI,CAACb,EAAE,CAACC,EAAE,CAACU,cAAc,CAACd,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC;EACN,CAAC,MAAM;IACH,IAAIN,CAAC,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC,CAAC;IACrBjB,CAAC,CAACmB,MAAM,CAAC,IAAI,CAACV,EAAE,CAACC,EAAE,CAACU,cAAc,CAACd,GAAG,CAAC,CAAC;EAC5C;EACA,OAAO,IAAI;AACf;AAEA,SAASiB,YAAYA,CAACjB,GAAG,EAAE;EACvB,IAAIA,GAAG,KAAKkB,SAAS,IAAIC,UAAU,CAACnB,GAAG,CAAC,KAAKA,GAAG,EAAE;IAC9C,MAAM,IAAIoB,SAAS,CAACjC,IAAI,CAACkC,MAAM,CAAC,+FAA+F,EAC3Hf,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,SAAS,CAAC,EAAAC,OAAA,CACtBT,GAAG,GACXA,GACJ,CAAC,CAAC;EACN;EACAA,GAAG,GAAGmB,UAAU,CAACnB,GAAG,CAAC;EAErB,IAAI,CAAC,IAAI,CAACU,MAAM,EAAE;IACd,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAE4B,CAAC,EAAK;MACzB5B,CAAC,CAAC6B,MAAM,CAACvB,GAAG,CAAC;IACjB,CAAC,CAAC;EACN,CAAC,MAAM;IACH,IAAIN,CAAC,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC,CAAC;IACrBjB,CAAC,CAAC6B,MAAM,CAACvB,GAAG,CAAC;EACjB;EACA,OAAO,IAAI;AACf;AAEA,SAASwB,aAAaA,CAACxB,GAAG,EAAE;EACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,EAAE;IAC/B,IAAIyB,SAAS,GAAGzB,GAAG,CAAC0B,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,IAAIF,SAAS,KAAK,MAAM,EAAE;MACtBzB,GAAG,GAAG,IAAI;IACd,CAAC,MAAM,IAAIyB,SAAS,KAAK,OAAO,EAAE;MAC9BzB,GAAG,GAAG,KAAK;IACf,CAAC,MAAM;MACH,MAAM,IAAIoB,SAAS,CAACjC,IAAI,CAACkC,MAAM,CAAC,2FAA2F,EACvHf,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,SAAS,CAAC,EAAAC,OAAA,CACtBT,GAAG,GACXA,GACJ,CAAC,CAAC;IACN;EACJ;EAEA,IAAI,CAAC,IAAI,CAACU,MAAM,EAAE;IACd,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAE4B,CAAC,EAAK;MACzB5B,CAAC,CAACkC,IAAI,CAAC5B,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAC3B,CAAC,CAAC;EACN,CAAC,MAAM;IACH,IAAIN,CAAC,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC,CAAC;IACrBjB,CAAC,CAACkC,IAAI,CAAC5B,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC3B;EACA,OAAO,IAAI;AACf;AAEA,SAAS6B,aAAaA,CAAC7B,GAAG,EAAE;EACxB,IAAI,OAAQA,GAAI,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIoB,SAAS,CAACjC,IAAI,CAACkC,MAAM,CAAC,gFAAgF,EAAEf,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,SAAS,CAAC,EAAAC,OAAA,CAAUT,GAAG,CAAC,CAAC,CAAC;EACpK;EACA,IAAI,IAAI,CAACU,MAAM,KAAK,IAAI,EAAE;IACtB,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAE4B,CAAC,EAAK;MACzB5B,CAAC,CAACoC,OAAO,CAAC9B,GAAG,CAAC;IAClB,CAAC,CAAC;EACN,CAAC,MAAM;IACH,IAAIN,CAAC,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC,CAAC;IACrBjB,CAAC,CAACoC,OAAO,CAAC9B,GAAG,CAAC;EAClB;EAEA,OAAO,IAAI;AACf;AAEA,SAAS+B,UAAUA,CAAC/B,GAAG,EAAE;EACrB,IAAIgC,QAAQ,GAAG,IAAIC,IAAI,CAACjC,GAAG,CAAC;EAC5B,IAAIkC,KAAK,CAACF,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;IAC3B,MAAM,IAAIf,SAAS,CAACjC,IAAI,CAACkC,MAAM,CAAC,mFAAmF,EAAErB,GAAG,CAAC,CAAC;EAC9H;EACA,IAAI,IAAI,CAACU,MAAM,KAAK,IAAI,EAAE;IACtB,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAE4B,CAAC,EAAK;MACzB5B,CAAC,CAAC0C,IAAI,CAACJ,QAAQ,CAAC;IACpB,CAAC,CAAC;EACN,CAAC,MAAM;IACH,IAAItC,CAAC,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC,CAAC;IACrBjB,CAAC,CAAC0C,IAAI,CAACJ,QAAQ,CAAC;EACpB;EACA,IAAMK,OAAO,GAAG,IAAIpD,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACC,EAAE,EAAE;IAClCkC,YAAY,EAAE,SAAS,GAAG,IAAI,CAACnC,EAAE,CAACC,EAAE,CAACmC,IAAI,CAACC;EAC9C,CAAC,CAAC;EACF,OAAOC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAACL,OAAO,CAAC;AAC1C;AAEA,SAASI,WAAWA,CAACzC,GAAG,EAAE;EAAA,IAAA2C,MAAA;EACtB,IAAIC,SAAS;EACb,IAAI5C,GAAG,YAAYf,KAAK,EAAE;IACtB2D,SAAS,GAAG5C,GAAG,CAAC6C,QAAQ,CAAC,CAAC;EAC9B,CAAC,MAAM,IAAI7C,GAAG,YAAY8C,MAAM,EAAE;IAC9BF,SAAS,GAAG5C,GAAG;EACnB,CAAC,MAAM;IACH,MAAM,IAAIoB,SAAS,CAACjC,IAAI,CAACkC,MAAM,CAAC,iGAAiG,CAAC,CAAC;EACvI;EAEA,IAAI0B,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIH,SAAS,CAACI,MAAM,IAAIJ,SAAS,CAACI,MAAM,CAACC,OAAO,EAAE;IAC9CF,WAAW,CAACG,IAAI,GAAG,IAAI,CAACC,QAAQ;IAChCJ,WAAW,CAACK,KAAK,GAAG,IAAI,CAACC,OAAO;IAChCN,WAAW,CAACO,GAAG,GAAG,IAAI,CAACC,QAAQ;IAC/BR,WAAW,CAACS,MAAM,GAAG,IAAI,CAACC,OAAO;EACrC;EAEA,IAAI,CAAC9C,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAK;IACtB,IAAIkD,SAAS,CAACI,MAAM,IAAIJ,SAAS,CAACI,MAAM,CAACC,OAAO,EAAE;MAC9C,IAAIS,eAAe,GAAG,CAAC,CAAC;MACxB,IAAIhE,CAAC,CAACiE,GAAG,KAAKZ,WAAW,CAACO,GAAG,IAAIV,SAAS,CAACI,MAAM,CAACM,GAAG,EAAE;QACnDI,eAAe,CAACJ,GAAG,GAAGV,SAAS,CAACI,MAAM,CAACM,GAAG;MAC9C;MACA,IAAI5D,CAAC,CAACiE,GAAG,KAAKZ,WAAW,CAACS,MAAM,IAAIZ,SAAS,CAACI,MAAM,CAACQ,MAAM,EAAE;QACzDE,eAAe,CAACF,MAAM,GAAGZ,SAAS,CAACI,MAAM,CAACQ,MAAM;MACpD;MACA,IAAI9D,CAAC,CAACkE,GAAG,KAAKb,WAAW,CAACG,IAAI,IAAIN,SAAS,CAACI,MAAM,CAACE,IAAI,EAAE;QACrDQ,eAAe,CAACR,IAAI,GAAGN,SAAS,CAACI,MAAM,CAACE,IAAI;MAChD;MACA,IAAIxD,CAAC,CAACkE,GAAG,KAAKb,WAAW,CAACK,KAAK,IAAIR,SAAS,CAACI,MAAM,CAACI,KAAK,EAAE;QACvDM,eAAe,CAACN,KAAK,GAAGR,SAAS,CAACI,MAAM,CAACI,KAAK;MAClD;MACAR,SAAS,CAACI,MAAM,GAAGU,eAAe;IACtC;IAEA,IAAIhE,CAAC,CAACmE,CAAC,KAAK,CAAC,EAAE;MACX,IAAIC,aAAa,GAAGnB,MAAI,CAACxC,EAAE,CAACC,EAAE,CAAC2D,WAAW,CAACnB,SAAS,CAAC;MACrDlD,CAAC,CAACsE,KAAK,CAACF,aAAa,CAACG,GAAG,CAACC,OAAO,CAAC;IACtC,CAAC,MAAM;MACH,IAAIC,QAAQ,GAAGxB,MAAI,CAACxC,EAAE,CAACC,EAAE,CAACgE,MAAM,CAAC1E,CAAC,CAACmE,CAAC,CAAC;MACrC,IAAIQ,YAAY,GAAG1F,SAAS,CAACwF,QAAQ,CAACtB,QAAQ,CAAC,CAAC,EAAED,SAAS,CAAC;MAC5D,IAAI0B,WAAW,GAAG3B,MAAI,CAACxC,EAAE,CAACC,EAAE,CAAC2D,WAAW,CAACM,YAAY,CAAC;MACtD3E,CAAC,CAACsE,KAAK,CAACM,WAAW,CAACL,GAAG,CAACC,OAAO,CAAC;IACpC;EACJ,CAAC,CAAC;EACF,OAAO,IAAI;AACf;AAEA,SAASK,eAAeA,CAACC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAE;EAAA,IAAAC,MAAA;EAC/C,IAAI,CAACnE,SAAS,CAACI,OAAO,CAAC,UAACgE,GAAG,EAAK;IAC5BH,UAAU,GAAG,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGD,GAAG;IAC9DG,MAAI,CAACxE,EAAE,CAAC0E,mBAAmB,CAACC,GAAG,CAAC;MAC5BC,QAAQ,EAAEP,GAAG;MACbQ,OAAO,EAAEP,UAAU;MACnBC,OAAO,EAAEA,OAAO;MAChBE,GAAG,EAAEA;IACT,CAAC,CAAC;EACN,CAAC,CAAC;EACF7E,YAAY,CAAC2C,IAAI,CAAC,IAAI,CAAC,CAAC+B,UAAU,CAAC;EACnC,OAAOhC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1BuC,IAAI,EAAE;MACFC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE;IACf;EACJ,CAAC,CAAC;AACN;AAEA,SAASC,aAAaA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAAA,IAAAC,MAAA;EACrC,IAAI,IAAI,CAAC7E,MAAM,KAAK,IAAI,EAAE;IACtB,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAE4B,CAAC,EAAK;MACzBiE,MAAI,CAACpF,EAAE,CAACqF,QAAQ,CAAC9F,CAAC,CAAC+F,CAAC,CAAC,GAAG,IAAI1G,OAAO,CAACW,CAAC,CAAC+F,CAAC,EAAEJ,OAAO,EAAEC,OAAO,CAAC;IAC9D,CAAC,CAAC;EACN,CAAC,MAAM;IACH,IAAI5F,CAAC,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC,CAAC;IACrB,IAAI,CAACR,EAAE,CAACqF,QAAQ,CAAC9F,CAAC,CAAC+F,CAAC,CAAC,GAAG,IAAI1G,OAAO,CAACW,CAAC,CAAC+F,CAAC,EAAEJ,OAAO,EAAEC,OAAO,CAAC;EAC9D;EACA,OAAO,IAAI;AACf;AAEA,SAASI,UAAUA,CAACC,SAAS,EAAE;EAC3B,IAAInF,SAAS,GAAGmF,SAAS,CAACnF,SAAS;EACnC,IAAIA,SAAS,YAAYjB,KAAK,IAAIiB,SAAS,CAACoF,MAAM,GAAG,CAAC,EAAE;IACpDpF,SAAS,CAACqF,IAAI,CAAC3G,KAAK,CAAC4G,YAAY,CAAC;IAElC,IAAIC,SAAS,GAAGvF,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,SAAS,CAACA,SAAS,CAACoF,MAAM,GAAG,CAAC,CAAC;IACpE,IAAII,UAAU,GAAGxF,SAAS;IAE1B,IAAIyF,SAAS,GAAG,IAAI;IACpBN,SAAS,CAACxF,EAAE,CAAC+F,WAAW,CAACtF,OAAO,CAAC,UAACuF,EAAE,EAAK;MACrC;MACA,IAAIC,QAAQ,GAAGlH,KAAK,CAACmH,uBAAuB,CAACF,EAAE,CAAC;MAChD,IAAIG,YAAY,GAAGpH,KAAK,CAACqH,kBAAkB,CAACP,UAAU,EAAEI,QAAQ,CAAC;MACjE,IAAIE,YAAY,CAACV,MAAM,GAAG,CAAC,EAAE;QACzBK,SAAS,GAAG,KAAK;QACjBN,SAAS,CAACxF,EAAE,CAACC,EAAE,CAACF,MAAM,CAACsG,KAAK,uBAAAC,MAAA,CAAuBV,SAAS,oFAAAU,MAAA,CAAiFN,EAAE,MAAG,CAAC;MACvJ;IACJ,CAAC,CAAC;IACF,IAAIF,SAAS,EAAE;MACXN,SAAS,CAACxF,EAAE,CAAC+F,WAAW,CAACQ,IAAI,CAACX,SAAS,CAAC;IAC5C;EACJ,CAAC,MAAM;IACH,MAAM,IAAI3E,SAAS,CAACjC,IAAI,CAACkC,MAAM,CAAC,iFAAiF,CAAC,CAAC;EACvH;AACJ;;AAEA;AACA;AACA;AAFA,IAGMsE,SAAS;EAEX,SAAAA,UAAA,EAAc;IAAAgB,eAAA,OAAAhB,SAAA;IACV,IAAI,CAACxF,EAAE;IACP,IAAI,CAACQ,KAAK,GAAG,EAAE;IACf,IAAI,CAACH,SAAS,GAAG,EAAE;IACnB,IAAI,CAACE,MAAM,GAAG,KAAK;EACvB;EAACkG,YAAA,CAAAjB,SAAA;IAAAkB,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,IAAIC,MAAM,GAAG,EAAE;MACf,IAAIC,MAAM,GAAG,CAAC,CAAC;MACf,IAAI,CAACrG,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAK;QACtB,IAAI,CAACsH,MAAM,CAACtH,CAAC,CAACiE,GAAG,CAAC,EAAE;UAChBqD,MAAM,CAACtH,CAAC,CAACiE,GAAG,CAAC,GAAG,EAAE;QACtB;QACAqD,MAAM,CAACtH,CAAC,CAACiE,GAAG,CAAC,CAAC+C,IAAI,CAAChH,CAAC,CAAC;MACzB,CAAC,CAAC;MACF,IAAIuH,IAAI,GAAGnE,MAAM,CAACoE,IAAI,CAACF,MAAM,CAAC;MAC9BC,IAAI,CAACrG,OAAO,CAAC,UAAC6E,CAAC,EAAK;QAChBuB,MAAM,CAACvB,CAAC,CAAC,CAACI,IAAI,CAAC,UAACsB,CAAC,EAAEC,CAAC,EAAK;UACrB,OAAOD,CAAC,CAACvD,GAAG,GAAGwD,CAAC,CAACxD,GAAG;QACxB,CAAC,CAAC;QACFmD,MAAM,CAACL,IAAI,CAACM,MAAM,CAACvB,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC;MACF,OAAOsB,MAAM;IACjB;EAAC;IAAAF,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAe;MACX,IAAIvD,QAAQ;MACZ,IAAI,CAAC5C,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAK;QACtB,IAAIA,CAAC,CAACiE,GAAG,GAAGJ,QAAQ,IAAIA,QAAQ,KAAKrC,SAAS,EAAE;UAC5CqC,QAAQ,GAAG7D,CAAC,CAACiE,GAAG;QACpB;MACJ,CAAC,CAAC;MACF,OAAOJ,QAAQ;IACnB;EAAC;IAAAsD,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAc;MACV,IAAIrD,OAAO;MACX,IAAI,CAAC9C,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAK;QACtB,IAAIA,CAAC,CAACiE,GAAG,GAAGF,OAAO,IAAIA,OAAO,KAAKvC,SAAS,EAAE;UAC1CuC,OAAO,GAAG/D,CAAC,CAACiE,GAAG;QACnB;MACJ,CAAC,CAAC;MACF,OAAOF,OAAO;IAClB;EAAC;IAAAoD,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAe;MACX,IAAI3D,QAAQ;MACZ,IAAI,CAACxC,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAK;QACtB,IAAIA,CAAC,CAACkE,GAAG,GAAGT,QAAQ,IAAIA,QAAQ,KAAKjC,SAAS,EAAE;UAC5CiC,QAAQ,GAAGzD,CAAC,CAACkE,GAAG;QACpB;MACJ,CAAC,CAAC;MACF,OAAOT,QAAQ;IACnB;EAAC;IAAA0D,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAc;MACV,IAAIzD,OAAO;MACX,IAAI,CAAC1C,KAAK,CAACC,OAAO,CAAC,UAAClB,CAAC,EAAK;QACtB,IAAIA,CAAC,CAACkE,GAAG,GAAGP,OAAO,IAAIA,OAAO,KAAKnC,SAAS,EAAE;UAC1CmC,OAAO,GAAG3D,CAAC,CAACkE,GAAG;QACnB;MACJ,CAAC,CAAC;MACF,OAAOP,OAAO;IAClB;EAAC;EAAA,OAAAsC,SAAA;AAAA;AAGL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0B,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EACpD,IAAIC,UAAU,GAAG,IAAIhC,SAAS,CAAC,CAAC;EAChCgC,UAAU,CAACxH,EAAE,GAAG,IAAI;EAEpBqH,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAGF,IAAI;EACzBG,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAGF,IAAI;EAEzB,IAAIC,IAAI,GAAG,IAAI,CAACI,WAAW,EAAE;IACzB,IAAI,CAACA,WAAW,GAAGJ,IAAI;EAC3B;EAEA,IAAIC,IAAI,GAAG,IAAI,CAACI,WAAW,EAAE;IACzB,IAAI,CAACA,WAAW,GAAGJ,IAAI;EAC3B;EAEA,KAAK,IAAIhC,CAAC,GAAG6B,IAAI,EAAE7B,CAAC,IAAI+B,IAAI,EAAE/B,CAAC,EAAE,EAAE;IAC/B,KAAK,IAAI/F,CAAC,GAAG6H,IAAI,EAAE7H,CAAC,IAAI+H,IAAI,EAAE/H,CAAC,EAAE,EAAE;MAC/B,IAAIkF,GAAG,MAAA6B,MAAA,CAAMvH,KAAK,CAAC4I,aAAa,CAACpI,CAAC,CAAC,EAAA+G,MAAA,CAAGhB,CAAC,CAAE;MACzC,IAAI,CAAC,IAAI,CAAC9E,KAAK,CAACiE,GAAG,CAAC,EAAE;QAClB,IAAI,CAACjE,KAAK,CAACiE,GAAG,CAAC,GAAG,IAAI/F,IAAI,CAAC4G,CAAC,EAAE/F,CAAC,CAAC;MACpC;MACA,IAAI,CAAC,IAAI,CAACuH,IAAI,CAACxB,CAAC,CAAC,EAAE;QACf,IAAI,CAACwB,IAAI,CAACxB,CAAC,CAAC,GAAG,IAAI3G,GAAG,CAAC2G,CAAC,EAAE,IAAI,CAAC;MACnC;MACA,IAAI,IAAI,CAACwB,IAAI,CAACxB,CAAC,CAAC,CAACsC,QAAQ,CAACC,OAAO,CAACpD,GAAG,CAAC,GAAG,CAAC,EAAE;QACxC,IAAI,CAACqC,IAAI,CAACxB,CAAC,CAAC,CAACsC,QAAQ,CAACrB,IAAI,CAAC9B,GAAG,CAAC;MACnC;MAEA+C,UAAU,CAAChH,KAAK,CAAC+F,IAAI,CAAC,IAAI,CAAC/F,KAAK,CAACiE,GAAG,CAAC,CAAC;MACtC+C,UAAU,CAACnH,SAAS,CAACkG,IAAI,CAAC9B,GAAG,CAAC;IAClC;EACJ;EACA,IAAI8C,QAAQ,EAAE;IACVC,UAAU,CAACjH,MAAM,GAAG,IAAI;IACxBgF,UAAU,CAACiC,UAAU,CAAC;EAC1B;EAEA,OAAOA,UAAU;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAhC,SAAS,CAACsC,SAAS,CAACpH,MAAM,GAAG,UAAUb,GAAG,EAAE;EACxC,IAAIA,GAAG,YAAYT,KAAK,EAAE;IACtB,OAAOwB,mBAAmB,CAAC2B,IAAI,CAAC,IAAI,CAAC,CAAC1C,GAAG,CAAC;EAC9C,CAAC,MAAM;IACH,OAAOD,YAAY,CAAC2C,IAAI,CAAC,IAAI,CAAC,CAAC1C,GAAG,CAAC;EACvC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA2F,SAAS,CAACsC,SAAS,CAACjE,KAAK,GAAGvB,WAAW;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACAkD,SAAS,CAACsC,SAAS,CAAC1G,MAAM,GAAGN,YAAY;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA0E,SAAS,CAACsC,SAAS,CAACrG,IAAI,GAAGJ,aAAa;;AAExC;AACA;AACA;AACA;AACA;AACA;AACAmE,SAAS,CAACsC,SAAS,CAACnG,OAAO,GAAGD,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA8D,SAAS,CAACsC,SAAS,CAAC7F,IAAI,GAAGL,UAAU;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA4D,SAAS,CAACsC,SAAS,CAACC,IAAI,GAAG3D,eAAe;AAE1CoB,SAAS,CAACsC,SAAS,CAAC5C,OAAO,GAAGD,aAAa;AAE3C+C,MAAM,CAACC,OAAO,GAAGf,YAAY"}