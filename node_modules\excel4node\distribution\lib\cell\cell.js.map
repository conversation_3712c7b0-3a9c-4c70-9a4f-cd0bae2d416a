{"version": 3, "file": "cell.js", "names": ["utils", "require", "Comment", "Cell", "row", "col", "_classCallCheck", "r", "concat", "getExcelAlpha", "s", "t", "f", "v", "_createClass", "key", "get", "comments", "value", "string", "index", "number", "val", "formula", "bool", "date", "dt", "getExcelTS", "style", "sId", "addToXMLele", "ele", "is", "c<PERSON>le", "att", "txt", "up", "module", "exports"], "sources": ["../../../source/lib/cell/cell.js"], "sourcesContent": ["const utils = require('../utils.js');\nconst Comment = require('../classes/comment');\n\n// §18.3.1.4 c (Cell)\nclass Cell {\n    /**\n   * Create an Excel Cell\n   * @private\n   * @param {Number} row Row of cell.\n   * @param {Number} col Column of cell\n   */\n    constructor(row, col) {\n        if (row <= 0) throw 'Row parameter must not be zero or negative.';\n        if (col <= 0) throw 'Col parameter must not be zero or negative.';\n        this.r = `${utils.getExcelAlpha(col)}${row}`; // 'r' attribute\n        this.s = 0; // 's' attribute refering to style index\n        this.t = null; // 't' attribute stating Cell data type - §18.18.11 ST_CellType (Cell Type)\n        this.f = null; // 'f' child element used for formulas\n        this.v = null; // 'v' child element for values\n        this.row = row; // used internally throughout code. Does not go into XML\n        this.col = col; // used internally throughout code. Does not go into XML\n    }\n\n    get comment() {\n        return this.comments[this.r];\n    }\n\n    string(index) {\n        this.t = 's';\n        this.v = index;\n        this.f = null;\n    }\n\n    number(val) {\n        this.t = 'n';\n        this.v = val;\n        this.f = null;\n    }\n\n    formula(formula) {\n        this.t = null;\n        this.v = null;\n        this.f = formula;\n    }\n\n    bool(val) {\n        this.t = 'b';\n        this.v = val;\n        this.f = null;\n    }\n\n    date(dt) {\n        this.t = null;\n        this.v = utils.getExcelTS(dt);\n        this.f = null;\n    }\n\n    style(sId) {\n        this.s = sId;\n    }\n\n    addToXMLele(ele) {\n        if (this.v === null && this.is === null) {\n            return;\n        }\n\n        let cEle = ele.ele('c').att('r', this.r).att('s', this.s);\n        if (this.t !== null) {\n            cEle.att('t', this.t);\n        }\n        if (this.f !== null) {\n            cEle.ele('f').txt(this.f).up();\n        }\n        if (this.v !== null) {\n            cEle.ele('v').txt(this.v).up();\n        }\n        cEle.up();\n    }\n}\n\nmodule.exports = Cell;\n"], "mappings": ";;;;;AAAA,IAAMA,KAAK,GAAGC,OAAO,CAAC,aAAa,CAAC;AACpC,IAAMC,OAAO,GAAGD,OAAO,CAAC,oBAAoB,CAAC;;AAE7C;AAAA,IACME,IAAI;EACN;AACJ;AACA;AACA;AACA;AACA;EACI,SAAAA,KAAYC,GAAG,EAAEC,GAAG,EAAE;IAAAC,eAAA,OAAAH,IAAA;IAClB,IAAIC,GAAG,IAAI,CAAC,EAAE,MAAM,6CAA6C;IACjE,IAAIC,GAAG,IAAI,CAAC,EAAE,MAAM,6CAA6C;IACjE,IAAI,CAACE,CAAC,MAAAC,MAAA,CAAMR,KAAK,CAACS,aAAa,CAACJ,GAAG,CAAC,EAAAG,MAAA,CAAGJ,GAAG,CAAE,CAAC,CAAC;IAC9C,IAAI,CAACM,CAAC,GAAG,CAAC,CAAC,CAAC;IACZ,IAAI,CAACC,CAAC,GAAG,IAAI,CAAC,CAAC;IACf,IAAI,CAACC,CAAC,GAAG,IAAI,CAAC,CAAC;IACf,IAAI,CAACC,CAAC,GAAG,IAAI,CAAC,CAAC;IACf,IAAI,CAACT,GAAG,GAAGA,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG,CAAC,CAAC;EACpB;EAACS,YAAA,CAAAX,IAAA;IAAAY,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAc;MACV,OAAO,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACV,CAAC,CAAC;IAChC;EAAC;IAAAQ,GAAA;IAAAG,KAAA,EAED,SAAAC,OAAOC,KAAK,EAAE;MACV,IAAI,CAACT,CAAC,GAAG,GAAG;MACZ,IAAI,CAACE,CAAC,GAAGO,KAAK;MACd,IAAI,CAACR,CAAC,GAAG,IAAI;IACjB;EAAC;IAAAG,GAAA;IAAAG,KAAA,EAED,SAAAG,OAAOC,GAAG,EAAE;MACR,IAAI,CAACX,CAAC,GAAG,GAAG;MACZ,IAAI,CAACE,CAAC,GAAGS,GAAG;MACZ,IAAI,CAACV,CAAC,GAAG,IAAI;IACjB;EAAC;IAAAG,GAAA;IAAAG,KAAA,EAED,SAAAK,QAAQA,QAAO,EAAE;MACb,IAAI,CAACZ,CAAC,GAAG,IAAI;MACb,IAAI,CAACE,CAAC,GAAG,IAAI;MACb,IAAI,CAACD,CAAC,GAAGW,QAAO;IACpB;EAAC;IAAAR,GAAA;IAAAG,KAAA,EAED,SAAAM,KAAKF,GAAG,EAAE;MACN,IAAI,CAACX,CAAC,GAAG,GAAG;MACZ,IAAI,CAACE,CAAC,GAAGS,GAAG;MACZ,IAAI,CAACV,CAAC,GAAG,IAAI;IACjB;EAAC;IAAAG,GAAA;IAAAG,KAAA,EAED,SAAAO,KAAKC,EAAE,EAAE;MACL,IAAI,CAACf,CAAC,GAAG,IAAI;MACb,IAAI,CAACE,CAAC,GAAGb,KAAK,CAAC2B,UAAU,CAACD,EAAE,CAAC;MAC7B,IAAI,CAACd,CAAC,GAAG,IAAI;IACjB;EAAC;IAAAG,GAAA;IAAAG,KAAA,EAED,SAAAU,MAAMC,GAAG,EAAE;MACP,IAAI,CAACnB,CAAC,GAAGmB,GAAG;IAChB;EAAC;IAAAd,GAAA;IAAAG,KAAA,EAED,SAAAY,YAAYC,GAAG,EAAE;MACb,IAAI,IAAI,CAAClB,CAAC,KAAK,IAAI,IAAI,IAAI,CAACmB,EAAE,KAAK,IAAI,EAAE;QACrC;MACJ;MAEA,IAAIC,IAAI,GAAGF,GAAG,CAACA,GAAG,CAAC,GAAG,CAAC,CAACG,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC3B,CAAC,CAAC,CAAC2B,GAAG,CAAC,GAAG,EAAE,IAAI,CAACxB,CAAC,CAAC;MACzD,IAAI,IAAI,CAACC,CAAC,KAAK,IAAI,EAAE;QACjBsB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,IAAI,CAACvB,CAAC,CAAC;MACzB;MACA,IAAI,IAAI,CAACC,CAAC,KAAK,IAAI,EAAE;QACjBqB,IAAI,CAACF,GAAG,CAAC,GAAG,CAAC,CAACI,GAAG,CAAC,IAAI,CAACvB,CAAC,CAAC,CAACwB,EAAE,CAAC,CAAC;MAClC;MACA,IAAI,IAAI,CAACvB,CAAC,KAAK,IAAI,EAAE;QACjBoB,IAAI,CAACF,GAAG,CAAC,GAAG,CAAC,CAACI,GAAG,CAAC,IAAI,CAACtB,CAAC,CAAC,CAACuB,EAAE,CAAC,CAAC;MAClC;MACAH,IAAI,CAACG,EAAE,CAAC,CAAC;IACb;EAAC;EAAA,OAAAjC,IAAA;AAAA;AAGLkC,MAAM,CAACC,OAAO,GAAGnC,IAAI"}