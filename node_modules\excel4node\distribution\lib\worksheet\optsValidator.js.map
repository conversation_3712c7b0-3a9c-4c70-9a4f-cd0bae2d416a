{"version": 3, "file": "optsValidator.js", "names": ["types", "require", "optsTypes", "getObjItem", "obj", "key", "returnObj", "levels", "split", "length", "thisLevelKey", "shift", "e", "validator", "val", "type", "sizes", "Object", "keys", "paperSize", "indexOf", "TypeError", "join", "pageOrder", "validate", "orientation", "positiveUniversalMeasure", "cellComment", "printError", "pane", "paneState", "parseFloat", "parseInt", "traverse", "o", "keyParts", "func", "i", "thisKeyParts", "concat", "thisKey", "thisType", "thisItem", "_typeof", "module", "exports", "opts"], "sources": ["../../../source/lib/worksheet/optsValidator.js"], "sourcesContent": ["const types = require('../types/index.js');\n\nconst optsTypes = {\n    'margins': {\n        'bottom': 'Float',\n        'footer': 'Float',\n        'header': 'Float',\n        'left': 'Float',\n        'right': 'Float',\n        'top': 'Float'\n    },\n    'printOptions': {\n        'centerHorizontal': 'Boolean',\n        'centerVertical': 'Boolean',\n        'printGridLines': 'Boolean',\n        'printHeadings': 'Boolean'\n    \n    },\n    'pageSetup': {\n        'blackAndWhite': 'Boolean',\n        'cellComments': 'CELL_COMMENTS',\n        'copies': 'Integer',\n        'draft': 'Boolean',\n        'errors': 'PRINT_ERROR',\n        'firstPageNumber': 'Boolean',\n        'fitToHeight': 'Integer',\n        'fitToWidth': 'Integer',\n        'horizontalDpi': 'Integer',\n        'orientation': 'ORIENTATION',\n        'pageOrder': 'PAGE_ORDER',\n        'paperHeight': 'POSITIVE_UNIVERSAL_MEASURE',\n        'paperSize': 'PAPER_SIZE',\n        'paperWidth': 'POSITIVE_UNIVERSAL_MEASURE',\n        'scale': 'Integer',\n        'useFirstPageNumber': 'Boolean',\n        'usePrinterDefaults': 'Boolean',\n        'verticalDpi': 'Integer'\n    },\n    'headerFooter': {\n        'evenFooter': 'String',\n        'evenHeader': 'String',\n        'firstFooter': 'String',\n        'firstHeader': 'String',\n        'oddFooter': 'String',\n        'oddHeader': 'String',\n        'alignWithMargins': 'Boolean',\n        'differentFirst': 'Boolean',\n        'differentOddEven': 'Boolean',\n        'scaleWithDoc': 'Boolean'\n    },\n    'sheetView': {\n        'pane': {\n            'activePane': 'PANE',\n            'state': 'PANE_STATE',\n            'topLeftCell': null,\n            'xSplit': null,\n            'ySplit': null\n        },\n        'tabSelected': null,\n        'workbookViewId': null,\n        'rightToLeft': null,\n        'showGridLines': null,\n        'zoomScale': null,\n        'zoomScaleNormal': null,\n        'zoomScalePageLayoutView': null\n    },\n    'sheetFormat': {\n        'baseColWidth': null,\n        'customHeight': null,\n        'defaultColWidth': null,\n        'defaultRowHeight': null,\n        'outlineLevelCol': null,\n        'outlineLevelRow': null,\n        'thickBottom': null,\n        'thickTop': null,\n        'zeroHeight': null\n    },\n    'sheetProtection': {\n        'autoFilter': null,\n        'deleteColumns': null,\n        'deleteRow': null,\n        'formatCells': null,\n        'formatColumns': null,\n        'formatRows': null,\n        'hashValue': null,\n        'insertColumns': null,\n        'insertHyperlinks': null,\n        'insertRows': null,\n        'objects': null,\n        'password': null,\n        'pivotTables': null,\n        'scenarios': null,\n        'selectLockedCells': null,\n        'selectUnlockedCell': null,\n        'sheet': null,\n        'sort': null\n    },\n    'outline': {\n        'summaryBelow': null\n    },\n    'autoFilter': {\n        'startRow': null,\n        'endRow': null,\n        'startCol': null,\n        'endCol': null,\n        'filters': null\n    },\n    'hidden': 'Boolean'\n};\n\nlet getObjItem = (obj, key) => {\n    let returnObj = obj;\n    let levels = key.split('.');\n\n    while (levels.length > 0) {\n        let thisLevelKey = levels.shift();\n        try {\n            returnObj = returnObj[thisLevelKey];\n        } catch (e) {\n            //returnObj = undefined;\n        }\n    }\n    return returnObj;\n};\n\nlet validator = function (key, val, type) {\n    switch (type) {\n\n    case 'PAPER_SIZE': \n        let sizes = Object.keys(types.paperSize);\n        if (sizes.indexOf(val) < 0) {\n            throw new TypeError('Invalid value for ' + key + '. Value must be one of ' + sizes.join(', '));\n        }\n        break;\n\n    case 'PAGE_ORDER':\n        types.pageOrder.validate(val);\n        break;\n\n    case 'ORIENTATION':\n        types.orientation.validate(val);\n        break;\n\n    case 'POSITIVE_UNIVERSAL_MEASURE': \n        types.positiveUniversalMeasure.validate(val);\n        break;\n\n    case 'CELL_COMMENTS':\n        types.cellComment.validate(val);\n        break;\n\n    case 'PRINT_ERROR': \n        types.printError.validate(val);\n        break;\n\n    case 'PANE':\n        types.pane.validate(val);\n        break;\n\n    case 'PANE_STATE':\n        types.paneState.validate(val);\n        break;\n\n    case 'Boolean':\n        if ([true, false, 1, 0].indexOf(val) < 0) {\n            throw new TypeError(key + ' expects value of true, false, 1 or 0');\n        }\n        break;\n\n    case 'Float': \n        if (parseFloat(val) !== val) {\n            throw new TypeError(key + ' expects value as a Float number');\n        }\n        break;\n\n    case 'Integer':\n        if (parseInt(val) !== val) {\n            throw new TypeError(key + ' expects value as an Integer');\n        }\n        break;\n\n    case 'String': \n        if (typeof val !== 'string') {\n            throw new TypeError(key + ' expects value as a String');\n        }\n        break;\n\n    default:\n        break;\n    }\n};\n\nlet traverse = function (o, keyParts, func) {\n    for (let i in o) {\n        let thisKeyParts = keyParts.concat(i);\n        let thisKey = thisKeyParts.join('.');\n        let thisType = getObjItem(optsTypes, thisKey);\n\n        if (typeof thisType === 'string') {\n            let thisItem = o[i];\n            func(thisKey, thisItem, thisType); \n        }\n        if (o[i] !== null && typeof o[i] === 'object') {\n            traverse(o[i], thisKeyParts, func);\n        }\n    }\n};\n\nmodule.exports = (opts) => {\n    traverse(opts, [], validator);\n};"], "mappings": ";;;AAAA,IAAMA,KAAK,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAE1C,IAAMC,SAAS,GAAG;EACd,SAAS,EAAE;IACP,QAAQ,EAAE,OAAO;IACjB,QAAQ,EAAE,OAAO;IACjB,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,OAAO;IACf,OAAO,EAAE,OAAO;IAChB,KAAK,EAAE;EACX,CAAC;EACD,cAAc,EAAE;IACZ,kBAAkB,EAAE,SAAS;IAC7B,gBAAgB,EAAE,SAAS;IAC3B,gBAAgB,EAAE,SAAS;IAC3B,eAAe,EAAE;EAErB,CAAC;EACD,WAAW,EAAE;IACT,eAAe,EAAE,SAAS;IAC1B,cAAc,EAAE,eAAe;IAC/B,QAAQ,EAAE,SAAS;IACnB,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,aAAa;IACvB,iBAAiB,EAAE,SAAS;IAC5B,aAAa,EAAE,SAAS;IACxB,YAAY,EAAE,SAAS;IACvB,eAAe,EAAE,SAAS;IAC1B,aAAa,EAAE,aAAa;IAC5B,WAAW,EAAE,YAAY;IACzB,aAAa,EAAE,4BAA4B;IAC3C,WAAW,EAAE,YAAY;IACzB,YAAY,EAAE,4BAA4B;IAC1C,OAAO,EAAE,SAAS;IAClB,oBAAoB,EAAE,SAAS;IAC/B,oBAAoB,EAAE,SAAS;IAC/B,aAAa,EAAE;EACnB,CAAC;EACD,cAAc,EAAE;IACZ,YAAY,EAAE,QAAQ;IACtB,YAAY,EAAE,QAAQ;IACtB,aAAa,EAAE,QAAQ;IACvB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,WAAW,EAAE,QAAQ;IACrB,kBAAkB,EAAE,SAAS;IAC7B,gBAAgB,EAAE,SAAS;IAC3B,kBAAkB,EAAE,SAAS;IAC7B,cAAc,EAAE;EACpB,CAAC;EACD,WAAW,EAAE;IACT,MAAM,EAAE;MACJ,YAAY,EAAE,MAAM;MACpB,OAAO,EAAE,YAAY;MACrB,aAAa,EAAE,IAAI;MACnB,QAAQ,EAAE,IAAI;MACd,QAAQ,EAAE;IACd,CAAC;IACD,aAAa,EAAE,IAAI;IACnB,gBAAgB,EAAE,IAAI;IACtB,aAAa,EAAE,IAAI;IACnB,eAAe,EAAE,IAAI;IACrB,WAAW,EAAE,IAAI;IACjB,iBAAiB,EAAE,IAAI;IACvB,yBAAyB,EAAE;EAC/B,CAAC;EACD,aAAa,EAAE;IACX,cAAc,EAAE,IAAI;IACpB,cAAc,EAAE,IAAI;IACpB,iBAAiB,EAAE,IAAI;IACvB,kBAAkB,EAAE,IAAI;IACxB,iBAAiB,EAAE,IAAI;IACvB,iBAAiB,EAAE,IAAI;IACvB,aAAa,EAAE,IAAI;IACnB,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE;EAClB,CAAC;EACD,iBAAiB,EAAE;IACf,YAAY,EAAE,IAAI;IAClB,eAAe,EAAE,IAAI;IACrB,WAAW,EAAE,IAAI;IACjB,aAAa,EAAE,IAAI;IACnB,eAAe,EAAE,IAAI;IACrB,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,IAAI;IACrB,kBAAkB,EAAE,IAAI;IACxB,YAAY,EAAE,IAAI;IAClB,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,IAAI;IACjB,mBAAmB,EAAE,IAAI;IACzB,oBAAoB,EAAE,IAAI;IAC1B,OAAO,EAAE,IAAI;IACb,MAAM,EAAE;EACZ,CAAC;EACD,SAAS,EAAE;IACP,cAAc,EAAE;EACpB,CAAC;EACD,YAAY,EAAE;IACV,UAAU,EAAE,IAAI;IAChB,QAAQ,EAAE,IAAI;IACd,UAAU,EAAE,IAAI;IAChB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;AACd,CAAC;AAED,IAAIC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,GAAG,EAAEC,GAAG,EAAK;EAC3B,IAAIC,SAAS,GAAGF,GAAG;EACnB,IAAIG,MAAM,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;EAE3B,OAAOD,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;IACtB,IAAIC,YAAY,GAAGH,MAAM,CAACI,KAAK,CAAC,CAAC;IACjC,IAAI;MACAL,SAAS,GAAGA,SAAS,CAACI,YAAY,CAAC;IACvC,CAAC,CAAC,OAAOE,CAAC,EAAE;MACR;IAAA;EAER;EACA,OAAON,SAAS;AACpB,CAAC;AAED,IAAIO,SAAS,GAAG,SAAZA,SAASA,CAAaR,GAAG,EAAES,GAAG,EAAEC,IAAI,EAAE;EACtC,QAAQA,IAAI;IAEZ,KAAK,YAAY;MACb,IAAIC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAAClB,KAAK,CAACmB,SAAS,CAAC;MACxC,IAAIH,KAAK,CAACI,OAAO,CAACN,GAAG,CAAC,GAAG,CAAC,EAAE;QACxB,MAAM,IAAIO,SAAS,CAAC,oBAAoB,GAAGhB,GAAG,GAAG,yBAAyB,GAAGW,KAAK,CAACM,IAAI,CAAC,IAAI,CAAC,CAAC;MAClG;MACA;IAEJ,KAAK,YAAY;MACbtB,KAAK,CAACuB,SAAS,CAACC,QAAQ,CAACV,GAAG,CAAC;MAC7B;IAEJ,KAAK,aAAa;MACdd,KAAK,CAACyB,WAAW,CAACD,QAAQ,CAACV,GAAG,CAAC;MAC/B;IAEJ,KAAK,4BAA4B;MAC7Bd,KAAK,CAAC0B,wBAAwB,CAACF,QAAQ,CAACV,GAAG,CAAC;MAC5C;IAEJ,KAAK,eAAe;MAChBd,KAAK,CAAC2B,WAAW,CAACH,QAAQ,CAACV,GAAG,CAAC;MAC/B;IAEJ,KAAK,aAAa;MACdd,KAAK,CAAC4B,UAAU,CAACJ,QAAQ,CAACV,GAAG,CAAC;MAC9B;IAEJ,KAAK,MAAM;MACPd,KAAK,CAAC6B,IAAI,CAACL,QAAQ,CAACV,GAAG,CAAC;MACxB;IAEJ,KAAK,YAAY;MACbd,KAAK,CAAC8B,SAAS,CAACN,QAAQ,CAACV,GAAG,CAAC;MAC7B;IAEJ,KAAK,SAAS;MACV,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAACM,OAAO,CAACN,GAAG,CAAC,GAAG,CAAC,EAAE;QACtC,MAAM,IAAIO,SAAS,CAAChB,GAAG,GAAG,uCAAuC,CAAC;MACtE;MACA;IAEJ,KAAK,OAAO;MACR,IAAI0B,UAAU,CAACjB,GAAG,CAAC,KAAKA,GAAG,EAAE;QACzB,MAAM,IAAIO,SAAS,CAAChB,GAAG,GAAG,kCAAkC,CAAC;MACjE;MACA;IAEJ,KAAK,SAAS;MACV,IAAI2B,QAAQ,CAAClB,GAAG,CAAC,KAAKA,GAAG,EAAE;QACvB,MAAM,IAAIO,SAAS,CAAChB,GAAG,GAAG,8BAA8B,CAAC;MAC7D;MACA;IAEJ,KAAK,QAAQ;MACT,IAAI,OAAOS,GAAG,KAAK,QAAQ,EAAE;QACzB,MAAM,IAAIO,SAAS,CAAChB,GAAG,GAAG,4BAA4B,CAAC;MAC3D;MACA;IAEJ;MACI;EACJ;AACJ,CAAC;AAED,IAAI4B,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,CAAC,EAAEC,QAAQ,EAAEC,IAAI,EAAE;EACxC,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACb,IAAII,YAAY,GAAGH,QAAQ,CAACI,MAAM,CAACF,CAAC,CAAC;IACrC,IAAIG,OAAO,GAAGF,YAAY,CAAChB,IAAI,CAAC,GAAG,CAAC;IACpC,IAAImB,QAAQ,GAAGtC,UAAU,CAACD,SAAS,EAAEsC,OAAO,CAAC;IAE7C,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;MAC9B,IAAIC,QAAQ,GAAGR,CAAC,CAACG,CAAC,CAAC;MACnBD,IAAI,CAACI,OAAO,EAAEE,QAAQ,EAAED,QAAQ,CAAC;IACrC;IACA,IAAIP,CAAC,CAACG,CAAC,CAAC,KAAK,IAAI,IAAIM,OAAA,CAAOT,CAAC,CAACG,CAAC,CAAC,MAAK,QAAQ,EAAE;MAC3CJ,QAAQ,CAACC,CAAC,CAACG,CAAC,CAAC,EAAEC,YAAY,EAAEF,IAAI,CAAC;IACtC;EACJ;AACJ,CAAC;AAEDQ,MAAM,CAACC,OAAO,GAAG,UAACC,IAAI,EAAK;EACvBb,QAAQ,CAACa,IAAI,EAAE,EAAE,EAAEjC,SAAS,CAAC;AACjC,CAAC"}