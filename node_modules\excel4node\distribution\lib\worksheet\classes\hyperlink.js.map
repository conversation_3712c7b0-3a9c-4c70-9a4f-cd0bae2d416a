{"version": 3, "file": "hyperlink.js", "names": ["Hyperlink", "opts", "_classCallCheck", "ref", "undefined", "TypeError", "display", "location", "tooltip", "id", "_createClass", "key", "get", "value", "addToXMLEle", "ele", "thisEle", "att", "rId", "up", "HyperlinkCollection", "links", "length", "add", "thisLink", "push", "addToXMLele", "linksEle", "for<PERSON>ach", "l", "module", "exports"], "sources": ["../../../../source/lib/worksheet/classes/hyperlink.js"], "sourcesContent": ["\nclass Hyperlink { //§********* hyperlink (Hyperlink)\n    constructor(opts) {\n        opts = opts ? opts : {};\n        \n        if (opts.ref === undefined) {\n            throw new TypeError('ref is a required option when creating a hyperlink');\n        }\n        this.ref = opts.ref;\n\n        if (opts.display !== undefined) {\n            this.display = opts.display;\n        } else {\n            this.display = opts.location;\n        }\n        if (opts.location !== undefined) {\n            this.location = opts.location;\n        }\n        if (opts.tooltip !== undefined) {\n            this.tooltip = opts.tooltip;\n        } else {\n            this.tooltip = opts.location;\n        }\n        this.id;\n    }\n\n    get rId() {\n        return 'rId' + this.id;\n    }\n\n    addToXMLEle(ele) {\n        let thisEle = ele.ele('hyperlink');\n        thisEle.att('ref', this.ref);\n        thisEle.att('r:id', this.rId);\n        if (this.display !== undefined) {\n            thisEle.att('display', this.display);\n        }\n        if (this.location !== undefined) {\n            thisEle.att('address', this.location);\n        }\n        if (this.tooltip !== undefined) {\n            thisEle.att('tooltip', this.tooltip);\n        }\n        thisEle.up();  \n    }\n}\n\nclass HyperlinkCollection { //§********* hyperlinks (Hyperlinks)\n    constructor() {\n        this.links = [];\n    }\n\n    get length() {\n        return this.links.length;\n    }\n\n    add(opts) {\n        let thisLink = new Hyperlink(opts);\n        thisLink.id = this.links.length + 1;\n        this.links.push(thisLink);\n        return thisLink;\n    }\n\n    addToXMLele(ele) {\n        if (this.length > 0) {\n            let linksEle = ele.ele('hyperlinks');\n            this.links.forEach((l) => {\n                l.addToXMLEle(linksEle);\n            });\n            linksEle.up();\n        }\n    }\n}\n\nmodule.exports = { HyperlinkCollection, Hyperlink };"], "mappings": ";;;;;IACMA,SAAS;EAAG;EACd,SAAAA,UAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,SAAA;IACdC,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC;IAEvB,IAAIA,IAAI,CAACE,GAAG,KAAKC,SAAS,EAAE;MACxB,MAAM,IAAIC,SAAS,CAAC,oDAAoD,CAAC;IAC7E;IACA,IAAI,CAACF,GAAG,GAAGF,IAAI,CAACE,GAAG;IAEnB,IAAIF,IAAI,CAACK,OAAO,KAAKF,SAAS,EAAE;MAC5B,IAAI,CAACE,OAAO,GAAGL,IAAI,CAACK,OAAO;IAC/B,CAAC,MAAM;MACH,IAAI,CAACA,OAAO,GAAGL,IAAI,CAACM,QAAQ;IAChC;IACA,IAAIN,IAAI,CAACM,QAAQ,KAAKH,SAAS,EAAE;MAC7B,IAAI,CAACG,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACjC;IACA,IAAIN,IAAI,CAACO,OAAO,KAAKJ,SAAS,EAAE;MAC5B,IAAI,CAACI,OAAO,GAAGP,IAAI,CAACO,OAAO;IAC/B,CAAC,MAAM;MACH,IAAI,CAACA,OAAO,GAAGP,IAAI,CAACM,QAAQ;IAChC;IACA,IAAI,CAACE,EAAE;EACX;EAACC,YAAA,CAAAV,SAAA;IAAAW,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAU;MACN,OAAO,KAAK,GAAG,IAAI,CAACH,EAAE;IAC1B;EAAC;IAAAE,GAAA;IAAAE,KAAA,EAED,SAAAC,YAAYC,GAAG,EAAE;MACb,IAAIC,OAAO,GAAGD,GAAG,CAACA,GAAG,CAAC,WAAW,CAAC;MAClCC,OAAO,CAACC,GAAG,CAAC,KAAK,EAAE,IAAI,CAACd,GAAG,CAAC;MAC5Ba,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACC,GAAG,CAAC;MAC7B,IAAI,IAAI,CAACZ,OAAO,KAAKF,SAAS,EAAE;QAC5BY,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACX,OAAO,CAAC;MACxC;MACA,IAAI,IAAI,CAACC,QAAQ,KAAKH,SAAS,EAAE;QAC7BY,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACV,QAAQ,CAAC;MACzC;MACA,IAAI,IAAI,CAACC,OAAO,KAAKJ,SAAS,EAAE;QAC5BY,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACT,OAAO,CAAC;MACxC;MACAQ,OAAO,CAACG,EAAE,CAAC,CAAC;IAChB;EAAC;EAAA,OAAAnB,SAAA;AAAA;AAAA,IAGCoB,mBAAmB;EAAG;EACxB,SAAAA,oBAAA,EAAc;IAAAlB,eAAA,OAAAkB,mBAAA;IACV,IAAI,CAACC,KAAK,GAAG,EAAE;EACnB;EAACX,YAAA,CAAAU,mBAAA;IAAAT,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,OAAO,IAAI,CAACS,KAAK,CAACC,MAAM;IAC5B;EAAC;IAAAX,GAAA;IAAAE,KAAA,EAED,SAAAU,IAAItB,IAAI,EAAE;MACN,IAAIuB,QAAQ,GAAG,IAAIxB,SAAS,CAACC,IAAI,CAAC;MAClCuB,QAAQ,CAACf,EAAE,GAAG,IAAI,CAACY,KAAK,CAACC,MAAM,GAAG,CAAC;MACnC,IAAI,CAACD,KAAK,CAACI,IAAI,CAACD,QAAQ,CAAC;MACzB,OAAOA,QAAQ;IACnB;EAAC;IAAAb,GAAA;IAAAE,KAAA,EAED,SAAAa,YAAYX,GAAG,EAAE;MACb,IAAI,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;QACjB,IAAIK,QAAQ,GAAGZ,GAAG,CAACA,GAAG,CAAC,YAAY,CAAC;QACpC,IAAI,CAACM,KAAK,CAACO,OAAO,CAAC,UAACC,CAAC,EAAK;UACtBA,CAAC,CAACf,WAAW,CAACa,QAAQ,CAAC;QAC3B,CAAC,CAAC;QACFA,QAAQ,CAACR,EAAE,CAAC,CAAC;MACjB;IACJ;EAAC;EAAA,OAAAC,mBAAA;AAAA;AAGLU,MAAM,CAACC,OAAO,GAAG;EAAEX,mBAAmB,EAAnBA,mBAAmB;EAAEpB,SAAS,EAATA;AAAU,CAAC"}