import{u as a,e,j as s,B as t,h as n,G as i,d as r,f as d,l,i as c,a5 as o,W as x,a6 as h,a7 as m,a8 as j,a9 as p,aa as u,A as g,m as y,L as b,I as f,z as v}from"./mui-core-BBO2DoRL.js";import{r as A}from"./vendor-CeOqOr8o.js";import{e as k,D as C,C as w,a as S,L as W,P as G,b as $,B,A as I,p as z,c as M,d as T,i as E}from"./charts-chartjs-Dl1vZNhv.js";import{u as P}from"./routing-B6PnZiBG.js";import{u as R}from"./i18n-DWU17bW_.js";import{m as D}from"./animation-BJm6nf7i.js";import{l as N,a8 as Y,a9 as F,C as L,m as K,n as O,i as Q}from"./mui-icons-BXhTkfAe.js";import"./utils-misc-NFmYzBmQ.js";w.register(S,W,G,$,B,I,z,M,T,<PERSON>);const U="Mrs. <PERSON>riya <PERSON>",V=[{grade:10,section:"A",students:35,board:"CBSE"},{grade:10,section:"B",students:32,board:"CBSE"},{grade:9,section:"A",students:38,board:"CBSE"}],Z=105,q=12,H=3,J=[{id:1,name:"Sanju Kumar Reddy",class:"10-A",subject:"Mathematics",lastGrade:"A1",attendance:95,status:"Excellent"},{id:2,name:"Niraimathi Selvam",class:"10-A",subject:"Mathematics",lastGrade:"A2",attendance:92,status:"Good"},{id:3,name:"Mahesh Reddy",class:"10-B",subject:"Mathematics",lastGrade:"B1",attendance:88,status:"Average"},{id:4,name:"Ravi Teja Sharma",class:"9-A",subject:"Physics",lastGrade:"A1",attendance:94,status:"Excellent"},{id:5,name:"Ankitha Patel",class:"10-A",subject:"Mathematics",lastGrade:"A2",attendance:90,status:"Good"},{id:6,name:"Sirisha Nair",class:"10-B",subject:"Physics",lastGrade:"A1",attendance:96,status:"Excellent"},{id:7,name:"Priya Agarwal",class:"9-A",subject:"Mathematics",lastGrade:"B2",attendance:85,status:"Average"}],X=()=>{const w=a(),S=P(),{t:W}=R(["common","dashboard"]),[G,$]=A.useState(V[0]),[B,I]=A.useState(!1),z={labels:["A1","A2","B1","B2","C1","C2"],datasets:[{label:"Grade Distribution",data:[12,15,8,6,3,1],backgroundColor:[w.palette.success.main,w.palette.success.light,w.palette.info.main,w.palette.info.light,w.palette.warning.main,w.palette.error.main],borderWidth:2,borderColor:"#fff"}]},M={labels:["Mon","Tue","Wed","Thu","Fri","Sat"],datasets:[{label:"Class 10-A",data:[95,92,94,96,93,89],borderColor:w.palette.primary.main,backgroundColor:e(w.palette.primary.main,.1),fill:!0,tension:.4},{label:"Class 10-B",data:[88,90,87,91,89,85],borderColor:w.palette.secondary.main,backgroundColor:e(w.palette.secondary.main,.1),fill:!0,tension:.4}]},T=()=>{S("/dashboard/grades")},E=()=>{S("/dashboard/attendance")},X=a=>{switch(a){case"Excellent":return"success";case"Good":return"info";case"Average":return"warning";case"Needs Attention":return"error";default:return"default"}};return s.jsxs(t,{sx:{maxWidth:1400,mx:"auto",p:3},children:[s.jsx(D.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(t,{sx:{mb:4},children:[s.jsx(n,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${w.palette.primary.main} 0%, ${w.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Teacher Dashboard"}),s.jsxs(n,{variant:"body1",color:"text.secondary",children:["Welcome back, ",U,"! Manage your classes and track student progress."]})]})}),s.jsxs(i,{container:!0,spacing:3,sx:{mb:4},children:[s.jsx(i,{item:!0,xs:12,sm:6,md:3,children:s.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},children:s.jsx(r,{sx:{background:`linear-gradient(135deg, ${w.palette.primary.main} 0%, ${w.palette.primary.dark} 100%)`,color:"white",cursor:"pointer",transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},children:s.jsx(d,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(t,{children:[s.jsx(n,{variant:"h4",sx:{fontWeight:600},children:Z}),s.jsx(n,{variant:"body2",sx:{opacity:.9},children:"Total Students"})]}),s.jsx(N,{sx:{fontSize:40,opacity:.8}})]})})})})}),s.jsx(i,{item:!0,xs:12,sm:6,md:3,children:s.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:s.jsx(r,{sx:{background:`linear-gradient(135deg, ${w.palette.success.main} 0%, ${w.palette.success.dark} 100%)`,color:"white",cursor:"pointer",transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},onClick:T,children:s.jsx(d,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(t,{children:[s.jsx(n,{variant:"h4",sx:{fontWeight:600},children:q}),s.jsx(n,{variant:"body2",sx:{opacity:.9},children:"Pending Grades"})]}),s.jsx(Y,{sx:{fontSize:40,opacity:.8}})]})})})})}),s.jsx(i,{item:!0,xs:12,sm:6,md:3,children:s.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},children:s.jsx(r,{sx:{background:`linear-gradient(135deg, ${w.palette.warning.main} 0%, ${w.palette.warning.dark} 100%)`,color:"white",cursor:"pointer",transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},children:s.jsx(d,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(t,{children:[s.jsx(n,{variant:"h4",sx:{fontWeight:600},children:H}),s.jsx(n,{variant:"body2",sx:{opacity:.9},children:"Upcoming Tests"})]}),s.jsx(F,{sx:{fontSize:40,opacity:.8}})]})})})})}),s.jsx(i,{item:!0,xs:12,sm:6,md:3,children:s.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:s.jsx(r,{sx:{background:`linear-gradient(135deg, ${w.palette.info.main} 0%, ${w.palette.info.dark} 100%)`,color:"white",cursor:"pointer",transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},onClick:E,children:s.jsx(d,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(t,{children:[s.jsx(n,{variant:"h4",sx:{fontWeight:600},children:"92%"}),s.jsx(n,{variant:"body2",sx:{opacity:.9},children:"Avg Attendance"})]}),s.jsx(L,{sx:{fontSize:40,opacity:.8}})]})})})})})]}),s.jsx(r,{sx:{mb:4},children:s.jsxs(d,{children:[s.jsx(n,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Quick Actions"}),s.jsxs(l,{direction:"row",spacing:2,flexWrap:"wrap",useFlexGap:!0,children:[s.jsx(c,{variant:"contained",startIcon:s.jsx(Y,{}),onClick:T,sx:{background:`linear-gradient(135deg, ${w.palette.primary.main} 0%, ${w.palette.secondary.main} 100%)`},children:"Enter Grades"}),s.jsx(c,{variant:"outlined",startIcon:s.jsx(L,{}),onClick:E,children:"Mark Attendance"}),s.jsx(c,{variant:"outlined",startIcon:s.jsx(K,{}),onClick:()=>S("/dashboard/swot/wizard"),children:"SWOT Analysis"}),s.jsx(c,{variant:"outlined",startIcon:s.jsx(F,{}),onClick:()=>S("/dashboard/reports/generate"),children:"Generate Reports"}),s.jsx(c,{variant:"outlined",startIcon:s.jsx(O,{}),onClick:()=>S("/dashboard/students/register"),children:"Add Student"})]})]})}),s.jsxs(i,{container:!0,spacing:3,sx:{mb:4},children:[s.jsx(i,{item:!0,xs:12,md:8,children:s.jsx(r,{children:s.jsxs(d,{children:[s.jsx(n,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Weekly Attendance Trends"}),s.jsx(t,{sx:{height:300},children:s.jsx(k,{data:M,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!1,min:80,max:100}}}})})]})})}),s.jsx(i,{item:!0,xs:12,md:4,children:s.jsx(r,{children:s.jsxs(d,{children:[s.jsx(n,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Grade Distribution"}),s.jsx(t,{sx:{height:300},children:s.jsx(C,{data:z,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}})})]})})})]}),s.jsx(r,{children:s.jsxs(d,{children:[s.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[s.jsx(n,{variant:"h6",sx:{fontWeight:600,color:"primary.main"},children:"Recent Student Performance"}),s.jsx(c,{variant:"outlined",size:"small",onClick:()=>S("/dashboard/students"),children:"View All Students"})]}),s.jsx(o,{component:x,variant:"outlined",children:s.jsxs(h,{children:[s.jsx(m,{children:s.jsxs(j,{children:[s.jsx(p,{children:"Student Name"}),s.jsx(p,{children:"Class"}),s.jsx(p,{children:"Subject"}),s.jsx(p,{children:"Last Grade"}),s.jsx(p,{children:"Attendance"}),s.jsx(p,{children:"Status"}),s.jsx(p,{children:"Actions"})]})}),s.jsx(u,{children:J.map((a=>s.jsxs(j,{hover:!0,children:[s.jsx(p,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",gap:2},children:[s.jsx(g,{sx:{width:32,height:32},children:a.name.charAt(0)}),s.jsx(n,{variant:"body2",sx:{fontWeight:500},children:a.name})]})}),s.jsx(p,{children:a.class}),s.jsx(p,{children:a.subject}),s.jsx(p,{children:s.jsx(y,{label:a.lastGrade,color:a.lastGrade.startsWith("A")?"success":"warning",size:"small"})}),s.jsx(p,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(b,{variant:"determinate",value:a.attendance,sx:{width:60,height:6,borderRadius:3},color:a.attendance>=90?"success":a.attendance>=75?"warning":"error"}),s.jsxs(n,{variant:"body2",children:[a.attendance,"%"]})]})}),s.jsx(p,{children:s.jsx(y,{label:a.status,color:X(a.status),size:"small",variant:"outlined"})}),s.jsx(p,{children:s.jsxs(l,{direction:"row",spacing:1,children:[s.jsx(f,{size:"small",onClick:()=>{return e=a.id,void S(`/dashboard/students/${e}`);var e},color:"primary",children:s.jsx(Q,{})}),s.jsx(f,{size:"small",onClick:()=>{return e=a.id,void S(`/dashboard/students/${e}/swot`);var e},color:"secondary",children:s.jsx(K,{})})]})})]},a.id)))})]})})]})}),s.jsx(v,{color:"primary",sx:{position:"fixed",bottom:24,right:24,background:`linear-gradient(135deg, ${w.palette.primary.main} 0%, ${w.palette.secondary.main} 100%)`},onClick:()=>S("/dashboard/students/register"),children:s.jsx(O,{})})]})};export{X as default};
//# sourceMappingURL=TeacherDashboard-D8R1Yo_8.js.map
