{"version": 3, "file": "StudentRegistration--Cq_MLtD.js", "sources": ["../../src/components/Students/StudentRegistration.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Student Registration Component\n * \n * Comprehensive student registration form with Indian educational board support\n * Features multi-step form, validation, and cultural context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Button,\n  TextField,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormControlLabel,\n  Checkbox,\n  Avatar,\n  IconButton,\n  Alert,\n  Chip,\n  Divider,\n  Stack,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Person,\n  School,\n  ContactPhone,\n  PhotoCamera,\n  Save,\n  ArrowBack,\n  ArrowForward,\n  CheckCircle,\n} from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\n\n// Indian Educational Boards Configuration\nconst EDUCATIONAL_BOARDS = [\n  { value: 'CBSE', label: 'Central Board of Secondary Education (CBSE)', color: '#2E5BA8' },\n  { value: 'ICSE', label: 'Indian Certificate of Secondary Education (ICSE)', color: '#FF9933' },\n  { value: 'STATE_AP', label: 'Andhra Pradesh State Board', color: '#00C853' },\n  { value: 'STATE_TN', label: 'Tamil Nadu State Board', color: '#9C27B0' },\n  { value: 'STATE_KA', label: 'Karnataka State Board', color: '#FF5722' },\n  { value: 'STATE_TG', label: 'Telangana State Board', color: '#607D8B' },\n  { value: 'IB', label: 'International Baccalaureate (IB)', color: '#795548' },\n];\n\nconst GRADES = [\n  { value: 1, label: 'Class 1' },\n  { value: 2, label: 'Class 2' },\n  { value: 3, label: 'Class 3' },\n  { value: 4, label: 'Class 4' },\n  { value: 5, label: 'Class 5' },\n  { value: 6, label: 'Class 6' },\n  { value: 7, label: 'Class 7' },\n  { value: 8, label: 'Class 8' },\n  { value: 9, label: 'Class 9' },\n  { value: 10, label: 'Class 10' },\n  { value: 11, label: 'Class 11' },\n  { value: 12, label: 'Class 12' },\n];\n\nconst SECTIONS = ['A', 'B', 'C', 'D', 'E', 'F'];\n\nconst LANGUAGES = [\n  { value: 'en', label: 'English' },\n  { value: 'hi', label: 'हिन्दी (Hindi)' },\n  { value: 'te', label: 'తెలుగు (Telugu)' },\n  { value: 'ta', label: 'தமிழ் (Tamil)' },\n  { value: 'kn', label: 'ಕನ್ನಡ (Kannada)' },\n  { value: 'ml', label: 'മലയാളം (Malayalam)' },\n];\n\nconst StudentRegistration = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { t } = useTranslation(['common', 'students']);\n  \n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [profilePhoto, setProfilePhoto] = useState(null);\n  \n  const [formData, setFormData] = useState({\n    // Basic Information\n    firstName: '',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    bloodGroup: '',\n    \n    // Academic Information\n    admissionNumber: '',\n    grade: '',\n    section: '',\n    board: '',\n    academicYear: '2024-2025',\n    rollNumber: '',\n    \n    // Contact Information\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    phone: '',\n    email: '',\n    \n    // Parent/Guardian Information\n    fatherName: '',\n    fatherOccupation: '',\n    fatherPhone: '',\n    motherName: '',\n    motherOccupation: '',\n    motherPhone: '',\n    guardianName: '',\n    guardianRelation: '',\n    guardianPhone: '',\n    \n    // Emergency Contact\n    emergencyContactName: '',\n    emergencyContactPhone: '',\n    emergencyContactRelation: '',\n    \n    // Preferences\n    preferredLanguage: 'en',\n    specialNeeds: '',\n    medicalConditions: '',\n    previousSchool: '',\n    \n    // Consent\n    dataConsent: false,\n    communicationConsent: false,\n  });\n\n  const steps = [\n    {\n      label: 'Basic Information',\n      icon: Person,\n      description: 'Student personal details',\n    },\n    {\n      label: 'Academic Details',\n      icon: School,\n      description: 'Educational information',\n    },\n    {\n      label: 'Contact Information',\n      icon: ContactPhone,\n      description: 'Address and contacts',\n    },\n    {\n      label: 'Review & Submit',\n      icon: CheckCircle,\n      description: 'Confirm details',\n    },\n  ];\n\n  const handleInputChange = (field) => (event) => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handlePhotoUpload = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setProfilePhoto(e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const validateStep = (step) => {\n    const newErrors = {};\n    \n    switch (step) {\n      case 0: // Basic Information\n        if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';\n        if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';\n        if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';\n        if (!formData.gender) newErrors.gender = 'Gender is required';\n        break;\n        \n      case 1: // Academic Details\n        if (!formData.admissionNumber.trim()) newErrors.admissionNumber = 'Admission number is required';\n        if (!formData.grade) newErrors.grade = 'Grade is required';\n        if (!formData.section) newErrors.section = 'Section is required';\n        if (!formData.board) newErrors.board = 'Educational board is required';\n        break;\n        \n      case 2: // Contact Information\n        if (!formData.address.trim()) newErrors.address = 'Address is required';\n        if (!formData.city.trim()) newErrors.city = 'City is required';\n        if (!formData.state.trim()) newErrors.state = 'State is required';\n        if (!formData.pincode.trim()) newErrors.pincode = 'Pincode is required';\n        if (!formData.fatherName.trim()) newErrors.fatherName = 'Father name is required';\n        if (!formData.motherName.trim()) newErrors.motherName = 'Mother name is required';\n        if (!formData.emergencyContactName.trim()) newErrors.emergencyContactName = 'Emergency contact is required';\n        if (!formData.emergencyContactPhone.trim()) newErrors.emergencyContactPhone = 'Emergency contact phone is required';\n        break;\n        \n      case 3: // Review & Submit\n        if (!formData.dataConsent) newErrors.dataConsent = 'Data consent is required';\n        break;\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleNext = () => {\n    if (validateStep(activeStep)) {\n      setActiveStep(prev => prev + 1);\n    }\n  };\n\n  const handleBack = () => {\n    setActiveStep(prev => prev - 1);\n  };\n\n  const handleSubmit = async () => {\n    if (!validateStep(activeStep)) return;\n    \n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // Navigate to student profile or success page\n      navigate('/dashboard/students');\n    } catch (error) {\n      console.error('Registration failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Student Registration\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Register a new student with comprehensive information for SWOT analysis\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Stepper */}\n      <Card sx={{ mb: 4, overflow: 'visible' }}>\n        <CardContent>\n          <Stepper activeStep={activeStep} alternativeLabel>\n            {steps.map((step, index) => (\n              <Step key={step.label}>\n                <StepLabel\n                  StepIconComponent={({ active, completed }) => (\n                    <Box\n                      sx={{\n                        width: 48,\n                        height: 48,\n                        borderRadius: '50%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        background: completed\n                          ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`\n                          : active\n                          ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`\n                          : alpha(theme.palette.action.disabled, 0.12),\n                        color: completed || active ? 'white' : theme.palette.action.disabled,\n                        transition: 'all 0.3s ease',\n                      }}\n                    >\n                      <step.icon sx={{ fontSize: 24 }} />\n                    </Box>\n                  )}\n                >\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 500 }}>\n                    {step.label}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {step.description}\n                  </Typography>\n                </StepLabel>\n              </Step>\n            ))}\n          </Stepper>\n        </CardContent>\n      </Card>\n\n      {/* Form Content */}\n      <Card>\n        <CardContent sx={{ p: 4 }}>\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={activeStep}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {/* Step content will be rendered here */}\n              {activeStep === 0 && (\n                <BasicInformationStep\n                  formData={formData}\n                  errors={errors}\n                  handleInputChange={handleInputChange}\n                  profilePhoto={profilePhoto}\n                  handlePhotoUpload={handlePhotoUpload}\n                />\n              )}\n              {activeStep === 1 && (\n                <AcademicDetailsStep\n                  formData={formData}\n                  errors={errors}\n                  handleInputChange={handleInputChange}\n                />\n              )}\n              {activeStep === 2 && (\n                <ContactInformationStep\n                  formData={formData}\n                  errors={errors}\n                  handleInputChange={handleInputChange}\n                />\n              )}\n              {activeStep === 3 && (\n                <ReviewSubmitStep\n                  formData={formData}\n                  errors={errors}\n                  handleInputChange={handleInputChange}\n                  profilePhoto={profilePhoto}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Navigation Buttons */}\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n            <Button\n              onClick={handleBack}\n              disabled={activeStep === 0}\n              startIcon={<ArrowBack />}\n              variant=\"outlined\"\n            >\n              Back\n            </Button>\n            \n            <Button\n              onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}\n              endIcon={activeStep === steps.length - 1 ? <Save /> : <ArrowForward />}\n              variant=\"contained\"\n              loading={loading}\n            >\n              {activeStep === steps.length - 1 ? 'Register Student' : 'Next'}\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\n// Step Components\nconst BasicInformationStep = ({ formData, errors, handleInputChange, profilePhoto, handlePhotoUpload }) => {\n  const theme = useTheme();\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n        Basic Information\n      </Typography>\n\n      {/* Profile Photo Upload */}\n      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>\n        <Box sx={{ position: 'relative' }}>\n          <Avatar\n            src={profilePhoto}\n            sx={{\n              width: 120,\n              height: 120,\n              border: `4px solid ${alpha(theme.palette.primary.main, 0.2)}`,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n            }}\n          >\n            <Person sx={{ fontSize: 60 }} />\n          </Avatar>\n          <IconButton\n            component=\"label\"\n            sx={{\n              position: 'absolute',\n              bottom: 0,\n              right: 0,\n              background: theme.palette.primary.main,\n              color: 'white',\n              '&:hover': {\n                background: theme.palette.primary.dark,\n              },\n            }}\n          >\n            <PhotoCamera />\n            <input\n              type=\"file\"\n              hidden\n              accept=\"image/*\"\n              onChange={handlePhotoUpload}\n            />\n          </IconButton>\n        </Box>\n      </Box>\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"First Name *\"\n            value={formData.firstName}\n            onChange={handleInputChange('firstName')}\n            error={!!errors.firstName}\n            helperText={errors.firstName}\n            placeholder=\"e.g., Sanju\"\n          />\n        </Grid>\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Middle Name\"\n            value={formData.middleName}\n            onChange={handleInputChange('middleName')}\n            placeholder=\"e.g., Kumar\"\n          />\n        </Grid>\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Last Name *\"\n            value={formData.lastName}\n            onChange={handleInputChange('lastName')}\n            error={!!errors.lastName}\n            helperText={errors.lastName}\n            placeholder=\"e.g., Reddy\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Date of Birth *\"\n            type=\"date\"\n            value={formData.dateOfBirth}\n            onChange={handleInputChange('dateOfBirth')}\n            error={!!errors.dateOfBirth}\n            helperText={errors.dateOfBirth}\n            InputLabelProps={{ shrink: true }}\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <FormControl fullWidth error={!!errors.gender}>\n            <InputLabel>Gender *</InputLabel>\n            <Select\n              value={formData.gender}\n              onChange={handleInputChange('gender')}\n              label=\"Gender *\"\n            >\n              <MenuItem value=\"Male\">Male</MenuItem>\n              <MenuItem value=\"Female\">Female</MenuItem>\n              <MenuItem value=\"Other\">Other</MenuItem>\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <FormControl fullWidth>\n            <InputLabel>Blood Group</InputLabel>\n            <Select\n              value={formData.bloodGroup}\n              onChange={handleInputChange('bloodGroup')}\n              label=\"Blood Group\"\n            >\n              <MenuItem value=\"A+\">A+</MenuItem>\n              <MenuItem value=\"A-\">A-</MenuItem>\n              <MenuItem value=\"B+\">B+</MenuItem>\n              <MenuItem value=\"B-\">B-</MenuItem>\n              <MenuItem value=\"AB+\">AB+</MenuItem>\n              <MenuItem value=\"AB-\">AB-</MenuItem>\n              <MenuItem value=\"O+\">O+</MenuItem>\n              <MenuItem value=\"O-\">O-</MenuItem>\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <FormControl fullWidth>\n            <InputLabel>Preferred Language</InputLabel>\n            <Select\n              value={formData.preferredLanguage}\n              onChange={handleInputChange('preferredLanguage')}\n              label=\"Preferred Language\"\n            >\n              {LANGUAGES.map((lang) => (\n                <MenuItem key={lang.value} value={lang.value}>\n                  {lang.label}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nconst AcademicDetailsStep = ({ formData, errors, handleInputChange }) => {\n  const theme = useTheme();\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n        Academic Information\n      </Typography>\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Admission Number *\"\n            value={formData.admissionNumber}\n            onChange={handleInputChange('admissionNumber')}\n            error={!!errors.admissionNumber}\n            helperText={errors.admissionNumber}\n            placeholder=\"e.g., VMS2024001\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Roll Number\"\n            value={formData.rollNumber}\n            onChange={handleInputChange('rollNumber')}\n            placeholder=\"e.g., 15\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <FormControl fullWidth error={!!errors.grade}>\n            <InputLabel>Grade/Class *</InputLabel>\n            <Select\n              value={formData.grade}\n              onChange={handleInputChange('grade')}\n              label=\"Grade/Class *\"\n            >\n              {GRADES.map((grade) => (\n                <MenuItem key={grade.value} value={grade.value}>\n                  {grade.label}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <FormControl fullWidth error={!!errors.section}>\n            <InputLabel>Section *</InputLabel>\n            <Select\n              value={formData.section}\n              onChange={handleInputChange('section')}\n              label=\"Section *\"\n            >\n              {SECTIONS.map((section) => (\n                <MenuItem key={section} value={section}>\n                  Section {section}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Academic Year\"\n            value={formData.academicYear}\n            onChange={handleInputChange('academicYear')}\n            placeholder=\"2024-2025\"\n          />\n        </Grid>\n\n        <Grid item xs={12}>\n          <FormControl fullWidth error={!!errors.board}>\n            <InputLabel>Educational Board *</InputLabel>\n            <Select\n              value={formData.board}\n              onChange={handleInputChange('board')}\n              label=\"Educational Board *\"\n            >\n              {EDUCATIONAL_BOARDS.map((board) => (\n                <MenuItem key={board.value} value={board.value}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <Chip\n                      size=\"small\"\n                      sx={{\n                        backgroundColor: board.color,\n                        color: 'white',\n                        minWidth: 60,\n                      }}\n                      label={board.value}\n                    />\n                    {board.label}\n                  </Box>\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12}>\n          <TextField\n            fullWidth\n            label=\"Previous School\"\n            value={formData.previousSchool}\n            onChange={handleInputChange('previousSchool')}\n            placeholder=\"Name of previous school (if applicable)\"\n            multiline\n            rows={2}\n          />\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nconst ContactInformationStep = ({ formData, errors, handleInputChange }) => {\n  return (\n    <Box>\n      <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n        Contact Information\n      </Typography>\n\n      {/* Address Information */}\n      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 500, color: 'primary.main' }}>\n        Address Details\n      </Typography>\n\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12}>\n          <TextField\n            fullWidth\n            label=\"Address *\"\n            value={formData.address}\n            onChange={handleInputChange('address')}\n            error={!!errors.address}\n            helperText={errors.address}\n            multiline\n            rows={3}\n            placeholder=\"Complete address with house number, street, area\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"City *\"\n            value={formData.city}\n            onChange={handleInputChange('city')}\n            error={!!errors.city}\n            helperText={errors.city}\n            placeholder=\"e.g., Hyderabad\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"State *\"\n            value={formData.state}\n            onChange={handleInputChange('state')}\n            error={!!errors.state}\n            helperText={errors.state}\n            placeholder=\"e.g., Telangana\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Pincode *\"\n            value={formData.pincode}\n            onChange={handleInputChange('pincode')}\n            error={!!errors.pincode}\n            helperText={errors.pincode}\n            placeholder=\"e.g., 500001\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Phone Number\"\n            value={formData.phone}\n            onChange={handleInputChange('phone')}\n            placeholder=\"+91 9876543210\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Email Address\"\n            type=\"email\"\n            value={formData.email}\n            onChange={handleInputChange('email')}\n            placeholder=\"<EMAIL>\"\n          />\n        </Grid>\n      </Grid>\n\n      <Divider sx={{ my: 3 }} />\n\n      {/* Parent/Guardian Information */}\n      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 500, color: 'primary.main' }}>\n        Parent/Guardian Information\n      </Typography>\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Father's Name *\"\n            value={formData.fatherName}\n            onChange={handleInputChange('fatherName')}\n            error={!!errors.fatherName}\n            helperText={errors.fatherName}\n            placeholder=\"Father's full name\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Father's Occupation\"\n            value={formData.fatherOccupation}\n            onChange={handleInputChange('fatherOccupation')}\n            placeholder=\"e.g., Software Engineer\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Father's Phone\"\n            value={formData.fatherPhone}\n            onChange={handleInputChange('fatherPhone')}\n            placeholder=\"+91 9876543210\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Mother's Name *\"\n            value={formData.motherName}\n            onChange={handleInputChange('motherName')}\n            error={!!errors.motherName}\n            helperText={errors.motherName}\n            placeholder=\"Mother's full name\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Mother's Occupation\"\n            value={formData.motherOccupation}\n            onChange={handleInputChange('motherOccupation')}\n            placeholder=\"e.g., Teacher\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Mother's Phone\"\n            value={formData.motherPhone}\n            onChange={handleInputChange('motherPhone')}\n            placeholder=\"+91 9876543210\"\n          />\n        </Grid>\n      </Grid>\n\n      <Divider sx={{ my: 3 }} />\n\n      {/* Emergency Contact */}\n      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 500, color: 'primary.main' }}>\n        Emergency Contact\n      </Typography>\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Emergency Contact Name *\"\n            value={formData.emergencyContactName}\n            onChange={handleInputChange('emergencyContactName')}\n            error={!!errors.emergencyContactName}\n            helperText={errors.emergencyContactName}\n            placeholder=\"Contact person name\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Emergency Contact Phone *\"\n            value={formData.emergencyContactPhone}\n            onChange={handleInputChange('emergencyContactPhone')}\n            error={!!errors.emergencyContactPhone}\n            helperText={errors.emergencyContactPhone}\n            placeholder=\"+91 9876543210\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Relation\"\n            value={formData.emergencyContactRelation}\n            onChange={handleInputChange('emergencyContactRelation')}\n            placeholder=\"e.g., Uncle, Aunt\"\n          />\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nconst ReviewSubmitStep = ({ formData, errors, handleInputChange, profilePhoto }) => {\n  const theme = useTheme();\n\n  const selectedBoard = EDUCATIONAL_BOARDS.find(board => board.value === formData.board);\n  const selectedGrade = GRADES.find(grade => grade.value === formData.grade);\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n        Review & Submit\n      </Typography>\n\n      <Alert severity=\"info\" sx={{ mb: 3 }}>\n        Please review all information carefully before submitting. You can go back to make changes if needed.\n      </Alert>\n\n      {/* Student Summary Card */}\n      <Card sx={{ mb: 3, background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)` }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 3 }}>\n            <Avatar\n              src={profilePhoto}\n              sx={{\n                width: 80,\n                height: 80,\n                border: `3px solid ${theme.palette.primary.main}`,\n              }}\n            >\n              <Person sx={{ fontSize: 40 }} />\n            </Avatar>\n            <Box>\n              <Typography variant=\"h5\" sx={{ fontWeight: 600 }}>\n                {formData.firstName} {formData.middleName} {formData.lastName}\n              </Typography>\n              <Typography variant=\"body1\" color=\"text.secondary\">\n                {selectedGrade?.label} - Section {formData.section}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Admission No: {formData.admissionNumber}\n              </Typography>\n            </Box>\n          </Box>\n\n          {selectedBoard && (\n            <Chip\n              label={selectedBoard.label}\n              sx={{\n                backgroundColor: selectedBoard.color,\n                color: 'white',\n                fontWeight: 500,\n              }}\n            />\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Information Summary */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Personal Information\n              </Typography>\n              <Stack spacing={1}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Date of Birth:</Typography>\n                  <Typography variant=\"body2\">{formData.dateOfBirth}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Gender:</Typography>\n                  <Typography variant=\"body2\">{formData.gender}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Blood Group:</Typography>\n                  <Typography variant=\"body2\">{formData.bloodGroup || 'Not specified'}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Preferred Language:</Typography>\n                  <Typography variant=\"body2\">\n                    {LANGUAGES.find(lang => lang.value === formData.preferredLanguage)?.label}\n                  </Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Contact Information\n              </Typography>\n              <Stack spacing={1}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">City:</Typography>\n                  <Typography variant=\"body2\">{formData.city}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">State:</Typography>\n                  <Typography variant=\"body2\">{formData.state}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Father's Name:</Typography>\n                  <Typography variant=\"body2\">{formData.fatherName}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Mother's Name:</Typography>\n                  <Typography variant=\"body2\">{formData.motherName}</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Consent Checkboxes */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Consent & Permissions\n          </Typography>\n\n          <Stack spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={formData.dataConsent}\n                  onChange={handleInputChange('dataConsent')}\n                  color=\"primary\"\n                />\n              }\n              label={\n                <Typography variant=\"body2\">\n                  I consent to the collection and processing of student data for educational purposes and SWOT analysis. *\n                </Typography>\n              }\n            />\n            {errors.dataConsent && (\n              <Typography variant=\"caption\" color=\"error\">\n                {errors.dataConsent}\n              </Typography>\n            )}\n\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={formData.communicationConsent}\n                  onChange={handleInputChange('communicationConsent')}\n                  color=\"primary\"\n                />\n              }\n              label={\n                <Typography variant=\"body2\">\n                  I consent to receive communications about student progress, events, and important updates.\n                </Typography>\n              }\n            />\n\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              <Typography variant=\"body2\">\n                By submitting this form, you confirm that all information provided is accurate and complete.\n                The student data will be used for educational assessment, SWOT analysis, and academic progress tracking.\n              </Typography>\n            </Alert>\n          </Stack>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default StudentRegistration;\n"], "names": ["EDUCATIONAL_BOARDS", "value", "label", "color", "GRADES", "SECTIONS", "LANGUAGES", "StudentRegistration", "theme", "useTheme", "navigate", "useNavigate", "t", "useTranslation", "activeStep", "setActiveStep", "useState", "loading", "setLoading", "errors", "setErrors", "profilePhoto", "setProfilePhoto", "formData", "setFormData", "firstName", "middleName", "lastName", "dateOfBirth", "gender", "bloodGroup", "admissionNumber", "grade", "section", "board", "academicYear", "rollNumber", "address", "city", "state", "pincode", "phone", "email", "<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "motherOccupation", "motherPhone", "guardianName", "guardianRelation", "guardianPhone", "emergencyContactName", "emergencyContactPhone", "emergencyContactRelation", "preferredLanguage", "specialNeeds", "medicalConditions", "previousSchool", "dataConsent", "communicationConsent", "steps", "icon", "Person", "description", "School", "ContactPhone", "CheckCircle", "handleInputChange", "field", "event", "target", "type", "checked", "prev", "validateStep", "step", "newErrors", "trim", "Object", "keys", "length", "Box", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "jsxRuntimeExports", "jsx", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "mb", "Typography", "variant", "fontWeight", "background", "palette", "primary", "main", "secondary", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "Card", "overflow", "<PERSON><PERSON><PERSON><PERSON>", "Stepper", "alternativeLabel", "map", "index", "Step", "jsxs", "<PERSON><PERSON><PERSON><PERSON>", "StepIconComponent", "active", "completed", "width", "height", "borderRadius", "display", "alignItems", "justifyContent", "success", "dark", "alpha", "action", "disabled", "fontSize", "AnimatePresence", "mode", "x", "exit", "BasicInformationStep", "handlePhotoUpload", "file", "files", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "AcademicDetailsStep", "ContactInformationStep", "ReviewSubmitStep", "mt", "<PERSON><PERSON>", "onClick", "startIcon", "ArrowBack", "async", "Promise", "resolve", "setTimeout", "error", "endIcon", "Save", "ArrowForward", "position", "Avatar", "src", "border", "IconButton", "component", "bottom", "right", "PhotoCamera", "hidden", "accept", "onChange", "Grid", "container", "spacing", "item", "xs", "md", "TextField", "fullWidth", "helperText", "placeholder", "InputLabelProps", "shrink", "FormControl", "InputLabel", "Select", "MenuItem", "lang", "gap", "Chip", "size", "backgroundColor", "min<PERSON><PERSON><PERSON>", "multiline", "rows", "Divider", "my", "selectedBoard", "find", "selected<PERSON><PERSON>", "<PERSON><PERSON>", "severity", "<PERSON><PERSON>", "_a", "FormControlLabel", "control", "Checkbox"], "mappings": "sgBAiDA,MAAMA,EAAqB,CACzB,CAAEC,MAAO,OAAQC,MAAO,8CAA+CC,MAAO,WAC9E,CAAEF,MAAO,OAAQC,MAAO,mDAAoDC,MAAO,WACnF,CAAEF,MAAO,WAAYC,MAAO,6BAA8BC,MAAO,WACjE,CAAEF,MAAO,WAAYC,MAAO,yBAA0BC,MAAO,WAC7D,CAAEF,MAAO,WAAYC,MAAO,wBAAyBC,MAAO,WAC5D,CAAEF,MAAO,WAAYC,MAAO,wBAAyBC,MAAO,WAC5D,CAAEF,MAAO,KAAMC,MAAO,mCAAoCC,MAAO,YAG7DC,EAAS,CACb,CAAEH,MAAO,EAAGC,MAAO,WACnB,CAAED,MAAO,EAAGC,MAAO,WACnB,CAAED,MAAO,EAAGC,MAAO,WACnB,CAAED,MAAO,EAAGC,MAAO,WACnB,CAAED,MAAO,EAAGC,MAAO,WACnB,CAAED,MAAO,EAAGC,MAAO,WACnB,CAAED,MAAO,EAAGC,MAAO,WACnB,CAAED,MAAO,EAAGC,MAAO,WACnB,CAAED,MAAO,EAAGC,MAAO,WACnB,CAAED,MAAO,GAAIC,MAAO,YACpB,CAAED,MAAO,GAAIC,MAAO,YACpB,CAAED,MAAO,GAAIC,MAAO,aAGhBG,EAAW,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,KAErCC,EAAY,CAChB,CAAEL,MAAO,KAAMC,MAAO,WACtB,CAAED,MAAO,KAAMC,MAAO,kBACtB,CAAED,MAAO,KAAMC,MAAO,mBACtB,CAAED,MAAO,KAAMC,MAAO,iBACtB,CAAED,MAAO,KAAMC,MAAO,mBACtB,CAAED,MAAO,KAAMC,MAAO,uBAGlBK,EAAsB,KAC1B,MAAMC,EAAQC,IACRC,EAAWC,KACXC,EAAEA,GAAMC,EAAe,CAAC,SAAU,cAEjCC,EAAYC,GAAiBC,EAAAA,SAAS,IACtCC,EAASC,GAAcF,EAAAA,UAAS,IAChCG,EAAQC,GAAaJ,EAAAA,SAAS,CAAA,IAC9BK,EAAcC,GAAmBN,EAAAA,SAAS,OAE1CO,EAAUC,GAAeR,WAAS,CAEvCS,UAAW,GACXC,WAAY,GACZC,SAAU,GACVC,YAAa,GACbC,OAAQ,GACRC,WAAY,GAGZC,gBAAiB,GACjBC,MAAO,GACPC,QAAS,GACTC,MAAO,GACPC,aAAc,YACdC,WAAY,GAGZC,QAAS,GACTC,KAAM,GACNC,MAAO,GACPC,QAAS,GACTC,MAAO,GACPC,MAAO,GAGPC,WAAY,GACZC,iBAAkB,GAClBC,YAAa,GACbC,WAAY,GACZC,iBAAkB,GAClBC,YAAa,GACbC,aAAc,GACdC,iBAAkB,GAClBC,cAAe,GAGfC,qBAAsB,GACtBC,sBAAuB,GACvBC,yBAA0B,GAG1BC,kBAAmB,KACnBC,aAAc,GACdC,kBAAmB,GACnBC,eAAgB,GAGhBC,aAAa,EACbC,sBAAsB,IAGlBC,EAAQ,CACZ,CACE3D,MAAO,oBACP4D,KAAMC,EACNC,YAAa,4BAEf,CACE9D,MAAO,mBACP4D,KAAMG,EACND,YAAa,2BAEf,CACE9D,MAAO,sBACP4D,KAAMI,EACNF,YAAa,wBAEf,CACE9D,MAAO,kBACP4D,KAAMK,EACNH,YAAa,oBAIXI,EAAqBC,GAAWC,IAC9B,MAAArE,EAA8B,aAAtBqE,EAAMC,OAAOC,KAAsBF,EAAMC,OAAOE,QAAUH,EAAMC,OAAOtE,MACrFuB,GAAqBkD,IAAA,IAChBA,EACHL,CAACA,GAAQpE,MAIPkB,EAAOkD,IACTjD,GAAmBsD,IAAA,IACdA,EACHL,CAACA,GAAQ,QACT,EAeAM,EAAgBC,IACpB,MAAMC,EAAY,CAAC,EAEnB,OAAQD,GACN,KAAK,EACErD,EAASE,UAAUqD,WAAkBrD,UAAY,0BACjDF,EAASI,SAASmD,WAAkBnD,SAAW,yBAC/CJ,EAASK,cAAaiD,EAAUjD,YAAc,6BAC9CL,EAASM,SAAQgD,EAAUhD,OAAS,sBACzC,MAEF,KAAK,EACEN,EAASQ,gBAAgB+C,WAAkB/C,gBAAkB,gCAC7DR,EAASS,QAAO6C,EAAU7C,MAAQ,qBAClCT,EAASU,UAAS4C,EAAU5C,QAAU,uBACtCV,EAASW,QAAO2C,EAAU3C,MAAQ,iCACvC,MAEF,KAAK,EACEX,EAASc,QAAQyC,WAAkBzC,QAAU,uBAC7Cd,EAASe,KAAKwC,WAAkBxC,KAAO,oBACvCf,EAASgB,MAAMuC,WAAkBvC,MAAQ,qBACzChB,EAASiB,QAAQsC,WAAkBtC,QAAU,uBAC7CjB,EAASoB,WAAWmC,WAAkBnC,WAAa,2BACnDpB,EAASuB,WAAWgC,WAAkBhC,WAAa,2BACnDvB,EAAS6B,qBAAqB0B,WAAkB1B,qBAAuB,iCACvE7B,EAAS8B,sBAAsByB,WAAkBzB,sBAAwB,uCAC9E,MAEF,KAAK,EACE9B,EAASoC,cAAakB,EAAUlB,YAAc,4BAKvD,OADAvC,EAAUyD,GAC+B,IAAlCE,OAAOC,KAAKH,GAAWI,MAAW,EA+BzC,cAACC,EAAI,CAAAC,GAAI,CAAEC,SAAU,KAAMC,GAAI,OAAQC,EAAG,GAExCC,SAAA,CAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,GAAO,IAC9BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAExBV,gBAACL,EAAI,CAAAC,GAAI,CAAEe,GAAI,GACbX,SAAA,CAAAC,EAAAC,IAACU,EAAA,CACCC,QAAQ,KACRjB,GAAI,CACFkB,WAAY,IACZH,GAAI,EACJI,WAAY,2BAA2B9F,EAAM+F,QAAQC,QAAQC,YAAYjG,EAAM+F,QAAQG,UAAUD,aACjGE,qBAAsB,OACtBC,oBAAqB,cACrBC,eAAgB,QAEnBtB,SAAA,+BAGAY,EAAW,CAAAC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAEnD,iFAKJE,EAAAA,IAACqB,GAAK3B,GAAI,CAAEe,GAAI,EAAGa,SAAU,WAC3BxB,SAACC,EAAAC,IAAAuB,EAAA,CACCzB,eAAC0B,EAAQ,CAAAnG,aAAwBoG,kBAAgB,EAC9C3B,SAAA1B,EAAMsD,KAAI,CAACvC,EAAMwC,IAChB3B,EAAAA,IAAC4B,EACC,CAAA9B,SAAAC,EAAA8B,KAACC,EAAA,CACCC,kBAAmB,EAAGC,SAAQC,eAC5BlC,EAAAC,IAACP,EAAA,CACCC,GAAI,CACFwC,MAAO,GACPC,OAAQ,GACRC,aAAc,MACdC,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChB1B,WAAYoB,EACR,2BAA2BlH,EAAM+F,QAAQ0B,QAAQxB,YAAYjG,EAAM+F,QAAQ0B,QAAQC,aACnFT,EACA,2BAA2BjH,EAAM+F,QAAQC,QAAQC,YAAYjG,EAAM+F,QAAQG,UAAUD,aACrF0B,EAAM3H,EAAM+F,QAAQ6B,OAAOC,SAAU,KACzClI,MAAOuH,GAAaD,EAAS,QAAUjH,EAAM+F,QAAQ6B,OAAOC,SAC5DrC,WAAY,iBAGdT,SAAAE,EAAAA,IAACb,EAAKd,KAAL,CAAUqB,GAAI,CAAEmD,SAAU,QAI/B/C,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,YAAYjB,GAAI,CAAEkB,WAAY,KAC/Cd,SAAAX,EAAK1E,cAEPiG,EAAW,CAAAC,QAAQ,UAAUjG,MAAM,iBACjCoF,WAAKvB,kBA5BDY,EAAK1E,eAsCxBuF,EAAAA,IAACqB,GACCvB,gBAACyB,EAAA,CAAY7B,GAAI,CAAEG,EAAG,GACpBC,SAAA,GAACE,IAAA8C,EAAA,CAAgBC,KAAK,OACpBjD,SAAAC,EAAA8B,KAAC5B,EAAOC,IAAP,CAECC,QAAS,CAAEC,QAAS,EAAG4C,EAAG,IAC1B1C,QAAS,CAAEF,QAAS,EAAG4C,EAAG,GAC1BC,KAAM,CAAE7C,QAAS,EAAG4C,GAAO,IAC3BzC,WAAY,CAAEC,SAAU,IAGvBV,SAAA,CAAe,IAAfzE,GACC0E,EAAAC,IAACkD,EAAA,CACCpH,WACAJ,SACAiD,oBACA/C,eACAuH,kBApKWtE,IACzB,MAAMuE,EAAOvE,EAAMC,OAAOuE,MAAM,GAChC,GAAID,EAAM,CACF,MAAAE,EAAS,IAAIC,WACZD,EAAAE,OAAUC,IACC5H,EAAA4H,EAAE3E,OAAO4E,OAAM,EAEjCJ,EAAOK,cAAcP,EAAI,KAgKD,IAAf/H,GACC0E,EAAAC,IAAC4D,EAAA,CACC9H,WACAJ,SACAiD,sBAGY,IAAftD,GACC0E,EAAAC,IAAC6D,EAAA,CACC/H,WACAJ,SACAiD,sBAGY,IAAftD,GACC0E,EAAAC,IAAC8D,EAAA,CACChI,WACAJ,SACAiD,oBACA/C,mBAnCCP,KA0CTwG,EAAAA,KAACpC,EAAI,CAAAC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,gBAAiBwB,GAAI,GAC/DjE,SAAA,CAAAC,EAAAC,IAACgE,EAAA,CACCC,QA5IO,KACH3I,GAAA2D,GAAQA,EAAO,GAAC,EA4IpB2D,SAAyB,IAAfvH,EACV6I,gBAAYC,EAAU,IACtBxD,QAAQ,WACTb,SAAA,SAIDC,EAAAC,IAACgE,EAAA,CACCC,QAAS5I,IAAe+C,EAAMoB,OAAS,EAjJ9B4E,UACf,GAAClF,EAAa7D,GAAd,CAEJI,GAAW,GACP,UAEI,IAAI4I,SAAQC,GAAWC,WAAWD,EAAS,OAGjDrJ,EAAS,6BACFuJ,GACoC,CAC3C,QACA/I,GAAW,EAAK,CAZa,CAYb,EAvBD,KACbyD,EAAa7D,IACDC,GAAA2D,GAAQA,EAAO,GAAC,EA0JtBwF,QAASpJ,IAAe+C,EAAMoB,OAAS,EAAKQ,EAAAA,IAAA0E,EAAA,IAAU1E,MAAC2E,EAAa,CAAA,GACpEhE,QAAQ,YACRnF,UAECsE,SAAezE,IAAA+C,EAAMoB,OAAS,EAAI,mBAAqB,mBAKlE,EAKE0D,EAAuB,EAAGpH,WAAUJ,SAAQiD,oBAAmB/C,eAAcuH,wBACjF,MAAMpI,EAAQC,IAEd,cACGyE,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,KAAOd,SAEzD,4BAGCL,EAAI,CAAAC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,SAAU9B,GAAI,GACxDX,SAACC,EAAA8B,KAAApC,EAAA,CAAIC,GAAI,CAAEkF,SAAU,YACnB9E,SAAA,CAAAC,EAAAC,IAAC6E,EAAA,CACCC,IAAKlJ,EACL8D,GAAI,CACFwC,MAAO,IACPC,OAAQ,IACR4C,OAAQ,aAAarC,EAAM3H,EAAM+F,QAAQC,QAAQC,KAAM,MACvDH,WAAY,2BAA2B9F,EAAM+F,QAAQC,QAAQC,YAAYjG,EAAM+F,QAAQG,UAAUD,cAGnGlB,eAACxB,EAAO,CAAAoB,GAAI,CAAEmD,SAAU,QAE1B9C,EAAA8B,KAACmD,EAAA,CACCC,UAAU,QACVvF,GAAI,CACFkF,SAAU,WACVM,OAAQ,EACRC,MAAO,EACPtE,WAAY9F,EAAM+F,QAAQC,QAAQC,KAClCtG,MAAO,QACP,UAAW,CACTmG,WAAY9F,EAAM+F,QAAQC,QAAQ0B,OAItC3C,SAAA,CAAAC,EAAAC,IAACoF,EAAY,IACbrF,EAAAC,IAAC,QAAA,CACCjB,KAAK,OACLsG,QAAM,EACNC,OAAO,UACPC,SAAUpC,YAMjBtB,EAAAA,KAAA2D,EAAA,CAAKC,WAAS,EAACC,QAAS,EACvB5F,SAAA,CAAAE,MAACwF,GAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,eACND,MAAOsB,EAASE,UAChBuJ,SAAU5G,EAAkB,aAC5B6F,QAAS9I,EAAOM,UAChBgK,WAAYtK,EAAOM,UACnBiK,YAAY,wBAGfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,cACND,MAAOsB,EAASG,WAChBsJ,SAAU5G,EAAkB,cAC5BsH,YAAY,wBAGfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,cACND,MAAOsB,EAASI,SAChBqJ,SAAU5G,EAAkB,YAC5B6F,QAAS9I,EAAOQ,SAChB8J,WAAYtK,EAAOQ,SACnB+J,YAAY,wBAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,kBACNsE,KAAK,OACLvE,MAAOsB,EAASK,YAChBoJ,SAAU5G,EAAkB,eAC5B6F,QAAS9I,EAAOS,YAChB6J,WAAYtK,EAAOS,YACnB+J,gBAAiB,CAAEC,QAAQ,aAI9BX,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,WAAC+B,KAAAuE,EAAA,CAAYL,WAAS,EAACvB,QAAS9I,EAAOU,OACrC0D,SAAA,GAAAE,IAACqG,GAAWvG,SAAQ,aACpBC,EAAA8B,KAACyE,EAAA,CACC9L,MAAOsB,EAASM,OAChBmJ,SAAU5G,EAAkB,UAC5BlE,MAAM,WAENqF,SAAA,CAACE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,OAAOsF,SAAI,SAC1BE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,SAASsF,SAAM,WAC9BE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,QAAQsF,SAAK,oBAKnCE,IAACwF,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAA+B,EAAAA,KAACuE,EAAY,CAAAL,WAAS,EACpBjG,SAAA,GAAAE,IAACqG,GAAWvG,SAAW,gBACvBC,EAAA8B,KAACyE,EAAA,CACC9L,MAAOsB,EAASO,WAChBkJ,SAAU5G,EAAkB,cAC5BlE,MAAM,cAENqF,SAAA,CAACE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,KAAKsF,SAAE,OACtBE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,KAAKsF,SAAE,OACtBE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,KAAKsF,SAAE,OACtBE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,KAAKsF,SAAE,OACtBE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,MAAMsF,SAAG,QACxBE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,MAAMsF,SAAG,QACxBE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,KAAKsF,SAAE,OACtBE,EAAAA,IAAAuG,EAAA,CAAS/L,MAAM,KAAKsF,SAAE,iBAK7BE,IAACwF,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAA+B,EAAAA,KAACuE,EAAY,CAAAL,WAAS,EACpBjG,SAAA,GAAAE,IAACqG,GAAWvG,SAAkB,uBAC9BC,EAAAC,IAACsG,EAAA,CACC9L,MAAOsB,EAASgC,kBAChByH,SAAU5G,EAAkB,qBAC5BlE,MAAM,qBAELqF,SAAUjF,EAAA6G,KAAK8E,GACbxG,EAAAA,IAAAuG,EAAA,CAA0B/L,MAAOgM,EAAKhM,MACpCsF,SAAA0G,EAAK/L,OADO+L,EAAKhM,qBAQhC,EAIEoJ,EAAsB,EAAG9H,WAAUJ,SAAQiD,wBACxB3D,WAGpByE,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,KAAOd,SAEzD,yBAEC+B,EAAAA,KAAA2D,EAAA,CAAKC,WAAS,EAACC,QAAS,EACvB5F,SAAA,CAAAE,MAACwF,GAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,qBACND,MAAOsB,EAASQ,gBAChBiJ,SAAU5G,EAAkB,mBAC5B6F,QAAS9I,EAAOY,gBAChB0J,WAAYtK,EAAOY,gBACnB2J,YAAY,6BAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,cACND,MAAOsB,EAASa,WAChB4I,SAAU5G,EAAkB,cAC5BsH,YAAY,qBAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,WAAC+B,KAAAuE,EAAA,CAAYL,WAAS,EAACvB,QAAS9I,EAAOa,MACrCuD,SAAA,GAAAE,IAACqG,GAAWvG,SAAa,kBACzBC,EAAAC,IAACsG,EAAA,CACC9L,MAAOsB,EAASS,MAChBgJ,SAAU5G,EAAkB,SAC5BlE,MAAM,gBAELqF,SAAOnF,EAAA+G,KAAKnF,GACVyD,EAAAA,IAAAuG,EAAA,CAA2B/L,MAAO+B,EAAM/B,MACtCsF,SAAAvD,EAAM9B,OADM8B,EAAM/B,sBAQ5BgL,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,WAAC+B,KAAAuE,EAAA,CAAYL,WAAS,EAACvB,QAAS9I,EAAOc,QACrCsD,SAAA,GAAAE,IAACqG,GAAWvG,SAAS,cACrBC,EAAAC,IAACsG,EAAA,CACC9L,MAAOsB,EAASU,QAChB+I,SAAU5G,EAAkB,WAC5BlE,MAAM,YAELqF,WAAS4B,KAAKlF,UACZ+J,EAAA,CAAuB/L,MAAOgC,EAASsD,SAAA,CAAA,WAC7BtD,IADIA,kBAQtBgJ,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,gBACND,MAAOsB,EAASY,aAChB6I,SAAU5G,EAAkB,gBAC5BsH,YAAY,gBAIfjG,EAAAA,IAAAwF,EAAA,CAAKG,MAAI,EAACC,GAAI,GACb9F,SAAA+B,EAAAA,KAACuE,EAAY,CAAAL,WAAS,EAACvB,QAAS9I,EAAOe,MACrCqD,SAAA,GAAAE,IAACqG,GAAWvG,SAAmB,wBAC/BC,EAAAC,IAACsG,EAAA,CACC9L,MAAOsB,EAASW,MAChB8I,SAAU5G,EAAkB,SAC5BlE,MAAM,sBAELqF,WAAmB4B,KAAKjF,GACtBuD,EAAAA,IAAAuG,EAAA,CAA2B/L,MAAOiC,EAAMjC,MACvCsF,gBAACL,EAAI,CAAAC,GAAI,CAAE2C,QAAS,OAAQC,WAAY,SAAUmE,IAAK,GACrD3G,SAAA,CAAAC,EAAAC,IAAC0G,EAAA,CACCC,KAAK,QACLjH,GAAI,CACFkH,gBAAiBnK,EAAM/B,MACvBA,MAAO,QACPmM,SAAU,IAEZpM,MAAOgC,EAAMjC,QAEdiC,EAAMhC,UAXIgC,EAAMjC,gBAmB5BwF,EAAAA,IAAAwF,EAAA,CAAKG,MAAI,EAACC,GAAI,GACb9F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,kBACND,MAAOsB,EAASmC,eAChBsH,SAAU5G,EAAkB,kBAC5BsH,YAAY,0CACZa,WAAS,EACTC,KAAM,aAQZlD,EAAyB,EAAG/H,WAAUJ,SAAQiD,8BAE/Cc,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,KAAOd,SAEzD,0BAGCE,IAAAU,EAAA,CAAWC,QAAQ,YAAYjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKlG,MAAO,gBAAkBoF,SAEvF,oBAEA+B,EAAAA,KAAC2D,EAAK,CAAAC,WAAS,EAACC,QAAS,EAAGhG,GAAI,CAAEe,GAAI,GACpCX,SAAA,CAAAE,EAAAA,IAACwF,EAAK,CAAAG,MAAI,EAACC,GAAI,GACb9F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,YACND,MAAOsB,EAASc,QAChB2I,SAAU5G,EAAkB,WAC5B6F,QAAS9I,EAAOkB,QAChBoJ,WAAYtK,EAAOkB,QACnBkK,WAAS,EACTC,KAAM,EACNd,YAAY,6DAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,SACND,MAAOsB,EAASe,KAChB0I,SAAU5G,EAAkB,QAC5B6F,QAAS9I,EAAOmB,KAChBmJ,WAAYtK,EAAOmB,KACnBoJ,YAAY,4BAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,UACND,MAAOsB,EAASgB,MAChByI,SAAU5G,EAAkB,SAC5B6F,QAAS9I,EAAOoB,MAChBkJ,WAAYtK,EAAOoB,MACnBmJ,YAAY,4BAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,YACND,MAAOsB,EAASiB,QAChBwI,SAAU5G,EAAkB,WAC5B6F,QAAS9I,EAAOqB,QAChBiJ,WAAYtK,EAAOqB,QACnBkJ,YAAY,yBAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,eACND,MAAOsB,EAASkB,MAChBuI,SAAU5G,EAAkB,SAC5BsH,YAAY,2BAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,gBACNsE,KAAK,QACLvE,MAAOsB,EAASmB,MAChBsI,SAAU5G,EAAkB,SAC5BsH,YAAY,mCAKjBe,EAAQ,CAAAtH,GAAI,CAAEuH,GAAI,OAGlBjH,IAAAU,EAAA,CAAWC,QAAQ,YAAYjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKlG,MAAO,gBAAkBoF,SAEvF,gCAEC+B,EAAAA,KAAA2D,EAAA,CAAKC,WAAS,EAACC,QAAS,EACvB5F,SAAA,CAAAE,MAACwF,GAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,kBACND,MAAOsB,EAASoB,WAChBqI,SAAU5G,EAAkB,cAC5B6F,QAAS9I,EAAOwB,WAChB8I,WAAYtK,EAAOwB,WACnB+I,YAAY,+BAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,sBACND,MAAOsB,EAASqB,iBAChBoI,SAAU5G,EAAkB,oBAC5BsH,YAAY,oCAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,iBACND,MAAOsB,EAASsB,YAChBmI,SAAU5G,EAAkB,eAC5BsH,YAAY,2BAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,kBACND,MAAOsB,EAASuB,WAChBkI,SAAU5G,EAAkB,cAC5B6F,QAAS9I,EAAO2B,WAChB2I,WAAYtK,EAAO2B,WACnB4I,YAAY,+BAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,sBACND,MAAOsB,EAASwB,iBAChBiI,SAAU5G,EAAkB,oBAC5BsH,YAAY,0BAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,iBACND,MAAOsB,EAASyB,YAChBgI,SAAU5G,EAAkB,eAC5BsH,YAAY,8BAKjBe,EAAQ,CAAAtH,GAAI,CAAEuH,GAAI,OAGlBjH,IAAAU,EAAA,CAAWC,QAAQ,YAAYjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKlG,MAAO,gBAAkBoF,SAEvF,sBAEC+B,EAAAA,KAAA2D,EAAA,CAAKC,WAAS,EAACC,QAAS,EACvB5F,SAAA,CAAAE,MAACwF,GAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,2BACND,MAAOsB,EAAS6B,qBAChB4H,SAAU5G,EAAkB,wBAC5B6F,QAAS9I,EAAOiC,qBAChBqI,WAAYtK,EAAOiC,qBACnBsI,YAAY,gCAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,4BACND,MAAOsB,EAAS8B,sBAChB2H,SAAU5G,EAAkB,yBAC5B6F,QAAS9I,EAAOkC,sBAChBoI,WAAYtK,EAAOkC,sBACnBqI,YAAY,2BAIfT,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAC,EAAAC,IAAC8F,EAAA,CACCC,WAAS,EACTtL,MAAM,WACND,MAAOsB,EAAS+B,yBAChB0H,SAAU5G,EAAkB,4BAC5BsH,YAAY,8BAQlBnC,EAAmB,EAAGhI,WAAUJ,SAAQiD,oBAAmB/C,yBAC/D,MAAMb,EAAQC,IAERkM,EAAgB3M,EAAmB4M,SAAc1K,EAAMjC,QAAUsB,EAASW,QAC1E2K,EAAgBzM,EAAOwM,SAAc5K,EAAM/B,QAAUsB,EAASS,QAEpE,cACGkD,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,KAAOd,SAEzD,oBAEAE,EAAAA,IAACqH,GAAMC,SAAS,OAAO5H,GAAI,CAAEe,GAAI,GAAKX,SAEtC,0GAGAE,EAAAA,IAACqB,EAAK,CAAA3B,GAAI,CAAEe,GAAI,EAAGI,WAAY,2BAA2B6B,EAAM3H,EAAM+F,QAAQC,QAAQC,KAAM,YAAa0B,EAAM3H,EAAM+F,QAAQG,UAAUD,KAAM,cAC3IlB,SAAA+B,EAAAA,KAACN,EACC,CAAAzB,SAAA,CAAC+B,EAAAA,KAAApC,EAAA,CAAIC,GAAI,CAAE2C,QAAS,OAAQC,WAAY,SAAUmE,IAAK,EAAGhG,GAAI,GAC5DX,SAAA,CAAAC,EAAAC,IAAC6E,EAAA,CACCC,IAAKlJ,EACL8D,GAAI,CACFwC,MAAO,GACPC,OAAQ,GACR4C,OAAQ,aAAahK,EAAM+F,QAAQC,QAAQC,QAG7ClB,eAACxB,EAAO,CAAAoB,GAAI,CAAEmD,SAAU,eAEzBpD,EACC,CAAAK,SAAA,CAAA+B,OAACnB,GAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAA,CAAShE,EAAAE,UAAU,IAAEF,EAASG,WAAW,IAAEH,EAASI,YAEtD2F,EAAAA,KAAAnB,EAAA,CAAWC,QAAQ,QAAQjG,MAAM,iBAC/BoF,SAAA,CAAe,MAAAsH,OAAA,EAAAA,EAAA3M,MAAM,cAAYqB,EAASU,WAE5CqF,EAAAA,KAAAnB,EAAA,CAAWC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAAA,CAAA,iBAClChE,EAASQ,yBAK7B4K,GACCnH,EAAAC,IAAC0G,EAAA,CACCjM,MAAOyM,EAAczM,MACrBiF,GAAI,CACFkH,gBAAiBM,EAAcxM,MAC/BA,MAAO,QACPkG,WAAY,YAQtBiB,EAAAA,KAAC2D,EAAK,CAAAC,WAAS,EAACC,QAAS,EAAGhG,GAAI,CAAEe,GAAI,GACpCX,SAAA,CAACE,EAAAA,IAAAwF,EAAA,CAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAE,EAAAA,IAACqB,EACC,CAAAvB,WAAA+B,KAACN,EACC,CAAAzB,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKlG,MAAO,gBAAkBoF,SAEhF,2BACA+B,KAAC0F,EAAM,CAAA7B,QAAS,EACd5F,SAAA,CAAA+B,OAACpC,GAAIC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,iBAC1CzC,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAAc,mBAChEE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QAASb,WAAS3D,iBAExC0F,OAACpC,GAAIC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,iBAC1CzC,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAAO,YACzDE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QAASb,WAAS1D,YAExCyF,OAACpC,GAAIC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,iBAC1CzC,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAAY,uBAC9DY,EAAW,CAAAC,QAAQ,QAASb,SAAAhE,EAASO,YAAc,qBAEtDwF,OAACpC,GAAIC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,iBAC1CzC,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAAmB,0BACrEE,IAAAU,EAAA,CAAWC,QAAQ,QACjBb,SAAU,OAAA0H,EAAA3M,EAAAsM,MAAaX,GAAAA,EAAKhM,QAAUsB,EAASgC,0BAArC,EAAA0J,EAAyD/M,qBAQhFuF,EAAAA,IAACwF,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB/F,SAAAE,EAAAA,IAACqB,EACC,CAAAvB,WAAA+B,KAACN,EACC,CAAAzB,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKlG,MAAO,gBAAkBoF,SAEhF,0BACA+B,KAAC0F,EAAM,CAAA7B,QAAS,EACd5F,SAAA,CAAA+B,OAACpC,GAAIC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,iBAC1CzC,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAAK,UACvDE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QAASb,WAASjD,UAExCgF,OAACpC,GAAIC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,iBAC1CzC,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAAM,WACxDE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QAASb,WAAShD,WAExC+E,OAACpC,GAAIC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,iBAC1CzC,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAAc,mBAChEE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QAASb,WAAS5C,gBAExC2E,OAACpC,GAAIC,GAAI,CAAE2C,QAAS,OAAQE,eAAgB,iBAC1CzC,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQjG,MAAM,iBAAiBoF,SAAc,mBAChEE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QAASb,WAASzC,6BASlD2C,EAAAA,IAACqB,EACC,CAAAvB,SAAA+B,EAAAA,KAACN,EACC,CAAAzB,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKlG,MAAO,gBAAkBoF,SAEhF,4BAEA+B,KAAC0F,EAAM,CAAA7B,QAAS,EACd5F,SAAA,CAAAC,EAAAC,IAACyH,EAAA,CACCC,QACE3H,EAAAC,IAAC2H,EAAA,CACC3I,QAASlD,EAASoC,YAClBqH,SAAU5G,EAAkB,eAC5BjE,MAAM,YAGVD,QACEuF,IAACU,EAAW,CAAAC,QAAQ,QAAQb,SAE5B,+GAGHpE,EAAOwC,mBACLwC,EAAA,CAAWC,QAAQ,UAAUjG,MAAM,QACjCoF,SAAApE,EAAOwC,cAIZ6B,EAAAC,IAACyH,EAAA,CACCC,QACE3H,EAAAC,IAAC2H,EAAA,CACC3I,QAASlD,EAASqC,qBAClBoH,SAAU5G,EAAkB,wBAC5BjE,MAAM,YAGVD,QACEuF,IAACU,EAAW,CAAAC,QAAQ,QAAQb,SAE5B,iGAIHE,EAAAA,IAAAqH,EAAA,CAAMC,SAAS,UAAU5H,GAAI,CAAEqE,GAAI,GAClCjE,SAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QAAQb,+NAQtC"}