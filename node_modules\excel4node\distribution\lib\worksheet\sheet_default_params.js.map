{"version": 3, "file": "sheet_default_params.js", "names": ["module", "exports"], "sources": ["../../../source/lib/worksheet/sheet_default_params.js"], "sourcesContent": ["module.exports = {\n    'margins': {\n        'bottom': 0.75,\n        'footer': 0.3,\n        'header': 0.3,\n        'left': 0.7,\n        'right': 0.7,\n        'top': 0.75\n    },\n    'printOptions': {\n        'centerHorizontal': null,\n        'centerVertical': null,\n        'printGridLines': null,\n        'printHeadings': null\n    \n    },\n    'headerFooter': {\n        'evenFooter': null,\n        'evenHeader': null,\n        'firstFooter': null,\n        'firstHeader': null,\n        'oddFooter': null,\n        'oddHeader': null,\n        'alignWithMargins': null,\n        'differentFirst': null,\n        'differentOddEven': null,\n        'scaleWithDoc': null\n    },\n    'pageSetup': {\n        'blackAndWhite': null,\n        'cellComments': null,\n        'copies': null,\n        'draft': null,\n        'errors': null,\n        'firstPageNumber': null,\n        'fitToHeight': null,\n        'fitToWidth': null,\n        'horizontalDpi': null,\n        'orientation': null,\n        'pageOrder': null,\n        'paperHeight': null,\n        'paperSize': null,\n        'paperWidth': null,\n        'scale': null,\n        'useFirstPageNumber': null,\n        'usePrinterDefaults': null,\n        'verticalDpi': null\n    },\n    'sheetView': {\n        'pane': {\n            'activePane': null,\n            'state': null,\n            'topLeftCell': null,\n            'xSplit': null,\n            'ySplit': null\n        },\n        'tabSelected': 0,\n        'workbookViewId': 0,\n        'rightToLeft': 0,\n        'showGridLines': 1,\n        'zoomScale': 100,\n        'zoomScaleNormal': 100,\n        'zoomScalePageLayoutView': 100\n    },\n    'sheetFormat': {\n        'baseColWidth': 10,\n        'customHeight': null,\n        'defaultColWidth': null,\n        'defaultRowHeight': null,\n        'outlineLevelCol': null,\n        'outlineLevelRow': null,\n        'thickBottom': null,\n        'thickTop': null,\n        'zeroHeight': null\n    },\n    'sheetProtection': {                 // same as \"Protect Sheet\" in Review tab of Excel \n        'autoFilter': null,\n        'deleteColumns': null,\n        'deleteRows': null,\n        'formatCells': null,\n        'formatColumns': null,\n        'formatRows': null,\n        'hashValue': null,\n        'insertColumns': null,\n        'insertHyperlinks': null,\n        'insertRows': null,\n        'objects': null,\n        'password': null,\n        'pivotTables': null,\n        'scenarios': null,\n        'selectLockedCells': null,\n        'selectUnlockedCells': null,\n        'sheet': null,\n        'sort': null\n    },\n    'outline': {\n        'summaryBelow': null,\n        'summaryRight': null\n    },\n    'autoFilter': {\n        'startRow': null,\n        'endRow': null,\n        'startCol': null,\n        'endCol': null,\n        'ref': null,\n        'filters': []\n    },\n    'disableRowSpansOptimization': false,\n    'hidden': false,\n};"], "mappings": ";;AAAAA,MAAM,CAACC,OAAO,GAAG;EACb,SAAS,EAAE;IACP,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE;EACX,CAAC;EACD,cAAc,EAAE;IACZ,kBAAkB,EAAE,IAAI;IACxB,gBAAgB,EAAE,IAAI;IACtB,gBAAgB,EAAE,IAAI;IACtB,eAAe,EAAE;EAErB,CAAC;EACD,cAAc,EAAE;IACZ,YAAY,EAAE,IAAI;IAClB,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,IAAI;IACjB,WAAW,EAAE,IAAI;IACjB,kBAAkB,EAAE,IAAI;IACxB,gBAAgB,EAAE,IAAI;IACtB,kBAAkB,EAAE,IAAI;IACxB,cAAc,EAAE;EACpB,CAAC;EACD,WAAW,EAAE;IACT,eAAe,EAAE,IAAI;IACrB,cAAc,EAAE,IAAI;IACpB,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,iBAAiB,EAAE,IAAI;IACvB,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,IAAI;IAClB,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,IAAI;IACjB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,IAAI;IACjB,YAAY,EAAE,IAAI;IAClB,OAAO,EAAE,IAAI;IACb,oBAAoB,EAAE,IAAI;IAC1B,oBAAoB,EAAE,IAAI;IAC1B,aAAa,EAAE;EACnB,CAAC;EACD,WAAW,EAAE;IACT,MAAM,EAAE;MACJ,YAAY,EAAE,IAAI;MAClB,OAAO,EAAE,IAAI;MACb,aAAa,EAAE,IAAI;MACnB,QAAQ,EAAE,IAAI;MACd,QAAQ,EAAE;IACd,CAAC;IACD,aAAa,EAAE,CAAC;IAChB,gBAAgB,EAAE,CAAC;IACnB,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE,CAAC;IAClB,WAAW,EAAE,GAAG;IAChB,iBAAiB,EAAE,GAAG;IACtB,yBAAyB,EAAE;EAC/B,CAAC;EACD,aAAa,EAAE;IACX,cAAc,EAAE,EAAE;IAClB,cAAc,EAAE,IAAI;IACpB,iBAAiB,EAAE,IAAI;IACvB,kBAAkB,EAAE,IAAI;IACxB,iBAAiB,EAAE,IAAI;IACvB,iBAAiB,EAAE,IAAI;IACvB,aAAa,EAAE,IAAI;IACnB,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE;EAClB,CAAC;EACD,iBAAiB,EAAE;IAAkB;IACjC,YAAY,EAAE,IAAI;IAClB,eAAe,EAAE,IAAI;IACrB,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE,IAAI;IACnB,eAAe,EAAE,IAAI;IACrB,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,IAAI;IACrB,kBAAkB,EAAE,IAAI;IACxB,YAAY,EAAE,IAAI;IAClB,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,IAAI;IACjB,mBAAmB,EAAE,IAAI;IACzB,qBAAqB,EAAE,IAAI;IAC3B,OAAO,EAAE,IAAI;IACb,MAAM,EAAE;EACZ,CAAC;EACD,SAAS,EAAE;IACP,cAAc,EAAE,IAAI;IACpB,cAAc,EAAE;EACpB,CAAC;EACD,YAAY,EAAE;IACV,UAAU,EAAE,IAAI;IAChB,QAAQ,EAAE,IAAI;IACd,UAAU,EAAE,IAAI;IAChB,QAAQ,EAAE,IAAI;IACd,KAAK,EAAE,IAAI;IACX,SAAS,EAAE;EACf,CAAC;EACD,6BAA6B,EAAE,KAAK;EACpC,QAAQ,EAAE;AACd,CAAC"}