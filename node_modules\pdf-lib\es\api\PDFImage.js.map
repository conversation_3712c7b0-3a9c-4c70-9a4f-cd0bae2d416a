{"version": 3, "file": "PDFImage.js", "sourceRoot": "", "sources": ["../../src/api/PDFImage.ts"], "names": [], "mappings": ";AACA,OAAO,WAAW,sBAA4B;AAC9C,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,gBAAiB;AAC7D,OAAO,EAAE,QAAQ,EAAE,iBAAkB;AAIrC;;GAEG;AACH;IA8BE,kBAAoB,GAAW,EAAE,GAAgB,EAAE,QAAuB;QACxE,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3C,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;QACrD,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE;YAC7B,CAAC,YAAY,EAAE,cAAc,CAAC;YAC9B,CAAC,WAAW,EAAE,aAAa,CAAC;SAC7B,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAE9B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,wBAAK,GAAL,UAAM,MAAc;QAClB,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC;IACtE,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,6BAAU,GAAV,UAAW,KAAa,EAAE,MAAc;QACtC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACrC,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEvC,IAAM,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzC,IAAM,cAAc,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC5C,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QAEtD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACH,uBAAI,GAAJ;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IAED;;;;;;;;OAQG;IACG,wBAAK,GAAX;;;;;;wBACE,IAAI,CAAC,IAAI,CAAC,QAAQ;4BAAE,sBAAO;wBAE3B,qEAAqE;wBACrE,mEAAmE;wBACnE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACb,KAAe,IAAI,EAAjB,GAAG,SAAA,EAAE,GAAG,SAAA,CAAU;4BAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;yBACnE;wBACD,qBAAM,IAAI,CAAC,SAAS,EAAA;;wBAApB,SAAoB,CAAC;wBAErB,wEAAwE;wBACxE,yDAAyD;wBACzD,0DAA0D;wBAC1D,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;;;;;KAC3B;IAlID;;;;;;;;;;OAUG;IACI,WAAE,GAAG,UAAC,GAAW,EAAE,GAAgB,EAAE,QAAuB;QACjE,OAAA,IAAI,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC;IAAhC,CAAgC,CAAC;IAuHrC,eAAC;CAAA,AApID,IAoIC;eApIoB,QAAQ"}