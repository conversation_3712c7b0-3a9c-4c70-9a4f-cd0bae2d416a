{"version": 3, "file": "PDFContext.d.ts", "sourceRoot": "", "sources": ["../../src/core/PDFContext.ts"], "names": [], "mappings": "AAEA,OAAO,SAAS,6BAAoC;AAEpD,OAAO,QAAQ,2BAAkC;AACjD,OAAO,OAAO,0BAAiC;AAC/C,OAAO,OAAO,0BAAiC;AAC/C,OAAO,YAAY,+BAAsC;AACzD,OAAO,OAAO,0BAAiC;AAC/C,OAAO,OAAO,0BAAiC;AAC/C,OAAO,SAAS,4BAAmC;AACnD,OAAO,SAAS,4BAAmC;AACnD,OAAO,YAAY,+BAAsC;AACzD,OAAO,MAAM,yBAAgC;AAC7C,OAAO,SAAS,4BAAmC;AACnD,OAAO,SAAS,4BAAmC;AACnD,OAAO,WAAW,gCAAuC;AAEzD,OAAO,gBAAgB,sCAA6C;AAEpE,OAAO,EAAE,SAAS,EAAE,qBAAsB;AAE1C,aAAK,SAAS,GAAG,MAAM,GAAG,SAAS,GAAG,SAAS,CAAC;AAEhD,UAAU,aAAa;IACrB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC;CACrC;AAED,UAAU,YAAY;IACpB,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC;CACtC;AAED,aAAK,OAAO,GACR,aAAa,GACb,YAAY,GACZ,MAAM,GACN,MAAM,GACN,OAAO,GACP,IAAI,GACJ,SAAS,CAAC;AAOd,cAAM,UAAU;IACd,MAAM,CAAC,MAAM,mBAA0B;IAEvC,mBAAmB,EAAE,MAAM,CAAC;IAC5B,MAAM,EAAE,SAAS,CAAC;IAClB,WAAW,EAAE;QACX,IAAI,CAAC,EAAE,SAAS,CAAC;QACjB,OAAO,CAAC,EAAE,SAAS,CAAC;QACpB,IAAI,CAAC,EAAE,SAAS,CAAC;QACjB,EAAE,CAAC,EAAE,SAAS,CAAC;KAChB,CAAC;IACF,GAAG,EAAE,SAAS,CAAC;IAEf,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAyB;IAEzD,OAAO,CAAC,iCAAiC,CAAC,CAAS;IACnD,OAAO,CAAC,gCAAgC,CAAC,CAAS;IAElD,OAAO;IASP,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,GAAG,IAAI;IAO5C,OAAO,IAAI,MAAM;IAKjB,QAAQ,CAAC,MAAM,EAAE,SAAS,GAAG,MAAM;IAMnC,MAAM,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAI5B,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,QAAQ,GAAG,QAAQ,GAAG,SAAS;IACxE,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,SAAS;IACtE,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,SAAS;IACtE,WAAW,CACT,GAAG,EAAE,SAAS,EACd,IAAI,EAAE,OAAO,YAAY,GACxB,YAAY,GAAG,SAAS;IAC3B,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,SAAS;IACtE,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,OAAO,GAAG,SAAS;IAC7E,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS,GAAG,SAAS;IAC1E,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS,GAAG,SAAS;IAC1E,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,MAAM,GAAG,MAAM,GAAG,SAAS;IACpE,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS,GAAG,SAAS;IAC1E,WAAW,CACT,GAAG,EAAE,SAAS,EACd,KAAK,EAAE,OAAO,SAAS,EACvB,KAAK,EAAE,OAAO,YAAY,GACzB,SAAS,GAAG,YAAY,GAAG,SAAS;IAsBvC,MAAM,CAAC,GAAG,EAAE,SAAS,GAAG,SAAS,GAAG,SAAS;IAC7C,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,QAAQ,GAAG,QAAQ;IACvD,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO;IACrD,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO;IACrD,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,YAAY,GAAG,YAAY;IAC/D,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO;IACrD,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,OAAO;IAC5D,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS;IACzD,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS;IACzD,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,MAAM,GAAG,MAAM;IACnD,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS;IACzD,MAAM,CACJ,GAAG,EAAE,SAAS,EACd,KAAK,EAAE,OAAO,SAAS,EACvB,KAAK,EAAE,OAAO,YAAY,GACzB,SAAS,GAAG,YAAY;IAmB3B,YAAY,CAAC,SAAS,EAAE,SAAS,GAAG,MAAM,GAAG,SAAS;IAYtD,wBAAwB,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;IAMjD,GAAG,CAAC,OAAO,EAAE,IAAI,GAAG,SAAS,GAAG,OAAO,OAAO;IAC9C,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO;IAC7B,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,SAAS;IAC/B,GAAG,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO;IAC9B,GAAG,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO;IACpC,GAAG,CAAC,OAAO,EAAE,YAAY,GAAG,QAAQ;IA+BpC,MAAM,CACJ,QAAQ,EAAE,MAAM,GAAG,UAAU,EAC7B,IAAI,GAAE,aAAkB,GACvB,YAAY;IAIf,WAAW,CACT,QAAQ,EAAE,MAAM,GAAG,UAAU,EAC7B,IAAI,GAAE,aAAkB,GACvB,YAAY;IAOf,aAAa,CACX,SAAS,EAAE,WAAW,EAAE,EACxB,IAAI,GAAE,aAAkB,GACvB,gBAAgB;IAInB,WAAW,CACT,SAAS,EAAE,WAAW,EAAE,EACxB,IAAI,GAAE,aAAkB,GACvB,gBAAgB;IAgBnB,iCAAiC,IAAI,MAAM;IAiB3C,gCAAgC,IAAI,MAAM;IAW1C,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,SAAI,GAAG,MAAM;CAG1D;AAED,eAAe,UAAU,CAAC"}