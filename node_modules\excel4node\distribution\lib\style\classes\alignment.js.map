{"version": 3, "file": "alignment.js", "names": ["types", "require", "xmlbuilder", "Alignment", "opts", "_classCallCheck", "horizontal", "undefined", "alignment", "validate", "vertical", "readingOrder", "value", "indent", "parseInt", "TypeError", "justifyLastLine", "relativeIndent", "shrinkToFit", "textRotation", "wrapText", "_createClass", "key", "toObject", "obj", "addToXMLele", "ele", "thisEle", "att", "module", "exports"], "sources": ["../../../../source/lib/style/classes/alignment.js"], "sourcesContent": ["const types = require('../../types/index.js');\nconst xmlbuilder = require('xmlbuilder');\n\nclass Alignment { // §18.8.1 alignment (Alignment)\n    /**\n     * @class Alignment\n     * @param {Object} opts Properties of Alignment object\n     * @param {String} opts.horizontal Horizontal Alignment property of text. \n     * @param {String} opts.vertical Vertical Alignment property of text. \n     * @param {String} opts.readingOrder Reading order for language of text.\n     * @param {Number} opts.indent How much text should be indented. Setting indent to 1 will indent text 3 spaces\n     * @param {Boolean} opts.justifyLastLine Specifies whether to justify last line of text\n     * @param {Number} opts.relativeIndent Used in conditional formatting to state how much more text should be indented if rule passes\n     * @param {Boolean} opts.shrinkToFit Indicates if text should be shrunk to fit into cell\n     * @param {Number} opts.textRotation Number of degrees to rotate text counterclockwise\n     * @param {Boolean} opts.wrapText States whether text with newline characters should wrap\n     * @returns {Alignment}\n     */\n    constructor(opts) {\n\n        if (opts.horizontal !== undefined) {\n            this.horizontal = types.alignment.horizontal.validate(opts.horizontal) === true ? opts.horizontal : null;\n        }\n\n        if (opts.vertical !== undefined) {\n            this.vertical = types.alignment.vertical.validate(opts.vertical) === true ? opts.vertical : null;\n        }\n\n        if (opts.readingOrder !== undefined) {\n            const value = types.alignment.readingOrder[opts.readingOrder];\n            this.readingOrder = types.alignment.readingOrder.validate(opts.readingOrder) === true ? value: null;\n        }\n\n        if (opts.indent !== undefined) {\n            if (typeof opts.indent === 'number' && parseInt(opts.indent) === opts.indent && opts.indent > 0) {\n                this.indent = opts.indent;\n            } else {\n                throw new TypeError('alignment indent must be a positive integer.');\n            }\n        }\n\n        if (opts.justifyLastLine !== undefined) {\n            if (typeof opts.justifyLastLine === 'boolean') {\n                this.justifyLastLine = opts.justifyLastLine;\n            } else {\n                throw new TypeError('justifyLastLine alignment option must be of type boolean');\n            }\n        }\n\n        if (opts.relativeIndent !== undefined) {\n            if (typeof opts.relativeIndent === 'number' && parseInt(opts.relativeIndent) === opts.relativeIndent && opts.relativeIndent > 0) {\n                this.relativeIndent = opts.relativeIndent;\n            } else {\n                throw new TypeError('alignment indent must be a positive integer.');\n            }\n        }\n\n        if (opts.shrinkToFit !== undefined) {\n            if (typeof opts.shrinkToFit === 'boolean') {\n                this.shrinkToFit = opts.shrinkToFit;\n            } else {\n                throw new TypeError('justifyLastLine alignment option must be of type boolean');\n            }\n        }\n\n        if (opts.textRotation !== undefined) {\n            if (typeof opts.textRotation === 'number' && parseInt(opts.textRotation) === opts.textRotation) {\n                this.textRotation = opts.textRotation;\n            } else if (opts.textRotation !== undefined) {\n                throw new TypeError('alignment indent must be an integer.');\n            }\n        }\n\n        if (opts.wrapText !== undefined) {\n            if (typeof opts.wrapText === 'boolean') {\n                this.wrapText = opts.wrapText;\n            } else {\n                throw new TypeError('justifyLastLine alignment option must be of type boolean');\n            }\n        }\n    }\n\n    /** \n     * @func Alignment.toObject\n     * @desc Converts the Alignment instance to a javascript object\n     * @returns {Object}\n     */\n    toObject() {\n        let obj = {};\n\n        this.horizontal !== undefined ? obj.horizontal = this.horizontal : null;\n        this.indent !== undefined ? obj.indent = this.indent : null;\n        this.justifyLastLine !== undefined ? obj.justifyLastLine = this.justifyLastLine : null;\n        this.readingOrder !== undefined ? obj.readingOrder = this.readingOrder : null;\n        this.relativeIndent !== undefined ? obj.relativeIndent = this.relativeIndent : null;\n        this.shrinkToFit !== undefined ? obj.shrinkToFit = this.shrinkToFit : null;\n        this.textRotation !== undefined ? obj.textRotation = this.textRotation : null;\n        this.vertical !== undefined ? obj.vertical = this.vertical : null;\n        this.wrapText !== undefined ? obj.wrapText = this.wrapText : null;\n\n        return obj;\n    }\n\n    /**\n     * @alias Alignment.addToXMLele\n     * @desc When generating Workbook output, attaches style to the styles xml file\n     * @func Alignment.addToXMLele\n     * @param {xmlbuilder.Element} ele Element object of the xmlbuilder module\n     */\n    addToXMLele(ele) {\n        let thisEle = ele.ele('alignment');\n        this.horizontal !== undefined ? thisEle.att('horizontal', this.horizontal) : null;\n        this.indent !== undefined ? thisEle.att('indent', this.indent) : null;\n        this.justifyLastLine === true ? thisEle.att('justifyLastLine', 1) : null;\n        this.readingOrder !== undefined ? thisEle.att('readingOrder', this.readingOrder) : null;\n        this.relativeIndent !== undefined ? thisEle.att('relativeIndent', this.relativeIndent) : null;\n        this.shrinkToFit === true ? thisEle.att('shrinkToFit', 1) : null;\n        this.textRotation !== undefined ? thisEle.att('textRotation', this.textRotation) : null;\n        this.vertical !== undefined ? thisEle.att('vertical', this.vertical) : null;\n        this.wrapText === true ? thisEle.att('wrapText', 1) : null;\n    }\n}\n\nmodule.exports = Alignment;"], "mappings": ";;;;;AAAA,IAAMA,KAAK,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAMC,UAAU,GAAGD,OAAO,CAAC,YAAY,CAAC;AAAC,IAEnCE,SAAS;EAAG;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,UAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,SAAA;IAEd,IAAIC,IAAI,CAACE,UAAU,KAAKC,SAAS,EAAE;MAC/B,IAAI,CAACD,UAAU,GAAGN,KAAK,CAACQ,SAAS,CAACF,UAAU,CAACG,QAAQ,CAACL,IAAI,CAACE,UAAU,CAAC,KAAK,IAAI,GAAGF,IAAI,CAACE,UAAU,GAAG,IAAI;IAC5G;IAEA,IAAIF,IAAI,CAACM,QAAQ,KAAKH,SAAS,EAAE;MAC7B,IAAI,CAACG,QAAQ,GAAGV,KAAK,CAACQ,SAAS,CAACE,QAAQ,CAACD,QAAQ,CAACL,IAAI,CAACM,QAAQ,CAAC,KAAK,IAAI,GAAGN,IAAI,CAACM,QAAQ,GAAG,IAAI;IACpG;IAEA,IAAIN,IAAI,CAACO,YAAY,KAAKJ,SAAS,EAAE;MACjC,IAAMK,KAAK,GAAGZ,KAAK,CAACQ,SAAS,CAACG,YAAY,CAACP,IAAI,CAACO,YAAY,CAAC;MAC7D,IAAI,CAACA,YAAY,GAAGX,KAAK,CAACQ,SAAS,CAACG,YAAY,CAACF,QAAQ,CAACL,IAAI,CAACO,YAAY,CAAC,KAAK,IAAI,GAAGC,KAAK,GAAE,IAAI;IACvG;IAEA,IAAIR,IAAI,CAACS,MAAM,KAAKN,SAAS,EAAE;MAC3B,IAAI,OAAOH,IAAI,CAACS,MAAM,KAAK,QAAQ,IAAIC,QAAQ,CAACV,IAAI,CAACS,MAAM,CAAC,KAAKT,IAAI,CAACS,MAAM,IAAIT,IAAI,CAACS,MAAM,GAAG,CAAC,EAAE;QAC7F,IAAI,CAACA,MAAM,GAAGT,IAAI,CAACS,MAAM;MAC7B,CAAC,MAAM;QACH,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;MACvE;IACJ;IAEA,IAAIX,IAAI,CAACY,eAAe,KAAKT,SAAS,EAAE;MACpC,IAAI,OAAOH,IAAI,CAACY,eAAe,KAAK,SAAS,EAAE;QAC3C,IAAI,CAACA,eAAe,GAAGZ,IAAI,CAACY,eAAe;MAC/C,CAAC,MAAM;QACH,MAAM,IAAID,SAAS,CAAC,0DAA0D,CAAC;MACnF;IACJ;IAEA,IAAIX,IAAI,CAACa,cAAc,KAAKV,SAAS,EAAE;MACnC,IAAI,OAAOH,IAAI,CAACa,cAAc,KAAK,QAAQ,IAAIH,QAAQ,CAACV,IAAI,CAACa,cAAc,CAAC,KAAKb,IAAI,CAACa,cAAc,IAAIb,IAAI,CAACa,cAAc,GAAG,CAAC,EAAE;QAC7H,IAAI,CAACA,cAAc,GAAGb,IAAI,CAACa,cAAc;MAC7C,CAAC,MAAM;QACH,MAAM,IAAIF,SAAS,CAAC,8CAA8C,CAAC;MACvE;IACJ;IAEA,IAAIX,IAAI,CAACc,WAAW,KAAKX,SAAS,EAAE;MAChC,IAAI,OAAOH,IAAI,CAACc,WAAW,KAAK,SAAS,EAAE;QACvC,IAAI,CAACA,WAAW,GAAGd,IAAI,CAACc,WAAW;MACvC,CAAC,MAAM;QACH,MAAM,IAAIH,SAAS,CAAC,0DAA0D,CAAC;MACnF;IACJ;IAEA,IAAIX,IAAI,CAACe,YAAY,KAAKZ,SAAS,EAAE;MACjC,IAAI,OAAOH,IAAI,CAACe,YAAY,KAAK,QAAQ,IAAIL,QAAQ,CAACV,IAAI,CAACe,YAAY,CAAC,KAAKf,IAAI,CAACe,YAAY,EAAE;QAC5F,IAAI,CAACA,YAAY,GAAGf,IAAI,CAACe,YAAY;MACzC,CAAC,MAAM,IAAIf,IAAI,CAACe,YAAY,KAAKZ,SAAS,EAAE;QACxC,MAAM,IAAIQ,SAAS,CAAC,sCAAsC,CAAC;MAC/D;IACJ;IAEA,IAAIX,IAAI,CAACgB,QAAQ,KAAKb,SAAS,EAAE;MAC7B,IAAI,OAAOH,IAAI,CAACgB,QAAQ,KAAK,SAAS,EAAE;QACpC,IAAI,CAACA,QAAQ,GAAGhB,IAAI,CAACgB,QAAQ;MACjC,CAAC,MAAM;QACH,MAAM,IAAIL,SAAS,CAAC,0DAA0D,CAAC;MACnF;IACJ;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EAJIM,YAAA,CAAAlB,SAAA;IAAAmB,GAAA;IAAAV,KAAA,EAKA,SAAAW,SAAA,EAAW;MACP,IAAIC,GAAG,GAAG,CAAC,CAAC;MAEZ,IAAI,CAAClB,UAAU,KAAKC,SAAS,GAAGiB,GAAG,CAAClB,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,IAAI;MACvE,IAAI,CAACO,MAAM,KAAKN,SAAS,GAAGiB,GAAG,CAACX,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI;MAC3D,IAAI,CAACG,eAAe,KAAKT,SAAS,GAAGiB,GAAG,CAACR,eAAe,GAAG,IAAI,CAACA,eAAe,GAAG,IAAI;MACtF,IAAI,CAACL,YAAY,KAAKJ,SAAS,GAAGiB,GAAG,CAACb,YAAY,GAAG,IAAI,CAACA,YAAY,GAAG,IAAI;MAC7E,IAAI,CAACM,cAAc,KAAKV,SAAS,GAAGiB,GAAG,CAACP,cAAc,GAAG,IAAI,CAACA,cAAc,GAAG,IAAI;MACnF,IAAI,CAACC,WAAW,KAAKX,SAAS,GAAGiB,GAAG,CAACN,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAI;MAC1E,IAAI,CAACC,YAAY,KAAKZ,SAAS,GAAGiB,GAAG,CAACL,YAAY,GAAG,IAAI,CAACA,YAAY,GAAG,IAAI;MAC7E,IAAI,CAACT,QAAQ,KAAKH,SAAS,GAAGiB,GAAG,CAACd,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAI;MACjE,IAAI,CAACU,QAAQ,KAAKb,SAAS,GAAGiB,GAAG,CAACJ,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAI;MAEjE,OAAOI,GAAG;IACd;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAF,GAAA;IAAAV,KAAA,EAMA,SAAAa,YAAYC,GAAG,EAAE;MACb,IAAIC,OAAO,GAAGD,GAAG,CAACA,GAAG,CAAC,WAAW,CAAC;MAClC,IAAI,CAACpB,UAAU,KAAKC,SAAS,GAAGoB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACtB,UAAU,CAAC,GAAG,IAAI;MACjF,IAAI,CAACO,MAAM,KAAKN,SAAS,GAAGoB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACf,MAAM,CAAC,GAAG,IAAI;MACrE,IAAI,CAACG,eAAe,KAAK,IAAI,GAAGW,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC,GAAG,IAAI;MACxE,IAAI,CAACjB,YAAY,KAAKJ,SAAS,GAAGoB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACjB,YAAY,CAAC,GAAG,IAAI;MACvF,IAAI,CAACM,cAAc,KAAKV,SAAS,GAAGoB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACX,cAAc,CAAC,GAAG,IAAI;MAC7F,IAAI,CAACC,WAAW,KAAK,IAAI,GAAGS,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,IAAI;MAChE,IAAI,CAACT,YAAY,KAAKZ,SAAS,GAAGoB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACT,YAAY,CAAC,GAAG,IAAI;MACvF,IAAI,CAACT,QAAQ,KAAKH,SAAS,GAAGoB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAClB,QAAQ,CAAC,GAAG,IAAI;MAC3E,IAAI,CAACU,QAAQ,KAAK,IAAI,GAAGO,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,IAAI;IAC9D;EAAC;EAAA,OAAAzB,SAAA;AAAA;AAGL0B,MAAM,CAACC,OAAO,GAAG3B,SAAS"}