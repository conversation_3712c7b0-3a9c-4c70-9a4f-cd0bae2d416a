/**
 * VidyaMitra Platform - Performance Optimization Utilities
 * 
 * Bundle size optimization, lazy loading, and performance monitoring
 * Target: Reduce bundle size from 5784KB to <2000KB
 */

import { lazy } from 'react';

// Lazy load heavy components to reduce initial bundle size
export const LazyComponents = {
  // Charts and Analytics (heavy Chart.js dependency)
  AnalyticsDashboard: lazy(() => import('../components/Analytics/AnalyticsDashboard')),
  
  // SWOT Analysis (complex visualizations)
  CulturalSWOTVisualization: lazy(() => import('../components/SWOT/CulturalSWOTVisualization')),
  IndividualStudentSWOT: lazy(() => import('../components/SWOT/IndividualStudentSWOT')),
  SWOTWizard: lazy(() => import('../components/SWOT/SWOTWizard')),
  
  // Reports (PDF generation libraries)
  ReportsPage: lazy(() => import('../components/Reports/ReportsPage')),
  ReportGeneration: lazy(() => import('../components/Reports/ReportGeneration')),
  
  // Student Management (data-heavy components)
  StudentRegistration: lazy(() => import('../components/Students/StudentRegistration')),
  StudentProfile: lazy(() => import('../components/Students/StudentProfile')),
  
  // Attendance and Grades (calendar and form libraries)
  AttendanceManagement: lazy(() => import('../components/Attendance/AttendanceManagement')),
  GradeEntry: lazy(() => import('../components/Grades/GradeEntry')),
  
  // Teacher Dashboard (role-specific heavy components)
  TeacherDashboard: lazy(() => import('../components/Dashboard/TeacherDashboard')),
  
  // Validation Dashboard (development tool)
  ValidationDashboard: lazy(() => import('../components/Debug/ValidationDashboard'))
};

// Bundle analysis utilities
export const bundleAnalysis = {
  // Track component load times
  trackComponentLoad: (componentName, startTime) => {
    const endTime = performance.now();
    const loadTime = endTime - startTime;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 Component Load Time: ${componentName} - ${loadTime.toFixed(2)}ms`);
    }
    
    // Store metrics for analysis
    if (typeof window !== 'undefined') {
      window.performanceMetrics = window.performanceMetrics || {};
      window.performanceMetrics[componentName] = loadTime;
    }
  },
  
  // Monitor bundle chunks
  trackChunkLoad: (chunkName) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`📦 Chunk Loaded: ${chunkName}`);
    }
  },
  
  // Get performance summary
  getPerformanceSummary: () => {
    if (typeof window === 'undefined') return null;
    
    const metrics = window.performanceMetrics || {};
    const navigation = performance.getEntriesByType('navigation')[0];
    
    return {
      componentLoadTimes: metrics,
      pageLoadTime: navigation?.loadEventEnd - navigation?.navigationStart,
      domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.navigationStart,
      firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime,
      largestContentfulPaint: performance.getEntriesByName('largest-contentful-paint')[0]?.startTime
    };
  }
};

// Image optimization utilities
export const imageOptimization = {
  // Lazy load images
  createLazyImage: (src, alt, className = '') => {
    const img = document.createElement('img');
    img.dataset.src = src;
    img.alt = alt;
    img.className = className;
    img.loading = 'lazy';
    
    // Intersection Observer for lazy loading
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.add('loaded');
          observer.unobserve(img);
        }
      });
    });
    
    observer.observe(img);
    return img;
  },
  
  // Optimize image format based on browser support
  getOptimizedImageSrc: (basePath, format = 'webp') => {
    const supportsWebP = () => {
      const canvas = document.createElement('canvas');
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    };
    
    const supportsAVIF = () => {
      const canvas = document.createElement('canvas');
      return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
    };
    
    if (format === 'avif' && supportsAVIF()) {
      return `${basePath}.avif`;
    } else if (format === 'webp' && supportsWebP()) {
      return `${basePath}.webp`;
    } else {
      return `${basePath}.jpg`;
    }
  }
};

// Memory management utilities
export const memoryManagement = {
  // Clean up component resources
  cleanupComponent: (componentName) => {
    // Clear any intervals or timeouts
    if (typeof window !== 'undefined') {
      const timers = window.componentTimers?.[componentName];
      if (timers) {
        timers.forEach(timer => clearTimeout(timer));
        delete window.componentTimers[componentName];
      }
    }
  },
  
  // Monitor memory usage
  getMemoryUsage: () => {
    if (performance.memory) {
      return {
        used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) // MB
      };
    }
    return null;
  },
  
  // Garbage collection hint (Chrome only)
  suggestGC: () => {
    if (window.gc && process.env.NODE_ENV === 'development') {
      window.gc();
      console.log('🗑️ Garbage collection suggested');
    }
  }
};

// Network optimization utilities
export const networkOptimization = {
  // Preload critical resources
  preloadResource: (href, as = 'script', crossorigin = null) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (crossorigin) link.crossOrigin = crossorigin;
    document.head.appendChild(link);
  },
  
  // Prefetch next page resources
  prefetchPage: (path) => {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = path;
    document.head.appendChild(link);
  },
  
  // Check connection quality
  getConnectionInfo: () => {
    if (navigator.connection) {
      return {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt,
        saveData: navigator.connection.saveData
      };
    }
    return null;
  },
  
  // Adaptive loading based on connection
  shouldLoadHeavyContent: () => {
    const connection = networkOptimization.getConnectionInfo();
    if (!connection) return true; // Default to loading if no info
    
    // Don't load heavy content on slow connections or save-data mode
    return connection.effectiveType !== 'slow-2g' && 
           connection.effectiveType !== '2g' && 
           !connection.saveData;
  }
};

// Performance monitoring
export const performanceMonitoring = {
  // Start performance measurement
  startMeasure: (name) => {
    performance.mark(`${name}-start`);
  },
  
  // End performance measurement
  endMeasure: (name) => {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const measure = performance.getEntriesByName(name)[0];
    if (process.env.NODE_ENV === 'development') {
      console.log(`⏱️ Performance: ${name} - ${measure.duration.toFixed(2)}ms`);
    }
    
    return measure.duration;
  },
  
  // Monitor Core Web Vitals
  monitorWebVitals: () => {
    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('🎯 LCP:', lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });
    
    // First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        console.log('🎯 FID:', entry.processingStart - entry.startTime);
      });
    }).observe({ entryTypes: ['first-input'] });
    
    // Cumulative Layout Shift
    new PerformanceObserver((list) => {
      let clsValue = 0;
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      console.log('🎯 CLS:', clsValue);
    }).observe({ entryTypes: ['layout-shift'] });
  }
};

// Bundle size optimization recommendations
export const optimizationRecommendations = {
  // Analyze current bundle
  analyzeBundleSize: () => {
    const recommendations = [];
    
    // Check for heavy dependencies
    if (window.Chart) {
      recommendations.push({
        type: 'dependency',
        message: 'Chart.js detected - consider lazy loading analytics components',
        impact: 'high',
        solution: 'Implement lazy loading for AnalyticsDashboard'
      });
    }
    
    // Check memory usage
    const memory = memoryManagement.getMemoryUsage();
    if (memory && memory.used > 50) {
      recommendations.push({
        type: 'memory',
        message: `High memory usage: ${memory.used}MB`,
        impact: 'medium',
        solution: 'Implement component cleanup and memory management'
      });
    }
    
    // Check connection
    const connection = networkOptimization.getConnectionInfo();
    if (connection && connection.saveData) {
      recommendations.push({
        type: 'network',
        message: 'User has save-data preference enabled',
        impact: 'high',
        solution: 'Reduce image quality and defer non-critical resources'
      });
    }
    
    return recommendations;
  },
  
  // Get optimization score
  getOptimizationScore: () => {
    const recommendations = optimizationRecommendations.analyzeBundleSize();
    const highImpact = recommendations.filter(r => r.impact === 'high').length;
    const mediumImpact = recommendations.filter(r => r.impact === 'medium').length;
    
    const score = Math.max(0, 100 - (highImpact * 20) - (mediumImpact * 10));
    
    return {
      score,
      grade: score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F',
      recommendations
    };
  }
};

export default {
  LazyComponents,
  bundleAnalysis,
  imageOptimization,
  memoryManagement,
  networkOptimization,
  performanceMonitoring,
  optimizationRecommendations
};
