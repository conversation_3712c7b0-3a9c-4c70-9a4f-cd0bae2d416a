"use strict";

module.exports = {
  'margins': {
    'bottom': 0.75,
    'footer': 0.3,
    'header': 0.3,
    'left': 0.7,
    'right': 0.7,
    'top': 0.75
  },
  'printOptions': {
    'centerHorizontal': null,
    'centerVertical': null,
    'printGridLines': null,
    'printHeadings': null
  },
  'headerFooter': {
    'evenFooter': null,
    'evenHeader': null,
    'firstFooter': null,
    'firstHeader': null,
    'oddFooter': null,
    'oddHeader': null,
    'alignWithMargins': null,
    'differentFirst': null,
    'differentOddEven': null,
    'scaleWithDoc': null
  },
  'pageSetup': {
    'blackAndWhite': null,
    'cellComments': null,
    'copies': null,
    'draft': null,
    'errors': null,
    'firstPageNumber': null,
    'fitToHeight': null,
    'fitToWidth': null,
    'horizontalDpi': null,
    'orientation': null,
    'pageOrder': null,
    'paperHeight': null,
    'paperSize': null,
    'paperWidth': null,
    'scale': null,
    'useFirstPageNumber': null,
    'usePrinterDefaults': null,
    'verticalDpi': null
  },
  'sheetView': {
    'pane': {
      'activePane': null,
      'state': null,
      'topLeftCell': null,
      'xSplit': null,
      'ySplit': null
    },
    'tabSelected': 0,
    'workbookViewId': 0,
    'rightToLeft': 0,
    'showGridLines': 1,
    'zoomScale': 100,
    'zoomScaleNormal': 100,
    'zoomScalePageLayoutView': 100
  },
  'sheetFormat': {
    'baseColWidth': 10,
    'customHeight': null,
    'defaultColWidth': null,
    'defaultRowHeight': null,
    'outlineLevelCol': null,
    'outlineLevelRow': null,
    'thickBottom': null,
    'thickTop': null,
    'zeroHeight': null
  },
  'sheetProtection': {
    // same as "Protect Sheet" in Review tab of Excel 
    'autoFilter': null,
    'deleteColumns': null,
    'deleteRows': null,
    'formatCells': null,
    'formatColumns': null,
    'formatRows': null,
    'hashValue': null,
    'insertColumns': null,
    'insertHyperlinks': null,
    'insertRows': null,
    'objects': null,
    'password': null,
    'pivotTables': null,
    'scenarios': null,
    'selectLockedCells': null,
    'selectUnlockedCells': null,
    'sheet': null,
    'sort': null
  },
  'outline': {
    'summaryBelow': null,
    'summaryRight': null
  },
  'autoFilter': {
    'startRow': null,
    'endRow': null,
    'startCol': null,
    'endCol': null,
    'ref': null,
    'filters': []
  },
  'disableRowSpansOptimization': false,
  'hidden': false
};
//# sourceMappingURL=sheet_default_params.js.map