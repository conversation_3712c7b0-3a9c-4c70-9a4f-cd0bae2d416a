/**
 * VidyaMitra Platform - Navigation Data
 * 
 * Comprehensive navigation structure ensuring all features are accessible
 * through the platform UI with role-based access control
 */

import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Analytics as AnalyticsIcon,
  School as SchoolIcon,
  Assignment as AssignmentIcon,
  EventNote as EventNoteIcon,
  Settings as SettingsIcon,
  Help as HelpIcon,
  Info as InfoIcon,
  ContactMail as ContactIcon,
  Features as FeaturesIcon,
  Report as ReportIcon,
  Psychology as SWOTIcon,
  CalendarToday as CalendarIcon,
  Grade as GradeIcon,
  Groups as GroupsIcon,
  PersonAdd as PersonAddIcon,
  BarChart as ChartIcon,
  TrendingUp as TrendingUpIcon,
  AccountCircle as ProfileIcon,
  Notifications as NotificationsIcon,
  Language as LanguageIcon,
  Brightness4 as ThemeIcon
} from '@mui/icons-material';

// Main navigation items for authenticated users
export const mainNavigation = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    path: '/dashboard',
    icon: DashboardIcon,
    roles: ['principal', 'teacher', 'student', 'parent'],
    description: 'Overview of key metrics and recent activities',
    children: [
      {
        id: 'modern-dashboard',
        title: 'Modern Dashboard',
        path: '/dashboard',
        icon: DashboardIcon,
        roles: ['principal', 'teacher']
      },
      {
        id: 'teacher-dashboard',
        title: 'Teacher Dashboard',
        path: '/dashboard/teacher',
        icon: SchoolIcon,
        roles: ['teacher']
      },
      {
        id: 'student-dashboard',
        title: 'Student Dashboard',
        path: '/dashboard/student',
        icon: PersonAddIcon,
        roles: ['student']
      },
      {
        id: 'parent-dashboard',
        title: 'Parent Dashboard',
        path: '/dashboard/parent',
        icon: GroupsIcon,
        roles: ['parent']
      }
    ]
  },
  {
    id: 'students',
    title: 'Student Management',
    path: '/students',
    icon: PeopleIcon,
    roles: ['principal', 'teacher'],
    description: 'Manage student profiles, enrollment, and academic records',
    children: [
      {
        id: 'student-list',
        title: 'All Students',
        path: '/students',
        icon: PeopleIcon,
        roles: ['principal', 'teacher']
      },
      {
        id: 'student-registration',
        title: 'Add New Student',
        path: '/students/register',
        icon: PersonAddIcon,
        roles: ['principal', 'teacher']
      },
      {
        id: 'student-profiles',
        title: 'Student Profiles',
        path: '/students/profiles',
        icon: ProfileIcon,
        roles: ['principal', 'teacher']
      }
    ]
  },
  {
    id: 'swot',
    title: 'SWOT Analysis',
    path: '/swot',
    icon: SWOTIcon,
    roles: ['principal', 'teacher', 'student', 'parent'],
    description: 'Comprehensive SWOT analysis and visualization tools',
    children: [
      {
        id: 'swot-overview',
        title: 'SWOT Overview',
        path: '/swot',
        icon: SWOTIcon,
        roles: ['principal', 'teacher', 'student', 'parent']
      },
      {
        id: 'cultural-swot',
        title: 'Cultural SWOT',
        path: '/swot/cultural',
        icon: LanguageIcon,
        roles: ['principal', 'teacher']
      },
      {
        id: 'individual-swot',
        title: 'Individual Analysis',
        path: '/swot/individual',
        icon: PersonAddIcon,
        roles: ['principal', 'teacher', 'student', 'parent']
      },
      {
        id: 'swot-wizard',
        title: 'SWOT Wizard',
        path: '/swot/wizard',
        icon: AssessmentIcon,
        roles: ['teacher']
      }
    ]
  },
  {
    id: 'analytics',
    title: 'Analytics',
    path: '/analytics',
    icon: AnalyticsIcon,
    roles: ['principal', 'teacher'],
    description: 'Advanced analytics and performance insights',
    children: [
      {
        id: 'performance-analytics',
        title: 'Performance Analytics',
        path: '/analytics',
        icon: ChartIcon,
        roles: ['principal', 'teacher']
      },
      {
        id: 'trend-analysis',
        title: 'Trend Analysis',
        path: '/analytics/trends',
        icon: TrendingUpIcon,
        roles: ['principal', 'teacher']
      },
      {
        id: 'comparative-analysis',
        title: 'Comparative Analysis',
        path: '/analytics/compare',
        icon: BarChart,
        roles: ['principal', 'teacher']
      }
    ]
  },
  {
    id: 'grades',
    title: 'Grade Management',
    path: '/grades',
    icon: GradeIcon,
    roles: ['principal', 'teacher'],
    description: 'Manage grades, assessments, and academic records',
    children: [
      {
        id: 'grade-entry',
        title: 'Grade Entry',
        path: '/grades',
        icon: AssignmentIcon,
        roles: ['teacher']
      },
      {
        id: 'grade-reports',
        title: 'Grade Reports',
        path: '/grades/reports',
        icon: ReportIcon,
        roles: ['principal', 'teacher']
      }
    ]
  },
  {
    id: 'attendance',
    title: 'Attendance',
    path: '/attendance',
    icon: EventNoteIcon,
    roles: ['principal', 'teacher'],
    description: 'Track and manage student attendance',
    children: [
      {
        id: 'attendance-management',
        title: 'Attendance Management',
        path: '/attendance',
        icon: EventNoteIcon,
        roles: ['teacher']
      },
      {
        id: 'attendance-reports',
        title: 'Attendance Reports',
        path: '/attendance/reports',
        icon: ReportIcon,
        roles: ['principal', 'teacher']
      }
    ]
  },
  {
    id: 'reports',
    title: 'Reports',
    path: '/reports',
    icon: ReportIcon,
    roles: ['principal', 'teacher', 'parent'],
    description: 'Generate and view comprehensive reports',
    children: [
      {
        id: 'student-reports',
        title: 'Student Reports',
        path: '/reports',
        icon: ReportIcon,
        roles: ['principal', 'teacher', 'parent']
      },
      {
        id: 'performance-reports',
        title: 'Performance Reports',
        path: '/reports/performance',
        icon: ChartIcon,
        roles: ['principal', 'teacher']
      },
      {
        id: 'custom-reports',
        title: 'Custom Reports',
        path: '/reports/custom',
        icon: AssignmentIcon,
        roles: ['principal']
      }
    ]
  }
];

// Quick actions for dashboard
export const quickActions = [
  {
    id: 'add-student',
    title: 'Add Student',
    path: '/students/register',
    icon: PersonAddIcon,
    color: 'primary',
    roles: ['principal', 'teacher']
  },
  {
    id: 'generate-report',
    title: 'Generate Report',
    path: '/reports',
    icon: ReportIcon,
    color: 'secondary',
    roles: ['principal', 'teacher', 'parent']
  },
  {
    id: 'swot-analysis',
    title: 'SWOT Analysis',
    path: '/swot/wizard',
    icon: SWOTIcon,
    color: 'success',
    roles: ['teacher']
  },
  {
    id: 'view-analytics',
    title: 'View Analytics',
    path: '/analytics',
    icon: AnalyticsIcon,
    color: 'info',
    roles: ['principal', 'teacher']
  }
];

// Settings and profile navigation
export const settingsNavigation = [
  {
    id: 'profile',
    title: 'Profile Settings',
    path: '/settings/profile',
    icon: ProfileIcon,
    roles: ['principal', 'teacher', 'student', 'parent']
  },
  {
    id: 'notifications',
    title: 'Notifications',
    path: '/settings/notifications',
    icon: NotificationsIcon,
    roles: ['principal', 'teacher', 'student', 'parent']
  },
  {
    id: 'language',
    title: 'Language & Region',
    path: '/settings/language',
    icon: LanguageIcon,
    roles: ['principal', 'teacher', 'student', 'parent']
  },
  {
    id: 'theme',
    title: 'Theme Settings',
    path: '/settings/theme',
    icon: ThemeIcon,
    roles: ['principal', 'teacher', 'student', 'parent']
  },
  {
    id: 'system',
    title: 'System Settings',
    path: '/settings/system',
    icon: SettingsIcon,
    roles: ['principal']
  }
];

// Public navigation (for non-authenticated users)
export const publicNavigation = [
  {
    id: 'home',
    title: 'Home',
    path: '/',
    icon: DashboardIcon
  },
  {
    id: 'about',
    title: 'About',
    path: '/about',
    icon: InfoIcon
  },
  {
    id: 'features',
    title: 'Features',
    path: '/features',
    icon: FeaturesIcon
  },
  {
    id: 'contact',
    title: 'Contact',
    path: '/contact',
    icon: ContactIcon
  },
  {
    id: 'help',
    title: 'Help',
    path: '/help',
    icon: HelpIcon
  }
];

// Footer navigation
export const footerNavigation = [
  {
    id: 'privacy',
    title: 'Privacy Policy',
    path: '/privacy',
    external: false
  },
  {
    id: 'terms',
    title: 'Terms of Service',
    path: '/terms',
    external: false
  },
  {
    id: 'support',
    title: 'Support',
    path: '/support',
    external: false
  },
  {
    id: 'documentation',
    title: 'Documentation',
    path: '/docs',
    external: false
  }
];

// Helper function to filter navigation by user role
export const getNavigationByRole = (navigation, userRole) => {
  return navigation.filter(item => 
    !item.roles || item.roles.includes(userRole)
  ).map(item => ({
    ...item,
    children: item.children ? item.children.filter(child => 
      !child.roles || child.roles.includes(userRole)
    ) : undefined
  }));
};

// Helper function to get all accessible paths for a role
export const getAccessiblePaths = (userRole) => {
  const paths = [];
  const navigation = getNavigationByRole(mainNavigation, userRole);
  
  navigation.forEach(item => {
    paths.push(item.path);
    if (item.children) {
      item.children.forEach(child => {
        paths.push(child.path);
      });
    }
  });
  
  return paths;
};

export default {
  mainNavigation,
  quickActions,
  settingsNavigation,
  publicNavigation,
  footerNavigation,
  getNavigationByRole,
  getAccessiblePaths
};
