#!/usr/bin/env node

/**
 * VidyaMitra Platform - Production Build Validation Script
 * 
 * Comprehensive validation script to ensure the platform is production-ready
 * Tests build process, bundle analysis, performance metrics, and deployment readiness
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

// Helper functions
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const logResult = (test, passed, message = '') => {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  const color = passed ? 'green' : 'red';
  log(`${status} ${test}${message ? ': ' + message : ''}`, color);
  
  if (passed) {
    results.passed++;
  } else {
    results.failed++;
  }
  
  results.details.push({ test, passed, message });
};

const logWarning = (test, message) => {
  log(`⚠️  WARN ${test}: ${message}`, 'yellow');
  results.warnings++;
  results.details.push({ test, passed: true, message, warning: true });
};

// Validation tests
const validateEnvironment = () => {
  log('\n🔍 Validating Environment...', 'cyan');
  
  try {
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    logResult('Node.js Version', majorVersion >= 16, `${nodeVersion} (requires >= 16)`);
    
    // Check if package.json exists
    const packageJsonExists = fs.existsSync('package.json');
    logResult('package.json exists', packageJsonExists);
    
    if (packageJsonExists) {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      logResult('Package name', packageJson.name === '@vidyamitra/web-frontend' || packageJson.name === 'vidyamitra-frontend');
      logResult('Package version defined', !!packageJson.version);
    }
    
    // Check if node_modules exists
    const nodeModulesExists = fs.existsSync('node_modules');
    logResult('Dependencies installed', nodeModulesExists);
    
  } catch (error) {
    logResult('Environment validation', false, error.message);
  }
};

const validateBuildProcess = () => {
  log('\n🏗️  Validating Build Process...', 'cyan');
  
  try {
    // Clean previous build
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true });
    }
    
    // Run build command
    log('Running production build...', 'blue');
    execSync('npm run build', { stdio: 'pipe' });
    
    // Check if dist folder was created
    const distExists = fs.existsSync('dist');
    logResult('Build output created', distExists);
    
    if (distExists) {
      // Check for essential files
      const indexHtmlExists = fs.existsSync('dist/index.html');
      logResult('index.html generated', indexHtmlExists);
      
      const assetsExists = fs.existsSync('dist/assets');
      logResult('Assets folder created', assetsExists);
      
      if (assetsExists) {
        const assetFiles = fs.readdirSync('dist/assets');
        const hasJS = assetFiles.some(file => file.endsWith('.js'));
        const hasCSS = assetFiles.some(file => file.endsWith('.css'));
        
        logResult('JavaScript bundles generated', hasJS);
        logResult('CSS bundles generated', hasCSS);
      }
    }
    
  } catch (error) {
    logResult('Build process', false, error.message);
  }
};

const validateBundleSize = () => {
  log('\n📦 Validating Bundle Size...', 'cyan');
  
  try {
    if (!fs.existsSync('dist/assets')) {
      logResult('Bundle size check', false, 'No build assets found');
      return;
    }
    
    const assetFiles = fs.readdirSync('dist/assets');
    let totalSize = 0;
    let jsSize = 0;
    let cssSize = 0;
    
    assetFiles.forEach(file => {
      const filePath = path.join('dist/assets', file);
      const stats = fs.statSync(filePath);
      const sizeKB = stats.size / 1024;
      
      totalSize += sizeKB;
      
      if (file.endsWith('.js')) {
        jsSize += sizeKB;
      } else if (file.endsWith('.css')) {
        cssSize += sizeKB;
      }
    });
    
    // Bundle size thresholds (in KB)
    const MAX_TOTAL_SIZE = 2000; // 2MB
    const MAX_JS_SIZE = 1500;    // 1.5MB
    const MAX_CSS_SIZE = 500;    // 500KB
    
    logResult('Total bundle size', totalSize < MAX_TOTAL_SIZE, 
      `${totalSize.toFixed(2)}KB (max: ${MAX_TOTAL_SIZE}KB)`);
    
    logResult('JavaScript bundle size', jsSize < MAX_JS_SIZE, 
      `${jsSize.toFixed(2)}KB (max: ${MAX_JS_SIZE}KB)`);
    
    logResult('CSS bundle size', cssSize < MAX_CSS_SIZE, 
      `${cssSize.toFixed(2)}KB (max: ${MAX_CSS_SIZE}KB)`);
    
    if (totalSize > MAX_TOTAL_SIZE * 0.8) {
      logWarning('Bundle size', 'Approaching size limit, consider code splitting');
    }
    
  } catch (error) {
    logResult('Bundle size validation', false, error.message);
  }
};

const validateCodeQuality = () => {
  log('\n🔍 Validating Code Quality...', 'cyan');
  
  try {
    // Check for TypeScript/ESLint configuration
    const eslintConfigExists = fs.existsSync('.eslintrc.js') || 
                              fs.existsSync('.eslintrc.json') || 
                              fs.existsSync('eslint.config.js');
    logResult('ESLint configuration', eslintConfigExists);
    
    // Check for important source files
    const srcExists = fs.existsSync('src');
    logResult('Source directory exists', srcExists);
    
    if (srcExists) {
      const appJsExists = fs.existsSync('src/App.jsx') || fs.existsSync('src/App.tsx');
      logResult('Main App component exists', appJsExists);
      
      const indexExists = fs.existsSync('src/main.jsx') || fs.existsSync('src/index.jsx');
      logResult('Entry point exists', indexExists);
      
      // Check for essential directories
      const componentsExists = fs.existsSync('src/components');
      logResult('Components directory exists', componentsExists);
      
      const servicesExists = fs.existsSync('src/services');
      logResult('Services directory exists', servicesExists);
    }
    
  } catch (error) {
    logResult('Code quality validation', false, error.message);
  }
};

const validateConfiguration = () => {
  log('\n⚙️  Validating Configuration...', 'cyan');
  
  try {
    // Check Vite configuration
    const viteConfigExists = fs.existsSync('vite.config.js') || fs.existsSync('vite.config.ts');
    logResult('Vite configuration exists', viteConfigExists);
    
    if (viteConfigExists) {
      const viteConfig = fs.readFileSync(
        fs.existsSync('vite.config.js') ? 'vite.config.js' : 'vite.config.ts', 
        'utf8'
      );
      
      const hasReactPlugin = viteConfig.includes('@vitejs/plugin-react');
      logResult('React plugin configured', hasReactPlugin);
      
      const hasProxyConfig = viteConfig.includes('proxy');
      logResult('API proxy configured', hasProxyConfig);
    }
    
    // Check for environment files
    const envExampleExists = fs.existsSync('.env.example');
    logResult('Environment example exists', envExampleExists);
    
    // Check package.json scripts
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const scripts = packageJson.scripts || {};
    
    logResult('Build script defined', !!scripts.build);
    logResult('Dev script defined', !!scripts.dev);
    logResult('Preview script defined', !!scripts.preview);
    
  } catch (error) {
    logResult('Configuration validation', false, error.message);
  }
};

const validateDependencies = () => {
  log('\n📚 Validating Dependencies...', 'cyan');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    // Essential dependencies
    const essentialDeps = [
      'react',
      'react-dom',
      '@mui/material',
      '@mui/icons-material',
      'react-router-dom',
      'axios',
      'framer-motion'
    ];
    
    essentialDeps.forEach(dep => {
      logResult(`Dependency: ${dep}`, !!dependencies[dep], dependencies[dep] || 'missing');
    });
    
    // Check for security vulnerabilities (if npm audit is available)
    try {
      execSync('npm audit --audit-level=high', { stdio: 'pipe' });
      logResult('Security audit', true, 'No high-severity vulnerabilities');
    } catch (auditError) {
      logWarning('Security audit', 'High-severity vulnerabilities found, run npm audit for details');
    }
    
  } catch (error) {
    logResult('Dependencies validation', false, error.message);
  }
};

const validatePerformance = () => {
  log('\n⚡ Validating Performance Features...', 'cyan');
  
  try {
    // Check for performance optimization files
    const srcFiles = getAllFiles('src', ['.jsx', '.tsx', '.js', '.ts']);
    
    let hasLazyLoading = false;
    let hasSuspense = false;
    let hasCodeSplitting = false;
    
    srcFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      if (content.includes('React.lazy') || content.includes('lazy(')) {
        hasLazyLoading = true;
      }
      
      if (content.includes('Suspense')) {
        hasSuspense = true;
      }
      
      if (content.includes('import(')) {
        hasCodeSplitting = true;
      }
    });
    
    logResult('Lazy loading implemented', hasLazyLoading);
    logResult('Suspense boundaries implemented', hasSuspense);
    logResult('Code splitting implemented', hasCodeSplitting);
    
    // Check Vite config for performance optimizations
    if (fs.existsSync('vite.config.js')) {
      const viteConfig = fs.readFileSync('vite.config.js', 'utf8');
      const hasManualChunks = viteConfig.includes('manualChunks');
      logResult('Manual chunk configuration', hasManualChunks);
    }
    
  } catch (error) {
    logResult('Performance validation', false, error.message);
  }
};

// Helper function to get all files with specific extensions
const getAllFiles = (dir, extensions) => {
  const files = [];
  
  const traverse = (currentDir) => {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  };
  
  if (fs.existsSync(dir)) {
    traverse(dir);
  }
  
  return files;
};

// Main validation function
const runValidation = () => {
  log('🚀 VidyaMitra Platform - Production Validation', 'bright');
  log('================================================', 'bright');
  
  validateEnvironment();
  validateCodeQuality();
  validateConfiguration();
  validateDependencies();
  validateBuildProcess();
  validateBundleSize();
  validatePerformance();
  
  // Summary
  log('\n📊 Validation Summary', 'cyan');
  log('===================', 'cyan');
  log(`✅ Passed: ${results.passed}`, 'green');
  log(`❌ Failed: ${results.failed}`, 'red');
  log(`⚠️  Warnings: ${results.warnings}`, 'yellow');
  
  const totalTests = results.passed + results.failed;
  const successRate = totalTests > 0 ? (results.passed / totalTests * 100).toFixed(1) : 0;
  
  log(`\n📈 Success Rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
  
  if (results.failed === 0) {
    log('\n🎉 All validations passed! Platform is production-ready.', 'green');
    process.exit(0);
  } else {
    log('\n❌ Some validations failed. Please fix the issues before deployment.', 'red');
    process.exit(1);
  }
};

// Run validation automatically
runValidation();

export {
  runValidation,
  validateEnvironment,
  validateBuildProcess,
  validateBundleSize,
  validateCodeQuality,
  validateConfiguration,
  validateDependencies,
  validatePerformance
};
