/**
 * VidyaMitra Platform - Comprehensive Indian Educational Data
 *
 * Authentic Indian student profiles with realistic names, academic performance,
 * attendance records, behavioral data, SWOT analysis, and cultural context
 *
 * Features:
 * - Multiple schools across Hyderabad/Telangana region
 * - Board-specific curricula (CBSE/ICSE/State/IB)
 * - Culturally relevant SWOT analysis
 * - Indian academic calendar alignment
 * - Regional language support
 */

// Schools in Hyderabad/Telangana region
export const schools = [
  {
    id: 'SCH001',
    name: 'Vidya Vikas High School',
    location: 'Banjara Hills, Hyderabad',
    board: 'cbse',
    established: 1985,
    principal: 'Dr. <PERSON><PERSON>',
    contact: '+91 40 2345 6789',
    email: '<EMAIL>',
    website: 'www.vidyavikas.edu.in',
    totalStudents: 1200,
    totalTeachers: 85,
    facilities: ['Smart Classrooms', 'Science Labs', 'Computer Lab', 'Library', 'Sports Complex', 'Auditorium']
  },
  {
    id: 'SCH002',
    name: 'Telangana Model School',
    location: 'Jubilee Hills, Hyderabad',
    board: 'state',
    established: 1992,
    principal: 'Mrs. <PERSON>',
    contact: '+91 40 2345 6790',
    email: '<EMAIL>',
    website: 'www.telanganamodel.edu.in',
    totalStudents: 800,
    totalTeachers: 60,
    facilities: ['Digital Library', 'Language Lab', 'Art Studio', 'Music Room', 'Playground', 'Cafeteria']
  },
  {
    id: 'SCH003',
    name: 'International Cambridge School',
    location: 'Gachibowli, Hyderabad',
    board: 'icse',
    established: 2005,
    principal: 'Mr. Suresh Reddy',
    contact: '+91 40 2345 6791',
    email: '<EMAIL>',
    website: 'www.cambridgeschool.edu.in',
    totalStudents: 950,
    totalTeachers: 72,
    facilities: ['STEM Lab', 'Robotics Lab', 'Swimming Pool', 'Tennis Court', 'Drama Theatre', 'Meditation Hall']
  }
];

// Academic subjects for different boards with Indian context
const subjects = {
  cbse: [
    'Mathematics', 'Science (Physics/Chemistry/Biology)', 'English', 'Hindi',
    'Social Studies (History/Geography/Civics)', 'Computer Science', 'Sanskrit', 'Physical Education'
  ],
  icse: [
    'Mathematics', 'Physics', 'Chemistry', 'Biology', 'English Language', 'English Literature',
    'Hindi', 'History & Civics', 'Geography', 'Computer Applications', 'Physical Education', 'Art'
  ],
  state: [
    'Mathematics', 'Physical Science', 'Biological Science', 'English', 'Telugu',
    'Social Studies', 'Environmental Science', 'Work Education', 'Physical Education'
  ],
  ib: [
    'Mathematics', 'Sciences', 'English A', 'Hindi B', 'Individuals & Societies',
    'Arts', 'Theory of Knowledge', 'Extended Essay', 'Creativity, Activity, Service'
  ]
};

// Teacher profiles for different schools
export const teachers = [
  {
    id: 'TCH001',
    name: 'Mrs. Priya Sharma',
    subject: 'Mathematics',
    experience: 12,
    qualification: 'M.Sc Mathematics, B.Ed',
    schoolId: 'SCH001',
    email: '<EMAIL>',
    phone: '+91 9876543220',
    classes: ['9th A', '10th B', '11th A'],
    specialization: 'Advanced Mathematics, Olympiad Training'
  },
  {
    id: 'TCH002',
    name: 'Mr. Rajesh Kumar',
    subject: 'Science',
    experience: 15,
    qualification: 'M.Sc Physics, B.Ed',
    schoolId: 'SCH001',
    email: '<EMAIL>',
    phone: '+91 9876543221',
    classes: ['10th A', '10th C', '12th A'],
    specialization: 'Physics, Science Projects'
  },
  {
    id: 'TCH003',
    name: 'Ms. Lakshmi Devi',
    subject: 'English',
    experience: 8,
    qualification: 'M.A English Literature, B.Ed',
    schoolId: 'SCH002',
    email: '<EMAIL>',
    phone: '+91 9876543222',
    classes: ['9th B', '11th B', '12th B'],
    specialization: 'Literature, Creative Writing'
  }
];

// Parent profiles with Indian context
export const parents = [
  {
    id: 'PAR001',
    name: 'Mr. Venkat Kumar',
    relation: 'Father',
    occupation: 'Software Engineer',
    education: 'B.Tech Computer Science',
    phone: '+91 **********',
    email: '<EMAIL>',
    address: 'Plot 123, Jubilee Hills, Hyderabad',
    studentIds: ['STU001']
  },
  {
    id: 'PAR002',
    name: 'Mrs. Sunitha Reddy',
    relation: 'Mother',
    occupation: 'Teacher',
    education: 'M.A Education',
    phone: '+91 **********',
    email: '<EMAIL>',
    address: 'Flat 45, Banjara Hills, Hyderabad',
    studentIds: ['STU003']
  }
];

// Generate realistic performance data with Indian academic context
const generatePerformanceData = (board, studentProfile) => {
  const subjectList = subjects[board] || subjects.cbse;
  const basePerformance = studentProfile.academicLevel;

  return subjectList.map(subject => ({
    subject,
    currentScore: Math.max(35, Math.min(100, basePerformance + (Math.random() - 0.5) * 20)),
    previousScore: Math.max(30, Math.min(95, basePerformance + (Math.random() - 0.5) * 25)),
    trend: Math.random() > 0.6 ? 'improving' : Math.random() > 0.3 ? 'stable' : 'declining',
    assignments: Math.floor(Math.random() * 10) + 15,
    assignmentsCompleted: Math.floor(Math.random() * 5) + 12,
    grade: getIndianGrade(Math.max(35, Math.min(100, basePerformance + (Math.random() - 0.5) * 20))),
    remarks: generateSubjectRemarks(subject, basePerformance),
    teacherId: getRandomTeacherId(),
    lastUpdated: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)
  }));
};

// Helper function to get Indian grading system
const getIndianGrade = (score) => {
  if (score >= 91) return 'A1';
  if (score >= 81) return 'A2';
  if (score >= 71) return 'B1';
  if (score >= 61) return 'B2';
  if (score >= 51) return 'C1';
  if (score >= 41) return 'C2';
  if (score >= 33) return 'D';
  return 'E';
};

// Generate subject-specific remarks
const generateSubjectRemarks = (subject, performance) => {
  const remarks = {
    high: [
      'Excellent understanding of concepts',
      'Shows exceptional analytical skills',
      'Consistently performs well in assessments',
      'Demonstrates leadership in group activities'
    ],
    medium: [
      'Good grasp of fundamental concepts',
      'Needs to focus on problem-solving techniques',
      'Shows improvement with consistent effort',
      'Participates actively in class discussions'
    ],
    low: [
      'Requires additional support in basic concepts',
      'Needs more practice in problem-solving',
      'Should attend remedial classes',
      'Requires parental guidance for homework'
    ]
  };

  const category = performance >= 80 ? 'high' : performance >= 60 ? 'medium' : 'low';
  return remarks[category][Math.floor(Math.random() * remarks[category].length)];
};

// Get random teacher ID
const getRandomTeacherId = () => {
  const teacherIds = ['TCH001', 'TCH002', 'TCH003'];
  return teacherIds[Math.floor(Math.random() * teacherIds.length)];
};

// Generate attendance data aligned with Indian academic calendar
const generateAttendanceData = () => {
  // Indian academic year: June to March
  const academicMonths = [
    { month: 'Jun', name: 'June', workingDays: 22, holidays: ['Summer Break'] },
    { month: 'Jul', name: 'July', workingDays: 24, holidays: [] },
    { month: 'Aug', name: 'August', workingDays: 23, holidays: ['Independence Day'] },
    { month: 'Sep', name: 'September', workingDays: 24, holidays: ['Ganesh Chaturthi'] },
    { month: 'Oct', name: 'October', workingDays: 22, holidays: ['Dussehra', 'Diwali'] },
    { month: 'Nov', name: 'November', workingDays: 24, holidays: [] },
    { month: 'Dec', name: 'December', workingDays: 20, holidays: ['Winter Break'] },
    { month: 'Jan', name: 'January', workingDays: 24, holidays: ['Republic Day'] },
    { month: 'Feb', name: 'February', workingDays: 22, holidays: [] },
    { month: 'Mar', name: 'March', workingDays: 20, holidays: ['Holi', 'Annual Exams'] }
  ];

  return academicMonths.map(monthData => {
    const present = Math.floor(Math.random() * 4) + (monthData.workingDays - 4); // High attendance
    const percentage = Math.round((present / monthData.workingDays) * 100);

    return {
      month: monthData.month,
      name: monthData.name,
      present,
      total: monthData.workingDays,
      percentage,
      holidays: monthData.holidays,
      lateArrivals: Math.floor(Math.random() * 3),
      earlyDepartures: Math.floor(Math.random() * 2),
      medicalLeaves: Math.floor(Math.random() * 2),
      casualLeaves: Math.floor(Math.random() * 2)
    };
  });
};

// Generate behavioral incidents with Indian cultural context
const generateBehavioralData = () => {
  const positiveIncidents = [
    'Excellent participation in class discussions',
    'Helped classmates with studies',
    'Showed leadership in group projects',
    'Demonstrated respect for teachers and elders',
    'Participated actively in cultural programs',
    'Maintained discipline during assembly',
    'Showed creativity in art and craft',
    'Exhibited good sportsmanship',
    'Helped in organizing school events',
    'Showed empathy towards junior students'
  ];

  const improvementAreas = [
    'Occasional late arrival to class',
    'Needs to complete homework on time',
    'Should participate more in discussions',
    'Needs to improve handwriting',
    'Should maintain notebook neatly',
    'Needs to focus during lessons',
    'Should follow uniform guidelines',
    'Needs to improve time management',
    'Should be more attentive in class',
    'Needs to organize study materials better'
  ];

  const culturalValues = [
    'Participated in Saraswati Puja celebration',
    'Showed respect during national anthem',
    'Helped in Diwali decoration',
    'Participated in yoga sessions',
    'Showed interest in Indian classical music',
    'Participated in tree plantation drive',
    'Helped in organizing Teachers Day',
    'Showed cultural awareness during festivals',
    'Participated in community service',
    'Demonstrated traditional values'
  ];

  const count = Math.floor(Math.random() * 8) + 4; // 4-11 incidents
  const allIncidents = [...positiveIncidents, ...improvementAreas, ...culturalValues];

  return Array.from({ length: count }, () => {
    const incident = allIncidents[Math.floor(Math.random() * allIncidents.length)];
    const isPositive = positiveIncidents.includes(incident) || culturalValues.includes(incident);

    return {
      date: new Date(2024, Math.floor(Math.random() * 10) + 2, Math.floor(Math.random() * 28) + 1), // Academic year
      type: incident,
      category: positiveIncidents.includes(incident) ? 'academic' :
                culturalValues.includes(incident) ? 'cultural' : 'improvement',
      severity: isPositive ? 'positive' : 'improvement_needed',
      impact: isPositive ? 'high' : 'medium',
      description: generateDetailedDescription(incident),
      reportedBy: getRandomTeacherId(),
      actionTaken: isPositive ? 'Appreciation given' : 'Guidance provided',
      parentNotified: !isPositive
    };
  });
};

// Generate detailed descriptions for behavioral incidents
const generateDetailedDescription = (incident) => {
  const descriptions = {
    'Excellent participation in class discussions': 'Student actively engages in classroom discussions, asks thoughtful questions, and provides insightful answers.',
    'Helped classmates with studies': 'Demonstrates peer support by helping struggling classmates understand difficult concepts.',
    'Occasional late arrival to class': 'Student has been arriving 5-10 minutes late to morning classes. Needs to improve punctuality.',
    'Participated in Saraswati Puja celebration': 'Actively participated in school\'s Saraswati Puja celebration, showing respect for Indian traditions.',
    'Needs to complete homework on time': 'Homework submission has been irregular. Requires better time management and study habits.'
  };

  return descriptions[incident] || `Student ${incident.toLowerCase()}. Detailed observation recorded by class teacher.`;
};

// Generate extracurricular activities with Indian cultural context
const generateExtracurriculars = () => {
  const sportsActivities = [
    'Cricket', 'Football', 'Basketball', 'Badminton', 'Table Tennis', 'Volleyball',
    'Athletics', 'Swimming', 'Kabaddi', 'Kho-Kho', 'Chess', 'Carrom'
  ];

  const culturalActivities = [
    'Classical Dance (Bharatanatyam)', 'Classical Dance (Kuchipudi)', 'Folk Dance',
    'Carnatic Music', 'Hindustani Music', 'Tabla', 'Veena', 'Flute',
    'Drama and Theatre', 'Storytelling', 'Poetry Recitation', 'Rangoli'
  ];

  const academicClubs = [
    'Science Club', 'Mathematics Olympiad', 'Debate Club', 'Quiz Club',
    'Literary Society', 'Environmental Club', 'Robotics Club', 'Computer Club',
    'Photography Club', 'Art and Craft Club', 'Nature Club', 'Heritage Club'
  ];

  const socialActivities = [
    'NSS (National Service Scheme)', 'NCC (National Cadet Corps)', 'Scouts and Guides',
    'Community Service', 'Tree Plantation', 'Cleanliness Drive', 'Blood Donation Camp',
    'Elderly Care Program', 'Teaching Underprivileged', 'Disaster Relief'
  ];

  const allActivities = [...sportsActivities, ...culturalActivities, ...academicClubs, ...socialActivities];
  const count = Math.floor(Math.random() * 5) + 2; // 2-6 activities

  return Array.from({ length: count }, () => {
    const activity = allActivities[Math.floor(Math.random() * allActivities.length)];
    const category = sportsActivities.includes(activity) ? 'sports' :
                    culturalActivities.includes(activity) ? 'cultural' :
                    academicClubs.includes(activity) ? 'academic' : 'social';

    return {
      activity,
      category,
      level: Math.random() > 0.6 ? 'advanced' : Math.random() > 0.3 ? 'intermediate' : 'beginner',
      duration: `${Math.floor(Math.random() * 3) + 1} years`,
      frequency: getActivityFrequency(category),
      achievements: generateAchievements(activity, category),
      skills: generateSkillsLearned(activity, category),
      mentor: getRandomTeacherId(),
      lastParticipation: new Date(2024, Math.floor(Math.random() * 10) + 2, Math.floor(Math.random() * 28) + 1)
    };
  });
};

// Generate activity frequency based on category
const getActivityFrequency = (category) => {
  const frequencies = {
    sports: ['Daily practice', 'Alternate days', '3 times a week'],
    cultural: ['Weekly sessions', 'Twice a week', 'During festivals'],
    academic: ['Weekly meetings', 'Monthly competitions', 'Project-based'],
    social: ['Monthly activities', 'Seasonal drives', 'Event-based']
  };

  const options = frequencies[category] || frequencies.academic;
  return options[Math.floor(Math.random() * options.length)];
};

// Generate achievements based on activity
const generateAchievements = (activity, category) => {
  const achievements = [];
  const random = Math.random();

  if (random > 0.8) {
    achievements.push('State Level Winner');
  } else if (random > 0.6) {
    achievements.push('District Level Winner');
  } else if (random > 0.3) {
    achievements.push('Inter-School Competition Winner');
  } else if (random > 0.1) {
    achievements.push('School Level Winner');
  }

  // Add participation certificates
  if (Math.random() > 0.5) {
    achievements.push('Participation Certificate');
  }

  // Add category-specific achievements
  if (category === 'cultural' && Math.random() > 0.7) {
    achievements.push('Cultural Ambassador');
  } else if (category === 'academic' && Math.random() > 0.7) {
    achievements.push('Academic Excellence Award');
  } else if (category === 'social' && Math.random() > 0.7) {
    achievements.push('Community Service Award');
  }

  return achievements;
};

// Generate skills learned from activities
const generateSkillsLearned = (activity, category) => {
  const skillSets = {
    sports: ['Teamwork', 'Leadership', 'Physical Fitness', 'Discipline', 'Time Management'],
    cultural: ['Creativity', 'Cultural Awareness', 'Performance Skills', 'Confidence', 'Artistic Expression'],
    academic: ['Critical Thinking', 'Research Skills', 'Problem Solving', 'Communication', 'Innovation'],
    social: ['Empathy', 'Social Responsibility', 'Community Engagement', 'Organizational Skills', 'Leadership']
  };

  const skills = skillSets[category] || skillSets.academic;
  const count = Math.floor(Math.random() * 3) + 2; // 2-4 skills
  return skills.sort(() => 0.5 - Math.random()).slice(0, count);
};

// Generate culturally relevant SWOT analysis for Indian students
const generateSWOTAnalysis = (studentProfile) => {
  const strengthsPool = [
    // Academic Strengths
    'Strong analytical and logical thinking',
    'Excellent mathematical problem-solving skills',
    'Good memory and retention capacity',
    'Strong foundation in science concepts',
    'Proficiency in multiple languages (Hindi, English, Regional)',
    'Good reading comprehension skills',
    'Ability to work with complex calculations',

    // Cultural & Social Strengths
    'Respect for teachers and elders (Guru-Shishya tradition)',
    'Strong family values and support system',
    'Cultural awareness and traditional knowledge',
    'Ability to work in diverse groups',
    'Adaptability to different learning environments',
    'Strong moral and ethical foundation',

    // Personal Strengths
    'Perseverance and determination (Dhairya)',
    'Curiosity and eagerness to learn',
    'Creative thinking and innovation',
    'Leadership qualities in group activities',
    'Good time management during festivals and studies',
    'Artistic abilities (music, dance, art)',
    'Sports and physical fitness',
    'Technical and computer skills'
  ];

  const weaknessesPool = [
    // Academic Weaknesses
    'Difficulty with abstract mathematical concepts',
    'Needs improvement in English communication',
    'Struggles with practical application of theories',
    'Requires more practice in problem-solving',
    'Needs to develop critical thinking skills',
    'Difficulty in expressing ideas clearly',

    // Study Habits
    'Inconsistent study schedule',
    'Over-reliance on rote learning',
    'Procrastination in completing assignments',
    'Difficulty in organizing study materials',
    'Needs better note-taking skills',
    'Limited use of technology for learning',

    // Social & Personal
    'Hesitation in public speaking',
    'Needs to build confidence in presentations',
    'Difficulty in peer interaction',
    'Stress during examination periods',
    'Needs to balance academics with extracurriculars',
    'Limited exposure to practical applications'
  ];

  const opportunitiesPool = [
    // Educational Opportunities
    'Advanced courses in STEM subjects',
    'Participation in National Science Olympiad',
    'IIT-JEE and NEET preparation programs',
    'International exchange programs',
    'Online learning platforms (BYJU\'S, Unacademy)',
    'Skill development through government schemes',
    'Scholarship opportunities for higher education',

    // Cultural & Social Opportunities
    'Leadership roles in cultural programs',
    'Participation in debate and quiz competitions',
    'Community service through NSS/NCC',
    'Mentorship from senior students',
    'Internship opportunities in local industries',
    'Participation in traditional arts and crafts',

    // Career Opportunities
    'Growing IT and technology sector',
    'Opportunities in renewable energy sector',
    'Government job opportunities through competitive exams',
    'Entrepreneurship support from government initiatives',
    'Research opportunities in Indian institutions',
    'Global career opportunities with Indian education background'
  ];

  const threatsPool = [
    // Academic Pressures
    'Intense competition for engineering and medical seats',
    'Pressure to excel in board examinations',
    'Limited seats in premier institutions (IITs, IIMs)',
    'Coaching culture creating additional stress',
    'Comparison with high-performing peers',

    // Social & Environmental
    'Excessive screen time and digital distractions',
    'Social media influence on study habits',
    'Family expectations and career pressure',
    'Economic constraints affecting educational choices',
    'Limited career guidance and counseling',

    // Systemic Challenges
    'Rapid changes in education system and curriculum',
    'Technology gap in rural vs urban education',
    'Language barriers in competitive examinations',
    'Health issues due to academic stress',
    'Uncertainty in career paths due to changing job market'
  ];

  // Select SWOT items based on student profile
  const selectedStrengths = selectRelevantItems(strengthsPool, studentProfile, 4);
  const selectedWeaknesses = selectRelevantItems(weaknessesPool, studentProfile, 3);
  const selectedOpportunities = selectRelevantItems(opportunitiesPool, studentProfile, 4);
  const selectedThreats = selectRelevantItems(threatsPool, studentProfile, 3);

  return {
    strengths: selectedStrengths.map(item => ({
      item,
      description: generateSWOTDescription(item, 'strength'),
      impact: 'high',
      evidence: generateEvidence(item, studentProfile)
    })),
    weaknesses: selectedWeaknesses.map(item => ({
      item,
      description: generateSWOTDescription(item, 'weakness'),
      impact: 'medium',
      improvementPlan: generateImprovementPlan(item)
    })),
    opportunities: selectedOpportunities.map(item => ({
      item,
      description: generateSWOTDescription(item, 'opportunity'),
      potential: 'high',
      actionSteps: generateActionSteps(item)
    })),
    threats: selectedThreats.map(item => ({
      item,
      description: generateSWOTDescription(item, 'threat'),
      severity: 'medium',
      mitigationStrategy: generateMitigationStrategy(item)
    })),
    lastUpdated: new Date(),
    confidence: Math.floor(Math.random() * 15) + 80, // 80-95% confidence
    culturalContext: 'Indian Educational System',
    boardSpecific: getBoardSpecificInsights(studentProfile.board),
    recommendations: generateRecommendations(studentProfile)
  };
};

// Select relevant SWOT items based on student profile
const selectRelevantItems = (pool, profile, count) => {
  // Add logic to select more relevant items based on academic level, board, etc.
  const shuffled = pool.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

// Generate detailed descriptions for SWOT items
const generateSWOTDescription = (item, type) => {
  const descriptions = {
    'Strong analytical and logical thinking': 'Student demonstrates excellent ability to break down complex problems and find logical solutions.',
    'Respect for teachers and elders (Guru-Shishya tradition)': 'Shows deep respect for the traditional teacher-student relationship, facilitating better learning.',
    'Intense competition for engineering and medical seats': 'High competition in entrance exams may create stress and limit options.',
    'Advanced courses in STEM subjects': 'Opportunity to excel in Science, Technology, Engineering, and Mathematics fields.'
  };

  return descriptions[item] || `${type.charAt(0).toUpperCase() + type.slice(1)} identified through comprehensive analysis.`;
};

// Generate evidence for strengths
const generateEvidence = (strength, profile) => {
  return `Observed through academic performance (${profile.academicLevel}%) and teacher feedback.`;
};

// Generate improvement plans for weaknesses
const generateImprovementPlan = (weakness) => {
  const plans = {
    'Difficulty with abstract mathematical concepts': 'Practice with visual aids and real-world applications',
    'Hesitation in public speaking': 'Join debate club and practice presentations',
    'Inconsistent study schedule': 'Create a structured timetable with regular breaks'
  };

  return plans[weakness] || 'Focused practice and guidance from teachers and parents.';
};

// Generate action steps for opportunities
const generateActionSteps = (opportunity) => {
  const steps = {
    'Advanced courses in STEM subjects': '1. Consult with teachers 2. Enroll in advanced classes 3. Join study groups',
    'Participation in National Science Olympiad': '1. Register for olympiad 2. Prepare with past papers 3. Join coaching if needed'
  };

  return steps[opportunity] || '1. Research the opportunity 2. Seek guidance 3. Take action';
};

// Generate mitigation strategies for threats
const generateMitigationStrategy = (threat) => {
  const strategies = {
    'Intense competition for engineering and medical seats': 'Focus on consistent preparation, explore alternative career paths',
    'Excessive screen time and digital distractions': 'Set screen time limits, use apps for productive learning only'
  };

  return strategies[threat] || 'Develop coping strategies with support from teachers and family.';
};

// Get board-specific insights
const getBoardSpecificInsights = (board) => {
  const insights = {
    cbse: 'CBSE curriculum emphasizes conceptual understanding and application-based learning.',
    icse: 'ICSE focuses on comprehensive education with emphasis on English and analytical skills.',
    state: 'State board provides strong foundation in regional language and local context.',
    ib: 'IB program develops international mindedness and critical thinking skills.'
  };

  return insights[board] || insights.cbse;
};

// Generate personalized recommendations
const generateRecommendations = (profile) => {
  const recommendations = [
    'Focus on strengthening weak subjects through additional practice',
    'Participate in extracurricular activities to develop well-rounded personality',
    'Seek mentorship from senior students and teachers',
    'Maintain balance between academics and personal interests'
  ];

  if (profile.academicLevel >= 90) {
    recommendations.push('Consider advanced courses and competitive exam preparation');
  } else if (profile.academicLevel < 70) {
    recommendations.push('Focus on building strong fundamentals with additional support');
  }

  return recommendations;
};

// Comprehensive student profiles with enhanced Indian context
export const studentProfiles = [
  {
    id: 'STU001',
    name: 'Sanju Kumar',
    grade: '10th',
    section: 'A',
    board: 'cbse',
    rollNumber: 'CBSE2024001',
    academicLevel: 85,
    schoolId: 'SCH001',
    region: 'Telangana',
    parentContact: '+91 **********',
    email: '<EMAIL>',
    dateOfBirth: '2009-03-15',
    address: 'Plot 123, Jubilee Hills, Hyderabad, Telangana - 500033',
    fatherName: 'Mr. Venkat Kumar',
    motherName: 'Mrs. Sunitha Kumar',
    guardian: 'Father',
    bloodGroup: 'B+',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Telugu',
    languages: ['Telugu', 'Hindi', 'English'],
    previousSchool: 'Little Flower High School',
    admissionDate: '2022-06-15',
    studentPhoto: '/uploads/student-photos/sanju-kumar.jpg',
    medicalInfo: {
      allergies: 'None',
      medications: 'None',
      emergencyContact: '+91 **********',
      doctorName: 'Dr. Ramesh Reddy',
      doctorContact: '+91 **********'
    }
  },
  {
    id: 'STU002',
    name: 'Niraimathi Selvam',
    grade: '9th',
    section: 'B',
    board: 'state',
    rollNumber: 'TN2024002',
    academicLevel: 78,
    schoolId: 'SCH002',
    region: 'Tamil Nadu',
    parentContact: '+91 **********',
    email: '<EMAIL>',
    dateOfBirth: '2010-07-22',
    address: 'No. 45, Anna Nagar, Chennai, Tamil Nadu - 600040',
    fatherName: 'Mr. Selvam Murugan',
    motherName: 'Mrs. Kamala Selvam',
    guardian: 'Mother',
    bloodGroup: 'O+',
    religion: 'Hindu',
    caste: 'OBC',
    nationality: 'Indian',
    motherTongue: 'Tamil',
    languages: ['Tamil', 'English', 'Hindi'],
    previousSchool: 'Government Higher Secondary School',
    admissionDate: '2023-06-10',
    studentPhoto: '/uploads/student-photos/niraimathi-selvam.jpg',
    medicalInfo: {
      allergies: 'Dust allergy',
      medications: 'Antihistamines as needed',
      emergencyContact: '+91 **********',
      doctorName: 'Dr. Priya Krishnan',
      doctorContact: '+91 **********'
    }
  },
  {
    id: 'STU003',
    name: 'Mahesh Reddy',
    grade: '11th',
    section: 'A',
    board: 'cbse',
    rollNumber: 'CBSE2024003',
    academicLevel: 92,
    schoolId: 'SCH001',
    region: 'Andhra Pradesh',
    parentContact: '+91 **********',
    email: '<EMAIL>',
    dateOfBirth: '2008-11-08',
    address: 'Flat 67, MG Road, Vijayawada, Andhra Pradesh - 520010',
    fatherName: 'Mr. Suresh Reddy',
    motherName: 'Mrs. Padmavathi Reddy',
    guardian: 'Father',
    bloodGroup: 'A+',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Telugu',
    languages: ['Telugu', 'Hindi', 'English', 'Sanskrit'],
    previousSchool: 'Narayana High School',
    admissionDate: '2021-06-20',
    studentPhoto: '/uploads/student-photos/mahesh-reddy.jpg',
    medicalInfo: {
      allergies: 'None',
      medications: 'None',
      emergencyContact: '+91 **********',
      doctorName: 'Dr. Srinivas Rao',
      doctorContact: '+91 **********'
    }
  },
  {
    id: 'STU004',
    name: 'Ravi Teja Sharma',
    grade: '10th',
    section: 'C',
    board: 'icse',
    rollNumber: 'ICSE2024004',
    academicLevel: 88,
    schoolId: 'SCH003',
    region: 'Karnataka',
    parentContact: '+91 **********',
    email: '<EMAIL>',
    dateOfBirth: '2009-01-30',
    address: 'House 89, Koramangala, Bangalore, Karnataka - 560034',
    fatherName: 'Mr. Rajesh Sharma',
    motherName: 'Mrs. Meera Sharma',
    guardian: 'Father',
    bloodGroup: 'AB+',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Hindi',
    languages: ['Hindi', 'English', 'Kannada', 'Telugu'],
    previousSchool: 'Delhi Public School',
    admissionDate: '2022-06-25',
    studentPhoto: '/uploads/student-photos/ravi-teja-sharma.jpg',
    medicalInfo: {
      allergies: 'Peanut allergy',
      medications: 'EpiPen (emergency)',
      emergencyContact: '+91 **********',
      doctorName: 'Dr. Anil Kumar',
      doctorContact: '+91 **********'
    }
  },
  {
    id: 'STU005',
    name: 'Ankitha Patel',
    grade: '12th',
    section: 'A',
    board: 'cbse',
    rollNumber: 'CBSE2024005',
    academicLevel: 95,
    schoolId: 'SCH001',
    region: 'Gujarat',
    parentContact: '+91 **********',
    email: '<EMAIL>',
    dateOfBirth: '2007-05-12',
    address: 'Bungalow 12, Satellite, Ahmedabad, Gujarat - 380015',
    fatherName: 'Mr. Kiran Patel',
    motherName: 'Mrs. Nisha Patel',
    guardian: 'Father',
    bloodGroup: 'O-',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Gujarati',
    languages: ['Gujarati', 'Hindi', 'English'],
    previousSchool: 'Zydus School for Excellence',
    admissionDate: '2020-06-15',
    studentPhoto: '/uploads/student-photos/ankitha-patel.jpg',
    medicalInfo: {
      allergies: 'None',
      medications: 'Vitamin D supplements',
      emergencyContact: '+91 **********',
      doctorName: 'Dr. Bharat Shah',
      doctorContact: '+91 **********'
    }
  }
];

// Generate complete student data
export const generateCompleteStudentData = () => {
  return studentProfiles.map(profile => ({
    ...profile,
    performance: generatePerformanceData(profile.board, profile),
    attendance: generateAttendanceData(),
    behavioral: generateBehavioralData(),
    extracurricular: generateExtracurriculars(),
    swotAnalysis: generateSWOTAnalysis(profile),
    overallGrade: profile.academicLevel >= 90 ? 'A+' : 
                  profile.academicLevel >= 80 ? 'A' :
                  profile.academicLevel >= 70 ? 'B' :
                  profile.academicLevel >= 60 ? 'C' : 'D',
    rank: Math.floor(Math.random() * 50) + 1, // Rank out of 50 students
    lastUpdated: new Date(),
  }));
};

// Analytics data for dashboard
export const generateAnalyticsData = (students) => {
  const totalStudents = students.length;
  const averagePerformance = students.reduce((sum, student) => sum + student.academicLevel, 0) / totalStudents;
  const averageAttendance = students.reduce((sum, student) => {
    const totalPresent = student.attendance.reduce((p, a) => p + a.present, 0);
    const totalDays = student.attendance.reduce((p, a) => p + a.total, 0);
    return sum + (totalPresent / totalDays * 100);
  }, 0) / totalStudents;

  return {
    totalStudents,
    averagePerformance: Math.round(averagePerformance),
    averageAttendance: Math.round(averageAttendance),
    topPerformers: students.filter(s => s.academicLevel >= 90).length,
    needsAttention: students.filter(s => s.academicLevel < 70).length,
    boardDistribution: {
      cbse: students.filter(s => s.board === 'cbse').length,
      icse: students.filter(s => s.board === 'icse').length,
      state: students.filter(s => s.board === 'state').length,
      ib: students.filter(s => s.board === 'ib').length,
    },
    gradeDistribution: {
      '9th': students.filter(s => s.grade === '9th').length,
      '10th': students.filter(s => s.grade === '10th').length,
      '11th': students.filter(s => s.grade === '11th').length,
      '12th': students.filter(s => s.grade === '12th').length,
    },
    performanceTrends: generatePerformanceTrends(students),
    subjectAnalysis: generateSubjectAnalysis(students),
    attendancePatterns: generateAttendancePatterns(students),
    swotDistribution: generateSWOTDistribution(students),
  };
};

// Generate performance trends over time
export const generatePerformanceTrends = (students) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months.map(month => ({
    month,
    average: Math.floor(Math.random() * 15) + 75, // 75-90 range
    cbse: Math.floor(Math.random() * 15) + 78,
    icse: Math.floor(Math.random() * 15) + 80,
    state: Math.floor(Math.random() * 15) + 73,
  }));
};

// Generate subject-wise analysis
export const generateSubjectAnalysis = (students) => {
  const allSubjects = ['Mathematics', 'Science', 'English', 'Hindi', 'Social Studies', 'Computer Science'];
  return allSubjects.map(subject => ({
    subject,
    averageScore: Math.floor(Math.random() * 20) + 70,
    topScore: Math.floor(Math.random() * 10) + 90,
    lowestScore: Math.floor(Math.random() * 20) + 45,
    studentsAbove80: Math.floor(Math.random() * students.length * 0.6),
    studentsBelow60: Math.floor(Math.random() * students.length * 0.2),
  }));
};

// Generate attendance patterns
export const generateAttendancePatterns = (students) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months.map(month => ({
    month,
    averageAttendance: Math.floor(Math.random() * 15) + 80, // 80-95%
    totalStudents: students.length,
    presentStudents: Math.floor(students.length * (0.8 + Math.random() * 0.15)),
  }));
};

// Generate SWOT distribution
export const generateSWOTDistribution = (students) => {
  return {
    strengthsDistribution: {
      'Analytical Thinking': Math.floor(students.length * 0.3),
      'Communication Skills': Math.floor(students.length * 0.25),
      'Leadership': Math.floor(students.length * 0.2),
      'Creativity': Math.floor(students.length * 0.35),
      'Technical Skills': Math.floor(students.length * 0.4),
    },
    weaknessesDistribution: {
      'Time Management': Math.floor(students.length * 0.4),
      'Public Speaking': Math.floor(students.length * 0.3),
      'Organization': Math.floor(students.length * 0.25),
      'Stress Management': Math.floor(students.length * 0.35),
    },
    opportunitiesDistribution: {
      'Advanced Courses': Math.floor(students.length * 0.6),
      'Leadership Roles': Math.floor(students.length * 0.3),
      'Competitions': Math.floor(students.length * 0.5),
      'Skill Development': Math.floor(students.length * 0.7),
    },
    threatsDistribution: {
      'Academic Pressure': Math.floor(students.length * 0.5),
      'Peer Competition': Math.floor(students.length * 0.4),
      'Technology Distractions': Math.floor(students.length * 0.6),
      'Time Constraints': Math.floor(students.length * 0.45),
    },
  };
};

export default generateCompleteStudentData;
