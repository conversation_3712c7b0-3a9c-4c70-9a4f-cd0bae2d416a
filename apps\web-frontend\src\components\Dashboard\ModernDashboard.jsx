/**
 * VidyaMitra Platform - Modern Dashboard
 * 
 * Contemporary dashboard with glassmorphism, smooth animations, and modern UI patterns
 * Features dark/light theme toggle, advanced filtering, and real-time updates
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  Button,
  IconButton,
  Avatar,
  Stack,
  Chip,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  useTheme,
  alpha,
  Skeleton,
  Fab,
  Tooltip,
  Badge,
  AppBar,
  Toolbar,
} from '@mui/material';
import {
  School,
  Person,
  Assessment,
  TrendingUp,
  Notifications,
  Settings,
  Search,
  FilterList,
  Refresh,
  DarkMode,
  LightMode,
  Psychology,
  Group,
  CalendarToday,
  Bar<PERSON>hart,
  Pie<PERSON>hart,
  Timeline,
  Add,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import ModernMetricCard from './ModernMetricCard';
import generateCompleteStudentData, { generateAnalyticsData } from '../../data/studentData';
import EnhancedButton from '../Common/EnhancedButton';
import EnhancedCard from '../Common/EnhancedCard';

// Theme Toggle Component
const ThemeToggle = ({ darkMode, onToggle }) => {
  const theme = useTheme();
  
  return (
    <Tooltip title={darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
      <IconButton
        onClick={onToggle}
        sx={{
          background: alpha(theme.palette.background.paper, 0.8),
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
          '&:hover': {
            background: alpha(theme.palette.background.paper, 0.9),
            transform: 'scale(1.05)',
          },
          transition: 'all 0.3s ease',
        }}
      >
        <motion.div
          initial={false}
          animate={{ rotate: darkMode ? 180 : 0 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
        >
          {darkMode ? <LightMode /> : <DarkMode />}
        </motion.div>
      </IconButton>
    </Tooltip>
  );
};

// Modern Header Component
const ModernHeader = ({ darkMode, onThemeToggle, user }) => {
  const theme = useTheme();
  
  return (
    <AppBar
      position="sticky"
      elevation={0}
      sx={{
        background: alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(20px)',
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        color: theme.palette.text.primary,
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>
        {/* Logo and Title */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Avatar
              sx={{
                bgcolor: theme.palette.primary.main,
                width: 48,
                height: 48,
              }}
            >
              <School />
            </Avatar>
          </motion.div>
          <Box>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 800,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}
            >
              VidyaMitra
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Modern Educational Dashboard
            </Typography>
          </Box>
        </Box>

        {/* Actions */}
        <Stack direction="row" spacing={1} alignItems="center">
          <IconButton>
            <Search />
          </IconButton>
          <IconButton>
            <FilterList />
          </IconButton>
          <Badge badgeContent={3} color="error">
            <IconButton>
              <Notifications />
            </IconButton>
          </Badge>
          <ThemeToggle darkMode={darkMode} onToggle={onThemeToggle} />
          <Avatar
            sx={{
              width: 40,
              height: 40,
              cursor: 'pointer',
              border: `2px solid ${theme.palette.primary.main}`,
            }}
          >
            {user?.name?.charAt(0) || 'U'}
          </Avatar>
        </Stack>
      </Toolbar>
    </AppBar>
  );
};

// Quick Stats Component
const QuickStats = ({ loading, analyticsData }) => {
  const navigate = useNavigate();

  const statsData = analyticsData ? [
    {
      title: 'Total Students',
      value: analyticsData.totalStudents,
      subtitle: 'Active learners',
      icon: Person,
      trend: 'up',
      trendValue: 12,
      progress: 85,
      progressLabel: 'Enrollment',
      color: 'primary',
      onClick: () => navigate('/dashboard/students'),
    },
    {
      title: 'SWOT Analyses',
      value: analyticsData.totalStudents,
      subtitle: 'Generated this month',
      icon: Psychology,
      trend: 'up',
      trendValue: 8,
      progress: 72,
      progressLabel: 'Completion',
      color: 'secondary',
      gradient: true,
      onClick: () => navigate('/dashboard/swot'),
    },
    {
      title: 'Average Performance',
      value: `${analyticsData.averagePerformance}%`,
      subtitle: 'Class average',
      icon: TrendingUp,
      trend: 'up',
      trendValue: 5,
      progress: analyticsData.averagePerformance,
      progressLabel: 'Target: 85%',
      color: 'success',
      onClick: () => navigate('/dashboard/analytics'),
    },
    {
      title: 'Attendance Rate',
      value: `${analyticsData.averageAttendance}%`,
      subtitle: 'Overall attendance',
      icon: CalendarToday,
      trend: analyticsData.averageAttendance >= 90 ? 'up' : 'down',
      trendValue: 2,
      progress: analyticsData.averageAttendance,
      progressLabel: 'Target: 95%',
      color: 'info',
      onClick: () => navigate('/dashboard/reports'),
    },
  ] : [];

  return (
    <Grid container spacing={3}>
      {statsData.map((stat, index) => (
        <Grid item xs={12} sm={6} lg={3} key={stat.title}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.6 }}
          >
            <ModernMetricCard
              {...stat}
              loading={loading}
              onClick={() => console.log(`Clicked ${stat.title}`)}
              tooltip={`View detailed ${stat.title.toLowerCase()} analytics`}
            />
          </motion.div>
        </Grid>
      ))}
    </Grid>
  );
};

// Recent Activity Component
const RecentActivity = ({ loading, studentsData }) => {
  const navigate = useNavigate();

  // Generate activities from real student data
  const generateActivities = () => {
    if (!studentsData || studentsData.length === 0) return [];

    const activities = [];
    const now = new Date();

    // Recent SWOT analyses
    studentsData.slice(0, 3).forEach((student, index) => {
      activities.push({
        id: `swot_${student.id}`,
        type: 'swot_generated',
        title: 'SWOT Analysis Generated',
        description: `For student ${student.name} (${student.grade} ${student.section})`,
        time: `${(index + 1) * 5} minutes ago`,
        icon: Psychology,
        color: 'primary',
        onClick: () => navigate(`/dashboard/students/${student.id}/swot`),
      });
    });

    // Performance alerts
    const lowPerformers = studentsData.filter(s => s.academicLevel < 70);
    if (lowPerformers.length > 0) {
      activities.push({
        id: 'performance_alert',
        type: 'performance_alert',
        title: 'Performance Alert',
        description: `${lowPerformers.length} students need attention`,
        time: '15 minutes ago',
        icon: TrendingUp,
        color: 'warning',
        onClick: () => navigate('/dashboard/analytics'),
      });
    }

    // New enrollments
    activities.push({
      id: 'new_student',
      type: 'new_student',
      title: 'New Student Enrolled',
      description: `${studentsData[studentsData.length - 1]?.name} joined ${studentsData[studentsData.length - 1]?.grade} ${studentsData[studentsData.length - 1]?.section}`,
      time: '1 hour ago',
      icon: Person,
      color: 'success',
      onClick: () => navigate('/dashboard/students'),
    });

    return activities.slice(0, 4); // Show only 4 most recent
  };

  const activities = generateActivities();

  return (
    <EnhancedCard
      loading={loading}
      glassmorphism={true}
      hoverable={true}
      sx={{ height: '100%' }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Recent Activity
        </Typography>
        <EnhancedButton
          size="small"
          variant="outlined"
          startIcon={<Refresh />}
          tooltip="Refresh activities"
          onClick={() => window.location.reload()}
        >
          Refresh
        </EnhancedButton>
      </Box>
        
        <Stack spacing={2}>
          {activities.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Box
                sx={{
                  display: 'flex',
                  gap: 2,
                  p: 2,
                  borderRadius: 2,
                  cursor: activity.onClick ? 'pointer' : 'default',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: alpha('#000', 0.02),
                    transform: 'translateX(4px)',
                  },
                }}
                onClick={activity.onClick}
              >
                <Avatar
                  sx={{
                    bgcolor: `${activity.color}.main`,
                    width: 40,
                    height: 40,
                  }}
                >
                  <activity.icon sx={{ fontSize: 20 }} />
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                    {activity.title}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                    {activity.description}
                  </Typography>
                  <Typography variant="caption" color="text.disabled">
                    {activity.time}
                  </Typography>
                </Box>
              </Box>
            </motion.div>
          ))}
        </Stack>
    </EnhancedCard>
  );
};

// Quick Actions Component
const QuickActions = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  const actions = [
    {
      label: 'Generate SWOT',
      icon: Psychology,
      color: 'primary',
      onClick: () => navigate('/dashboard/swot')
    },
    {
      label: 'Add Student',
      icon: Person,
      color: 'secondary',
      onClick: () => navigate('/dashboard/students')
    },
    {
      label: 'View Reports',
      icon: BarChart,
      color: 'success',
      onClick: () => navigate('/dashboard/reports')
    },
    {
      label: 'Analytics',
      icon: Timeline,
      color: 'info',
      onClick: () => navigate('/dashboard/analytics')
    },
  ];

  return (
    <Box sx={{ position: 'fixed', bottom: 24, right: 24, zIndex: 1000 }}>
      <Stack spacing={2}>
        {actions.map((action, index) => (
          <motion.div
            key={action.label}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1, type: 'spring' }}
          >
            <EnhancedButton
              variant="contained"
              color={action.color}
              onClick={action.onClick}
              tooltip={action.label}
              startIcon={<action.icon />}
              sx={{
                borderRadius: '50%',
                minWidth: 56,
                width: 56,
                height: 56,
                backdropFilter: 'blur(10px)',
                boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.15)',
              }}
            >
            </EnhancedButton>
          </motion.div>
        ))}
      </Stack>
    </Box>
  );
};

// Main Modern Dashboard Component
const ModernDashboard = () => {
  const { t } = useTranslation(['dashboard', 'common']);
  const theme = useTheme();
  const [darkMode, setDarkMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [studentsData, setStudentsData] = useState([]);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [user] = useState({
    name: 'Dr. Priya Sharma',
    role: 'Principal',
    school: 'Delhi Public School',
  });

  // Load real student data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        const students = generateCompleteStudentData();
        const analytics = generateAnalyticsData(students);

        setStudentsData(students);
        setAnalyticsData(analytics);
      } catch (error) {
        console.error('Error loading student data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handleThemeToggle = () => {
    setDarkMode(!darkMode);
    // In a real app, this would update the theme context
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.background.surface || theme.palette.background.paper} 100%)`,
      }}
    >
      {/* Modern Header */}
      <ModernHeader
        darkMode={darkMode}
        onThemeToggle={handleThemeToggle}
        user={user}
      />

      {/* Main Content */}
      <Box sx={{ p: 3 }}>
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Box sx={{ mb: 4 }}>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 700,
                mb: 1,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}
            >
              Welcome back, {user.name}! 👋
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
              Here's what's happening at {user.school} today
            </Typography>
          </Box>
        </motion.div>

        {/* Quick Stats */}
        <Box sx={{ mb: 4 }}>
          <QuickStats loading={loading} analyticsData={analyticsData} />
        </Box>

        {/* Secondary Content Grid */}
        <Grid container spacing={3}>
          {/* Recent Activity */}
          <Grid item xs={12} lg={6}>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <RecentActivity loading={loading} studentsData={studentsData} />
            </motion.div>
          </Grid>

          {/* Performance Overview */}
          <Grid item xs={12} lg={6}>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                    Performance Overview
                  </Typography>

                  {loading ? (
                    <Stack spacing={2}>
                      <Skeleton variant="rectangular" height={200} />
                      <Skeleton variant="text" />
                      <Skeleton variant="text" width="60%" />
                    </Stack>
                  ) : (
                    <Box>
                      {/* Board Performance */}
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" sx={{ mb: 2, fontWeight: 500 }}>
                          Board Performance
                        </Typography>
                        <Stack spacing={2}>
                          {[
                            { board: 'CBSE', score: 92, students: 450 },
                            { board: 'ICSE', score: 89, students: 320 },
                            { board: 'State Board', score: 85, students: 477 },
                          ].map((item, index) => (
                            <motion.div
                              key={item.board}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: 0.5 + index * 0.1 }}
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  alignItems: 'center',
                                  p: 2,
                                  borderRadius: 2,
                                  background: alpha(theme.palette.board?.[item.board] || theme.palette.primary.main, 0.1),
                                  border: `1px solid ${alpha(theme.palette.board?.[item.board] || theme.palette.primary.main, 0.2)}`,
                                }}
                              >
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    {item.board}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {item.students} students
                                  </Typography>
                                </Box>
                                <Chip
                                  label={`${item.score}%`}
                                  size="small"
                                  sx={{
                                    bgcolor: theme.palette.board?.[item.board] || theme.palette.primary.main,
                                    color: 'white',
                                    fontWeight: 600,
                                  }}
                                />
                              </Box>
                            </motion.div>
                          ))}
                        </Stack>
                      </Box>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>

        {/* Bottom Action Cards */}
        <Box sx={{ mt: 4 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.6 }}
              >
                <Card
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                    color: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0px 20px 40px rgba(46, 91, 168, 0.3)',
                    },
                  }}
                >
                  <Psychology sx={{ fontSize: 48, mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Generate SWOT Analysis
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Create comprehensive student assessments
                  </Typography>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.6 }}
              >
                <Card
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`,
                    color: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0px 20px 40px rgba(255, 153, 51, 0.3)',
                    },
                  }}
                >
                  <BarChart sx={{ fontSize: 48, mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    View Analytics
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Detailed performance insights
                  </Typography>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.6 }}
              >
                <Card
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                    color: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0px 20px 40px rgba(0, 200, 83, 0.3)',
                    },
                  }}
                >
                  <Group sx={{ fontSize: 48, mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Manage Students
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Student profiles and records
                  </Typography>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </Box>
      </Box>

      {/* Floating Quick Actions */}
      <QuickActions />
    </Box>
  );
};

export default ModernDashboard;
