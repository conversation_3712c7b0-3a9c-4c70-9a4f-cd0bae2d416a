# VidyaMitra Platform - Staging Deployment Guide

## 🚀 **STAGING DEPLOYMENT ROADMAP**

This guide provides step-by-step instructions for deploying VidyaMitra to a staging environment for testing and validation.

---

## 📋 **PRE-DEPLOYMENT CHECKLIST**

### ✅ **Phase 4 Backend Completion Status**
- [x] **Authentication System** - JWT with role-based access
- [x] **15+ API Endpoints** - Complete CRUD operations
- [x] **WebSocket Server** - Real-time communication
- [x] **Indian Educational Context** - CBSE/ICSE/State boards
- [x] **Database Schema** - 15+ models with sample data
- [x] **File Upload System** - Student photo management
- [x] **Analytics Engine** - Performance insights
- [x] **Security Features** - Audit logging, validation

### ✅ **Frontend Integration Ready**
- [x] **API Service Layer** - Complete integration hooks
- [x] **WebSocket Hooks** - Real-time feature support
- [x] **Authentication Context** - Role-based UI rendering
- [x] **Performance Optimizations** - Lazy loading, virtualization
- [x] **Testing Framework** - Comprehensive test coverage

---

## 🌐 **DEPLOYMENT ARCHITECTURE**

### **Recommended Infrastructure**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (Vercel)      │◄──►│   (Railway)     │◄──►│   (MongoDB     │
│   React + Vite  │    │   Node.js       │    │    Atlas)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│   WebSocket     │◄─────────────┘
                        │   (Same Server) │
                        └─────────────────┘
```

### **Technology Stack**
- **Frontend Hosting:** Vercel (recommended) or Netlify
- **Backend Hosting:** Railway, Render, or Heroku
- **Database:** MongoDB Atlas (cloud)
- **File Storage:** Cloudinary or AWS S3
- **Domain:** Custom domain with SSL
- **Monitoring:** LogRocket, Sentry

---

## 🔧 **STEP 1: DATABASE SETUP (MongoDB Atlas)**

### **1.1 Create MongoDB Atlas Account**
```bash
# Visit: https://www.mongodb.com/cloud/atlas
# Create free tier cluster (M0)
# Choose region closest to your users (e.g., Mumbai for India)
```

### **1.2 Configure Database**
```javascript
// Cluster Configuration
Cluster Name: vidyamitra-staging
Region: Asia-Pacific (Mumbai)
Tier: M0 Sandbox (Free)
MongoDB Version: 7.0
```

### **1.3 Setup Database User**
```javascript
// Database Access
Username: vidyamitra-admin
Password: [Generate secure password]
Database: vidyamitra
Privileges: Read and write to any database
```

### **1.4 Configure Network Access**
```javascript
// Network Access
IP Whitelist: 0.0.0.0/0 (Allow access from anywhere - for staging)
// For production, restrict to specific IPs
```

### **1.5 Get Connection String**
```bash
# Example connection string:
mongodb+srv://vidyamitra-admin:<password>@vidyamitra-staging.xxxxx.mongodb.net/vidyamitra?retryWrites=true&w=majority
```

---

## 🖥️ **STEP 2: BACKEND DEPLOYMENT (Railway)**

### **2.1 Prepare Backend for Deployment**
```bash
# Navigate to backend directory
cd apps/api-server

# Create production environment file
cp .env.example .env.production
```

### **2.2 Update Production Environment**
```env
# .env.production
NODE_ENV=production
PORT=3001

# MongoDB Atlas connection
MONGODB_URI=mongodb+srv://vidyamitra-admin:<password>@vidyamitra-staging.xxxxx.mongodb.net/vidyamitra

# JWT Configuration (Generate new secrets for production)
JWT_SECRET=your-production-jwt-secret-256-bit-key
JWT_REFRESH_SECRET=your-production-refresh-secret-256-bit-key

# CORS Configuration
CORS_ORIGIN=https://vidyamitra-staging.vercel.app
CORS_CREDENTIALS=true

# File Upload (Cloudinary recommended)
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=info
```

### **2.3 Deploy to Railway**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Initialize project
railway init

# Deploy
railway up
```

### **2.4 Configure Railway Environment**
```bash
# Set environment variables in Railway dashboard
railway variables set NODE_ENV=production
railway variables set MONGODB_URI="your-mongodb-connection-string"
railway variables set JWT_SECRET="your-jwt-secret"
# ... set all other environment variables
```

### **2.5 Seed Production Database**
```bash
# Run seeding script on deployed backend
railway run node scripts/seedDatabase.js
```

---

## 🌐 **STEP 3: FRONTEND DEPLOYMENT (Vercel)**

### **3.1 Prepare Frontend for Deployment**
```bash
# Navigate to frontend directory
cd apps/web-frontend

# Update environment variables
cp .env.example .env.production
```

### **3.2 Update Frontend Environment**
```env
# .env.production
VITE_API_URL=https://your-railway-app.railway.app/api/v1
VITE_WS_URL=wss://your-railway-app.railway.app/ws
VITE_APP_ENV=staging
VITE_ENABLE_ANALYTICS=true
```

### **3.3 Deploy to Vercel**
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod

# Set environment variables in Vercel dashboard
vercel env add VITE_API_URL
vercel env add VITE_WS_URL
# ... add all environment variables
```

### **3.4 Configure Custom Domain (Optional)**
```bash
# In Vercel dashboard:
# 1. Go to Project Settings > Domains
# 2. Add custom domain: staging.vidyamitra.com
# 3. Configure DNS records as instructed
```

---

## 📁 **STEP 4: FILE STORAGE SETUP (Cloudinary)**

### **4.1 Create Cloudinary Account**
```bash
# Visit: https://cloudinary.com
# Create free account (25GB storage, 25GB bandwidth)
```

### **4.2 Configure Cloudinary**
```javascript
// Get credentials from Cloudinary dashboard
Cloud Name: your-cloud-name
API Key: your-api-key
API Secret: your-api-secret
```

### **4.3 Update Backend for Cloudinary**
```javascript
// Add to package.json dependencies
"cloudinary": "^1.41.0",
"multer-storage-cloudinary": "^4.0.0"
```

---

## 🔒 **STEP 5: SECURITY CONFIGURATION**

### **5.1 SSL Certificate**
```bash
# Vercel and Railway provide automatic SSL
# Verify HTTPS is working for both frontend and backend
```

### **5.2 CORS Configuration**
```javascript
// In backend server.js
const corsOptions = {
  origin: [
    'https://vidyamitra-staging.vercel.app',
    'https://staging.vidyamitra.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};
```

### **5.3 Rate Limiting**
```javascript
// Configure rate limiting for production
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP'
});
```

---

## 🧪 **STEP 6: TESTING & VALIDATION**

### **6.1 Smoke Tests**
```bash
# Test basic functionality
1. Frontend loads: https://vidyamitra-staging.vercel.app
2. API responds: https://your-railway-app.railway.app/api/v1/health
3. Authentication works: Login with seeded credentials
4. WebSocket connects: Real-time features functional
```

### **6.2 Integration Tests**
```bash
# Test all Phase 3 components
1. Student Registration: Complete workflow
2. Student Profile: Data display and editing
3. Teacher Dashboard: Real-time analytics
4. Attendance Management: Marking and statistics
5. Grade Entry: Indian grading system
6. SWOT Wizard: Analysis creation
7. Report Generation: PDF/Excel export
```

### **6.3 Performance Tests**
```bash
# Test with realistic data
1. Load 100+ students: Performance check
2. Concurrent users: 10+ simultaneous sessions
3. Real-time updates: WebSocket stability
4. File uploads: Photo upload functionality
5. Mobile responsiveness: All screen sizes
```

---

## 📊 **STEP 7: MONITORING & ANALYTICS**

### **7.1 Error Monitoring (Sentry)**
```bash
# Install Sentry
npm install @sentry/react @sentry/node

# Configure for both frontend and backend
# Get DSN from Sentry dashboard
```

### **7.2 Performance Monitoring**
```bash
# Frontend: Web Vitals tracking
# Backend: Response time monitoring
# Database: Query performance
# WebSocket: Connection stability
```

### **7.3 User Analytics**
```bash
# Google Analytics 4
# User behavior tracking
# Feature usage analytics
# Performance metrics
```

---

## 🚀 **STEP 8: GO-LIVE CHECKLIST**

### **8.1 Final Verification**
- [ ] **Frontend accessible** at staging URL
- [ ] **Backend API** responding correctly
- [ ] **Database** populated with sample data
- [ ] **Authentication** working for all roles
- [ ] **WebSocket** real-time features functional
- [ ] **File uploads** working correctly
- [ ] **All 7 Phase 3 components** operational
- [ ] **Mobile responsive** design verified
- [ ] **Cross-browser** compatibility tested
- [ ] **SSL certificates** active
- [ ] **Error monitoring** configured
- [ ] **Performance metrics** acceptable

### **8.2 Demo Credentials**
```javascript
// Principal Account
Username: principal
Password: password123
Role: Principal
Access: Full system access

// Teacher Account
Username: teacher1
Password: password123
Role: Teacher
Access: Assigned classes and subjects

// Sample Students
- Sanju Kumar Reddy (Class 10-A)
- Niraimathi Selvam (Class 10-A)
- Mahesh Reddy (Class 10-B)
```

---

## 📋 **STAGING ENVIRONMENT URLS**

### **Access Points**
```bash
# Frontend Application
https://vidyamitra-staging.vercel.app

# Backend API
https://your-railway-app.railway.app/api/v1

# API Documentation
https://your-railway-app.railway.app/api/v1/docs

# WebSocket Endpoint
wss://your-railway-app.railway.app/ws

# Database
MongoDB Atlas Cluster (Private)
```

---

## 🎯 **SUCCESS CRITERIA**

### **Technical Metrics**
- **Page Load Time:** < 2 seconds
- **API Response Time:** < 500ms
- **WebSocket Connection:** < 1 second
- **Uptime:** > 99.5%
- **Error Rate:** < 0.1%

### **Functional Validation**
- **All 7 Phase 3 components** fully functional
- **Real-time features** working correctly
- **Indian educational context** preserved
- **Role-based access** properly enforced
- **File uploads** successful
- **Reports generation** working
- **Mobile responsiveness** verified

---

## 🎉 **DEPLOYMENT COMPLETION**

Once all steps are completed successfully:

1. **Staging Environment** will be live and accessible
2. **End-to-end testing** can begin
3. **Stakeholder review** can be conducted
4. **User acceptance testing** can proceed
5. **Production deployment** planning can start

**VidyaMitra Platform will be ready for comprehensive testing and validation in a production-like environment.**
