/**
 * VidyaMitra Platform - Feature Integration Tests
 * 
 * Comprehensive tests to ensure all features are accessible and working
 * Tests navigation, data integration, and user workflows
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { describe, it, expect, beforeEach } from 'vitest';
import App from '../App';
import { createTheme } from '@mui/material/styles';
import { mainNavigation, getNavigationByRole, getAccessiblePaths } from '../data/navigationData';
import { extendedStudentProfiles, schools, teachers, parents } from '../data/comprehensiveData';

// Mock theme for testing
const mockTheme = createTheme();

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={mockTheme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('VidyaMitra Feature Integration Tests', () => {
  beforeEach(() => {
    // Reset any global state before each test
    localStorage.clear();
  });

  describe('Navigation System', () => {
    it('should have all main navigation items defined', () => {
      expect(mainNavigation).toBeDefined();
      expect(mainNavigation.length).toBeGreaterThan(0);
      
      // Check that all navigation items have required properties
      mainNavigation.forEach(item => {
        expect(item).toHaveProperty('id');
        expect(item).toHaveProperty('title');
        expect(item).toHaveProperty('path');
        expect(item).toHaveProperty('icon');
        expect(item).toHaveProperty('roles');
      });
    });

    it('should filter navigation by user role correctly', () => {
      const teacherNav = getNavigationByRole(mainNavigation, 'teacher');
      const studentNav = getNavigationByRole(mainNavigation, 'student');
      const parentNav = getNavigationByRole(mainNavigation, 'parent');
      const principalNav = getNavigationByRole(mainNavigation, 'principal');

      expect(teacherNav.length).toBeGreaterThan(0);
      expect(studentNav.length).toBeGreaterThan(0);
      expect(parentNav.length).toBeGreaterThan(0);
      expect(principalNav.length).toBeGreaterThan(0);

      // Principal should have access to most features
      expect(principalNav.length).toBeGreaterThanOrEqual(teacherNav.length);
    });

    it('should generate accessible paths for each role', () => {
      const roles = ['principal', 'teacher', 'student', 'parent'];
      
      roles.forEach(role => {
        const paths = getAccessiblePaths(role);
        expect(paths).toBeDefined();
        expect(paths.length).toBeGreaterThan(0);
        expect(paths).toContain('/dashboard');
      });
    });
  });

  describe('Mock Data Integrity', () => {
    it('should have comprehensive student data', () => {
      expect(extendedStudentProfiles).toBeDefined();
      expect(extendedStudentProfiles.length).toBeGreaterThan(10);

      // Check student data structure
      extendedStudentProfiles.forEach(student => {
        expect(student).toHaveProperty('id');
        expect(student).toHaveProperty('name');
        expect(student).toHaveProperty('grade');
        expect(student).toHaveProperty('board');
        expect(student).toHaveProperty('schoolId');
        expect(student).toHaveProperty('academicLevel');
        expect(student).toHaveProperty('region');
        expect(student).toHaveProperty('languages');
        expect(student.languages).toBeInstanceOf(Array);
      });
    });

    it('should have authentic Indian names and context', () => {
      const indianNames = [
        'Sanju Kumar', 'Niraimathi Selvam', 'Mahesh Reddy', 
        'Ravi Teja Sharma', 'Ankitha Patel', 'Sirisha Nair', 
        'Priya Agarwal', 'Ananya Krishnan', 'Vikram Joshi'
      ];

      indianNames.forEach(name => {
        const student = extendedStudentProfiles.find(s => s.name === name);
        expect(student).toBeDefined();
      });

      // Check for Indian regional context
      const regions = extendedStudentProfiles.map(s => s.region);
      expect(regions).toContain('Telangana');
      expect(regions).toContain('Tamil Nadu');
      expect(regions).toContain('Kerala');
      expect(regions).toContain('Karnataka');
    });

    it('should have schools in Hyderabad/Telangana region', () => {
      expect(schools).toBeDefined();
      expect(schools.length).toBeGreaterThan(0);

      schools.forEach(school => {
        expect(school).toHaveProperty('id');
        expect(school).toHaveProperty('name');
        expect(school).toHaveProperty('location');
        expect(school).toHaveProperty('board');
        expect(school.location).toContain('Hyderabad');
      });
    });

    it('should have teacher and parent profiles', () => {
      expect(teachers).toBeDefined();
      expect(teachers.length).toBeGreaterThan(0);
      expect(parents).toBeDefined();
      expect(parents.length).toBeGreaterThan(0);

      teachers.forEach(teacher => {
        expect(teacher).toHaveProperty('id');
        expect(teacher).toHaveProperty('name');
        expect(teacher).toHaveProperty('subject');
        expect(teacher).toHaveProperty('schoolId');
      });
    });

    it('should support multiple Indian education boards', () => {
      const boards = [...new Set(extendedStudentProfiles.map(s => s.board))];
      expect(boards).toContain('cbse');
      expect(boards).toContain('icse');
      expect(boards).toContain('state');
    });
  });

  describe('Component Accessibility', () => {
    it('should render the main app without crashing', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      // Should render without throwing errors
      expect(document.body).toBeInTheDocument();
    });

    it('should have accessible navigation elements', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // Wait for the app to load
      await waitFor(() => {
        // Check for main navigation elements
        const dashboardElement = screen.queryByText(/dashboard/i);
        expect(dashboardElement).toBeInTheDocument();
      });
    });
  });

  describe('Feature Completeness', () => {
    it('should have all required features accessible', () => {
      const requiredFeatures = [
        'dashboard',
        'students',
        'swot',
        'analytics',
        'reports'
      ];

      requiredFeatures.forEach(feature => {
        const navItem = mainNavigation.find(item => item.id === feature);
        expect(navItem).toBeDefined();
        expect(navItem.path).toBeDefined();
      });
    });

    it('should support role-based access control', () => {
      const roles = ['principal', 'teacher', 'student', 'parent'];
      
      roles.forEach(role => {
        const navigation = getNavigationByRole(mainNavigation, role);
        expect(navigation.length).toBeGreaterThan(0);
        
        // Each role should have access to dashboard
        const dashboardAccess = navigation.find(item => item.id === 'dashboard');
        expect(dashboardAccess).toBeDefined();
      });
    });
  });

  describe('Indian Educational Context', () => {
    it('should have culturally relevant data', () => {
      // Check for Indian languages
      const languages = extendedStudentProfiles.flatMap(s => s.languages);
      expect(languages).toContain('Hindi');
      expect(languages).toContain('Telugu');
      expect(languages).toContain('Tamil');
      expect(languages).toContain('Malayalam');

      // Check for Indian educational boards
      const boards = extendedStudentProfiles.map(s => s.board);
      expect(boards).toContain('cbse');
      expect(boards).toContain('icse');
      expect(boards).toContain('state');
    });

    it('should have proper Indian address formats', () => {
      extendedStudentProfiles.forEach(student => {
        expect(student.address).toBeDefined();
        // Indian addresses should contain state names and pin codes
        const hasIndianContext = 
          student.address.includes('Hyderabad') ||
          student.address.includes('Chennai') ||
          student.address.includes('Bangalore') ||
          student.address.includes('Kerala') ||
          student.address.includes('Tamil Nadu') ||
          student.address.includes('Telangana');
        expect(hasIndianContext).toBe(true);
      });
    });
  });

  describe('Performance and Quality', () => {
    it('should have clean data structure without undefined values', () => {
      extendedStudentProfiles.forEach(student => {
        Object.values(student).forEach(value => {
          expect(value).toBeDefined();
          expect(value).not.toBe('');
        });
      });
    });

    it('should have consistent ID formats', () => {
      extendedStudentProfiles.forEach(student => {
        expect(student.id).toMatch(/^STU\d{3}$/);
      });

      schools.forEach(school => {
        expect(school.id).toMatch(/^SCH\d{3}$/);
      });

      teachers.forEach(teacher => {
        expect(teacher.id).toMatch(/^TCH\d{3}$/);
      });
    });
  });
});
