{"version": 3, "file": "dataValidation.js", "names": ["myUtils", "require", "cleanFormula", "f", "substr", "DataValidation", "opts", "_classCallCheck", "sqref", "undefined", "TypeError", "formulas", "Array", "formula1", "formula2", "allowBlank", "parseInt", "errorStyle", "enums", "indexOf", "join", "error", "showErrorMessage", "errorTitle", "imeMode", "operator", "prompt", "showInputMessage", "promptTitle", "showDropDown", "type", "_createClass", "key", "value", "addToXMLele", "ele", "valEle", "att", "boolToInt", "text", "up", "DataValidationCollection", "items", "get", "length", "add", "thisValidation", "push", "valsEle", "for<PERSON>ach", "val", "module", "exports"], "sources": ["../../../../source/lib/worksheet/classes/dataValidation.js"], "sourcesContent": ["const myUtils = require('../../utils.js');\n\nlet cleanFormula = (f) => {\n    if (typeof f === 'number' || f.substr(0, 1) === '=') {\n        return f;\n    } else {\n        return '\"' + f + '\"';\n    }\n};\n\nclass DataValidation { // §18.3.1.32 dataValidation (Data Validation)\n    constructor(opts) {\n        opts = opts ? opts : {};\n        if (opts.sqref === undefined) {\n            throw new TypeError('sqref must be specified when creating a DataValidation instance.');\n        }\n        this.sqref = opts.sqref;\n        if (opts.formulas instanceof Array) {\n            opts.formulas[0] !== undefined ? this.formula1 = opts.formulas[0] : null;\n            opts.formulas[1] !== undefined ? this.formula2 = opts.formulas[1] : null;\n        }\n\n        if (opts.allowBlank !== undefined) {\n            if (parseInt(opts.allowBlank) === 1) {\n                opts.allowBlank = true;\n            }\n            if (parseInt(opts.allowBlank) === 0) {\n                opts.allowBlank = false;\n            }\n            if (typeof opts.allowBlank !== 'boolean') {\n                throw new TypeError('DataValidation allowBlank must be true, false, 1 or 0');\n            }\n            this.allowBlank = opts.allowBlank; \n        }\n\n        if (opts.errorStyle !== undefined) {\n            let enums = ['stop', 'warning', 'information'];\n            if (enums.indexOf(opts.errorStyle) < 0) {\n                throw new TypeError('DataValidation errorStyle must be one of ' + enums.join(', '));\n            }\n            this.errorStyle = opts.errorStyle;\n        }\n\n        if (opts.error !== undefined) {\n            if (typeof opts.error !== 'string') {\n                throw new TypeError('DataValidation error must be a string');\n            }\n            this.error = opts.error;\n            this.showErrorMessage = opts.showErrorMessage = true;\n        }\n\n        if (opts.errorTitle !== undefined) {\n            if (typeof opts.errorTitle !== 'string') {\n                throw new TypeError('DataValidation errorTitle must be a string');\n            }\n            this.errorTitle = opts.errorTitle;\n            this.showErrorMessage = opts.showErrorMessage = true;\n        }\n\n        if (opts.imeMode !== undefined) {\n            let enums = ['noControl', 'off', 'on', 'disabled', 'hiragana', 'fullKatakana', 'halfKatakana', 'fullAlpha', 'halfAlpha', 'fullHangul', 'halfHangul'];\n            if (enums.indexOf(opts.imeMode) < 0) {\n                throw new TypeError('DataValidation imeMode must be one of ' + enums.join(', '));\n            }\n            this.imeMode = opts.imeMode;\n        }\n            \n        if (opts.operator !== undefined) {\n            let enums = ['between', 'notBetween', 'equal', 'notEqual', 'lessThan', 'lessThanOrEqual', 'greaterThan', 'greaterThanOrEqual'];\n            if (enums.indexOf(opts.operator) < 0) {\n                throw new TypeError('DataValidation operator must be one of ' + enums.join(', '));\n            }\n            this.operator = opts.operator;\n        }\n\n        if (opts.prompt !== undefined) {\n            if (typeof opts.prompt !== 'string') {\n                throw new TypeError('DataValidation prompt must be a string');\n            }\n            this.prompt = opts.prompt;\n            this.showInputMessage = opts.showInputMessage = true;\n        }\n\n        if (opts.promptTitle !== undefined) {\n            if (typeof opts.promptTitle !== 'string') {\n                throw new TypeError('DataValidation promptTitle must be a string');\n            }\n            this.promptTitle = opts.promptTitle;\n            this.showInputMessage = opts.showInputMessage = true;\n        }\n\n        if (opts.showDropDown !== undefined) {\n            if (parseInt(opts.showDropDown) === 1) {\n                opts.showDropDown = true;\n            }\n            if (parseInt(opts.showDropDown) === 0) {\n                opts.showDropDown = false;\n            }\n            if (typeof opts.showDropDown !== 'boolean') {\n                throw new TypeError('DataValidation showDropDown must be true, false, 1 or 0');\n            }\n            this.showDropDown = opts.showDropDown;\n        }\n\n        if (opts.showErrorMessage !== undefined) {\n            if (parseInt(opts.showErrorMessage) === 1) {\n                opts.showErrorMessage = true;\n            }\n            if (parseInt(opts.showErrorMessage) === 0) {\n                opts.showErrorMessage = false;\n            }\n            if (typeof opts.showErrorMessage !== 'boolean') {\n                throw new TypeError('DataValidation showErrorMessage must be true, false, 1 or 0');\n            }\n            this.showErrorMessage = opts.showErrorMessage;\n        }\n\n        if (opts.showInputMessage !== undefined) {\n            if (parseInt(opts.showInputMessage) === 1) {\n                opts.showInputMessage = true;\n            }\n            if (parseInt(opts.showInputMessage) === 0) {\n                opts.showInputMessage = false;\n            }\n            if (typeof opts.showInputMessage !== 'boolean') {\n                throw new TypeError('DataValidation showInputMessage must be true, false, 1 or 0');\n            }\n            this.showInputMessage = opts.showInputMessage;\n        }\n\n        if (opts.type !== undefined) {\n            let enums = ['none', 'whole', 'decimal', 'list', 'date', 'time', 'textLength', 'custom'];\n            if (enums.indexOf(opts.type) < 0) {\n                throw new TypeError('DataValidation type must be one of ' + enums.join(', '));\n            }\n            this.type = opts.type;\n        }\n    }\n\n    addToXMLele(ele) {\n        let valEle = ele.ele('dataValidation');\n        this.type !== undefined ? valEle.att('type', this.type) : null;\n        this.errorStyle !== undefined ? valEle.att('errorStyle', this.errorStyle) : null;\n        this.imeMode !== undefined ? valEle.att('imeMode', this.imeMode) : null;\n        this.operator !== undefined ? valEle.att('operator', this.operator) : null;\n        this.allowBlank !== undefined ? valEle.att('allowBlank', myUtils.boolToInt(this.allowBlank)) : null;\n        this.showDropDown === false ? valEle.att('showDropDown', 1) : null; // For some reason, the Excel app sets this property to true if the \"In-cell dropdown\" option is selected in the data validation screen.\n        this.showInputMessage !== undefined ? valEle.att('showInputMessage', myUtils.boolToInt(this.showInputMessage)) : null;\n        this.showErrorMessage !== undefined ? valEle.att('showErrorMessage', myUtils.boolToInt(this.showErrorMessage)) : null;\n        this.errorTitle !== undefined ? valEle.att('errorTitle', this.errorTitle) : null;\n        this.error !== undefined ? valEle.att('error', this.error) : null;\n        this.promptTitle !== undefined ? valEle.att('promptTitle', this.promptTitle) : null;\n        this.prompt !== undefined ? valEle.att('prompt', this.prompt) : null;\n        this.sqref !== undefined ? valEle.att('sqref', this.sqref) : null;\n        if (this.formula1 !== undefined) {\n            valEle.ele('formula1').text(cleanFormula(this.formula1));\n            valEle.up();\n            if (this.formula2 !== undefined) {\n                valEle.ele('formula2').text(cleanFormula(this.formula2));\n                valEle.up();\n            }\n        }\n        valEle.up();\n    }\n}\n\nclass DataValidationCollection { // §18.3.1.33 dataValidations (Data Validations)\n    constructor(opts) {\n        opts = opts ? opts : {};\n        this.items = [];\n    }\n\n    get length() {\n        return this.items.length;\n    }\n\n    add(opts) {\n        let thisValidation = new DataValidation(opts);\n        this.items.push(thisValidation);\n        return thisValidation;\n    }\n\n    addToXMLele(ele) {\n        let valsEle = ele.ele('dataValidations').att('count', this.length);\n        this.items.forEach((val) => {\n            val.addToXMLele(valsEle);\n        });\n        valsEle.up();\n    }\n}\n\nmodule.exports = { DataValidationCollection, DataValidation };\n"], "mappings": ";;;;;AAAA,IAAMA,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAEzC,IAAIC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,CAAC,EAAK;EACtB,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;IACjD,OAAOD,CAAC;EACZ,CAAC,MAAM;IACH,OAAO,GAAG,GAAGA,CAAC,GAAG,GAAG;EACxB;AACJ,CAAC;AAAC,IAEIE,cAAc;EAAG;EACnB,SAAAA,eAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,cAAA;IACdC,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC;IACvB,IAAIA,IAAI,CAACE,KAAK,KAAKC,SAAS,EAAE;MAC1B,MAAM,IAAIC,SAAS,CAAC,kEAAkE,CAAC;IAC3F;IACA,IAAI,CAACF,KAAK,GAAGF,IAAI,CAACE,KAAK;IACvB,IAAIF,IAAI,CAACK,QAAQ,YAAYC,KAAK,EAAE;MAChCN,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC,KAAKF,SAAS,GAAG,IAAI,CAACI,QAAQ,GAAGP,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI;MACxEL,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC,KAAKF,SAAS,GAAG,IAAI,CAACK,QAAQ,GAAGR,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5E;IAEA,IAAIL,IAAI,CAACS,UAAU,KAAKN,SAAS,EAAE;MAC/B,IAAIO,QAAQ,CAACV,IAAI,CAACS,UAAU,CAAC,KAAK,CAAC,EAAE;QACjCT,IAAI,CAACS,UAAU,GAAG,IAAI;MAC1B;MACA,IAAIC,QAAQ,CAACV,IAAI,CAACS,UAAU,CAAC,KAAK,CAAC,EAAE;QACjCT,IAAI,CAACS,UAAU,GAAG,KAAK;MAC3B;MACA,IAAI,OAAOT,IAAI,CAACS,UAAU,KAAK,SAAS,EAAE;QACtC,MAAM,IAAIL,SAAS,CAAC,uDAAuD,CAAC;MAChF;MACA,IAAI,CAACK,UAAU,GAAGT,IAAI,CAACS,UAAU;IACrC;IAEA,IAAIT,IAAI,CAACW,UAAU,KAAKR,SAAS,EAAE;MAC/B,IAAIS,KAAK,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC;MAC9C,IAAIA,KAAK,CAACC,OAAO,CAACb,IAAI,CAACW,UAAU,CAAC,GAAG,CAAC,EAAE;QACpC,MAAM,IAAIP,SAAS,CAAC,2CAA2C,GAAGQ,KAAK,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;MACvF;MACA,IAAI,CAACH,UAAU,GAAGX,IAAI,CAACW,UAAU;IACrC;IAEA,IAAIX,IAAI,CAACe,KAAK,KAAKZ,SAAS,EAAE;MAC1B,IAAI,OAAOH,IAAI,CAACe,KAAK,KAAK,QAAQ,EAAE;QAChC,MAAM,IAAIX,SAAS,CAAC,uCAAuC,CAAC;MAChE;MACA,IAAI,CAACW,KAAK,GAAGf,IAAI,CAACe,KAAK;MACvB,IAAI,CAACC,gBAAgB,GAAGhB,IAAI,CAACgB,gBAAgB,GAAG,IAAI;IACxD;IAEA,IAAIhB,IAAI,CAACiB,UAAU,KAAKd,SAAS,EAAE;MAC/B,IAAI,OAAOH,IAAI,CAACiB,UAAU,KAAK,QAAQ,EAAE;QACrC,MAAM,IAAIb,SAAS,CAAC,4CAA4C,CAAC;MACrE;MACA,IAAI,CAACa,UAAU,GAAGjB,IAAI,CAACiB,UAAU;MACjC,IAAI,CAACD,gBAAgB,GAAGhB,IAAI,CAACgB,gBAAgB,GAAG,IAAI;IACxD;IAEA,IAAIhB,IAAI,CAACkB,OAAO,KAAKf,SAAS,EAAE;MAC5B,IAAIS,MAAK,GAAG,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC;MACpJ,IAAIA,MAAK,CAACC,OAAO,CAACb,IAAI,CAACkB,OAAO,CAAC,GAAG,CAAC,EAAE;QACjC,MAAM,IAAId,SAAS,CAAC,wCAAwC,GAAGQ,MAAK,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;MACpF;MACA,IAAI,CAACI,OAAO,GAAGlB,IAAI,CAACkB,OAAO;IAC/B;IAEA,IAAIlB,IAAI,CAACmB,QAAQ,KAAKhB,SAAS,EAAE;MAC7B,IAAIS,OAAK,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,oBAAoB,CAAC;MAC9H,IAAIA,OAAK,CAACC,OAAO,CAACb,IAAI,CAACmB,QAAQ,CAAC,GAAG,CAAC,EAAE;QAClC,MAAM,IAAIf,SAAS,CAAC,yCAAyC,GAAGQ,OAAK,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;MACrF;MACA,IAAI,CAACK,QAAQ,GAAGnB,IAAI,CAACmB,QAAQ;IACjC;IAEA,IAAInB,IAAI,CAACoB,MAAM,KAAKjB,SAAS,EAAE;MAC3B,IAAI,OAAOH,IAAI,CAACoB,MAAM,KAAK,QAAQ,EAAE;QACjC,MAAM,IAAIhB,SAAS,CAAC,wCAAwC,CAAC;MACjE;MACA,IAAI,CAACgB,MAAM,GAAGpB,IAAI,CAACoB,MAAM;MACzB,IAAI,CAACC,gBAAgB,GAAGrB,IAAI,CAACqB,gBAAgB,GAAG,IAAI;IACxD;IAEA,IAAIrB,IAAI,CAACsB,WAAW,KAAKnB,SAAS,EAAE;MAChC,IAAI,OAAOH,IAAI,CAACsB,WAAW,KAAK,QAAQ,EAAE;QACtC,MAAM,IAAIlB,SAAS,CAAC,6CAA6C,CAAC;MACtE;MACA,IAAI,CAACkB,WAAW,GAAGtB,IAAI,CAACsB,WAAW;MACnC,IAAI,CAACD,gBAAgB,GAAGrB,IAAI,CAACqB,gBAAgB,GAAG,IAAI;IACxD;IAEA,IAAIrB,IAAI,CAACuB,YAAY,KAAKpB,SAAS,EAAE;MACjC,IAAIO,QAAQ,CAACV,IAAI,CAACuB,YAAY,CAAC,KAAK,CAAC,EAAE;QACnCvB,IAAI,CAACuB,YAAY,GAAG,IAAI;MAC5B;MACA,IAAIb,QAAQ,CAACV,IAAI,CAACuB,YAAY,CAAC,KAAK,CAAC,EAAE;QACnCvB,IAAI,CAACuB,YAAY,GAAG,KAAK;MAC7B;MACA,IAAI,OAAOvB,IAAI,CAACuB,YAAY,KAAK,SAAS,EAAE;QACxC,MAAM,IAAInB,SAAS,CAAC,yDAAyD,CAAC;MAClF;MACA,IAAI,CAACmB,YAAY,GAAGvB,IAAI,CAACuB,YAAY;IACzC;IAEA,IAAIvB,IAAI,CAACgB,gBAAgB,KAAKb,SAAS,EAAE;MACrC,IAAIO,QAAQ,CAACV,IAAI,CAACgB,gBAAgB,CAAC,KAAK,CAAC,EAAE;QACvChB,IAAI,CAACgB,gBAAgB,GAAG,IAAI;MAChC;MACA,IAAIN,QAAQ,CAACV,IAAI,CAACgB,gBAAgB,CAAC,KAAK,CAAC,EAAE;QACvChB,IAAI,CAACgB,gBAAgB,GAAG,KAAK;MACjC;MACA,IAAI,OAAOhB,IAAI,CAACgB,gBAAgB,KAAK,SAAS,EAAE;QAC5C,MAAM,IAAIZ,SAAS,CAAC,6DAA6D,CAAC;MACtF;MACA,IAAI,CAACY,gBAAgB,GAAGhB,IAAI,CAACgB,gBAAgB;IACjD;IAEA,IAAIhB,IAAI,CAACqB,gBAAgB,KAAKlB,SAAS,EAAE;MACrC,IAAIO,QAAQ,CAACV,IAAI,CAACqB,gBAAgB,CAAC,KAAK,CAAC,EAAE;QACvCrB,IAAI,CAACqB,gBAAgB,GAAG,IAAI;MAChC;MACA,IAAIX,QAAQ,CAACV,IAAI,CAACqB,gBAAgB,CAAC,KAAK,CAAC,EAAE;QACvCrB,IAAI,CAACqB,gBAAgB,GAAG,KAAK;MACjC;MACA,IAAI,OAAOrB,IAAI,CAACqB,gBAAgB,KAAK,SAAS,EAAE;QAC5C,MAAM,IAAIjB,SAAS,CAAC,6DAA6D,CAAC;MACtF;MACA,IAAI,CAACiB,gBAAgB,GAAGrB,IAAI,CAACqB,gBAAgB;IACjD;IAEA,IAAIrB,IAAI,CAACwB,IAAI,KAAKrB,SAAS,EAAE;MACzB,IAAIS,OAAK,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,CAAC;MACxF,IAAIA,OAAK,CAACC,OAAO,CAACb,IAAI,CAACwB,IAAI,CAAC,GAAG,CAAC,EAAE;QAC9B,MAAM,IAAIpB,SAAS,CAAC,qCAAqC,GAAGQ,OAAK,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;MACjF;MACA,IAAI,CAACU,IAAI,GAAGxB,IAAI,CAACwB,IAAI;IACzB;EACJ;EAACC,YAAA,CAAA1B,cAAA;IAAA2B,GAAA;IAAAC,KAAA,EAED,SAAAC,YAAYC,GAAG,EAAE;MACb,IAAIC,MAAM,GAAGD,GAAG,CAACA,GAAG,CAAC,gBAAgB,CAAC;MACtC,IAAI,CAACL,IAAI,KAAKrB,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACP,IAAI,CAAC,GAAG,IAAI;MAC9D,IAAI,CAACb,UAAU,KAAKR,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACpB,UAAU,CAAC,GAAG,IAAI;MAChF,IAAI,CAACO,OAAO,KAAKf,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACb,OAAO,CAAC,GAAG,IAAI;MACvE,IAAI,CAACC,QAAQ,KAAKhB,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACZ,QAAQ,CAAC,GAAG,IAAI;MAC1E,IAAI,CAACV,UAAU,KAAKN,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,YAAY,EAAErC,OAAO,CAACsC,SAAS,CAAC,IAAI,CAACvB,UAAU,CAAC,CAAC,GAAG,IAAI;MACnG,IAAI,CAACc,YAAY,KAAK,KAAK,GAAGO,MAAM,CAACC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;MACpE,IAAI,CAACV,gBAAgB,KAAKlB,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,kBAAkB,EAAErC,OAAO,CAACsC,SAAS,CAAC,IAAI,CAACX,gBAAgB,CAAC,CAAC,GAAG,IAAI;MACrH,IAAI,CAACL,gBAAgB,KAAKb,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,kBAAkB,EAAErC,OAAO,CAACsC,SAAS,CAAC,IAAI,CAAChB,gBAAgB,CAAC,CAAC,GAAG,IAAI;MACrH,IAAI,CAACC,UAAU,KAAKd,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACd,UAAU,CAAC,GAAG,IAAI;MAChF,IAAI,CAACF,KAAK,KAAKZ,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAChB,KAAK,CAAC,GAAG,IAAI;MACjE,IAAI,CAACO,WAAW,KAAKnB,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACT,WAAW,CAAC,GAAG,IAAI;MACnF,IAAI,CAACF,MAAM,KAAKjB,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACX,MAAM,CAAC,GAAG,IAAI;MACpE,IAAI,CAAClB,KAAK,KAAKC,SAAS,GAAG2B,MAAM,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC7B,KAAK,CAAC,GAAG,IAAI;MACjE,IAAI,IAAI,CAACK,QAAQ,KAAKJ,SAAS,EAAE;QAC7B2B,MAAM,CAACD,GAAG,CAAC,UAAU,CAAC,CAACI,IAAI,CAACrC,YAAY,CAAC,IAAI,CAACW,QAAQ,CAAC,CAAC;QACxDuB,MAAM,CAACI,EAAE,CAAC,CAAC;QACX,IAAI,IAAI,CAAC1B,QAAQ,KAAKL,SAAS,EAAE;UAC7B2B,MAAM,CAACD,GAAG,CAAC,UAAU,CAAC,CAACI,IAAI,CAACrC,YAAY,CAAC,IAAI,CAACY,QAAQ,CAAC,CAAC;UACxDsB,MAAM,CAACI,EAAE,CAAC,CAAC;QACf;MACJ;MACAJ,MAAM,CAACI,EAAE,CAAC,CAAC;IACf;EAAC;EAAA,OAAAnC,cAAA;AAAA;AAAA,IAGCoC,wBAAwB;EAAG;EAC7B,SAAAA,yBAAYnC,IAAI,EAAE;IAAAC,eAAA,OAAAkC,wBAAA;IACdnC,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC;IACvB,IAAI,CAACoC,KAAK,GAAG,EAAE;EACnB;EAACX,YAAA,CAAAU,wBAAA;IAAAT,GAAA;IAAAW,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,OAAO,IAAI,CAACD,KAAK,CAACE,MAAM;IAC5B;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EAED,SAAAY,IAAIvC,IAAI,EAAE;MACN,IAAIwC,cAAc,GAAG,IAAIzC,cAAc,CAACC,IAAI,CAAC;MAC7C,IAAI,CAACoC,KAAK,CAACK,IAAI,CAACD,cAAc,CAAC;MAC/B,OAAOA,cAAc;IACzB;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAED,SAAAC,YAAYC,GAAG,EAAE;MACb,IAAIa,OAAO,GAAGb,GAAG,CAACA,GAAG,CAAC,iBAAiB,CAAC,CAACE,GAAG,CAAC,OAAO,EAAE,IAAI,CAACO,MAAM,CAAC;MAClE,IAAI,CAACF,KAAK,CAACO,OAAO,CAAC,UAACC,GAAG,EAAK;QACxBA,GAAG,CAAChB,WAAW,CAACc,OAAO,CAAC;MAC5B,CAAC,CAAC;MACFA,OAAO,CAACR,EAAE,CAAC,CAAC;IAChB;EAAC;EAAA,OAAAC,wBAAA;AAAA;AAGLU,MAAM,CAACC,OAAO,GAAG;EAAEX,wBAAwB,EAAxBA,wBAAwB;EAAEpC,cAAc,EAAdA;AAAe,CAAC"}