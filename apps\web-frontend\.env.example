# VidyaMitra Platform - Environment Configuration Example
# Copy this file to .env and update the values for your environment

# API Configuration
VITE_API_URL=http://localhost:3001/api/v1
VITE_WS_URL=ws://localhost:3001/ws

# AI Service Configuration
VITE_AI_SERVICE_URL=http://localhost:8000

# Authentication
VITE_JWT_SECRET=your-jwt-secret-key-here
VITE_TOKEN_EXPIRY=24h

# Features
VITE_ENABLE_WEBSOCKET=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_OFFLINE_MODE=false

# Performance
VITE_CACHE_DURATION=300000
VITE_MAX_RETRIES=3
VITE_REQUEST_TIMEOUT=10000

# Development
VITE_DEBUG_MODE=false
VITE_MOCK_API=false

# Indian Educational Context
VITE_DEFAULT_BOARD=CBSE
VITE_DEFAULT_LANGUAGE=en
VITE_SUPPORTED_LANGUAGES=en,hi,te,ta,ml

# File Upload
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Analytics
VITE_GOOGLE_ANALYTICS_ID=
VITE_MIXPANEL_TOKEN=

# Error Reporting
VITE_SENTRY_DSN=

# Social Login (Optional)
VITE_GOOGLE_CLIENT_ID=
VITE_FACEBOOK_APP_ID=

# School Integration (Optional)
VITE_FEDENA_API_URL=
VITE_ENTAB_API_URL=
VITE_SKOLARO_API_URL=
