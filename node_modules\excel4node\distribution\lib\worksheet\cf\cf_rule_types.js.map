{"version": 3, "file": "cf_rule_types.js", "names": ["module", "exports", "cellIs", "supported", "expression", "requiredProps", "colorScale", "dataBar", "iconSet", "containsText", "notContainsText", "beginsWith", "endsWith", "containsBlanks", "notContainsBlanks", "containsErrors", "notContainsErrors"], "sources": ["../../../../source/lib/worksheet/cf/cf_rule_types.js"], "sourcesContent": ["// Types from xlsx spec:\n//     http://download.microsoft.com/download/D/3/3/D334A189-E51B-47FF-B0E8-C0479AFB0E3C/[MS-XLSX].pdf\n\nmodule.exports = {\n    cellIs: {\n        supported: false\n    },\n    expression: {\n        supported: true,\n        requiredProps: ['dxfId', 'priority', 'formula']\n    },\n    colorScale: {\n        supported: false\n    },\n    dataBar: {\n        supported: false\n    },\n    iconSet: {\n        supported: false\n    },\n    containsText: {\n        supported: false\n    },\n    notContainsText: {\n        supported: false\n    },\n    beginsWith: {\n        supported: false\n    },\n    endsWith: {\n        supported: false\n    },\n    containsBlanks: {\n        supported: false\n    },\n    notContainsBlanks: {\n        supported: false\n    },\n    containsErrors: {\n        supported: false\n    },\n    notContainsErrors: {\n        supported: false\n    }\n};\n"], "mappings": ";;AAAA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAG;EACbC,MAAM,EAAE;IACJC,SAAS,EAAE;EACf,CAAC;EACDC,UAAU,EAAE;IACRD,SAAS,EAAE,IAAI;IACfE,aAAa,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS;EAClD,CAAC;EACDC,UAAU,EAAE;IACRH,SAAS,EAAE;EACf,CAAC;EACDI,OAAO,EAAE;IACLJ,SAAS,EAAE;EACf,CAAC;EACDK,OAAO,EAAE;IACLL,SAAS,EAAE;EACf,CAAC;EACDM,YAAY,EAAE;IACVN,SAAS,EAAE;EACf,CAAC;EACDO,eAAe,EAAE;IACbP,SAAS,EAAE;EACf,CAAC;EACDQ,UAAU,EAAE;IACRR,SAAS,EAAE;EACf,CAAC;EACDS,QAAQ,EAAE;IACNT,SAAS,EAAE;EACf,CAAC;EACDU,cAAc,EAAE;IACZV,SAAS,EAAE;EACf,CAAC;EACDW,iBAAiB,EAAE;IACfX,SAAS,EAAE;EACf,CAAC;EACDY,cAAc,EAAE;IACZZ,SAAS,EAAE;EACf,CAAC;EACDa,iBAAiB,EAAE;IACfb,SAAS,EAAE;EACf;AACJ,CAAC"}