{"version": 3, "file": "font.js", "names": ["xmlbuilder", "require", "types", "Font", "opts", "_classCallCheck", "color", "excelColor", "getColor", "name", "scheme", "size", "family", "fontFamily", "validate", "vertAlign", "charset", "condense", "extend", "bold", "italics", "outline", "shadow", "strike", "underline", "_createClass", "key", "value", "toObject", "obj", "addToXMLele", "fontXML", "fEle", "ele", "att", "undefined", "toLowerCase", "module", "exports"], "sources": ["../../../../source/lib/style/classes/font.js"], "sourcesContent": ["const xmlbuilder = require('xmlbuilder');\nconst types = require('../../types/index.js');\n\nclass Font {\n    /**\n     * @class Font\n     * @desc Instance of Font with properties\n     * @param {Object} opts Options for Font\n     * @param {String} opts.color HEX color of font\n     * @param {String} opts.name Name of Font. i.e. Calibri\n     * @param {String} opts.scheme Font Scheme. defaults to major\n     * @param {Number} opts.size Pt size of Font\n     * @param {String} opts.family Font Family. defaults to roman\n     * @param {String} opts.vertAlign Specifies font as subscript or superscript\n     * @param {Number} opts.charset Character set of font as defined in §18.4.1 charset (Character Set) or standard\n     * @param {Boolean} opts.condense Macintosh compatibility settings to squeeze text together when rendering\n     * @param {Boolean} opts.extend Stretches out the text when rendering\n     * @param {Boolean} opts.bold States whether font should be bold\n     * @param {Boolean} opts.italics States whether font should be in italics\n     * @param {Boolean} opts.outline States whether font should be outlined\n     * @param {Boolean} opts.shadow States whether font should have a shadow\n     * @param {Boolean} opts.strike States whether font should have a strikethrough\n     * @param {Boolean} opts.underline States whether font should be underlined\n     * @retuns {Font}\n     */\n    constructor(opts) {\n        opts = opts ? opts : {};\n\n        typeof opts.color === 'string' ? this.color = types.excelColor.getColor(opts.color) : null;\n        typeof opts.name === 'string' ? this.name = opts.name : null;\n        typeof opts.scheme === 'string' ? this.scheme = opts.scheme : null;\n        typeof opts.size === 'number' ? this.size = opts.size : null;\n        typeof opts.family === 'string' && types.fontFamily.validate(opts.family) === true ? this.family = opts.family : null;\n\n        typeof opts.vertAlign === 'string' ? this.vertAlign = opts.vertAlign : null;\n        typeof opts.charset === 'number' ? this.charset = opts.charset : null;\n\n        typeof opts.condense === 'boolean' ? this.condense = opts.condense : null;\n        typeof opts.extend === 'boolean' ? this.extend = opts.extend : null;\n        typeof opts.bold === 'boolean' ? this.bold = opts.bold : null;\n        typeof opts.italics === 'boolean' ? this.italics = opts.italics : null;\n        typeof opts.outline === 'boolean' ? this.outline = opts.outline : null;\n        typeof opts.shadow === 'boolean' ? this.shadow = opts.shadow : null;\n        typeof opts.strike === 'boolean' ? this.strike = opts.strike : null;\n        typeof opts.underline === 'boolean' ? this.underline = opts.underline : null;\n    }\n\n    /** \n     * @func Font.toObject\n     * @desc Converts the Font instance to a javascript object\n     * @returns {Object}\n     */\n    toObject() {\n        let obj = {};\n\n        typeof this.charset === 'number' ? obj.charset = this.charset : null;\n        typeof this.color === 'string' ? obj.color = this.color : null;\n        typeof this.family === 'string' ? obj.family = this.family : null;\n        typeof this.name === 'string' ? obj.name = this.name : null;\n        typeof this.scheme === 'string' ? obj.scheme = this.scheme : null;\n        typeof this.size === 'number' ? obj.size = this.size : null;\n        typeof this.vertAlign === 'string' ? obj.vertAlign = this.vertAlign : null;\n\n        typeof this.condense === 'boolean' ? obj.condense = this.condense : null;\n        typeof this.extend === 'boolean' ? obj.extend = this.extend : null;\n        typeof this.bold === 'boolean' ? obj.bold = this.bold : null;\n        typeof this.italics === 'boolean' ? obj.italics = this.italics : null;\n        typeof this.outline === 'boolean' ? obj.outline = this.outline : null;\n        typeof this.shadow === 'boolean' ? obj.shadow = this.shadow : null;\n        typeof this.strike === 'boolean' ? obj.strike = this.strike : null;\n        typeof this.underline === 'boolean' ? obj.underline = this.underline : null;\n\n        return obj;\n    }\n\n    /**\n     * @alias Font.addToXMLele\n     * @desc When generating Workbook output, attaches style to the styles xml file\n     * @func Font.addToXMLele\n     * @param {xmlbuilder.Element} ele Element object of the xmlbuilder module\n     */\n    addToXMLele(fontXML) {\n        let fEle = fontXML.ele('font');\n\n        // Place styling elements first to avoid validation errors with .NET validator\n        this.condense === true ? fEle.ele('condense') : null;\n        this.extend === true ? fEle.ele('extend') : null;\n        this.bold === true ? fEle.ele('b') : null;\n        this.italics === true ? fEle.ele('i') : null;\n        this.outline === true ? fEle.ele('outline') : null;\n        this.shadow === true ? fEle.ele('shadow') : null;\n        this.strike === true ? fEle.ele('strike') : null;\n        this.underline === true ? fEle.ele('u') : null;\n        this.vertAlign === true ? fEle.ele('vertAlign') : null;\n\n        fEle.ele('sz').att('val', this.size !== undefined ? this.size : 12);\n        fEle.ele('color').att('rgb', this.color !== undefined ? this.color : 'FF000000');\n        fEle.ele('name').att('val', this.name !== undefined ? this.name : 'Calibri');\n        if (this.family !== undefined) {\n            fEle.ele('family').att('val', types.fontFamily[this.family.toLowerCase()]);\n        }\n        if (this.scheme !== undefined) {\n            fEle.ele('scheme').att('val', this.scheme);\n        }\n\n\n        return true;\n    }\n\n\n}\n\nmodule.exports = Font;"], "mappings": ";;;;;AAAA,IAAMA,UAAU,GAAGC,OAAO,CAAC,YAAY,CAAC;AACxC,IAAMC,KAAK,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAAC,IAExCE,IAAI;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,KAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,IAAA;IACdC,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC;IAEvB,OAAOA,IAAI,CAACE,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACA,KAAK,GAAGJ,KAAK,CAACK,UAAU,CAACC,QAAQ,CAACJ,IAAI,CAACE,KAAK,CAAC,GAAG,IAAI;IAC1F,OAAOF,IAAI,CAACK,IAAI,KAAK,QAAQ,GAAG,IAAI,CAACA,IAAI,GAAGL,IAAI,CAACK,IAAI,GAAG,IAAI;IAC5D,OAAOL,IAAI,CAACM,MAAM,KAAK,QAAQ,GAAG,IAAI,CAACA,MAAM,GAAGN,IAAI,CAACM,MAAM,GAAG,IAAI;IAClE,OAAON,IAAI,CAACO,IAAI,KAAK,QAAQ,GAAG,IAAI,CAACA,IAAI,GAAGP,IAAI,CAACO,IAAI,GAAG,IAAI;IAC5D,OAAOP,IAAI,CAACQ,MAAM,KAAK,QAAQ,IAAIV,KAAK,CAACW,UAAU,CAACC,QAAQ,CAACV,IAAI,CAACQ,MAAM,CAAC,KAAK,IAAI,GAAG,IAAI,CAACA,MAAM,GAAGR,IAAI,CAACQ,MAAM,GAAG,IAAI;IAErH,OAAOR,IAAI,CAACW,SAAS,KAAK,QAAQ,GAAG,IAAI,CAACA,SAAS,GAAGX,IAAI,CAACW,SAAS,GAAG,IAAI;IAC3E,OAAOX,IAAI,CAACY,OAAO,KAAK,QAAQ,GAAG,IAAI,CAACA,OAAO,GAAGZ,IAAI,CAACY,OAAO,GAAG,IAAI;IAErE,OAAOZ,IAAI,CAACa,QAAQ,KAAK,SAAS,GAAG,IAAI,CAACA,QAAQ,GAAGb,IAAI,CAACa,QAAQ,GAAG,IAAI;IACzE,OAAOb,IAAI,CAACc,MAAM,KAAK,SAAS,GAAG,IAAI,CAACA,MAAM,GAAGd,IAAI,CAACc,MAAM,GAAG,IAAI;IACnE,OAAOd,IAAI,CAACe,IAAI,KAAK,SAAS,GAAG,IAAI,CAACA,IAAI,GAAGf,IAAI,CAACe,IAAI,GAAG,IAAI;IAC7D,OAAOf,IAAI,CAACgB,OAAO,KAAK,SAAS,GAAG,IAAI,CAACA,OAAO,GAAGhB,IAAI,CAACgB,OAAO,GAAG,IAAI;IACtE,OAAOhB,IAAI,CAACiB,OAAO,KAAK,SAAS,GAAG,IAAI,CAACA,OAAO,GAAGjB,IAAI,CAACiB,OAAO,GAAG,IAAI;IACtE,OAAOjB,IAAI,CAACkB,MAAM,KAAK,SAAS,GAAG,IAAI,CAACA,MAAM,GAAGlB,IAAI,CAACkB,MAAM,GAAG,IAAI;IACnE,OAAOlB,IAAI,CAACmB,MAAM,KAAK,SAAS,GAAG,IAAI,CAACA,MAAM,GAAGnB,IAAI,CAACmB,MAAM,GAAG,IAAI;IACnE,OAAOnB,IAAI,CAACoB,SAAS,KAAK,SAAS,GAAG,IAAI,CAACA,SAAS,GAAGpB,IAAI,CAACoB,SAAS,GAAG,IAAI;EAChF;;EAEA;AACJ;AACA;AACA;AACA;EAJIC,YAAA,CAAAtB,IAAA;IAAAuB,GAAA;IAAAC,KAAA,EAKA,SAAAC,SAAA,EAAW;MACP,IAAIC,GAAG,GAAG,CAAC,CAAC;MAEZ,OAAO,IAAI,CAACb,OAAO,KAAK,QAAQ,GAAGa,GAAG,CAACb,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI;MACpE,OAAO,IAAI,CAACV,KAAK,KAAK,QAAQ,GAAGuB,GAAG,CAACvB,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI;MAC9D,OAAO,IAAI,CAACM,MAAM,KAAK,QAAQ,GAAGiB,GAAG,CAACjB,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI;MACjE,OAAO,IAAI,CAACH,IAAI,KAAK,QAAQ,GAAGoB,GAAG,CAACpB,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI;MAC3D,OAAO,IAAI,CAACC,MAAM,KAAK,QAAQ,GAAGmB,GAAG,CAACnB,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI;MACjE,OAAO,IAAI,CAACC,IAAI,KAAK,QAAQ,GAAGkB,GAAG,CAAClB,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI;MAC3D,OAAO,IAAI,CAACI,SAAS,KAAK,QAAQ,GAAGc,GAAG,CAACd,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,IAAI;MAE1E,OAAO,IAAI,CAACE,QAAQ,KAAK,SAAS,GAAGY,GAAG,CAACZ,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAI;MACxE,OAAO,IAAI,CAACC,MAAM,KAAK,SAAS,GAAGW,GAAG,CAACX,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI;MAClE,OAAO,IAAI,CAACC,IAAI,KAAK,SAAS,GAAGU,GAAG,CAACV,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI;MAC5D,OAAO,IAAI,CAACC,OAAO,KAAK,SAAS,GAAGS,GAAG,CAACT,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI;MACrE,OAAO,IAAI,CAACC,OAAO,KAAK,SAAS,GAAGQ,GAAG,CAACR,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI;MACrE,OAAO,IAAI,CAACC,MAAM,KAAK,SAAS,GAAGO,GAAG,CAACP,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI;MAClE,OAAO,IAAI,CAACC,MAAM,KAAK,SAAS,GAAGM,GAAG,CAACN,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI;MAClE,OAAO,IAAI,CAACC,SAAS,KAAK,SAAS,GAAGK,GAAG,CAACL,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,IAAI;MAE3E,OAAOK,GAAG;IACd;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAH,GAAA;IAAAC,KAAA,EAMA,SAAAG,YAAYC,OAAO,EAAE;MACjB,IAAIC,IAAI,GAAGD,OAAO,CAACE,GAAG,CAAC,MAAM,CAAC;;MAE9B;MACA,IAAI,CAAChB,QAAQ,KAAK,IAAI,GAAGe,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI;MACpD,IAAI,CAACf,MAAM,KAAK,IAAI,GAAGc,IAAI,CAACC,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI;MAChD,IAAI,CAACd,IAAI,KAAK,IAAI,GAAGa,IAAI,CAACC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;MACzC,IAAI,CAACb,OAAO,KAAK,IAAI,GAAGY,IAAI,CAACC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;MAC5C,IAAI,CAACZ,OAAO,KAAK,IAAI,GAAGW,IAAI,CAACC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI;MAClD,IAAI,CAACX,MAAM,KAAK,IAAI,GAAGU,IAAI,CAACC,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI;MAChD,IAAI,CAACV,MAAM,KAAK,IAAI,GAAGS,IAAI,CAACC,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI;MAChD,IAAI,CAACT,SAAS,KAAK,IAAI,GAAGQ,IAAI,CAACC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;MAC9C,IAAI,CAAClB,SAAS,KAAK,IAAI,GAAGiB,IAAI,CAACC,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI;MAEtDD,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,KAAK,EAAE,IAAI,CAACvB,IAAI,KAAKwB,SAAS,GAAG,IAAI,CAACxB,IAAI,GAAG,EAAE,CAAC;MACnEqB,IAAI,CAACC,GAAG,CAAC,OAAO,CAAC,CAACC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC5B,KAAK,KAAK6B,SAAS,GAAG,IAAI,CAAC7B,KAAK,GAAG,UAAU,CAAC;MAChF0B,IAAI,CAACC,GAAG,CAAC,MAAM,CAAC,CAACC,GAAG,CAAC,KAAK,EAAE,IAAI,CAACzB,IAAI,KAAK0B,SAAS,GAAG,IAAI,CAAC1B,IAAI,GAAG,SAAS,CAAC;MAC5E,IAAI,IAAI,CAACG,MAAM,KAAKuB,SAAS,EAAE;QAC3BH,IAAI,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACC,GAAG,CAAC,KAAK,EAAEhC,KAAK,CAACW,UAAU,CAAC,IAAI,CAACD,MAAM,CAACwB,WAAW,CAAC,CAAC,CAAC,CAAC;MAC9E;MACA,IAAI,IAAI,CAAC1B,MAAM,KAAKyB,SAAS,EAAE;QAC3BH,IAAI,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACC,GAAG,CAAC,KAAK,EAAE,IAAI,CAACxB,MAAM,CAAC;MAC9C;MAGA,OAAO,IAAI;IACf;EAAC;EAAA,OAAAP,IAAA;AAAA;AAKLkC,MAAM,CAACC,OAAO,GAAGnC,IAAI"}