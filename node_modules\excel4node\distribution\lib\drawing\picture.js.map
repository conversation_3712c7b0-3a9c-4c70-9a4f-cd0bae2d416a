{"version": 3, "file": "picture.js", "names": ["Drawing", "require", "path", "imgsz", "mime", "uniqueId", "EMU", "xmlbuilder", "Picture", "_Drawing", "_inherits", "_super", "_createSuper", "opts", "_this", "_classCallCheck", "call", "kind", "type", "imagePath", "image", "_name", "name", "basename", "size", "_pxWidth", "width", "_pxHeight", "height", "_extension", "extname", "substr", "contentType", "getType", "_descr", "_title", "_id", "_rId", "noGrp", "noSelect", "noRot", "noChangeAspect", "noMove", "noResize", "noEditPoints", "noAdjust<PERSON>andles", "noChangeArrowheads", "noChangeShapeType", "indexOf", "position", "anchor", "from", "to", "x", "y", "TypeError", "_createClass", "key", "get", "set", "newName", "id", "rId", "desc", "title", "inWidth", "emu", "value", "inHeight", "addToXMLele", "ele", "anchorEle", "anchorType", "editAs", "att", "_position", "af", "anchorFrom", "afEle", "text", "col", "co<PERSON><PERSON><PERSON>", "row", "<PERSON><PERSON><PERSON>", "anchorTo", "at", "atEle", "pic<PERSON><PERSON>", "nvPicPrEle", "cNvPrEle", "description", "cNvPicPrEle", "blipFill<PERSON>le", "spPrEle", "xfrmEle", "prstGeom", "module", "exports"], "sources": ["../../../source/lib/drawing/picture.js"], "sourcesContent": ["const Drawing = require('./drawing.js');\nconst path = require('path');\nconst imgsz = require('image-size');\nconst mime = require('mime');\nconst uniqueId = require('lodash.uniqueid');\n\nconst EMU = require('../classes/emu.js');\nconst xmlbuilder = require('xmlbuilder');\n\nclass Picture extends Drawing {\n    /**\n     * Element representing an Excel Picture subclass of Drawing\n     * @property {String} kind Kind of picture (currently only image is supported)\n     * @property {String} type ooxml schema\n     * @property {String} imagePath Filesystem path to image\n     * @property {Buffer} image Buffer with image\n     * @property {String} contentType Mime type of image\n     * @property {String} description Description of image\n     * @property {String} title Title of image\n     * @property {String} id ID of image\n     * @property {String} noGrp pickLocks property\n     * @property {String} noSelect pickLocks property\n     * @property {String} noRot pickLocks property\n     * @property {String} noChangeAspect pickLocks property\n     * @property {String} noMove pickLocks property\n     * @property {String} noResize pickLocks property\n     * @property {String} noEditPoints pickLocks property\n     * @property {String} noAdjustHandles pickLocks property\n     * @property {String} noChangeArrowheads pickLocks property\n     * @property {String} noChangeShapeType pickLocks property\n     * @returns {Picture} Excel Picture  pickLocks property\n     */\n    constructor(opts) {\n        super();\n        this.kind = 'image';\n        this.type = 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/image';\n        this.imagePath = opts.path;\n        this.image = opts.image;\n\n        this._name = this.image ?\n            opts.name || uniqueId('image-') :\n            opts.name || path.basename(this.imagePath);\n\n        const size = imgsz(this.imagePath || this.image);\n\n        this._pxWidth = size.width;\n        this._pxHeight = size.height;\n\n        this._extension = this.image ?\n            size.type :\n            path.extname(this.imagePath).substr(1);\n\n        this.contentType = mime.getType(this._extension);\n\n        this._descr = null;\n        this._title = null;\n        this._id;\n        this._rId;\n        // picLocks §********.31 picLocks (Picture Locks)\n        this.noGrp;\n        this.noSelect;\n        this.noRot;\n        this.noChangeAspect = true;\n        this.noMove;\n        this.noResize;\n        this.noEditPoints;\n        this.noAdjustHandles;\n        this.noChangeArrowheads;\n        this.noChangeShapeType;\n        if (['oneCellAnchor', 'twoCellAnchor'].indexOf(opts.position.type) >= 0) {\n            this.anchor(opts.position.type, opts.position.from, opts.position.to);\n        } else if (opts.position.type === 'absoluteAnchor') {\n            this.position(opts.position.x, opts.position.y);\n        } else {\n            throw new TypeError('Invalid option for anchor type. anchorType must be one of oneCellAnchor, twoCellAnchor, or absoluteAnchor');\n        }\n    }\n\n    get name() {\n        return this._name;\n    }\n    set name(newName) {\n        this._name = newName;\n    }\n    get id() {\n        return this._id;\n    }\n    set id(id) {\n        this._id = id;\n    }\n\n    get rId() {\n        return (this._rId)\n            ? this._rId\n            : 'rId' + this._id;\n    }\n    set rId(rId) {\n      this._rId = 'rId' + rId;\n    }\n\n    get description() {\n        return this._descr !== null ? this._descr : this._name;\n    }\n    set description(desc) {\n        this._descr = desc;\n    }\n\n    get title() {\n        return this._title !== null ? this._title : this._name;\n    }\n    set title(title) {\n        this._title = title;\n    }\n\n    get extension() {\n        return this._extension;\n    }\n\n    get width() {\n        let inWidth = this._pxWidth / 96;\n        let emu = new EMU(inWidth + 'in');\n        return emu.value;\n    }\n\n    get height() {\n        let inHeight = this._pxHeight / 96;\n        let emu = new EMU(inHeight + 'in');\n        return emu.value;\n    }\n\n    /**\n     * @alias Picture.addToXMLele\n     * @desc When generating Workbook output, attaches pictures to the drawings xml file\n     * @func Picture.addToXMLele\n     * @param {xmlbuilder.Element} ele Element object of the xmlbuilder module\n     */\n    addToXMLele(ele) {\n\n        let anchorEle = ele.ele('xdr:' + this.anchorType);\n\n        if (this.editAs !== null) {\n            anchorEle.att('editAs', this.editAs);\n        }\n\n        if (this.anchorType === 'absoluteAnchor') {\n            anchorEle.ele('xdr:pos').att('x', this._position.x).att('y', this._position.y);\n        }\n\n        if (this.anchorType !== 'absoluteAnchor') {\n            let af = this.anchorFrom;\n            let afEle = anchorEle.ele('xdr:from');\n            afEle.ele('xdr:col').text(af.col);\n            afEle.ele('xdr:colOff').text(af.colOff);\n            afEle.ele('xdr:row').text(af.row);\n            afEle.ele('xdr:rowOff').text(af.rowOff);\n        }\n\n        if (this.anchorTo && this.anchorType === 'twoCellAnchor') {\n            let at = this.anchorTo;\n            let atEle = anchorEle.ele('xdr:to');\n            atEle.ele('xdr:col').text(at.col);\n            atEle.ele('xdr:colOff').text(at.colOff);\n            atEle.ele('xdr:row').text(at.row);\n            atEle.ele('xdr:rowOff').text(at.rowOff);\n        }\n\n        if (this.anchorType === 'oneCellAnchor' || this.anchorType === 'absoluteAnchor') {\n            anchorEle.ele('xdr:ext').att('cx', this.width).att('cy', this.height);\n        }\n\n        let picEle = anchorEle.ele('xdr:pic');\n        let nvPicPrEle = picEle.ele('xdr:nvPicPr');\n        let cNvPrEle = nvPicPrEle.ele('xdr:cNvPr');\n        cNvPrEle.att('descr', this.description);\n        cNvPrEle.att('id', this.id + 1);\n        cNvPrEle.att('name', this.name);\n        cNvPrEle.att('title', this.title);\n        let cNvPicPrEle = nvPicPrEle.ele('xdr:cNvPicPr');\n\n        this.noGrp === true ? cNvPicPrEle.ele('a:picLocks').att('noGrp', 1) : null;\n        this.noSelect === true ? cNvPicPrEle.ele('a:picLocks').att('noSelect', 1) : null;\n        this.noRot === true ? cNvPicPrEle.ele('a:picLocks').att('noRot', 1) : null;\n        this.noChangeAspect === true ? cNvPicPrEle.ele('a:picLocks').att('noChangeAspect', 1) : null;\n        this.noMove === true ? cNvPicPrEle.ele('a:picLocks').att('noMove', 1) : null;\n        this.noResize === true ? cNvPicPrEle.ele('a:picLocks').att('noResize', 1) : null;\n        this.noEditPoints === true ? cNvPicPrEle.ele('a:picLocks').att('noEditPoints', 1) : null;\n        this.noAdjustHandles === true ? cNvPicPrEle.ele('a:picLocks').att('noAdjustHandles', 1) : null;\n        this.noChangeArrowheads === true ? cNvPicPrEle.ele('a:picLocks').att('noChangeArrowheads', 1) : null;\n        this.noChangeShapeType === true ? cNvPicPrEle.ele('a:picLocks').att('noChangeShapeType', 1) : null;\n\n        let blipFillEle = picEle.ele('xdr:blipFill');\n        blipFillEle.ele('a:blip').att('r:embed', this.rId).att('xmlns:r', 'http://schemas.openxmlformats.org/officeDocument/2006/relationships');\n        blipFillEle.ele('a:stretch').ele('a:fillRect');\n\n        let spPrEle = picEle.ele('xdr:spPr');\n        let xfrmEle = spPrEle.ele('a:xfrm');\n        xfrmEle.ele('a:off').att('x', 0).att('y', 0);\n        xfrmEle.ele('a:ext').att('cx', this.width).att('cy', this.height);\n\n        let prstGeom = spPrEle.ele('a:prstGeom').att('prst', 'rect');\n        prstGeom.ele('a:avLst');\n\n        anchorEle.ele('xdr:clientData');\n    }\n}\n\nmodule.exports = Picture;\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAMA,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,IAAMC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;AAC5B,IAAME,KAAK,GAAGF,OAAO,CAAC,YAAY,CAAC;AACnC,IAAMG,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;AAC5B,IAAMI,QAAQ,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;AAE3C,IAAMK,GAAG,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AACxC,IAAMM,UAAU,GAAGN,OAAO,CAAC,YAAY,CAAC;AAAC,IAEnCO,OAAO,0BAAAC,QAAA;EAAAC,SAAA,CAAAF,OAAA,EAAAC,QAAA;EAAA,IAAAE,MAAA,GAAAC,YAAA,CAAAJ,OAAA;EACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,QAAYK,IAAI,EAAE;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAP,OAAA;IACdM,KAAA,GAAAH,MAAA,CAAAK,IAAA;IACAF,KAAA,CAAKG,IAAI,GAAG,OAAO;IACnBH,KAAA,CAAKI,IAAI,GAAG,2EAA2E;IACvFJ,KAAA,CAAKK,SAAS,GAAGN,IAAI,CAACX,IAAI;IAC1BY,KAAA,CAAKM,KAAK,GAAGP,IAAI,CAACO,KAAK;IAEvBN,KAAA,CAAKO,KAAK,GAAGP,KAAA,CAAKM,KAAK,GACnBP,IAAI,CAACS,IAAI,IAAIjB,QAAQ,CAAC,QAAQ,CAAC,GAC/BQ,IAAI,CAACS,IAAI,IAAIpB,IAAI,CAACqB,QAAQ,CAACT,KAAA,CAAKK,SAAS,CAAC;IAE9C,IAAMK,IAAI,GAAGrB,KAAK,CAACW,KAAA,CAAKK,SAAS,IAAIL,KAAA,CAAKM,KAAK,CAAC;IAEhDN,KAAA,CAAKW,QAAQ,GAAGD,IAAI,CAACE,KAAK;IAC1BZ,KAAA,CAAKa,SAAS,GAAGH,IAAI,CAACI,MAAM;IAE5Bd,KAAA,CAAKe,UAAU,GAAGf,KAAA,CAAKM,KAAK,GACxBI,IAAI,CAACN,IAAI,GACThB,IAAI,CAAC4B,OAAO,CAAChB,KAAA,CAAKK,SAAS,CAAC,CAACY,MAAM,CAAC,CAAC,CAAC;IAE1CjB,KAAA,CAAKkB,WAAW,GAAG5B,IAAI,CAAC6B,OAAO,CAACnB,KAAA,CAAKe,UAAU,CAAC;IAEhDf,KAAA,CAAKoB,MAAM,GAAG,IAAI;IAClBpB,KAAA,CAAKqB,MAAM,GAAG,IAAI;IAClBrB,KAAA,CAAKsB,GAAG;IACRtB,KAAA,CAAKuB,IAAI;IACT;IACAvB,KAAA,CAAKwB,KAAK;IACVxB,KAAA,CAAKyB,QAAQ;IACbzB,KAAA,CAAK0B,KAAK;IACV1B,KAAA,CAAK2B,cAAc,GAAG,IAAI;IAC1B3B,KAAA,CAAK4B,MAAM;IACX5B,KAAA,CAAK6B,QAAQ;IACb7B,KAAA,CAAK8B,YAAY;IACjB9B,KAAA,CAAK+B,eAAe;IACpB/B,KAAA,CAAKgC,kBAAkB;IACvBhC,KAAA,CAAKiC,iBAAiB;IACtB,IAAI,CAAC,eAAe,EAAE,eAAe,CAAC,CAACC,OAAO,CAACnC,IAAI,CAACoC,QAAQ,CAAC/B,IAAI,CAAC,IAAI,CAAC,EAAE;MACrEJ,KAAA,CAAKoC,MAAM,CAACrC,IAAI,CAACoC,QAAQ,CAAC/B,IAAI,EAAEL,IAAI,CAACoC,QAAQ,CAACE,IAAI,EAAEtC,IAAI,CAACoC,QAAQ,CAACG,EAAE,CAAC;IACzE,CAAC,MAAM,IAAIvC,IAAI,CAACoC,QAAQ,CAAC/B,IAAI,KAAK,gBAAgB,EAAE;MAChDJ,KAAA,CAAKmC,QAAQ,CAACpC,IAAI,CAACoC,QAAQ,CAACI,CAAC,EAAExC,IAAI,CAACoC,QAAQ,CAACK,CAAC,CAAC;IACnD,CAAC,MAAM;MACH,MAAM,IAAIC,SAAS,CAAC,2GAA2G,CAAC;IACpI;IAAC,OAAAzC,KAAA;EACL;EAAC0C,YAAA,CAAAhD,OAAA;IAAAiD,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAW;MACP,OAAO,IAAI,CAACrC,KAAK;IACrB,CAAC;IAAAsC,GAAA,EACD,SAAAA,IAASC,OAAO,EAAE;MACd,IAAI,CAACvC,KAAK,GAAGuC,OAAO;IACxB;EAAC;IAAAH,GAAA;IAAAC,GAAA,EACD,SAAAA,IAAA,EAAS;MACL,OAAO,IAAI,CAACtB,GAAG;IACnB,CAAC;IAAAuB,GAAA,EACD,SAAAA,IAAOE,EAAE,EAAE;MACP,IAAI,CAACzB,GAAG,GAAGyB,EAAE;IACjB;EAAC;IAAAJ,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAU;MACN,OAAQ,IAAI,CAACrB,IAAI,GACX,IAAI,CAACA,IAAI,GACT,KAAK,GAAG,IAAI,CAACD,GAAG;IAC1B,CAAC;IAAAuB,GAAA,EACD,SAAAA,IAAQG,GAAG,EAAE;MACX,IAAI,CAACzB,IAAI,GAAG,KAAK,GAAGyB,GAAG;IACzB;EAAC;IAAAL,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAkB;MACd,OAAO,IAAI,CAACxB,MAAM,KAAK,IAAI,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACb,KAAK;IAC1D,CAAC;IAAAsC,GAAA,EACD,SAAAA,IAAgBI,IAAI,EAAE;MAClB,IAAI,CAAC7B,MAAM,GAAG6B,IAAI;IACtB;EAAC;IAAAN,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAY;MACR,OAAO,IAAI,CAACvB,MAAM,KAAK,IAAI,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACd,KAAK;IAC1D,CAAC;IAAAsC,GAAA,EACD,SAAAA,IAAUK,KAAK,EAAE;MACb,IAAI,CAAC7B,MAAM,GAAG6B,KAAK;IACvB;EAAC;IAAAP,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAgB;MACZ,OAAO,IAAI,CAAC7B,UAAU;IAC1B;EAAC;IAAA4B,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAY;MACR,IAAIO,OAAO,GAAG,IAAI,CAACxC,QAAQ,GAAG,EAAE;MAChC,IAAIyC,GAAG,GAAG,IAAI5D,GAAG,CAAC2D,OAAO,GAAG,IAAI,CAAC;MACjC,OAAOC,GAAG,CAACC,KAAK;IACpB;EAAC;IAAAV,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,IAAIU,QAAQ,GAAG,IAAI,CAACzC,SAAS,GAAG,EAAE;MAClC,IAAIuC,GAAG,GAAG,IAAI5D,GAAG,CAAC8D,QAAQ,GAAG,IAAI,CAAC;MAClC,OAAOF,GAAG,CAACC,KAAK;IACpB;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAV,GAAA;IAAAU,KAAA,EAMA,SAAAE,YAAYC,GAAG,EAAE;MAEb,IAAIC,SAAS,GAAGD,GAAG,CAACA,GAAG,CAAC,MAAM,GAAG,IAAI,CAACE,UAAU,CAAC;MAEjD,IAAI,IAAI,CAACC,MAAM,KAAK,IAAI,EAAE;QACtBF,SAAS,CAACG,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACD,MAAM,CAAC;MACxC;MAEA,IAAI,IAAI,CAACD,UAAU,KAAK,gBAAgB,EAAE;QACtCD,SAAS,CAACD,GAAG,CAAC,SAAS,CAAC,CAACI,GAAG,CAAC,GAAG,EAAE,IAAI,CAACC,SAAS,CAACtB,CAAC,CAAC,CAACqB,GAAG,CAAC,GAAG,EAAE,IAAI,CAACC,SAAS,CAACrB,CAAC,CAAC;MAClF;MAEA,IAAI,IAAI,CAACkB,UAAU,KAAK,gBAAgB,EAAE;QACtC,IAAII,EAAE,GAAG,IAAI,CAACC,UAAU;QACxB,IAAIC,KAAK,GAAGP,SAAS,CAACD,GAAG,CAAC,UAAU,CAAC;QACrCQ,KAAK,CAACR,GAAG,CAAC,SAAS,CAAC,CAACS,IAAI,CAACH,EAAE,CAACI,GAAG,CAAC;QACjCF,KAAK,CAACR,GAAG,CAAC,YAAY,CAAC,CAACS,IAAI,CAACH,EAAE,CAACK,MAAM,CAAC;QACvCH,KAAK,CAACR,GAAG,CAAC,SAAS,CAAC,CAACS,IAAI,CAACH,EAAE,CAACM,GAAG,CAAC;QACjCJ,KAAK,CAACR,GAAG,CAAC,YAAY,CAAC,CAACS,IAAI,CAACH,EAAE,CAACO,MAAM,CAAC;MAC3C;MAEA,IAAI,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACZ,UAAU,KAAK,eAAe,EAAE;QACtD,IAAIa,EAAE,GAAG,IAAI,CAACD,QAAQ;QACtB,IAAIE,KAAK,GAAGf,SAAS,CAACD,GAAG,CAAC,QAAQ,CAAC;QACnCgB,KAAK,CAAChB,GAAG,CAAC,SAAS,CAAC,CAACS,IAAI,CAACM,EAAE,CAACL,GAAG,CAAC;QACjCM,KAAK,CAAChB,GAAG,CAAC,YAAY,CAAC,CAACS,IAAI,CAACM,EAAE,CAACJ,MAAM,CAAC;QACvCK,KAAK,CAAChB,GAAG,CAAC,SAAS,CAAC,CAACS,IAAI,CAACM,EAAE,CAACH,GAAG,CAAC;QACjCI,KAAK,CAAChB,GAAG,CAAC,YAAY,CAAC,CAACS,IAAI,CAACM,EAAE,CAACF,MAAM,CAAC;MAC3C;MAEA,IAAI,IAAI,CAACX,UAAU,KAAK,eAAe,IAAI,IAAI,CAACA,UAAU,KAAK,gBAAgB,EAAE;QAC7ED,SAAS,CAACD,GAAG,CAAC,SAAS,CAAC,CAACI,GAAG,CAAC,IAAI,EAAE,IAAI,CAAChD,KAAK,CAAC,CAACgD,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC9C,MAAM,CAAC;MACzE;MAEA,IAAI2D,MAAM,GAAGhB,SAAS,CAACD,GAAG,CAAC,SAAS,CAAC;MACrC,IAAIkB,UAAU,GAAGD,MAAM,CAACjB,GAAG,CAAC,aAAa,CAAC;MAC1C,IAAImB,QAAQ,GAAGD,UAAU,CAAClB,GAAG,CAAC,WAAW,CAAC;MAC1CmB,QAAQ,CAACf,GAAG,CAAC,OAAO,EAAE,IAAI,CAACgB,WAAW,CAAC;MACvCD,QAAQ,CAACf,GAAG,CAAC,IAAI,EAAE,IAAI,CAACb,EAAE,GAAG,CAAC,CAAC;MAC/B4B,QAAQ,CAACf,GAAG,CAAC,MAAM,EAAE,IAAI,CAACpD,IAAI,CAAC;MAC/BmE,QAAQ,CAACf,GAAG,CAAC,OAAO,EAAE,IAAI,CAACV,KAAK,CAAC;MACjC,IAAI2B,WAAW,GAAGH,UAAU,CAAClB,GAAG,CAAC,cAAc,CAAC;MAEhD,IAAI,CAAChC,KAAK,KAAK,IAAI,GAAGqD,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,IAAI;MAC1E,IAAI,CAACnC,QAAQ,KAAK,IAAI,GAAGoD,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,IAAI;MAChF,IAAI,CAAClC,KAAK,KAAK,IAAI,GAAGmD,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,IAAI;MAC1E,IAAI,CAACjC,cAAc,KAAK,IAAI,GAAGkD,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,GAAG,IAAI;MAC5F,IAAI,CAAChC,MAAM,KAAK,IAAI,GAAGiD,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,IAAI;MAC5E,IAAI,CAAC/B,QAAQ,KAAK,IAAI,GAAGgD,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,IAAI;MAChF,IAAI,CAAC9B,YAAY,KAAK,IAAI,GAAG+C,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC,GAAG,IAAI;MACxF,IAAI,CAAC7B,eAAe,KAAK,IAAI,GAAG8C,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC,GAAG,IAAI;MAC9F,IAAI,CAAC5B,kBAAkB,KAAK,IAAI,GAAG6C,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,oBAAoB,EAAE,CAAC,CAAC,GAAG,IAAI;MACpG,IAAI,CAAC3B,iBAAiB,KAAK,IAAI,GAAG4C,WAAW,CAACrB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,mBAAmB,EAAE,CAAC,CAAC,GAAG,IAAI;MAElG,IAAIkB,WAAW,GAAGL,MAAM,CAACjB,GAAG,CAAC,cAAc,CAAC;MAC5CsB,WAAW,CAACtB,GAAG,CAAC,QAAQ,CAAC,CAACI,GAAG,CAAC,SAAS,EAAE,IAAI,CAACZ,GAAG,CAAC,CAACY,GAAG,CAAC,SAAS,EAAE,qEAAqE,CAAC;MACxIkB,WAAW,CAACtB,GAAG,CAAC,WAAW,CAAC,CAACA,GAAG,CAAC,YAAY,CAAC;MAE9C,IAAIuB,OAAO,GAAGN,MAAM,CAACjB,GAAG,CAAC,UAAU,CAAC;MACpC,IAAIwB,OAAO,GAAGD,OAAO,CAACvB,GAAG,CAAC,QAAQ,CAAC;MACnCwB,OAAO,CAACxB,GAAG,CAAC,OAAO,CAAC,CAACI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAACA,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;MAC5CoB,OAAO,CAACxB,GAAG,CAAC,OAAO,CAAC,CAACI,GAAG,CAAC,IAAI,EAAE,IAAI,CAAChD,KAAK,CAAC,CAACgD,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC9C,MAAM,CAAC;MAEjE,IAAImE,QAAQ,GAAGF,OAAO,CAACvB,GAAG,CAAC,YAAY,CAAC,CAACI,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;MAC5DqB,QAAQ,CAACzB,GAAG,CAAC,SAAS,CAAC;MAEvBC,SAAS,CAACD,GAAG,CAAC,gBAAgB,CAAC;IACnC;EAAC;EAAA,OAAA9D,OAAA;AAAA,EAlMiBR,OAAO;AAqM7BgG,MAAM,CAACC,OAAO,GAAGzF,OAAO"}