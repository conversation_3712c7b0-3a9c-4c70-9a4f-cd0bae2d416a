import{u as e,j as s,B as t,h as a,d as n,f as i,G as r,F as l,w as d,x as c,M as o,l as x,i as h,a5 as j,W as u,a6 as m,a7 as p,a8 as g,a9 as b,aa as f,A as y,m as v,I as A}from"./mui-core-BBO2DoRL.js";import{r as w}from"./vendor-CeOqOr8o.js";import{u as C}from"./routing-B6PnZiBG.js";import{m as k}from"./animation-BJm6nf7i.js";import{a4 as S,v as W,t as $,f as N,a7 as I,Q as z,C as B}from"./mui-icons-BXhTkfAe.js";import"./utils-misc-NFmYzBmQ.js";const P=[{id:1,name:"<PERSON><PERSON>",rollNumber:1,status:"present",lastAttendance:"95%"},{id:2,name:"<PERSON><PERSON><PERSON><PERSON>",rollNumber:2,status:"present",lastAttendance:"92%"},{id:3,name:"<PERSON><PERSON><PERSON>",rollNumber:3,status:"absent",lastAttendance:"88%"},{id:4,name:"Ravi <PERSON><PERSON>",rollNumber:4,status:"present",lastAttendance:"94%"},{id:5,name:"Ankitha Patel",rollNumber:5,status:"late",lastAttendance:"90%"},{id:6,name:"Sirisha Nair",rollNumber:6,status:"present",lastAttendance:"96%"},{id:7,name:"Priya Agarwal",rollNumber:7,status:"present",lastAttendance:"85%"}],R=()=>{const R=e();C();const[T,M]=w.useState("10-A"),[O,E]=w.useState((new Date).toISOString().split("T")[0]),[D,F]=w.useState({}),[G,K]=w.useState(!1);w.useEffect((()=>{const e={};P.forEach((s=>{e[s.id]=s.status})),F(e)}),[]);const L=(e,s)=>{F((t=>({...t,[e]:s})))},Q=e=>{switch(e){case"present":return"success";case"absent":return"error";case"late":return"warning";default:return"default"}},U=e=>{switch(e){case"present":return s.jsx(N,{});case"absent":return s.jsx(I,{});case"late":return s.jsx(z,{});default:return null}},q={present:Object.values(D).filter((e=>"present"===e)).length,absent:Object.values(D).filter((e=>"absent"===e)).length,late:Object.values(D).filter((e=>"late"===e)).length};return s.jsxs(t,{sx:{maxWidth:1200,mx:"auto",p:3},children:[s.jsx(k.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(t,{sx:{mb:4},children:[s.jsx(a,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${R.palette.primary.main} 0%, ${R.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Attendance Management"}),s.jsx(a,{variant:"body1",color:"text.secondary",children:"Mark daily attendance for your students"})]})}),s.jsx(n,{sx:{mb:4},children:s.jsx(i,{children:s.jsxs(r,{container:!0,spacing:3,alignItems:"center",children:[s.jsx(r,{item:!0,xs:12,md:3,children:s.jsxs(l,{fullWidth:!0,children:[s.jsx(d,{children:"Class"}),s.jsxs(c,{value:T,onChange:e=>M(e.target.value),label:"Class",children:[s.jsx(o,{value:"9-A",children:"Class 9-A"}),s.jsx(o,{value:"9-B",children:"Class 9-B"}),s.jsx(o,{value:"10-A",children:"Class 10-A"}),s.jsx(o,{value:"10-B",children:"Class 10-B"})]})]})}),s.jsx(r,{item:!0,xs:12,md:3,children:s.jsxs(l,{fullWidth:!0,children:[s.jsx(a,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"Date"}),s.jsx("input",{type:"date",value:O,onChange:e=>E(e.target.value),style:{width:"100%",padding:"12px",border:`1px solid ${R.palette.divider}`,borderRadius:"4px",fontSize:"16px"}})]})}),s.jsx(r,{item:!0,xs:12,md:6,children:s.jsxs(x,{direction:"row",spacing:2,children:[s.jsx(h,{variant:"contained",startIcon:s.jsx(S,{}),onClick:async()=>{K(!0);try{await new Promise((e=>setTimeout(e,1e3))),alert("Attendance saved successfully!")}catch(e){}finally{K(!1)}},loading:G,sx:{background:`linear-gradient(135deg, ${R.palette.primary.main} 0%, ${R.palette.secondary.main} 100%)`},children:"Save Attendance"}),s.jsx(h,{variant:"outlined",startIcon:s.jsx(W,{}),children:"Print Report"}),s.jsx(h,{variant:"outlined",startIcon:s.jsx($,{}),children:"Export"})]})})]})})}),s.jsxs(r,{container:!0,spacing:3,sx:{mb:4},children:[s.jsx(r,{item:!0,xs:12,md:3,children:s.jsx(n,{sx:{background:`linear-gradient(135deg, ${R.palette.success.main} 0%, ${R.palette.success.dark} 100%)`,color:"white"},children:s.jsx(i,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(t,{children:[s.jsx(a,{variant:"h4",sx:{fontWeight:600},children:q.present}),s.jsx(a,{variant:"body2",sx:{opacity:.9},children:"Present"})]}),s.jsx(N,{sx:{fontSize:40,opacity:.8}})]})})})}),s.jsx(r,{item:!0,xs:12,md:3,children:s.jsx(n,{sx:{background:`linear-gradient(135deg, ${R.palette.error.main} 0%, ${R.palette.error.dark} 100%)`,color:"white"},children:s.jsx(i,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(t,{children:[s.jsx(a,{variant:"h4",sx:{fontWeight:600},children:q.absent}),s.jsx(a,{variant:"body2",sx:{opacity:.9},children:"Absent"})]}),s.jsx(I,{sx:{fontSize:40,opacity:.8}})]})})})}),s.jsx(r,{item:!0,xs:12,md:3,children:s.jsx(n,{sx:{background:`linear-gradient(135deg, ${R.palette.warning.main} 0%, ${R.palette.warning.dark} 100%)`,color:"white"},children:s.jsx(i,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(t,{children:[s.jsx(a,{variant:"h4",sx:{fontWeight:600},children:q.late}),s.jsx(a,{variant:"body2",sx:{opacity:.9},children:"Late"})]}),s.jsx(z,{sx:{fontSize:40,opacity:.8}})]})})})}),s.jsx(r,{item:!0,xs:12,md:3,children:s.jsx(n,{sx:{background:`linear-gradient(135deg, ${R.palette.info.main} 0%, ${R.palette.info.dark} 100%)`,color:"white"},children:s.jsx(i,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s.jsxs(t,{children:[s.jsx(a,{variant:"h4",sx:{fontWeight:600},children:P.length}),s.jsx(a,{variant:"body2",sx:{opacity:.9},children:"Total Students"})]}),s.jsx(B,{sx:{fontSize:40,opacity:.8}})]})})})})]}),s.jsx(n,{children:s.jsxs(i,{children:[s.jsxs(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:["Student Attendance - ",T," (",O,")"]}),s.jsx(j,{component:u,variant:"outlined",children:s.jsxs(m,{children:[s.jsx(p,{children:s.jsxs(g,{children:[s.jsx(b,{children:"Roll No."}),s.jsx(b,{children:"Student Name"}),s.jsx(b,{children:"Previous Attendance"}),s.jsx(b,{children:"Status"}),s.jsx(b,{children:"Actions"})]})}),s.jsx(f,{children:P.map((e=>{var n,i;return s.jsxs(g,{hover:!0,children:[s.jsx(b,{children:s.jsx(a,{variant:"body2",sx:{fontWeight:600},children:e.rollNumber})}),s.jsx(b,{children:s.jsxs(t,{sx:{display:"flex",alignItems:"center",gap:2},children:[s.jsx(y,{sx:{width:32,height:32},children:e.name.charAt(0)}),s.jsx(a,{variant:"body2",sx:{fontWeight:500},children:e.name})]})}),s.jsx(b,{children:s.jsx(a,{variant:"body2",children:e.lastAttendance})}),s.jsx(b,{children:s.jsx(v,{icon:U(D[e.id]),label:(null==(n=D[e.id])?void 0:n.charAt(0).toUpperCase())+(null==(i=D[e.id])?void 0:i.slice(1)),color:Q(D[e.id]),size:"small"})}),s.jsx(b,{children:s.jsxs(x,{direction:"row",spacing:1,children:[s.jsx(A,{size:"small",onClick:()=>L(e.id,"present"),color:"present"===D[e.id]?"success":"default",children:s.jsx(N,{})}),s.jsx(A,{size:"small",onClick:()=>L(e.id,"absent"),color:"absent"===D[e.id]?"error":"default",children:s.jsx(I,{})}),s.jsx(A,{size:"small",onClick:()=>L(e.id,"late"),color:"late"===D[e.id]?"warning":"default",children:s.jsx(z,{})})]})})]},e.id)}))})]})})]})})]})};export{R as default};
//# sourceMappingURL=AttendanceManagement-Cuapupr3.js.map
