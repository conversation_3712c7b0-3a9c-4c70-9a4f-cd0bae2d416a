{"version": 3, "file": "cellComment.js", "names": ["items", "_this", "opts", "for<PERSON>ach", "o", "i", "prototype", "validate", "val", "undefined", "name", "hasOwnProperty", "push", "TypeError", "join", "module", "exports"], "sources": ["../../../source/lib/types/cellComment.js"], "sourcesContent": ["//§18.18.5 ST_CellComments (Cell Comments)\n\nfunction items() {\n    this.opts = ['none', 'asDisplayed', 'atEnd'];\n    this.opts.forEach((o, i) => {\n        this[o] = i + 1;\n    });\n}\n\n\nitems.prototype.validate = function (val) {\n    if (this[val] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError('Invalid value for ST_CellComments; Value must be one of ' + this.opts.join(', '));\n    } else {\n        return true;\n    }\n};\n\nmodule.exports = new items();"], "mappings": ";;AAAA;;AAEA,SAASA,KAAKA,CAAA,EAAG;EAAA,IAAAC,KAAA;EACb,IAAI,CAACC,IAAI,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC;EAC5C,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACxBJ,KAAI,CAACG,CAAC,CAAC,GAAGC,CAAC,GAAG,CAAC;EACnB,CAAC,CAAC;AACN;AAGAL,KAAK,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACtC,IAAI,IAAI,CAACA,GAAG,CAAC,KAAKC,SAAS,EAAE;IACzB,IAAIP,IAAI,GAAG,EAAE;IACb,KAAK,IAAIQ,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BR,IAAI,CAACU,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIG,SAAS,CAAC,0DAA0D,GAAG,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,IAAI,CAAC,CAAC;EAC1G,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,IAAIhB,KAAK,CAAC,CAAC"}