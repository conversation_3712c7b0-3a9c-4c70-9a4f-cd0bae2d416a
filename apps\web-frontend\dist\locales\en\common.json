{"platformTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - Student SWOT Analysis Platform", "welcome": "Welcome", "language": "Language", "logout": "Logout", "login": "<PERSON><PERSON>", "dashboard": "Dashboard", "students": "Students", "reports": "Reports", "settings": "Settings", "profile": "Profile", "notifications": "Notifications", "help": "Help", "support": "Support", "about": "About", "contact": "Contact", "home": "Home", "back": "Back", "next": "Next", "previous": "Previous", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "remove": "Remove", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "print": "Print", "download": "Download", "upload": "Upload", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "submit": "Submit", "reset": "Reset", "clear": "Clear", "refresh": "Refresh", "retry": "Retry", "goHome": "Go to Dashboard", "notFoundTitle": "Page Not Found", "notFoundMessage": "The page you are looking for does not exist.", "errorBoundaryTitle": "Something went wrong", "errorBoundaryMessage": "An unexpected error occurred. Please refresh the page or contact support.", "networkError": "Network error. Please check your internet connection.", "serverError": "Server error. Please try again later.", "validationError": "Please check your input and try again.", "unauthorizedError": "You are not authorized to access this resource.", "forbiddenError": "Access denied. You don't have permission to perform this action.", "sessionExpired": "Your session has expired. Please login again.", "dataLoadError": "Failed to load data. Please try again.", "dataSaveError": "Failed to save data. Please try again.", "fileUploadError": "Failed to upload file. Please try again.", "fileDownloadError": "Failed to download file. Please try again.", "emailInvalid": "Please enter a valid email address.", "phoneInvalid": "Please enter a valid phone number.", "passwordWeak": "Password is too weak. Please choose a stronger password.", "passwordMismatch": "Passwords do not match.", "fieldRequired": "This field is required.", "fieldTooShort": "This field is too short.", "fieldTooLong": "This field is too long.", "dateInvalid": "Please enter a valid date.", "numberInvalid": "Please enter a valid number.", "urlInvalid": "Please enter a valid URL.", "selectOption": "Please select an option.", "noDataAvailable": "No data available.", "noResultsFound": "No results found.", "searchPlaceholder": "Type to search...", "selectAll": "Select All", "deselectAll": "Deselect All", "itemsSelected": "items selected", "showMore": "Show More", "showLess": "Show Less", "expand": "Expand", "collapse": "Collapse", "fullScreen": "Full Screen", "exitFullScreen": "Exit Full Screen", "copyToClipboard": "Copy to Clipboard", "copiedToClipboard": "Copied to clipboard", "shareLink": "Share Link", "emailLink": "Email Link", "printPage": "Print Page", "lastUpdated": "Last updated", "createdOn": "Created on", "modifiedOn": "Modified on", "version": "Version", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected", "available": "Available", "unavailable": "Unavailable", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "inProgress": "In Progress", "cancelled": "Cancelled", "draft": "Draft", "published": "Published", "archived": "Archived", "deleted": "Deleted", "getStarted": "Get Started", "learnMore": "Learn More", "backToHome": "Back to Home", "ourVision": "Our Vision", "ourMission": "Our Mission", "coreFeatures": "Core Features", "additionalCapabilities": "Additional Capabilities", "builtForIndianEducation": "Built for Indian Education", "experienceThePower": "Experience the Power of VidyaMitra", "tryVidyaMitra": "<PERSON>", "requestDemo": "Request Demo", "getInTouch": "Get In Touch", "sendMessage": "Send us a Message", "howCanWeHelp": "How Can We Help?", "frequentlyAskedQuestions": "Frequently Asked Questions", "readyToTransform": "Ready to Transform Education?", "joinThousands": "Join thousands of educators who are already using VidyaMitra to enhance student outcomes.", "getStartedToday": "Get Started Today", "designedForEveryStakeholder": "Designed for Every Stakeholder", "vidyamitraServes": "VidyaMitra serves the entire educational ecosystem with tailored solutions for each user type.", "powerfulFeaturesForIndianEducation": "Powerful Features for Indian Education", "designedSpecifically": "Designed specifically for the Indian education system with cultural sensitivity and local requirements in mind.", "empoweringIndianEducation": "Empowering Indian education through intelligent student analysis. Supporting CBSE, ICSE, and State boards with AI-powered insights.", "comprehensiveStudentAnalysis": "Comprehensive Student Analysis", "aiPoweredSWOTAnalysis": "AI-powered SWOT analysis tailored for Indian educational boards"}