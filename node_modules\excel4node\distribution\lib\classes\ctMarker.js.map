{"version": 3, "file": "ctMarker.js", "names": ["EMU", "require", "CTMarker", "colId", "colOffset", "rowId", "rowOffset", "_classCallCheck", "_col", "_colOff", "_row", "_rowOff", "_createClass", "key", "get", "set", "val", "parseInt", "TypeError", "value", "module", "exports"], "sources": ["../../../source/lib/classes/ctMarker.js"], "sourcesContent": ["let EMU = require('./emu.js');\n\nclass CTMarker {\n    /**\n     * Element representing an Excel position marker\n     * @param {Number} colId Column Number\n     * @param {String} colOffset Offset stating how far right to shift the start edge\n     * @param {Number} rowId Row Number\n     * @param {String} rowOffset Offset stating how far down to shift the start edge\n     * @property {Number} col Column number\n     * @property {EMU} colOff EMUs of right shift\n     * @property {Number} row Row number\n     * @property {EMU} rowOff EMUs of top shift\n     * @returns {CTMarker} Excel CTMarker \n     */\n    constructor(colId, colOffset, rowId, rowOffset) {\n        this._col = colId;\n        this._colOff = new EMU(colOffset);\n        this._row = rowId;\n        this._rowOff = new EMU(rowOffset);\n    }\n\n    get col() {\n        return this._col;\n    }\n    set col(val) {\n        if (parseInt(val, 10) !== val || val < 0) {\n            throw new TypeError('CTMarker column must be a positive integer');\n        }\n        this._col = val;\n    }\n\n    get row() {\n        return this._row;\n    }\n    set row(val) {\n        if (parseInt(val, 10) !== val || val < 0) {\n            throw new TypeError('CTMarker row must be a positive integer');\n        }\n        this._row = val;\n    }\n\n    get colOff() {\n        return this._colOff.value;\n    }\n    set colOff(val) {\n        this._colOff = new EMU(val);\n    }\n\n    get rowOff() {\n        return this._rowOff.value;\n    }\n    set rowOff(val) {\n        this._rowOff = new EMU(val);\n    }\n}\n\nmodule.exports = CTMarker;"], "mappings": ";;;;;AAAA,IAAIA,GAAG,GAAGC,OAAO,CAAC,UAAU,CAAC;AAAC,IAExBC,QAAQ;EACV;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,SAAYC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAE;IAAAC,eAAA,OAAAL,QAAA;IAC5C,IAAI,CAACM,IAAI,GAAGL,KAAK;IACjB,IAAI,CAACM,OAAO,GAAG,IAAIT,GAAG,CAACI,SAAS,CAAC;IACjC,IAAI,CAACM,IAAI,GAAGL,KAAK;IACjB,IAAI,CAACM,OAAO,GAAG,IAAIX,GAAG,CAACM,SAAS,CAAC;EACrC;EAACM,YAAA,CAAAV,QAAA;IAAAW,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAU;MACN,OAAO,IAAI,CAACN,IAAI;IACpB,CAAC;IAAAO,GAAA,EACD,SAAAA,IAAQC,GAAG,EAAE;MACT,IAAIC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC,KAAKA,GAAG,IAAIA,GAAG,GAAG,CAAC,EAAE;QACtC,MAAM,IAAIE,SAAS,CAAC,4CAA4C,CAAC;MACrE;MACA,IAAI,CAACV,IAAI,GAAGQ,GAAG;IACnB;EAAC;IAAAH,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAU;MACN,OAAO,IAAI,CAACJ,IAAI;IACpB,CAAC;IAAAK,GAAA,EACD,SAAAA,IAAQC,GAAG,EAAE;MACT,IAAIC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC,KAAKA,GAAG,IAAIA,GAAG,GAAG,CAAC,EAAE;QACtC,MAAM,IAAIE,SAAS,CAAC,yCAAyC,CAAC;MAClE;MACA,IAAI,CAACR,IAAI,GAAGM,GAAG;IACnB;EAAC;IAAAH,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,OAAO,IAAI,CAACL,OAAO,CAACU,KAAK;IAC7B,CAAC;IAAAJ,GAAA,EACD,SAAAA,IAAWC,GAAG,EAAE;MACZ,IAAI,CAACP,OAAO,GAAG,IAAIT,GAAG,CAACgB,GAAG,CAAC;IAC/B;EAAC;IAAAH,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,OAAO,IAAI,CAACH,OAAO,CAACQ,KAAK;IAC7B,CAAC;IAAAJ,GAAA,EACD,SAAAA,IAAWC,GAAG,EAAE;MACZ,IAAI,CAACL,OAAO,GAAG,IAAIX,GAAG,CAACgB,GAAG,CAAC;IAC/B;EAAC;EAAA,OAAAd,QAAA;AAAA;AAGLkB,MAAM,CAACC,OAAO,GAAGnB,QAAQ"}