{"version": 3, "file": "positiveUniversalMeasure.js", "names": ["measure", "prototype", "validate", "val", "re", "RegExp", "test", "TypeError", "module", "exports"], "sources": ["../../../source/lib/types/positiveUniversalMeasure.js"], "sourcesContent": ["//§22.9.2.12 ST_PositiveUniversalMeasure (Positive Universal Measurement)\n\nfunction measure() {\n}\n\nmeasure.prototype.validate = function (val) {\n    let re = new RegExp('[0-9]+(\\.[0-9]+)?(mm|cm|in|pt|pc|pi)');\n    if (re.test(val) !== true) {\n        throw new TypeError('Invalid value for universal positive measure. Value must a positive Float immediately followed by unit of measure from list mm, cm, in, pt, pc, pi. i.e. 10.5cm');\n    } else {\n        return true;\n    }\n};\n\nmodule.exports = new measure();"], "mappings": ";;AAAA;;AAEA,SAASA,OAAOA,CAAA,EAAG,CACnB;AAEAA,OAAO,CAACC,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACxC,IAAIC,EAAE,GAAG,IAAIC,MAAM,CAAC,sCAAsC,CAAC;EAC3D,IAAID,EAAE,CAACE,IAAI,CAACH,GAAG,CAAC,KAAK,IAAI,EAAE;IACvB,MAAM,IAAII,SAAS,CAAC,iKAAiK,CAAC;EAC1L,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,IAAIT,OAAO,CAAC,CAAC"}