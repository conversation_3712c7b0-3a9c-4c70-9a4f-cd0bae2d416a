{"version": 3, "file": "comment.js", "names": ["_require", "require", "uuid", "v4", "utils", "Comment", "_createClass", "ref", "comment", "options", "arguments", "length", "undefined", "_classCallCheck", "toUpperCase", "row", "getExcelRowCol", "col", "marginLeft", "marginTop", "width", "height", "position", "zIndex", "fillColor", "visibility", "module", "exports"], "sources": ["../../../source/lib/classes/comment.js"], "sourcesContent": ["// const uuid = require('uuid/v4');\nconst { v4: uuid } = require('uuid');\nconst utils = require('../utils');\n\n// §18.7.3 Comment\nclass Comment {\n    constructor(ref, comment, options = {}) {\n        this.ref = ref;\n        this.comment = comment;\n        this.uuid = '{' + uuid().toUpperCase() + '}';\n        this.row = utils.getExcelRowCol(ref).row;\n        this.col = utils.getExcelRowCol(ref).col;\n        this.marginLeft = options.marginLeft || ((this.col) * 88 + 8) + 'pt';\n        this.marginTop = options.marginTop || ((this.row - 1) * 16 + 8) + 'pt';\n        this.width = options.width || '104pt';\n        this.height = options.height || '69pt';\n        this.position = options.position || 'absolute';\n        this.zIndex = options.zIndex || '1';\n        this.fillColor = options.fillColor || '#ffffe1';\n        this.visibility = options.visibility || 'hidden';\n    }\n\n}\n\nmodule.exports = Comment;\n"], "mappings": ";;;;;AAAA;AACA,IAAAA,QAAA,GAAqBC,OAAO,CAAC,MAAM,CAAC;EAAxBC,IAAI,GAAAF,QAAA,CAARG,EAAE;AACV,IAAMC,KAAK,GAAGH,OAAO,CAAC,UAAU,CAAC;;AAEjC;AAAA,IACMI,OAAO,gBAAAC,YAAA,CACT,SAAAD,QAAYE,GAAG,EAAEC,OAAO,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAAG,eAAA,OAAAR,OAAA;EAClC,IAAI,CAACE,GAAG,GAAGA,GAAG;EACd,IAAI,CAACC,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACN,IAAI,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC,CAACY,WAAW,CAAC,CAAC,GAAG,GAAG;EAC5C,IAAI,CAACC,GAAG,GAAGX,KAAK,CAACY,cAAc,CAACT,GAAG,CAAC,CAACQ,GAAG;EACxC,IAAI,CAACE,GAAG,GAAGb,KAAK,CAACY,cAAc,CAACT,GAAG,CAAC,CAACU,GAAG;EACxC,IAAI,CAACC,UAAU,GAAGT,OAAO,CAACS,UAAU,IAAM,IAAI,CAACD,GAAG,GAAI,EAAE,GAAG,CAAC,GAAI,IAAI;EACpE,IAAI,CAACE,SAAS,GAAGV,OAAO,CAACU,SAAS,IAAK,CAAC,IAAI,CAACJ,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAI,IAAI;EACtE,IAAI,CAACK,KAAK,GAAGX,OAAO,CAACW,KAAK,IAAI,OAAO;EACrC,IAAI,CAACC,MAAM,GAAGZ,OAAO,CAACY,MAAM,IAAI,MAAM;EACtC,IAAI,CAACC,QAAQ,GAAGb,OAAO,CAACa,QAAQ,IAAI,UAAU;EAC9C,IAAI,CAACC,MAAM,GAAGd,OAAO,CAACc,MAAM,IAAI,GAAG;EACnC,IAAI,CAACC,SAAS,GAAGf,OAAO,CAACe,SAAS,IAAI,SAAS;EAC/C,IAAI,CAACC,UAAU,GAAGhB,OAAO,CAACgB,UAAU,IAAI,QAAQ;AACpD,CAAC;AAILC,MAAM,CAACC,OAAO,GAAGtB,OAAO"}