/**
 * VidyaMitra Platform - Enhanced Card Component
 * 
 * Interactive card component with glassmorphism design, hover effects,
 * and proper accessibility for the Indian educational context
 */

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Box,
  useTheme,
  alpha,
  Skeleton,
} from '@mui/material';
import { motion } from 'framer-motion';

const EnhancedCard = ({
  children,
  onClick,
  elevation = 2,
  variant = 'default', // 'default', 'glassmorphism', 'gradient'
  loading = false,
  disabled = false,
  hoverable = true,
  clickable = false,
  sx = {},
  contentSx = {},
  actions,
  header,
  footer,
  color = 'primary',
  gradient = false,
  glassmorphism = false,
  borderRadius = 3,
  ...props
}) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = (event) => {
    if (disabled || loading || !onClick) return;
    onClick(event);
  };

  const getCardBackground = () => {
    if (glassmorphism) {
      return theme.palette.mode === 'dark'
        ? `linear-gradient(135deg, ${alpha('#1E293B', 0.8)} 0%, ${alpha('#334155', 0.6)} 100%)`
        : `linear-gradient(135deg, ${alpha('#FFFFFF', 0.9)} 0%, ${alpha('#F8FAFC', 0.8)} 100%)`;
    }

    if (gradient) {
      return `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`;
    }

    return theme.palette.background.paper;
  };

  const cardVariants = {
    initial: { 
      scale: 1, 
      y: 0,
      boxShadow: theme.shadows[elevation],
    },
    hover: hoverable ? { 
      scale: 1.02, 
      y: -8,
      boxShadow: theme.shadows[Math.min(elevation + 4, 24)],
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 20,
      },
    } : {},
    tap: clickable ? {
      scale: 0.98,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 25,
      },
    } : {},
  };

  const enhancedSx = {
    borderRadius: borderRadius,
    background: getCardBackground(),
    backdropFilter: glassmorphism ? 'blur(20px)' : 'none',
    border: glassmorphism 
      ? `1px solid ${alpha(theme.palette.divider, 0.1)}`
      : 'none',
    cursor: clickable ? 'pointer' : 'default',
    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    position: 'relative',
    overflow: 'hidden',
    
    // Hover effects
    '&:hover': hoverable ? {
      '& .card-content': {
        transform: 'translateY(-2px)',
      },
      '& .card-actions': {
        opacity: 1,
        transform: 'translateY(0)',
      },
    } : {},

    // Focus state for accessibility
    ...(clickable && {
      '&:focus-visible': {
        outline: `2px solid ${theme.palette[color].main}`,
        outlineOffset: '2px',
      },
    }),

    // Disabled state
    ...(disabled && {
      opacity: 0.6,
      pointerEvents: 'none',
    }),

    // Loading state
    ...(loading && {
      pointerEvents: 'none',
    }),

    ...sx,
  };

  const contentEnhancedSx = {
    transition: 'transform 0.3s ease',
    ...contentSx,
  };

  const LoadingSkeleton = () => (
    <CardContent>
      <Skeleton variant="text" width="60%" height={32} sx={{ mb: 2 }} />
      <Skeleton variant="text" width="100%" height={20} sx={{ mb: 1 }} />
      <Skeleton variant="text" width="80%" height={20} sx={{ mb: 2 }} />
      <Skeleton variant="rectangular" width="100%" height={120} sx={{ borderRadius: 2 }} />
    </CardContent>
  );

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      whileHover={!disabled && !loading ? "hover" : "initial"}
      whileTap={!disabled && !loading && clickable ? "tap" : "initial"}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card
        elevation={0}
        onClick={handleClick}
        sx={enhancedSx}
        tabIndex={clickable ? 0 : -1}
        role={clickable ? 'button' : undefined}
        {...props}
      >
        {/* Glassmorphism overlay effect */}
        {glassmorphism && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `linear-gradient(135deg, ${alpha('#FFFFFF', 0.1)} 0%, ${alpha('#FFFFFF', 0.05)} 100%)`,
              pointerEvents: 'none',
              zIndex: 0,
            }}
          />
        )}

        {/* Header */}
        {header && (
          <Box sx={{ position: 'relative', zIndex: 1 }}>
            {header}
          </Box>
        )}

        {/* Content */}
        <CardContent 
          className="card-content"
          sx={contentEnhancedSx}
          style={{ position: 'relative', zIndex: 1 }}
        >
          {loading ? <LoadingSkeleton /> : children}
        </CardContent>

        {/* Actions */}
        {actions && (
          <CardActions 
            className="card-actions"
            sx={{
              position: 'relative',
              zIndex: 1,
              opacity: hoverable ? 0.8 : 1,
              transform: hoverable ? 'translateY(4px)' : 'none',
              transition: 'all 0.3s ease',
            }}
          >
            {actions}
          </CardActions>
        )}

        {/* Footer */}
        {footer && (
          <Box sx={{ position: 'relative', zIndex: 1 }}>
            {footer}
          </Box>
        )}

        {/* Ripple effect for clickable cards */}
        {clickable && isHovered && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              width: 0,
              height: 0,
              borderRadius: '50%',
              background: alpha(theme.palette[color].main, 0.1),
              transform: 'translate(-50%, -50%)',
              animation: 'ripple 0.6s linear',
              '@keyframes ripple': {
                to: {
                  width: '200%',
                  height: '200%',
                  opacity: 0,
                },
              },
            }}
          />
        )}
      </Card>
    </motion.div>
  );
};

export default EnhancedCard;
