{"version": 3, "file": "index.js", "names": ["Drawing", "require", "Picture", "DrawingCollection", "_classCallCheck", "drawings", "_createClass", "key", "get", "length", "value", "add", "opts", "type", "newPic", "push", "TypeError", "module", "exports"], "sources": ["../../../source/lib/drawing/index.js"], "sourcesContent": ["let Drawing = require('./drawing.js');\nlet Picture = require('./picture.js');\n\nclass DrawingCollection {\n    constructor() {\n        this.drawings = [];\n    }\n\n    get length() {\n        return this.drawings.length;\n    }\n\n    add(opts) {\n        switch (opts.type) {\n        case 'picture':\n            let newPic = new Picture(opts);\n            this.drawings.push(newPic);\n            return newPic;\n\n        default:\n            throw new TypeError('this option is not yet supported');\n        }\n    }\n\n    get isEmpty() {\n        if (this.drawings.length === 0) {\n            return true;\n        } else {\n            return false;\n        }\n    }\n}\n\nmodule.exports = { DrawingCollection, Drawing, Picture };\n"], "mappings": ";;;;;AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACrC,IAAIC,OAAO,GAAGD,OAAO,CAAC,cAAc,CAAC;AAAC,IAEhCE,iBAAiB;EACnB,SAAAA,kBAAA,EAAc;IAAAC,eAAA,OAAAD,iBAAA;IACV,IAAI,CAACE,QAAQ,GAAG,EAAE;EACtB;EAACC,YAAA,CAAAH,iBAAA;IAAAI,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,OAAO,IAAI,CAACH,QAAQ,CAACI,MAAM;IAC/B;EAAC;IAAAF,GAAA;IAAAG,KAAA,EAED,SAAAC,IAAIC,IAAI,EAAE;MACN,QAAQA,IAAI,CAACC,IAAI;QACjB,KAAK,SAAS;UACV,IAAIC,MAAM,GAAG,IAAIZ,OAAO,CAACU,IAAI,CAAC;UAC9B,IAAI,CAACP,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC;UAC1B,OAAOA,MAAM;QAEjB;UACI,MAAM,IAAIE,SAAS,CAAC,kCAAkC,CAAC;MAC3D;IACJ;EAAC;IAAAT,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAc;MACV,IAAI,IAAI,CAACH,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;QAC5B,OAAO,IAAI;MACf,CAAC,MAAM;QACH,OAAO,KAAK;MAChB;IACJ;EAAC;EAAA,OAAAN,iBAAA;AAAA;AAGLc,MAAM,CAACC,OAAO,GAAG;EAAEf,iBAAiB,EAAjBA,iBAAiB;EAAEH,OAAO,EAAPA,OAAO;EAAEE,OAAO,EAAPA;AAAQ,CAAC"}