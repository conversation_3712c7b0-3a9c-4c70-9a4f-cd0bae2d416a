{"version": 3, "file": "SWOTWizard-cbD3CjwT.js", "sources": ["../../src/components/SWOT/SWOTWizard.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - SWOT Analysis Wizard Component\n * \n * Interactive SWOT generation interface with AI-powered suggestions\n * and Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Button,\n  TextField,\n  Grid,\n  Chip,\n  Avatar,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  IconButton,\n  Alert,\n  Stack,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Psychology,\n  TrendingUp,\n  Warning,\n  Lightbulb,\n  Add,\n  Delete,\n  Save,\n  ArrowBack,\n  ArrowForward,\n  CheckCircle,\n  AutoAwesome,\n} from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\n\n// Sample AI suggestions for Indian educational context\nconst aiSuggestions = {\n  strengths: [\n    'Strong mathematical reasoning skills',\n    'Excellent memory and retention',\n    'Good leadership qualities in group activities',\n    'Consistent academic performance',\n    'Active participation in cultural events',\n    'Strong family support system',\n    'Multilingual communication abilities',\n  ],\n  weaknesses: [\n    'Needs improvement in English speaking confidence',\n    'Time management during examinations',\n    'Hesitant to ask questions in class',\n    'Difficulty with practical applications',\n    'Limited exposure to technology',\n    'Peer pressure sensitivity',\n  ],\n  opportunities: [\n    'Science Olympiad participation',\n    'Student council leadership roles',\n    'Inter-school cultural competitions',\n    'STEM career exploration programs',\n    'Scholarship opportunities for higher education',\n    'Skill development workshops',\n    'Community service projects',\n  ],\n  threats: [\n    'Increased academic competition',\n    'Board examination pressure',\n    'Limited career guidance resources',\n    'Technology adaptation challenges',\n    'Economic constraints for higher education',\n    'Social media distractions',\n  ],\n};\n\nconst SWOTWizard = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const [activeStep, setActiveStep] = useState(0);\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [swotData, setSWOTData] = useState({\n    strengths: [],\n    weaknesses: [],\n    opportunities: [],\n    threats: [],\n  });\n  const [currentInput, setCurrentInput] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const steps = [\n    {\n      label: 'Select Student',\n      icon: Psychology,\n      description: 'Choose student for SWOT analysis',\n    },\n    {\n      label: 'Strengths',\n      icon: TrendingUp,\n      description: 'Identify student strengths',\n    },\n    {\n      label: 'Weaknesses',\n      icon: Warning,\n      description: 'Areas for improvement',\n    },\n    {\n      label: 'Opportunities',\n      icon: Lightbulb,\n      description: 'Growth opportunities',\n    },\n    {\n      label: 'Threats',\n      icon: Warning,\n      description: 'Potential challenges',\n    },\n    {\n      label: 'Review & Save',\n      icon: CheckCircle,\n      description: 'Finalize SWOT analysis',\n    },\n  ];\n\n  // Sample students\n  const students = [\n    { id: 1, name: 'Sanju Kumar Reddy', class: '10-A', avatar: 'S' },\n    { id: 2, name: 'Niraimathi Selvam', class: '10-A', avatar: 'N' },\n    { id: 3, name: 'Mahesh Reddy', class: '10-B', avatar: 'M' },\n    { id: 4, name: 'Ravi Teja Sharma', class: '9-A', avatar: 'R' },\n    { id: 5, name: 'Ankitha Patel', class: '10-A', avatar: 'A' },\n  ];\n\n  const getCurrentCategory = () => {\n    const categories = ['', 'strengths', 'weaknesses', 'opportunities', 'threats'];\n    return categories[activeStep];\n  };\n\n  const handleAddItem = () => {\n    if (currentInput.trim() && activeStep >= 1 && activeStep <= 4) {\n      const category = getCurrentCategory();\n      setSWOTData(prev => ({\n        ...prev,\n        [category]: [...prev[category], currentInput.trim()],\n      }));\n      setCurrentInput('');\n    }\n  };\n\n  const handleRemoveItem = (category, index) => {\n    setSWOTData(prev => ({\n      ...prev,\n      [category]: prev[category].filter((_, i) => i !== index),\n    }));\n  };\n\n  const handleAddSuggestion = (category, suggestion) => {\n    if (!swotData[category].includes(suggestion)) {\n      setSWOTData(prev => ({\n        ...prev,\n        [category]: [...prev[category], suggestion],\n      }));\n    }\n  };\n\n  const handleNext = () => {\n    if (activeStep < steps.length - 1) {\n      setActiveStep(prev => prev + 1);\n    }\n  };\n\n  const handleBack = () => {\n    if (activeStep > 0) {\n      setActiveStep(prev => prev - 1);\n    }\n  };\n\n  const handleSave = async () => {\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      alert('SWOT Analysis saved successfully!');\n      navigate('/dashboard/students');\n    } catch (error) {\n      console.error('Error saving SWOT analysis:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getCategoryColor = (category) => {\n    switch (category) {\n      case 'strengths':\n        return theme.palette.success.main;\n      case 'weaknesses':\n        return theme.palette.error.main;\n      case 'opportunities':\n        return theme.palette.info.main;\n      case 'threats':\n        return theme.palette.warning.main;\n      default:\n        return theme.palette.primary.main;\n    }\n  };\n\n  const getCategoryIcon = (category) => {\n    switch (category) {\n      case 'strengths':\n        return '💪';\n      case 'weaknesses':\n        return '⚠️';\n      case 'opportunities':\n        return '🚀';\n      case 'threats':\n        return '⚡';\n      default:\n        return '📝';\n    }\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            SWOT Analysis Wizard\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Create comprehensive SWOT analysis with AI-powered suggestions\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Stepper */}\n      <Card sx={{ mb: 4, overflow: 'visible' }}>\n        <CardContent>\n          <Stepper activeStep={activeStep} alternativeLabel>\n            {steps.map((step, index) => (\n              <Step key={step.label}>\n                <StepLabel\n                  StepIconComponent={({ active, completed }) => (\n                    <Box\n                      sx={{\n                        width: 48,\n                        height: 48,\n                        borderRadius: '50%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        background: completed\n                          ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`\n                          : active\n                          ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`\n                          : alpha(theme.palette.action.disabled, 0.12),\n                        color: completed || active ? 'white' : theme.palette.action.disabled,\n                        transition: 'all 0.3s ease',\n                      }}\n                    >\n                      <step.icon sx={{ fontSize: 24 }} />\n                    </Box>\n                  )}\n                >\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 500 }}>\n                    {step.label}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {step.description}\n                  </Typography>\n                </StepLabel>\n              </Step>\n            ))}\n          </Stepper>\n        </CardContent>\n      </Card>\n\n      {/* Step Content */}\n      <Card>\n        <CardContent sx={{ p: 4 }}>\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={activeStep}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {/* Student Selection */}\n              {activeStep === 0 && (\n                <Box>\n                  <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n                    Select Student for SWOT Analysis\n                  </Typography>\n                  <Grid container spacing={2}>\n                    {students.map((student) => (\n                      <Grid item xs={12} sm={6} md={4} key={student.id}>\n                        <Card\n                          sx={{\n                            cursor: 'pointer',\n                            border: selectedStudent?.id === student.id ? `2px solid ${theme.palette.primary.main}` : '1px solid',\n                            borderColor: selectedStudent?.id === student.id ? 'primary.main' : 'divider',\n                            transition: 'all 0.2s ease',\n                            '&:hover': {\n                              transform: 'translateY(-2px)',\n                              boxShadow: theme.shadows[4],\n                            },\n                          }}\n                          onClick={() => setSelectedStudent(student)}\n                        >\n                          <CardContent>\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                              <Avatar sx={{ bgcolor: 'primary.main' }}>\n                                {student.avatar}\n                              </Avatar>\n                              <Box>\n                                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600 }}>\n                                  {student.name}\n                                </Typography>\n                                <Typography variant=\"body2\" color=\"text.secondary\">\n                                  Class {student.class}\n                                </Typography>\n                              </Box>\n                            </Box>\n                          </CardContent>\n                        </Card>\n                      </Grid>\n                    ))}\n                  </Grid>\n                </Box>\n              )}\n\n              {/* SWOT Categories */}\n              {activeStep >= 1 && activeStep <= 4 && (\n                <Box>\n                  <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n                    {getCategoryIcon(getCurrentCategory())} {steps[activeStep].label}\n                  </Typography>\n                  \n                  {/* Input Section */}\n                  <Box sx={{ mb: 4 }}>\n                    <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n                      <TextField\n                        fullWidth\n                        label={`Add ${steps[activeStep].label.toLowerCase()}`}\n                        value={currentInput}\n                        onChange={(e) => setCurrentInput(e.target.value)}\n                        onKeyPress={(e) => e.key === 'Enter' && handleAddItem()}\n                        placeholder={`Enter student ${steps[activeStep].label.toLowerCase()}...`}\n                      />\n                      <Button\n                        variant=\"contained\"\n                        startIcon={<Add />}\n                        onClick={handleAddItem}\n                        disabled={!currentInput.trim()}\n                      >\n                        Add\n                      </Button>\n                    </Stack>\n                  </Box>\n\n                  <Grid container spacing={3}>\n                    {/* Current Items */}\n                    <Grid item xs={12} md={6}>\n                      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 600 }}>\n                        Current {steps[activeStep].label}\n                      </Typography>\n                      <List>\n                        {swotData[getCurrentCategory()]?.map((item, index) => (\n                          <ListItem\n                            key={index}\n                            sx={{\n                              border: `1px solid ${alpha(getCategoryColor(getCurrentCategory()), 0.2)}`,\n                              borderRadius: 1,\n                              mb: 1,\n                              background: alpha(getCategoryColor(getCurrentCategory()), 0.05),\n                            }}\n                          >\n                            <ListItemText primary={item} />\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleRemoveItem(getCurrentCategory(), index)}\n                              color=\"error\"\n                            >\n                              <Delete />\n                            </IconButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                    </Grid>\n\n                    {/* AI Suggestions */}\n                    <Grid item xs={12} md={6}>\n                      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 600 }}>\n                        <AutoAwesome sx={{ mr: 1, verticalAlign: 'middle' }} />\n                        AI Suggestions\n                      </Typography>\n                      <List>\n                        {aiSuggestions[getCurrentCategory()]?.map((suggestion, index) => (\n                          <ListItem\n                            key={index}\n                            sx={{\n                              border: '1px solid',\n                              borderColor: 'divider',\n                              borderRadius: 1,\n                              mb: 1,\n                              cursor: 'pointer',\n                              '&:hover': {\n                                background: alpha(theme.palette.primary.main, 0.05),\n                              },\n                            }}\n                            onClick={() => handleAddSuggestion(getCurrentCategory(), suggestion)}\n                          >\n                            <ListItemIcon>\n                              <Add color=\"primary\" />\n                            </ListItemIcon>\n                            <ListItemText primary={suggestion} />\n                          </ListItem>\n                        ))}\n                      </List>\n                    </Grid>\n                  </Grid>\n                </Box>\n              )}\n\n              {/* Review Step */}\n              {activeStep === 5 && (\n                <Box>\n                  <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n                    Review SWOT Analysis\n                  </Typography>\n                  \n                  {selectedStudent && (\n                    <Alert severity=\"info\" sx={{ mb: 3 }}>\n                      SWOT Analysis for <strong>{selectedStudent.name}</strong> - Class {selectedStudent.class}\n                    </Alert>\n                  )}\n\n                  <Grid container spacing={3}>\n                    {Object.entries(swotData).map(([category, items]) => (\n                      <Grid item xs={12} md={6} key={category}>\n                        <Card\n                          sx={{\n                            border: `2px solid ${alpha(getCategoryColor(category), 0.2)}`,\n                            background: `linear-gradient(135deg, ${alpha(getCategoryColor(category), 0.05)} 0%, ${alpha(getCategoryColor(category), 0.02)} 100%)`,\n                          }}\n                        >\n                          <CardContent>\n                            <Typography\n                              variant=\"h6\"\n                              sx={{\n                                fontWeight: 600,\n                                color: getCategoryColor(category),\n                                mb: 2,\n                              }}\n                            >\n                              {getCategoryIcon(category)} {category.charAt(0).toUpperCase() + category.slice(1)}\n                            </Typography>\n                            <List dense>\n                              {items.map((item, index) => (\n                                <ListItem key={index}>\n                                  <ListItemText primary={`• ${item}`} />\n                                </ListItem>\n                              ))}\n                            </List>\n                          </CardContent>\n                        </Card>\n                      </Grid>\n                    ))}\n                  </Grid>\n                </Box>\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Navigation Buttons */}\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n            <Button\n              onClick={handleBack}\n              disabled={activeStep === 0}\n              startIcon={<ArrowBack />}\n              variant=\"outlined\"\n            >\n              Back\n            </Button>\n            \n            <Button\n              onClick={activeStep === steps.length - 1 ? handleSave : handleNext}\n              endIcon={activeStep === steps.length - 1 ? <Save /> : <ArrowForward />}\n              variant=\"contained\"\n              disabled={activeStep === 0 && !selectedStudent}\n              loading={loading}\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              }}\n            >\n              {activeStep === steps.length - 1 ? 'Save SWOT Analysis' : 'Next'}\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default SWOTWizard;\n"], "names": ["aiSuggestions", "strengths", "weaknesses", "opportunities", "threats", "SWOTWizard", "theme", "useTheme", "navigate", "useNavigate", "activeStep", "setActiveStep", "useState", "selectedStudent", "setSelectedStudent", "swotData", "setSWOTData", "currentInput", "setCurrentInput", "loading", "setLoading", "steps", "label", "icon", "Psychology", "description", "TrendingUp", "Warning", "Lightbulb", "CheckCircle", "getCurrentCategory", "handleAddItem", "trim", "category", "prev", "getCategoryColor", "palette", "success", "main", "error", "info", "warning", "primary", "getCategoryIcon", "Box", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "jsxRuntimeExports", "jsx", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "mb", "Typography", "variant", "fontWeight", "background", "secondary", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "color", "Card", "overflow", "<PERSON><PERSON><PERSON><PERSON>", "Stepper", "alternativeLabel", "map", "step", "index", "Step", "jsxs", "<PERSON><PERSON><PERSON><PERSON>", "StepIconComponent", "active", "completed", "width", "height", "borderRadius", "display", "alignItems", "justifyContent", "dark", "alpha", "action", "disabled", "fontSize", "AnimatePresence", "mode", "x", "exit", "Grid", "container", "spacing", "id", "name", "class", "avatar", "student", "item", "xs", "sm", "md", "cursor", "border", "borderColor", "transform", "boxShadow", "shadows", "onClick", "gap", "Avatar", "bgcolor", "<PERSON><PERSON>", "direction", "TextField", "fullWidth", "toLowerCase", "value", "onChange", "e", "target", "onKeyPress", "key", "placeholder", "<PERSON><PERSON>", "startIcon", "Add", "List", "_a", "ListItem", "ListItemText", "IconButton", "size", "filter", "_", "i", "handleRemoveItem", "Delete", "AutoAwesome", "mr", "verticalAlign", "_b", "suggestion", "includes", "handleAddSuggestion", "ListItemIcon", "<PERSON><PERSON>", "severity", "Object", "entries", "items", "char<PERSON>t", "toUpperCase", "slice", "dense", "mt", "ArrowBack", "length", "async", "Promise", "resolve", "setTimeout", "alert", "endIcon", "Save", "ArrowForward"], "mappings": "udAgDA,MAAMA,EAAgB,CACpBC,UAAW,CACT,uCACA,iCACA,gDACA,kCACA,0CACA,+BACA,wCAEFC,WAAY,CACV,mDACA,sCACA,qCACA,yCACA,iCACA,6BAEFC,cAAe,CACb,iCACA,mCACA,qCACA,mCACA,iDACA,8BACA,8BAEFC,QAAS,CACP,iCACA,6BACA,oCACA,mCACA,4CACA,8BAIEC,EAAa,aACjB,MAAMC,EAAQC,IACRC,EAAWC,KACVC,EAAYC,GAAiBC,EAAAA,SAAS,IACtCC,EAAiBC,GAAsBF,EAAAA,SAAS,OAChDG,EAAUC,GAAeJ,WAAS,CACvCX,UAAW,GACXC,WAAY,GACZC,cAAe,GACfC,QAAS,MAEJa,EAAcC,GAAmBN,EAAAA,SAAS,KAC1CO,EAASC,GAAcR,EAAAA,UAAS,GAEjCS,EAAQ,CACZ,CACEC,MAAO,iBACPC,KAAMC,EACNC,YAAa,oCAEf,CACEH,MAAO,YACPC,KAAMG,EACND,YAAa,8BAEf,CACEH,MAAO,aACPC,KAAMI,EACNF,YAAa,yBAEf,CACEH,MAAO,gBACPC,KAAMK,EACNH,YAAa,wBAEf,CACEH,MAAO,UACPC,KAAMI,EACNF,YAAa,wBAEf,CACEH,MAAO,gBACPC,KAAMM,EACNJ,YAAa,2BAaXK,EAAqB,IACN,CAAC,GAAI,YAAa,aAAc,gBAAiB,WAClDpB,GAGdqB,EAAgB,KACpB,GAAId,EAAae,QAAUtB,GAAc,GAAKA,GAAc,EAAG,CAC7D,MAAMuB,EAAWH,IACjBd,GAAqBkB,IAAA,IAChBA,EACHD,CAACA,GAAW,IAAIC,EAAKD,GAAWhB,EAAae,YAE/Cd,EAAgB,GAAE,GA8ChBiB,GAAoBF,IACxB,OAAQA,GACN,IAAK,YACI,OAAA3B,EAAM8B,QAAQC,QAAQC,KAC/B,IAAK,aACI,OAAAhC,EAAM8B,QAAQG,MAAMD,KAC7B,IAAK,gBACI,OAAAhC,EAAM8B,QAAQI,KAAKF,KAC5B,IAAK,UACI,OAAAhC,EAAM8B,QAAQK,QAAQH,KAC/B,QACS,OAAAhC,EAAM8B,QAAQM,QAAQJ,KAAA,EAI7BK,GAAmBV,IACvB,OAAQA,GACN,IAAK,YACI,MAAA,KACT,IAAK,aACI,MAAA,KACT,IAAK,gBACI,MAAA,KACT,IAAK,UACI,MAAA,IACT,QACS,MAAA,KAAA,EAKX,cAACW,EAAI,CAAAC,GAAI,CAAEC,SAAU,KAAMC,GAAI,OAAQC,EAAG,GAExCC,SAAA,CAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,GAAO,IAC9BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAExBV,gBAACL,EAAI,CAAAC,GAAI,CAAEe,GAAI,GACbX,SAAA,CAAAC,EAAAC,IAACU,EAAA,CACCC,QAAQ,KACRjB,GAAI,CACFkB,WAAY,IACZH,GAAI,EACJI,WAAY,2BAA2B1D,EAAM8B,QAAQM,QAAQJ,YAAYhC,EAAM8B,QAAQ6B,UAAU3B,aACjG4B,qBAAsB,OACtBC,oBAAqB,cACrBC,eAAgB,QAEnBnB,SAAA,+BAGAY,EAAW,CAAAC,QAAQ,QAAQO,MAAM,iBAAiBpB,SAEnD,wEAKJE,EAAAA,IAACmB,GAAKzB,GAAI,CAAEe,GAAI,EAAGW,SAAU,WAC3BtB,SAACC,EAAAC,IAAAqB,EAAA,CACCvB,eAACwB,EAAQ,CAAA/D,aAAwBgE,kBAAgB,EAC9CzB,SAAA5B,EAAMsD,KAAI,CAACC,EAAMC,IAChB1B,EAAAA,IAAC2B,EACC,CAAA7B,SAAAC,EAAA6B,KAACC,EAAA,CACCC,kBAAmB,EAAGC,SAAQC,eAC5BjC,EAAAC,IAACP,EAAA,CACCC,GAAI,CACFuC,MAAO,GACPC,OAAQ,GACRC,aAAc,MACdC,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBzB,WAAYmB,EACR,2BAA2B7E,EAAM8B,QAAQC,QAAQC,YAAYhC,EAAM8B,QAAQC,QAAQqD,aACnFR,EACA,2BAA2B5E,EAAM8B,QAAQM,QAAQJ,YAAYhC,EAAM8B,QAAQ6B,UAAU3B,aACrFqD,EAAMrF,EAAM8B,QAAQwD,OAAOC,SAAU,KACzCxB,MAAOc,GAAaD,EAAS,QAAU5E,EAAM8B,QAAQwD,OAAOC,SAC5DnC,WAAY,iBAGdT,SAAAE,EAAAA,IAACyB,EAAKrD,KAAL,CAAUsB,GAAI,CAAEiD,SAAU,QAI/B7C,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,YAAYjB,GAAI,CAAEkB,WAAY,KAC/Cd,SAAA2B,EAAKtD,cAEPuC,EAAW,CAAAC,QAAQ,UAAUO,MAAM,iBACjCpB,WAAKxB,kBA5BDmD,EAAKtD,eAsCxB6B,EAAAA,IAACmB,GACCrB,gBAACuB,EAAA,CAAY3B,GAAI,CAAEG,EAAG,GACpBC,SAAA,GAACE,IAAA4C,EAAA,CAAgBC,KAAK,OACpB/C,SAAAC,EAAA6B,KAAC3B,EAAOC,IAAP,CAECC,QAAS,CAAEC,QAAS,EAAG0C,EAAG,IAC1BxC,QAAS,CAAEF,QAAS,EAAG0C,EAAG,GAC1BC,KAAM,CAAE3C,QAAS,EAAG0C,GAAO,IAC3BvC,WAAY,CAAEC,SAAU,IAGvBV,SAAA,CAAe,IAAAvC,UACbkC,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,KAAOd,SAEzD,2CACCkD,EAAK,CAAAC,WAAS,EAACC,QAAS,EACtBpD,SAxLF,CACf,CAAEqD,GAAI,EAAGC,KAAM,oBAAqBC,MAAO,OAAQC,OAAQ,KAC3D,CAAEH,GAAI,EAAGC,KAAM,oBAAqBC,MAAO,OAAQC,OAAQ,KAC3D,CAAEH,GAAI,EAAGC,KAAM,eAAgBC,MAAO,OAAQC,OAAQ,KACtD,CAAEH,GAAI,EAAGC,KAAM,mBAAoBC,MAAO,MAAOC,OAAQ,KACzD,CAAEH,GAAI,EAAGC,KAAM,gBAAiBC,MAAO,OAAQC,OAAQ,MAmL7B9B,KAAK+B,SACZP,EAAA,CAAKQ,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAC5B7D,SAAAC,EAAAC,IAACmB,EAAA,CACCzB,GAAI,CACFkE,OAAQ,UACRC,QAAyB,MAAjBnG,OAAiB,EAAAA,EAAAyF,MAAOI,EAAQJ,GAAK,aAAahG,EAAM8B,QAAQM,QAAQJ,OAAS,YACzF2E,aAAa,MAAApG,OAAA,EAAAA,EAAiByF,MAAOI,EAAQJ,GAAK,eAAiB,UACnE5C,WAAY,gBACZ,UAAW,CACTwD,UAAW,mBACXC,UAAW7G,EAAM8G,QAAQ,KAG7BC,QAAS,IAAMvG,EAAmB4F,GAElCzD,SAACC,EAAAC,IAAAqB,EAAA,CACCvB,WAAC8B,KAAAnC,EAAA,CAAIC,GAAI,CAAE0C,QAAS,OAAQC,WAAY,SAAU8B,IAAK,GACrDrE,SAAA,OAACsE,GAAO1E,GAAI,CAAE2E,QAAS,gBACpBvE,WAAQwD,gBAEV7D,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,YAAYjB,GAAI,CAAEkB,WAAY,KAC/Cd,SAAAyD,EAAQH,OAEVxB,EAAAA,KAAAlB,EAAA,CAAWC,QAAQ,QAAQO,MAAM,iBAAiBpB,SAAA,CAAA,SAC1CyD,EAAQF,oBAxBWE,EAAQJ,WAqCrD5F,GAAc,GAAKA,GAAc,UAC/BkC,EACC,CAAAK,SAAA,CAAC8B,EAAAA,KAAAlB,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,KAC/Cd,SAAA,CAAAN,GAAgBb,KAAsB,IAAET,EAAMX,GAAYY,SAI5D6B,EAAAA,IAAAP,EAAA,CAAIC,GAAI,CAAEe,GAAI,GACbX,WAAC8B,KAAA0C,EAAA,CAAMC,UAAU,MAAMrB,QAAS,EAAGb,WAAW,SAC5CvC,SAAA,CAAAC,EAAAC,IAACwE,EAAA,CACCC,WAAS,EACTtG,MAAO,OAAOD,EAAMX,GAAYY,MAAMuG,gBACtCC,MAAO7G,EACP8G,SAAWC,GAAM9G,EAAgB8G,EAAEC,OAAOH,OAC1CI,WAAaF,GAAgB,UAAVA,EAAEG,KAAmBpG,IACxCqG,YAAa,iBAAiB/G,EAAMX,GAAYY,MAAMuG,qBAExD3E,EAAAC,IAACkF,EAAA,CACCvE,QAAQ,YACRwE,gBAAYC,EAAI,IAChBlB,QAAStF,EACT8D,UAAW5E,EAAae,OACzBiB,SAAA,aAMJ8B,EAAAA,KAAAoB,EAAA,CAAKC,WAAS,EAACC,QAAS,EAEvBpD,SAAA,CAAA8B,OAACoB,GAAKQ,MAAI,EAACC,GAAI,GAAIE,GAAI,EACrB7D,SAAA,CAAC8B,EAAAA,KAAAlB,EAAA,CAAWC,QAAQ,YAAYjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,KAAOd,SAAA,CAAA,WACrD5B,EAAMX,GAAYY,SAE7B6B,EAAAA,IAACqF,GACEvF,SAAS,OAAAwF,EAAA1H,EAAAe,WAAuB,EAAA2G,EAAA9D,KAAI,CAACgC,EAAM9B,IAC1C3B,EAAA6B,KAAC2D,EAAA,CAEC7F,GAAI,CACFmE,OAAQ,aAAarB,EAAMxD,GAAiBL,KAAuB,MACnEwD,aAAc,EACd1B,GAAI,EACJI,WAAY2B,EAAMxD,GAAiBL,KAAuB,MAG5DmB,SAAA,GAACE,IAAAwF,EAAA,CAAajG,QAASiE,IACvBzD,EAAAC,IAACyF,EAAA,CACCC,KAAK,QACLxB,QAAS,IAtPZ,EAACpF,EAAU4C,KAClC7D,GAAqBkB,IAAA,IAChBA,EACHD,CAACA,GAAWC,EAAKD,GAAU6G,QAAO,CAACC,EAAGC,IAAMA,IAAMnE,OAClD,EAkPuCoE,CAAiBnH,IAAsB+C,GACtDR,MAAM,QAENpB,eAACiG,EAAO,CAAA,OAdLrE,iBAsBZsB,EAAK,CAAAQ,MAAI,EAACC,GAAI,GAAIE,GAAI,EACrB7D,SAAA,CAAC8B,EAAAA,KAAAlB,EAAA,CAAWC,QAAQ,YAAYjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,KACvDd,SAAA,CAAAE,MAACgG,GAAYtG,GAAI,CAAEuG,GAAI,EAAGC,cAAe,YAAc,oBAGzDlG,EAAAA,IAACqF,GACEvF,SAAc,OAAAqG,EAAAtJ,EAAA8B,WAAuB,EAAAwH,EAAA3E,KAAI,CAAC4E,EAAY1E,IACrD3B,EAAA6B,KAAC2D,EAAA,CAEC7F,GAAI,CACFmE,OAAQ,YACRC,YAAa,UACb3B,aAAc,EACd1B,GAAI,EACJmD,OAAQ,UACR,UAAW,CACT/C,WAAY2B,EAAMrF,EAAM8B,QAAQM,QAAQJ,KAAM,OAGlD+E,QAAS,IA7QP,EAACpF,EAAUsH,KAChCxI,EAASkB,GAAUuH,SAASD,IAC/BvI,GAAqBkB,IAAA,IAChBA,EACHD,CAACA,GAAW,IAAIC,EAAKD,GAAWsH,MAChC,EAwQmCE,CAAoB3H,IAAsByH,GAEzDtG,SAAA,OAACyG,EACC,CAAAzG,SAAAE,EAAAA,IAACoF,EAAI,CAAAlE,MAAM,gBAEblB,IAACwF,EAAa,CAAAjG,QAAS6G,MAhBlB1E,gBA0BH,IAAfnE,GACCqE,EAAAA,KAACnC,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,KAAOd,SAEzD,yBAECpC,UACE8I,EAAM,CAAAC,SAAS,OAAO/G,GAAI,CAAEe,GAAI,GAAKX,SAAA,CAAA,uBAClBE,IAAC,SAAQ,CAAAF,SAAApC,EAAgB0F,OAAc,YAAU1F,EAAgB2F,SAIvFrD,EAAAA,IAACgD,GAAKC,WAAS,EAACC,QAAS,EACtBpD,SAAA4G,OAAOC,QAAQ/I,GAAU4D,KAAI,EAAE1C,EAAU8H,WACvC5D,EAAA,CAAKQ,MAAI,EAACC,GAAI,GAAIE,GAAI,EACrB7D,SAAAC,EAAAC,IAACmB,EAAA,CACCzB,GAAI,CACFmE,OAAQ,aAAarB,EAAMxD,GAAiBF,GAAW,MACvD+B,WAAY,2BAA2B2B,EAAMxD,GAAiBF,GAAW,YAAa0D,EAAMxD,GAAiBF,GAAW,cAG1HgB,gBAACuB,EACC,CAAAvB,SAAA,CAAAC,EAAA6B,KAAClB,EAAA,CACCC,QAAQ,KACRjB,GAAI,CACFkB,WAAY,IACZM,MAAOlC,GAAiBF,GACxB2B,GAAI,GAGLX,SAAA,CAAAN,GAAgBV,GAAU,IAAEA,EAAS+H,OAAO,GAAGC,cAAgBhI,EAASiI,MAAM,MAEjF/G,EAAAA,IAACqF,GAAK2B,OAAK,EACRlH,WAAM0B,KAAI,CAACgC,EAAM9B,MAChB1B,IAACuF,GACCzF,SAACC,EAAAC,IAAAwF,EAAA,CAAajG,QAAS,KAAKiE,OADf9B,aApBM5C,YA9JlCvB,KAkMTqE,EAAAA,KAACnC,EAAI,CAAAC,GAAI,CAAE0C,QAAS,OAAQE,eAAgB,gBAAiB2E,GAAI,GAC/DnH,SAAA,CAAAC,EAAAC,IAACkF,EAAA,CACChB,QAjUO,KACb3G,EAAa,GACDC,GAAAuB,GAAQA,EAAO,GAAC,EAgUtB2D,SAAyB,IAAfnF,EACV4H,gBAAY+B,EAAU,IACtBvG,QAAQ,WACTb,SAAA,SAIDC,EAAAC,IAACkF,EAAA,CACChB,QAAS3G,IAAeW,EAAMiJ,OAAS,EApUhCC,UACjBnJ,GAAW,GACP,UAEI,IAAIoJ,SAAQC,GAAWC,WAAWD,EAAS,OACjDE,MAAM,qCACNnK,EAAS,6BACF+B,GAC2C,CAClD,QACAnB,GAAW,EAAK,GAtBD,KACbV,EAAaW,EAAMiJ,OAAS,GAChB3J,GAAAuB,GAAQA,EAAO,GAAC,EA+UtB0I,QAASlK,IAAeW,EAAMiJ,OAAS,EAAKnH,EAAAA,IAAA0H,EAAA,IAAU1H,MAAC2H,EAAa,CAAA,GACpEhH,QAAQ,YACR+B,SAAyB,IAAfnF,IAAqBG,EAC/BM,UACA0B,GAAI,CACFmB,WAAY,2BAA2B1D,EAAM8B,QAAQM,QAAQJ,YAAYhC,EAAM8B,QAAQ6B,UAAU3B,cAGlGW,SAAevC,IAAAW,EAAMiJ,OAAS,EAAI,qBAAuB,mBAKpE"}