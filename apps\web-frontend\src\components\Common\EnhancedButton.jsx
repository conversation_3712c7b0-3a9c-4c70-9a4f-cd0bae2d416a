/**
 * VidyaMitra Platform - Enhanced Button Component
 * 
 * Comprehensive button component with proper hover states, loading states,
 * and accessibility features for the Indian educational context
 */

import React, { useState } from 'react';
import {
  Button,
  CircularProgress,
  Box,
  Tooltip,
  useTheme,
  alpha,
} from '@mui/material';
import { motion } from 'framer-motion';

const EnhancedButton = ({
  children,
  loading = false,
  disabled = false,
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  startIcon,
  endIcon,
  onClick,
  tooltip,
  fullWidth = false,
  sx = {},
  loadingText = 'Loading...',
  successFeedback = false,
  errorFeedback = false,
  ...props
}) => {
  const theme = useTheme();
  const [isClicked, setIsClicked] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);

  const handleClick = async (event) => {
    if (loading || disabled) return;

    setIsClicked(true);
    
    try {
      if (onClick) {
        const result = await onClick(event);
        
        if (successFeedback && result !== false) {
          setShowSuccess(true);
          setTimeout(() => setShowSuccess(false), 2000);
        }
      }
    } catch (error) {
      if (errorFeedback) {
        setShowError(true);
        setTimeout(() => setShowError(false), 2000);
      }
    } finally {
      setTimeout(() => setIsClicked(false), 150);
    }
  };

  const getButtonColor = () => {
    if (showSuccess) return 'success';
    if (showError) return 'error';
    return color;
  };

  const getButtonContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CircularProgress 
            size={16} 
            color="inherit" 
            sx={{ 
              color: variant === 'contained' ? 'white' : theme.palette[color].main 
            }} 
          />
          {loadingText}
        </Box>
      );
    }

    if (showSuccess) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          ✓ Success
        </Box>
      );
    }

    if (showError) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          ✗ Error
        </Box>
      );
    }

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {startIcon}
        {children}
        {endIcon}
      </Box>
    );
  };

  const buttonVariants = {
    initial: { scale: 1 },
    hover: { 
      scale: 1.02,
      transition: { 
        type: 'spring', 
        stiffness: 400, 
        damping: 25 
      }
    },
    tap: { 
      scale: 0.98,
      transition: { 
        type: 'spring', 
        stiffness: 400, 
        damping: 25 
      }
    },
  };

  const enhancedSx = {
    borderRadius: 3,
    textTransform: 'none',
    fontWeight: 600,
    minHeight: size === 'small' ? 36 : size === 'large' ? 52 : 44,
    position: 'relative',
    overflow: 'hidden',
    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    
    // Enhanced hover effects
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: variant === 'contained' 
        ? `0px 8px 24px ${alpha(theme.palette[getButtonColor()].main, 0.4)}`
        : `0px 4px 12px ${alpha(theme.palette[getButtonColor()].main, 0.2)}`,
    },

    // Active state
    '&:active': {
      transform: 'translateY(0px)',
    },

    // Focus state for accessibility
    '&:focus-visible': {
      outline: `2px solid ${theme.palette[getButtonColor()].main}`,
      outlineOffset: '2px',
    },

    // Disabled state
    '&:disabled': {
      opacity: 0.6,
      transform: 'none',
      boxShadow: 'none',
    },

    // Loading state
    ...(loading && {
      pointerEvents: 'none',
    }),

    // Custom styles
    ...sx,
  };

  const ButtonComponent = (
    <motion.div
      variants={buttonVariants}
      initial="initial"
      whileHover={!loading && !disabled ? "hover" : "initial"}
      whileTap={!loading && !disabled ? "tap" : "initial"}
      style={{ display: fullWidth ? 'block' : 'inline-block', width: fullWidth ? '100%' : 'auto' }}
    >
      <Button
        variant={variant}
        color={getButtonColor()}
        size={size}
        disabled={disabled || loading}
        onClick={handleClick}
        fullWidth={fullWidth}
        sx={enhancedSx}
        {...props}
      >
        {getButtonContent()}
      </Button>
    </motion.div>
  );

  if (tooltip) {
    return (
      <Tooltip title={tooltip} arrow placement="top">
        <span style={{ display: fullWidth ? 'block' : 'inline-block' }}>
          {ButtonComponent}
        </span>
      </Tooltip>
    );
  }

  return ButtonComponent;
};

export default EnhancedButton;
