{"version": 3, "file": "borderStyle.js", "names": ["items", "_this", "opts", "for<PERSON>ach", "o", "i", "prototype", "validate", "val", "undefined", "name", "hasOwnProperty", "push", "TypeError", "join", "module", "exports"], "sources": ["../../../source/lib/types/borderStyle.js"], "sourcesContent": ["function items() {\n    this.opts = [//§18.18.3 ST_BorderStyle (Border Line Styles)\n        'none', \n        'thin', \n        'medium', \n        'dashed', \n        'dotted', \n        'thick', \n        'double', \n        'hair', \n        'mediumDashed', \n        'dashDot', \n        'mediumDashDot', \n        'dashDotDot', \n        'mediumDashDotDot', \n        'slantDashDot'\n    ];\n    this.opts.forEach((o, i) => {\n        this[o] = i + 1;\n    });\n}\n\n\nitems.prototype.validate = function (val) {\n    if (this[val] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError('Invalid value for ST_BorderStyle; Value must be one of ' + this.opts.join(', '));\n    } else {\n        return true;\n    }\n};\n\nmodule.exports = new items();"], "mappings": ";;AAAA,SAASA,KAAKA,CAAA,EAAG;EAAA,IAAAC,KAAA;EACb,IAAI,CAACC,IAAI,GAAG;EAAC;EACT,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,MAAM,EACN,cAAc,EACd,SAAS,EACT,eAAe,EACf,YAAY,EACZ,kBAAkB,EAClB,cAAc,CACjB;EACD,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACxBJ,KAAI,CAACG,CAAC,CAAC,GAAGC,CAAC,GAAG,CAAC;EACnB,CAAC,CAAC;AACN;AAGAL,KAAK,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACtC,IAAI,IAAI,CAACA,GAAG,CAAC,KAAKC,SAAS,EAAE;IACzB,IAAIP,IAAI,GAAG,EAAE;IACb,KAAK,IAAIQ,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BR,IAAI,CAACU,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIG,SAAS,CAAC,yDAAyD,GAAG,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,IAAI,CAAC,CAAC;EACzG,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,IAAIhB,KAAK,CAAC,CAAC"}