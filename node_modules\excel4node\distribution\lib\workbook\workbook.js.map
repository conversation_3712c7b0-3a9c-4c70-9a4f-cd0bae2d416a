{"version": 3, "file": "workbook.js", "names": ["_isUndefined", "require", "deepmerge", "fs", "utils", "Worksheet", "Style", "Border", "Fill", "Font", "DXFCollection", "MediaCollection", "DefinedNameCollection", "types", "builder", "http", "SimpleLogger", "workbookDefaultOpts", "j<PERSON><PERSON>", "compression", "defaultFont", "dateFormat", "Workbook", "opts", "arguments", "length", "undefined", "_classCallCheck", "hasCustomLogger", "logger", "hasValidCustomLogger", "warn", "error", "logLevel", "Number", "isNaN", "parseInt", "log", "author", "sheets", "sharedStrings", "sharedStringLookup", "Map", "styles", "stylesLookup", "dxfCollection", "mediaCollection", "definedNameCollection", "styleData", "type", "patternType", "styleDataLookup", "fills", "reduce", "ret", "fill", "index", "JSON", "stringify", "toObject", "borders", "border", "createStyle", "font", "_createClass", "key", "value", "setSelectedTab", "id", "for<PERSON>ach", "s", "sheetId", "sheetView", "tabSelected", "writeToBuffer", "write", "fileName", "handler", "_this", "then", "buffer", "_typeof", "ServerResponse", "writeHead", "concat", "encodeURIComponent", "end", "TypeError", "writeFile", "err", "stat", "e", "stack", "status", "<PERSON><PERSON><PERSON><PERSON>", "addWorksheet", "name", "<PERSON><PERSON><PERSON><PERSON>", "push", "thisStyle", "lookup<PERSON><PERSON>", "get", "set", "ids", "cellXfs", "getStringIndex", "val", "target", "_generateXML", "workbookXML", "module", "exports"], "sources": ["../../../source/lib/workbook/workbook.js"], "sourcesContent": ["const _isUndefined = require('lodash.isundefined');\nconst deepmerge = require('deepmerge');\nconst fs = require('fs');\nconst utils = require('../utils.js');\nconst Worksheet = require('../worksheet');\nconst Style = require('../style');\nconst Border = require('../style/classes/border.js');\nconst Fill = require('../style/classes/fill.js');\nconst Font = require('../style/classes/font');\nconst DXFCollection = require('./dxfCollection.js');\nconst MediaCollection = require('./mediaCollection.js');\nconst DefinedNameCollection = require('../classes/definedNameCollection.js');\nconst types = require('../types/index.js');\nconst builder = require('./builder.js');\nconst http = require('http');\nconst SimpleLogger = require('../logger');\n\n/* Available options for Workbook\n{\n    jszip : {\n        compression : 'DEFLATE'\n    },\n    defaultFont : {\n        size : 12,\n        family : 'Calibri',\n        color : 'FFFFFFFF'\n    }\n}\n*/\n// Default Options for Workbook\nlet workbookDefaultOpts = {\n    jszip: {\n        compression: 'DEFLATE'\n    },\n    defaultFont: {\n        'color': 'FF000000',\n        'name': 'Calibri',\n        'size': 12,\n        'family': 'roman'\n    },\n    dateFormat: 'm/d/yy'\n};\n\n\nclass Workbook {\n\n    /**\n     * @class Workbook\n     * @param {Object} opts Workbook settings\n     * @param {Object} opts.jszip\n     * @param {String} opts.jszip.compression JSZip compression type. defaults to 'DEFLATE'\n     * @param {Object} opts.defaultFont\n     * @param {String} opts.defaultFont.color HEX value of default font color. defaults to #000000\n     * @param {String} opts.defaultFont.name Font name. defaults to Calibri\n     * @param {Number} opts.defaultFont.size Font size. defaults to 12\n     * @param {String} opts.defaultFont.family Font family. defaults to roman\n     * @param {String} opts.dataFormat Specifies the format for dates in the Workbook. defaults to 'm/d/yy'\n     * @param {Number} opts.workbookView.activeTab Specifies an unsignedInt that contains the index to the active sheet in this book view.\n     * @param {Boolean} opts.workbookView.autoFilterDateGrouping Specifies a boolean value that indicates whether to group dates when presenting the user with filtering options in the user interface.\n     * @param {Number} opts.workbookView.firstSheet Specifies the index to the first sheet in this book view.\n     * @param {Boolean} opts.workbookView.minimized Specifies a boolean value that indicates whether the workbook window is minimized.\n     * @param {Boolean} opts.workbookView.showHorizontalScroll Specifies a boolean value that indicates whether to display the horizontal scroll bar in the user interface.\n     * @param {Boolean} opts.workbookView.showSheetTabs Specifies a boolean value that indicates whether to display the sheet tabs in the user interface.\n     * @param {Boolean} opts.workbookView.showVerticalScroll Specifies a boolean value that indicates whether to display the vertical scroll bar.\n     * @param {Number} opts.workbookView.tabRatio Specifies ratio between the workbook tabs bar and the horizontal scroll bar.\n     * @param {String} opts.workbookView.visibility Specifies visible state of the workbook window. ('hidden', 'veryHidden', 'visible') (§18.18.89)\n     * @param {Number} opts.workbookView.windowHeight Specifies the height of the workbook window. The unit of measurement for this value is twips.\n     * @param {Number} opts.workbookView.windowWidth Specifies the width of the workbook window. The unit of measurement for this value is twips..\n     * @param {Number} opts.workbookView.xWindow Specifies the X coordinate for the upper left corner of the workbook window. The unit of measurement for this value is twips.\n     * @param {Number} opts.workbookView.yWindow Specifies the Y coordinate for the upper left corner of the workbook window. The unit of measurement for this value is twips.\n     * @param {Boolean} opts.workbookView\n     * @param {Object} opts.logger Logger that supports warn and error method, defaults to console\n     * @param {String} opts.author Name displayed as document's author\n     * @returns {Workbook}\n     */\n    constructor(opts = {}) {\n\n        const hasCustomLogger = opts.logger !== undefined;\n        const hasValidCustomLogger = hasCustomLogger && typeof opts.logger.warn === 'function' && typeof opts.logger.error === 'function';\n\n        this.logger = hasValidCustomLogger ? opts.logger : new SimpleLogger({\n            logLevel: Number.isNaN(parseInt(opts.logLevel)) ? 0 : parseInt(opts.logLevel)\n        });\n        if (hasCustomLogger && !hasValidCustomLogger) {\n            this.logger.log('opts.logger is not a valid logger');\n        }\n\n        this.opts = deepmerge(workbookDefaultOpts, opts);\n        this.author = this.opts.author || 'Microsoft Office User';\n\n        this.sheets = [];\n        this.sharedStrings = [];\n        this.sharedStringLookup = new Map();\n        this.styles = [];\n        this.stylesLookup = new Map();\n        this.dxfCollection = new DXFCollection(this);\n        this.mediaCollection = new MediaCollection();\n        this.definedNameCollection = new DefinedNameCollection();\n        this.styleData = {\n            'numFmts': [],\n            'fonts': [],\n            'fills': [new Fill({\n                type: 'pattern',\n                patternType: 'none'\n            }), new Fill({\n                type: 'pattern',\n                patternType: 'gray125'\n            })],\n            'borders': [new Border()],\n            'cellXfs': [{\n                'borderId': null,\n                'fillId': null,\n                'fontId': 0,\n                'numFmtId': null\n            }]\n        };\n\n        // Lookups for style components to quickly find existing entries\n        // - Lookup keys are stringified JSON of a style's toObject result\n        // - Lookup values are the indexes for the actual entry in the styleData arrays\n        this.styleDataLookup = {\n            'fonts': {},\n            'fills': this.styleData.fills.reduce((ret, fill, index) => {\n                ret[JSON.stringify(fill.toObject())] = index;\n                return ret;\n            }, {}),\n            'borders': this.styleData.borders.reduce((ret, border, index) => {\n                ret[JSON.stringify(border.toObject())] = index;\n                return ret;\n            }, {})\n        };\n\n        // Set Default Font and Style\n        this.createStyle({\n            font: this.opts.defaultFont\n        });\n    }\n\n    /**\n     * setSelectedTab\n     * @param {Number} tab number of sheet that should be displayed when workbook opens. tabs are indexed starting with 1\n     **/\n    setSelectedTab(id) {\n        this.sheets.forEach((s) => {\n            if (s.sheetId === id) {\n                s.opts.sheetView.tabSelected = 1;\n            } else {\n                s.opts.sheetView.tabSelected = 0;\n            }\n        });\n    }\n\n    /**\n     * writeToBuffer\n     * Writes Excel data to a node Buffer.\n     */\n    writeToBuffer() {\n        return builder.writeToBuffer(this);\n    }\n\n    /**\n     * Generate .xlsx file.\n     * @param {String} fileName Name of Excel workbook with .xslx extension\n     * @param {http.response | callback} http response object or callback function (optional).\n     * If http response object is given, file is written to http response. Useful for web applications.\n     * If callback is given, callback called with (err, fs.Stats) passed\n     */\n    write(fileName, handler) {\n\n        builder.writeToBuffer(this)\n            .then((buffer) => {\n                switch (typeof handler) {\n                    // handler passed as http response object.\n\n                    case 'object':\n                        if (handler instanceof http.ServerResponse) {\n                            handler.writeHead(200, {\n                                'Content-Length': buffer.length,\n                                'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n                                'Content-Disposition': `attachment; filename=\"${encodeURIComponent(fileName)}\"; filename*=utf-8''${encodeURIComponent(fileName)};`,\n                            });\n                            handler.end(buffer);\n                        } else {\n                            throw new TypeError('Unknown object sent to write function.');\n                        }\n                        break;\n\n                        // handler passed as callback function\n                    case 'function':\n                        fs.writeFile(fileName, buffer, function (err) {\n                            if (err) {\n                                handler(err);\n                            } else {\n                                fs.stat(fileName, handler);\n                            }\n                        });\n                        break;\n\n                        // no handler passed, write file to FS.\n                    default:\n\n                        fs.writeFile(fileName, buffer, function (err) {\n                            if (err) {\n                                throw err;\n                            }\n                        });\n                        break;\n                }\n            })\n            .catch((e) => {\n                if (handler instanceof http.ServerResponse) {\n                    this.logger.error(e.stack);\n                    handler.status = 500;\n                    handler.setHeader('Content-Type', 'text/plain');\n                    handler.end('500 Server Error');\n                } else if (typeof handler === 'function') {\n                    handler(e.stack);\n                } else {\n                    this.logger.error(e.stack);\n                }\n            });\n    }\n\n    /**\n     * Add a worksheet to the Workbook\n     * @param {String} name Name of the Worksheet\n     * @param {Object} opts Options for Worksheet. See Worksheet class definition\n     * @returns {Worksheet}\n     */\n    addWorksheet(name, opts) {\n        let newLength = this.sheets.push(new Worksheet(this, name, opts));\n        return this.sheets[newLength - 1];\n    }\n\n    /**\n     * Add a Style to the Workbook\n     * @param {Object} opts Options for the style. See Style class definition\n     * @returns {Style}\n     */\n    createStyle(opts) {\n        const thisStyle = new Style(this, opts);\n        const lookupKey = JSON.stringify(thisStyle.toObject());\n\n        // Use existing style if one exists\n        if (this.stylesLookup.get(lookupKey)) {\n            return this.stylesLookup.get(lookupKey);\n        }\n\n        this.stylesLookup.set(lookupKey, thisStyle);\n        const index = this.styles.push(thisStyle) - 1;\n        this.styles[index].ids.cellXfs = index;\n        return this.styles[index];\n    }\n\n    /**\n     * Gets the index of a string from the shared string array if exists and adds the string if it does not and returns the new index\n     * @param {String} val Text of string\n     * @returns {Number} index of the string in the shared strings array\n     */\n    getStringIndex(val) {\n        const lookupKey = typeof val === \"string\" ? val : JSON.stringify(val);\n        const target = this.sharedStringLookup.get(lookupKey);\n        if (_isUndefined(target)) {\n            const index = this.sharedStrings.push(val) - 1;\n            this.sharedStringLookup.set(lookupKey, index);\n            return index;\n        } else {\n            return target;\n        }\n    }\n\n    /**\n     * @func Workbook._generateXML\n     * @desc used for testing the Workbook XML generated by the builder\n     * @return {Promise} resolves with Workbook XML \n     */\n    _generateXML() {\n        return builder.workbookXML(this);\n    }\n}\n\nmodule.exports = Workbook;"], "mappings": ";;;;;;AAAA,IAAMA,YAAY,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAClD,IAAMC,SAAS,GAAGD,OAAO,CAAC,WAAW,CAAC;AACtC,IAAME,EAAE,GAAGF,OAAO,CAAC,IAAI,CAAC;AACxB,IAAMG,KAAK,GAAGH,OAAO,CAAC,aAAa,CAAC;AACpC,IAAMI,SAAS,GAAGJ,OAAO,CAAC,cAAc,CAAC;AACzC,IAAMK,KAAK,GAAGL,OAAO,CAAC,UAAU,CAAC;AACjC,IAAMM,MAAM,GAAGN,OAAO,CAAC,4BAA4B,CAAC;AACpD,IAAMO,IAAI,GAAGP,OAAO,CAAC,0BAA0B,CAAC;AAChD,IAAMQ,IAAI,GAAGR,OAAO,CAAC,uBAAuB,CAAC;AAC7C,IAAMS,aAAa,GAAGT,OAAO,CAAC,oBAAoB,CAAC;AACnD,IAAMU,eAAe,GAAGV,OAAO,CAAC,sBAAsB,CAAC;AACvD,IAAMW,qBAAqB,GAAGX,OAAO,CAAC,qCAAqC,CAAC;AAC5E,IAAMY,KAAK,GAAGZ,OAAO,CAAC,mBAAmB,CAAC;AAC1C,IAAMa,OAAO,GAAGb,OAAO,CAAC,cAAc,CAAC;AACvC,IAAMc,IAAI,GAAGd,OAAO,CAAC,MAAM,CAAC;AAC5B,IAAMe,YAAY,GAAGf,OAAO,CAAC,WAAW,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIgB,mBAAmB,GAAG;EACtBC,KAAK,EAAE;IACHC,WAAW,EAAE;EACjB,CAAC;EACDC,WAAW,EAAE;IACT,OAAO,EAAE,UAAU;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,EAAE;IACV,QAAQ,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;AAChB,CAAC;AAAC,IAGIC,QAAQ;EAEV;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,SAAA,EAAuB;IAAA,IAAXC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA,OAAAL,QAAA;IAEjB,IAAMM,eAAe,GAAGL,IAAI,CAACM,MAAM,KAAKH,SAAS;IACjD,IAAMI,oBAAoB,GAAGF,eAAe,IAAI,OAAOL,IAAI,CAACM,MAAM,CAACE,IAAI,KAAK,UAAU,IAAI,OAAOR,IAAI,CAACM,MAAM,CAACG,KAAK,KAAK,UAAU;IAEjI,IAAI,CAACH,MAAM,GAAGC,oBAAoB,GAAGP,IAAI,CAACM,MAAM,GAAG,IAAIb,YAAY,CAAC;MAChEiB,QAAQ,EAAEC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACb,IAAI,CAACU,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAGG,QAAQ,CAACb,IAAI,CAACU,QAAQ;IAChF,CAAC,CAAC;IACF,IAAIL,eAAe,IAAI,CAACE,oBAAoB,EAAE;MAC1C,IAAI,CAACD,MAAM,CAACQ,GAAG,CAAC,mCAAmC,CAAC;IACxD;IAEA,IAAI,CAACd,IAAI,GAAGrB,SAAS,CAACe,mBAAmB,EAAEM,IAAI,CAAC;IAChD,IAAI,CAACe,MAAM,GAAG,IAAI,CAACf,IAAI,CAACe,MAAM,IAAI,uBAAuB;IAEzD,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnC,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,YAAY,GAAG,IAAIF,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACG,aAAa,GAAG,IAAInC,aAAa,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACoC,eAAe,GAAG,IAAInC,eAAe,CAAC,CAAC;IAC5C,IAAI,CAACoC,qBAAqB,GAAG,IAAInC,qBAAqB,CAAC,CAAC;IACxD,IAAI,CAACoC,SAAS,GAAG;MACb,SAAS,EAAE,EAAE;MACb,OAAO,EAAE,EAAE;MACX,OAAO,EAAE,CAAC,IAAIxC,IAAI,CAAC;QACfyC,IAAI,EAAE,SAAS;QACfC,WAAW,EAAE;MACjB,CAAC,CAAC,EAAE,IAAI1C,IAAI,CAAC;QACTyC,IAAI,EAAE,SAAS;QACfC,WAAW,EAAE;MACjB,CAAC,CAAC,CAAC;MACH,SAAS,EAAE,CAAC,IAAI3C,MAAM,CAAC,CAAC,CAAC;MACzB,SAAS,EAAE,CAAC;QACR,UAAU,EAAE,IAAI;QAChB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,CAAC;QACX,UAAU,EAAE;MAChB,CAAC;IACL,CAAC;;IAED;IACA;IACA;IACA,IAAI,CAAC4C,eAAe,GAAG;MACnB,OAAO,EAAE,CAAC,CAAC;MACX,OAAO,EAAE,IAAI,CAACH,SAAS,CAACI,KAAK,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAK;QACvDF,GAAG,CAACG,IAAI,CAACC,SAAS,CAACH,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGH,KAAK;QAC5C,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,SAAS,EAAE,IAAI,CAACN,SAAS,CAACY,OAAO,CAACP,MAAM,CAAC,UAACC,GAAG,EAAEO,MAAM,EAAEL,KAAK,EAAK;QAC7DF,GAAG,CAACG,IAAI,CAACC,SAAS,CAACG,MAAM,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGH,KAAK;QAC9C,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC;IACT,CAAC;;IAED;IACA,IAAI,CAACQ,WAAW,CAAC;MACbC,IAAI,EAAE,IAAI,CAACxC,IAAI,CAACH;IACpB,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;EAHI4C,YAAA,CAAA1C,QAAA;IAAA2C,GAAA;IAAAC,KAAA,EAIA,SAAAC,eAAeC,EAAE,EAAE;MACf,IAAI,CAAC7B,MAAM,CAAC8B,OAAO,CAAC,UAACC,CAAC,EAAK;QACvB,IAAIA,CAAC,CAACC,OAAO,KAAKH,EAAE,EAAE;UAClBE,CAAC,CAAC/C,IAAI,CAACiD,SAAS,CAACC,WAAW,GAAG,CAAC;QACpC,CAAC,MAAM;UACHH,CAAC,CAAC/C,IAAI,CAACiD,SAAS,CAACC,WAAW,GAAG,CAAC;QACpC;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;EAHI;IAAAR,GAAA;IAAAC,KAAA,EAIA,SAAAQ,cAAA,EAAgB;MACZ,OAAO5D,OAAO,CAAC4D,aAAa,CAAC,IAAI,CAAC;IACtC;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAT,GAAA;IAAAC,KAAA,EAOA,SAAAS,MAAMC,QAAQ,EAAEC,OAAO,EAAE;MAAA,IAAAC,KAAA;MAErBhE,OAAO,CAAC4D,aAAa,CAAC,IAAI,CAAC,CACtBK,IAAI,CAAC,UAACC,MAAM,EAAK;QACd,QAAAC,OAAA,CAAeJ,OAAO;UAClB;;UAEA,KAAK,QAAQ;YACT,IAAIA,OAAO,YAAY9D,IAAI,CAACmE,cAAc,EAAE;cACxCL,OAAO,CAACM,SAAS,CAAC,GAAG,EAAE;gBACnB,gBAAgB,EAAEH,MAAM,CAACvD,MAAM;gBAC/B,cAAc,EAAE,mEAAmE;gBACnF,qBAAqB,4BAAA2D,MAAA,CAA2BC,kBAAkB,CAACT,QAAQ,CAAC,2BAAAQ,MAAA,CAAuBC,kBAAkB,CAACT,QAAQ,CAAC;cACnI,CAAC,CAAC;cACFC,OAAO,CAACS,GAAG,CAACN,MAAM,CAAC;YACvB,CAAC,MAAM;cACH,MAAM,IAAIO,SAAS,CAAC,wCAAwC,CAAC;YACjE;YACA;;UAEA;UACJ,KAAK,UAAU;YACXpF,EAAE,CAACqF,SAAS,CAACZ,QAAQ,EAAEI,MAAM,EAAE,UAAUS,GAAG,EAAE;cAC1C,IAAIA,GAAG,EAAE;gBACLZ,OAAO,CAACY,GAAG,CAAC;cAChB,CAAC,MAAM;gBACHtF,EAAE,CAACuF,IAAI,CAACd,QAAQ,EAAEC,OAAO,CAAC;cAC9B;YACJ,CAAC,CAAC;YACF;;UAEA;UACJ;YAEI1E,EAAE,CAACqF,SAAS,CAACZ,QAAQ,EAAEI,MAAM,EAAE,UAAUS,GAAG,EAAE;cAC1C,IAAIA,GAAG,EAAE;gBACL,MAAMA,GAAG;cACb;YACJ,CAAC,CAAC;YACF;QACR;MACJ,CAAC,CAAC,SACI,CAAC,UAACE,CAAC,EAAK;QACV,IAAId,OAAO,YAAY9D,IAAI,CAACmE,cAAc,EAAE;UACxCJ,KAAI,CAACjD,MAAM,CAACG,KAAK,CAAC2D,CAAC,CAACC,KAAK,CAAC;UAC1Bf,OAAO,CAACgB,MAAM,GAAG,GAAG;UACpBhB,OAAO,CAACiB,SAAS,CAAC,cAAc,EAAE,YAAY,CAAC;UAC/CjB,OAAO,CAACS,GAAG,CAAC,kBAAkB,CAAC;QACnC,CAAC,MAAM,IAAI,OAAOT,OAAO,KAAK,UAAU,EAAE;UACtCA,OAAO,CAACc,CAAC,CAACC,KAAK,CAAC;QACpB,CAAC,MAAM;UACHd,KAAI,CAACjD,MAAM,CAACG,KAAK,CAAC2D,CAAC,CAACC,KAAK,CAAC;QAC9B;MACJ,CAAC,CAAC;IACV;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA3B,GAAA;IAAAC,KAAA,EAMA,SAAA6B,aAAaC,IAAI,EAAEzE,IAAI,EAAE;MACrB,IAAI0E,SAAS,GAAG,IAAI,CAAC1D,MAAM,CAAC2D,IAAI,CAAC,IAAI7F,SAAS,CAAC,IAAI,EAAE2F,IAAI,EAAEzE,IAAI,CAAC,CAAC;MACjE,OAAO,IAAI,CAACgB,MAAM,CAAC0D,SAAS,GAAG,CAAC,CAAC;IACrC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAhC,GAAA;IAAAC,KAAA,EAKA,SAAAJ,YAAYvC,IAAI,EAAE;MACd,IAAM4E,SAAS,GAAG,IAAI7F,KAAK,CAAC,IAAI,EAAEiB,IAAI,CAAC;MACvC,IAAM6E,SAAS,GAAG3C,IAAI,CAACC,SAAS,CAACyC,SAAS,CAACxC,QAAQ,CAAC,CAAC,CAAC;;MAEtD;MACA,IAAI,IAAI,CAACf,YAAY,CAACyD,GAAG,CAACD,SAAS,CAAC,EAAE;QAClC,OAAO,IAAI,CAACxD,YAAY,CAACyD,GAAG,CAACD,SAAS,CAAC;MAC3C;MAEA,IAAI,CAACxD,YAAY,CAAC0D,GAAG,CAACF,SAAS,EAAED,SAAS,CAAC;MAC3C,IAAM3C,KAAK,GAAG,IAAI,CAACb,MAAM,CAACuD,IAAI,CAACC,SAAS,CAAC,GAAG,CAAC;MAC7C,IAAI,CAACxD,MAAM,CAACa,KAAK,CAAC,CAAC+C,GAAG,CAACC,OAAO,GAAGhD,KAAK;MACtC,OAAO,IAAI,CAACb,MAAM,CAACa,KAAK,CAAC;IAC7B;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAS,GAAA;IAAAC,KAAA,EAKA,SAAAuC,eAAeC,GAAG,EAAE;MAChB,IAAMN,SAAS,GAAG,OAAOM,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGjD,IAAI,CAACC,SAAS,CAACgD,GAAG,CAAC;MACrE,IAAMC,MAAM,GAAG,IAAI,CAAClE,kBAAkB,CAAC4D,GAAG,CAACD,SAAS,CAAC;MACrD,IAAIpG,YAAY,CAAC2G,MAAM,CAAC,EAAE;QACtB,IAAMnD,KAAK,GAAG,IAAI,CAAChB,aAAa,CAAC0D,IAAI,CAACQ,GAAG,CAAC,GAAG,CAAC;QAC9C,IAAI,CAACjE,kBAAkB,CAAC6D,GAAG,CAACF,SAAS,EAAE5C,KAAK,CAAC;QAC7C,OAAOA,KAAK;MAChB,CAAC,MAAM;QACH,OAAOmD,MAAM;MACjB;IACJ;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAA1C,GAAA;IAAAC,KAAA,EAKA,SAAA0C,aAAA,EAAe;MACX,OAAO9F,OAAO,CAAC+F,WAAW,CAAC,IAAI,CAAC;IACpC;EAAC;EAAA,OAAAvF,QAAA;AAAA;AAGLwF,MAAM,CAACC,OAAO,GAAGzF,QAAQ"}