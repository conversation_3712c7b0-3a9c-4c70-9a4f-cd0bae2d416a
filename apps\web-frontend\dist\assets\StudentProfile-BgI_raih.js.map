{"version": 3, "file": "StudentProfile-BgI_raih.js", "sources": ["../../src/components/Students/StudentProfile.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Student Profile Component\n * \n * Comprehensive student profile display with academic performance,\n * SWOT analysis, and Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Avatar,\n  Grid,\n  Chip,\n  Button,\n  Tab,\n  Tabs,\n  LinearProgress,\n  IconButton,\n  Divider,\n  Stack,\n  Alert,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Person,\n  School,\n  Assessment,\n  TrendingUp,\n  Edit,\n  Print,\n  Share,\n  Phone,\n  Email,\n  LocationOn,\n  CalendarToday,\n  Star,\n  EmojiEvents,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Line, Radar, Bar } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  RadialLinearScale,\n  BarElement,\n  Title,\n  Tooltip as ChartTooltip,\n  Legend,\n  Filler,\n} from 'chart.js';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  RadialLinearScale,\n  BarElement,\n  Title,\n  ChartTooltip,\n  Legend,\n  Filler\n);\n\n// Sample student data (in real app, this would come from API)\nconst sampleStudentData = {\n  id: 1,\n  firstName: 'Sanju',\n  middleName: 'Kumar',\n  lastName: 'Reddy',\n  admissionNumber: 'VMS2024001',\n  grade: 10,\n  section: 'A',\n  rollNumber: 15,\n  board: 'CBSE',\n  dateOfBirth: '2008-05-15',\n  gender: 'Male',\n  bloodGroup: 'B+',\n  profilePhoto: null,\n  \n  // Contact Information\n  address: 'H.No 12-34, Jubilee Hills, Hyderabad',\n  city: 'Hyderabad',\n  state: 'Telangana',\n  pincode: '500033',\n  phone: '+91 9876543210',\n  email: '<EMAIL>',\n  \n  // Parent Information\n  fatherName: 'Rajesh Kumar Reddy',\n  fatherOccupation: 'Software Engineer',\n  fatherPhone: '+91 9876543211',\n  motherName: 'Priya Reddy',\n  motherOccupation: 'Teacher',\n  motherPhone: '+91 9876543212',\n  \n  // Academic Performance\n  currentGPA: 8.7,\n  attendance: 92,\n  subjects: [\n    { name: 'Mathematics', grade: 'A1', marks: 95, teacher: 'Mrs. Sharma' },\n    { name: 'Science', grade: 'A1', marks: 92, teacher: 'Mr. Patel' },\n    { name: 'English', grade: 'A2', marks: 88, teacher: 'Ms. Johnson' },\n    { name: 'Hindi', grade: 'A1', marks: 94, teacher: 'Mrs. Gupta' },\n    { name: 'Social Studies', grade: 'A2', marks: 86, teacher: 'Mr. Singh' },\n    { name: 'Telugu', grade: 'A1', marks: 96, teacher: 'Mrs. Rao' },\n  ],\n  \n  // Performance Trends\n  performanceTrends: [\n    { month: 'Apr', gpa: 8.2 },\n    { month: 'May', gpa: 8.4 },\n    { month: 'Jun', gpa: 8.6 },\n    { month: 'Jul', gpa: 8.5 },\n    { month: 'Aug', gpa: 8.7 },\n    { month: 'Sep', gpa: 8.8 },\n  ],\n  \n  // SWOT Analysis\n  swotAnalysis: {\n    strengths: ['Strong in Mathematics', 'Good leadership skills', 'Excellent attendance'],\n    weaknesses: ['Needs improvement in English writing', 'Shy in group discussions'],\n    opportunities: ['Science Olympiad participation', 'Student council elections'],\n    threats: ['Increased competition', 'Time management challenges'],\n  },\n  \n  // Achievements\n  achievements: [\n    { title: 'Mathematics Olympiad - District Level', date: '2024-03-15', type: 'Academic' },\n    { title: 'Best Student of the Month', date: '2024-02-28', type: 'Behavioral' },\n    { title: 'Science Fair - First Prize', date: '2024-01-20', type: 'Academic' },\n  ],\n  \n  // Behavioral Assessment\n  behavioralScores: {\n    discipline: 9,\n    teamwork: 8,\n    leadership: 9,\n    creativity: 7,\n    communication: 6,\n    responsibility: 9,\n  },\n};\n\nconst StudentProfile = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { studentId } = useParams();\n  const [activeTab, setActiveTab] = useState(0);\n  const [student, setStudent] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Simulate API call to fetch student data\n    const fetchStudentData = async () => {\n      setLoading(true);\n      try {\n        // In real app, fetch data based on studentId\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        setStudent(sampleStudentData);\n      } catch (error) {\n        console.error('Error fetching student data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStudentData();\n  }, [studentId]);\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const handleEditProfile = () => {\n    navigate(`/dashboard/students/${studentId}/edit`);\n  };\n\n  const handleSWOTAnalysis = () => {\n    navigate(`/dashboard/students/${studentId}/swot`);\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>\n        <LinearProgress sx={{ width: 300 }} />\n      </Box>\n    );\n  }\n\n  if (!student) {\n    return (\n      <Alert severity=\"error\">\n        Student not found. Please check the student ID and try again.\n      </Alert>\n    );\n  }\n\n  // Chart configurations\n  const performanceChartData = {\n    labels: student.performanceTrends.map(trend => trend.month),\n    datasets: [\n      {\n        label: 'GPA Trend',\n        data: student.performanceTrends.map(trend => trend.gpa),\n        borderColor: theme.palette.primary.main,\n        backgroundColor: alpha(theme.palette.primary.main, 0.1),\n        fill: true,\n        tension: 0.4,\n      },\n    ],\n  };\n\n  const behavioralRadarData = {\n    labels: Object.keys(student.behavioralScores).map(key => \n      key.charAt(0).toUpperCase() + key.slice(1)\n    ),\n    datasets: [\n      {\n        label: 'Behavioral Assessment',\n        data: Object.values(student.behavioralScores),\n        borderColor: theme.palette.secondary.main,\n        backgroundColor: alpha(theme.palette.secondary.main, 0.2),\n        pointBackgroundColor: theme.palette.secondary.main,\n        pointBorderColor: '#fff',\n        pointHoverBackgroundColor: '#fff',\n        pointHoverBorderColor: theme.palette.secondary.main,\n      },\n    ],\n  };\n\n  const subjectPerformanceData = {\n    labels: student.subjects.map(subject => subject.name),\n    datasets: [\n      {\n        label: 'Marks',\n        data: student.subjects.map(subject => subject.marks),\n        backgroundColor: student.subjects.map((_, index) => \n          `hsl(${(index * 60) % 360}, 70%, 60%)`\n        ),\n        borderColor: student.subjects.map((_, index) => \n          `hsl(${(index * 60) % 360}, 70%, 50%)`\n        ),\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>\n      {/* Header Section */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Card sx={{ mb: 3, overflow: 'visible' }}>\n          <CardContent sx={{ p: 4 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>\n                <Avatar\n                  src={student.profilePhoto}\n                  sx={{\n                    width: 120,\n                    height: 120,\n                    border: `4px solid ${theme.palette.primary.main}`,\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  <Person sx={{ fontSize: 60 }} />\n                </Avatar>\n                \n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600, mb: 1 }}>\n                    {student.firstName} {student.middleName} {student.lastName}\n                  </Typography>\n                  <Typography variant=\"h6\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    Class {student.grade} - Section {student.section}\n                  </Typography>\n                  <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Admission No: {student.admissionNumber} | Roll No: {student.rollNumber}\n                  </Typography>\n                  \n                  <Stack direction=\"row\" spacing={1} sx={{ mb: 2 }}>\n                    <Chip\n                      label={student.board}\n                      color=\"primary\"\n                      variant=\"filled\"\n                      sx={{ fontWeight: 500 }}\n                    />\n                    <Chip\n                      label={`GPA: ${student.currentGPA}`}\n                      color=\"success\"\n                      variant=\"outlined\"\n                      icon={<Star />}\n                    />\n                    <Chip\n                      label={`${student.attendance}% Attendance`}\n                      color={student.attendance >= 90 ? 'success' : student.attendance >= 75 ? 'warning' : 'error'}\n                      variant=\"outlined\"\n                    />\n                  </Stack>\n                </Box>\n              </Box>\n              \n              <Stack direction=\"row\" spacing={1}>\n                <IconButton onClick={handleEditProfile} color=\"primary\">\n                  <Edit />\n                </IconButton>\n                <IconButton color=\"primary\">\n                  <Print />\n                </IconButton>\n                <IconButton color=\"primary\">\n                  <Share />\n                </IconButton>\n                <Button\n                  variant=\"contained\"\n                  startIcon={<Assessment />}\n                  onClick={handleSWOTAnalysis}\n                  sx={{\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  SWOT Analysis\n                </Button>\n              </Stack>\n            </Box>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Tabs Navigation */}\n      <Card sx={{ mb: 3 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          variant=\"scrollable\"\n          scrollButtons=\"auto\"\n          sx={{\n            '& .MuiTab-root': {\n              minHeight: 64,\n              fontWeight: 500,\n            },\n          }}\n        >\n          <Tab icon={<Person />} label=\"Personal Info\" />\n          <Tab icon={<School />} label=\"Academic Performance\" />\n          <Tab icon={<Assessment />} label=\"SWOT Analysis\" />\n          <Tab icon={<EmojiEvents />} label=\"Achievements\" />\n        </Tabs>\n      </Card>\n\n      {/* Tab Content */}\n      <motion.div\n        key={activeTab}\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        {activeTab === 0 && <PersonalInfoTab student={student} />}\n        {activeTab === 1 && (\n          <AcademicPerformanceTab \n            student={student} \n            performanceChartData={performanceChartData}\n            subjectPerformanceData={subjectPerformanceData}\n            behavioralRadarData={behavioralRadarData}\n          />\n        )}\n        {activeTab === 2 && <SWOTAnalysisTab student={student} />}\n        {activeTab === 3 && <AchievementsTab student={student} />}\n      </motion.div>\n    </Box>\n  );\n};\n\n// Tab Components\nconst PersonalInfoTab = ({ student }) => {\n  const theme = useTheme();\n\n  return (\n    <Grid container spacing={3}>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Basic Information\n            </Typography>\n            <Stack spacing={2}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <CalendarToday color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Date of Birth:</Typography>\n                <Typography variant=\"body2\">{student.dateOfBirth}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <Person color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Gender:</Typography>\n                <Typography variant=\"body2\">{student.gender}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">Blood Group:</Typography>\n                <Typography variant=\"body2\">{student.bloodGroup}</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Contact Information\n            </Typography>\n            <Stack spacing={2}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <LocationOn color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Address:</Typography>\n                <Typography variant=\"body2\">{student.address}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <Phone color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Phone:</Typography>\n                <Typography variant=\"body2\">{student.phone}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <Email color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Email:</Typography>\n                <Typography variant=\"body2\">{student.email}</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Parent/Guardian Information\n            </Typography>\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 500 }}>Father's Details</Typography>\n                <Stack spacing={1}>\n                  <Typography variant=\"body2\">Name: {student.fatherName}</Typography>\n                  <Typography variant=\"body2\">Occupation: {student.fatherOccupation}</Typography>\n                  <Typography variant=\"body2\">Phone: {student.fatherPhone}</Typography>\n                </Stack>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 500 }}>Mother's Details</Typography>\n                <Stack spacing={1}>\n                  <Typography variant=\"body2\">Name: {student.motherName}</Typography>\n                  <Typography variant=\"body2\">Occupation: {student.motherOccupation}</Typography>\n                  <Typography variant=\"body2\">Phone: {student.motherPhone}</Typography>\n                </Stack>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n};\n\nconst AcademicPerformanceTab = ({ student, performanceChartData, subjectPerformanceData, behavioralRadarData }) => {\n  return (\n    <Grid container spacing={3}>\n      {/* Performance Overview */}\n      <Grid item xs={12} md={4}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Performance Overview\n            </Typography>\n            <Stack spacing={2}>\n              <Box>\n                <Typography variant=\"body2\" color=\"text.secondary\">Current GPA</Typography>\n                <Typography variant=\"h4\" color=\"primary.main\" sx={{ fontWeight: 600 }}>\n                  {student.currentGPA}\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" color=\"text.secondary\">Attendance</Typography>\n                <Typography variant=\"h4\" color=\"success.main\" sx={{ fontWeight: 600 }}>\n                  {student.attendance}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" color=\"text.secondary\">Class Rank</Typography>\n                <Typography variant=\"h4\" color=\"secondary.main\" sx={{ fontWeight: 600 }}>\n                  3rd\n                </Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* GPA Trend Chart */}\n      <Grid item xs={12} md={8}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              GPA Trend\n            </Typography>\n            <Box sx={{ height: 300 }}>\n              <Line\n                data={performanceChartData}\n                options={{\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      display: false,\n                    },\n                  },\n                  scales: {\n                    y: {\n                      beginAtZero: false,\n                      min: 7,\n                      max: 10,\n                    },\n                  },\n                }}\n              />\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* Subject Performance */}\n      <Grid item xs={12} md={8}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Subject Performance\n            </Typography>\n            <Box sx={{ height: 300 }}>\n              <Bar\n                data={subjectPerformanceData}\n                options={{\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      display: false,\n                    },\n                  },\n                  scales: {\n                    y: {\n                      beginAtZero: true,\n                      max: 100,\n                    },\n                  },\n                }}\n              />\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* Behavioral Assessment */}\n      <Grid item xs={12} md={4}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Behavioral Assessment\n            </Typography>\n            <Box sx={{ height: 300 }}>\n              <Radar\n                data={behavioralRadarData}\n                options={{\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      display: false,\n                    },\n                  },\n                  scales: {\n                    r: {\n                      beginAtZero: true,\n                      max: 10,\n                    },\n                  },\n                }}\n              />\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* Subject Details */}\n      <Grid item xs={12}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Subject Details\n            </Typography>\n            <Grid container spacing={2}>\n              {student.subjects.map((subject, index) => (\n                <Grid item xs={12} sm={6} md={4} key={index}>\n                  <Card variant=\"outlined\">\n                    <CardContent sx={{ p: 2 }}>\n                      <Typography variant=\"subtitle1\" sx={{ fontWeight: 600 }}>\n                        {subject.name}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                        Teacher: {subject.teacher}\n                      </Typography>\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <Chip\n                          label={subject.grade}\n                          color={subject.grade.startsWith('A') ? 'success' : 'warning'}\n                          size=\"small\"\n                        />\n                        <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                          {subject.marks}%\n                        </Typography>\n                      </Box>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n};\n\nconst SWOTAnalysisTab = ({ student }) => {\n  const theme = useTheme();\n\n  const swotCategories = [\n    {\n      title: 'Strengths',\n      items: student.swotAnalysis.strengths,\n      color: theme.palette.success.main,\n      icon: '💪',\n    },\n    {\n      title: 'Weaknesses',\n      items: student.swotAnalysis.weaknesses,\n      color: theme.palette.error.main,\n      icon: '⚠️',\n    },\n    {\n      title: 'Opportunities',\n      items: student.swotAnalysis.opportunities,\n      color: theme.palette.info.main,\n      icon: '🚀',\n    },\n    {\n      title: 'Threats',\n      items: student.swotAnalysis.threats,\n      color: theme.palette.warning.main,\n      icon: '⚡',\n    },\n  ];\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600 }}>\n          SWOT Analysis Overview\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<Assessment />}\n          sx={{\n            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n          }}\n        >\n          Update SWOT\n        </Button>\n      </Box>\n\n      <Grid container spacing={3}>\n        {swotCategories.map((category, index) => (\n          <Grid item xs={12} md={6} key={index}>\n            <Card\n              sx={{\n                height: '100%',\n                border: `2px solid ${alpha(category.color, 0.2)}`,\n                background: `linear-gradient(135deg, ${alpha(category.color, 0.05)} 0%, ${alpha(category.color, 0.02)} 100%)`,\n              }}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>\n                  <Typography variant=\"h2\" sx={{ fontSize: 24 }}>\n                    {category.icon}\n                  </Typography>\n                  <Typography\n                    variant=\"h6\"\n                    sx={{\n                      fontWeight: 600,\n                      color: category.color,\n                    }}\n                  >\n                    {category.title}\n                  </Typography>\n                </Box>\n\n                <Stack spacing={1}>\n                  {category.items.map((item, itemIndex) => (\n                    <Box\n                      key={itemIndex}\n                      sx={{\n                        p: 2,\n                        borderRadius: 1,\n                        background: alpha(category.color, 0.1),\n                        border: `1px solid ${alpha(category.color, 0.2)}`,\n                      }}\n                    >\n                      <Typography variant=\"body2\">\n                        {item}\n                      </Typography>\n                    </Box>\n                  ))}\n                </Stack>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* SWOT Matrix Visualization */}\n      <Card sx={{ mt: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            SWOT Matrix\n          </Typography>\n          <Grid container spacing={2}>\n            <Grid item xs={6}>\n              <Box\n                sx={{\n                  p: 2,\n                  border: `2px solid ${theme.palette.success.main}`,\n                  borderRadius: 1,\n                  background: alpha(theme.palette.success.main, 0.05),\n                  minHeight: 150,\n                }}\n              >\n                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, color: 'success.main', mb: 1 }}>\n                  Strengths (Internal Positive)\n                </Typography>\n                {student.swotAnalysis.strengths.map((strength, index) => (\n                  <Typography key={index} variant=\"body2\" sx={{ mb: 0.5 }}>\n                    • {strength}\n                  </Typography>\n                ))}\n              </Box>\n            </Grid>\n            <Grid item xs={6}>\n              <Box\n                sx={{\n                  p: 2,\n                  border: `2px solid ${theme.palette.error.main}`,\n                  borderRadius: 1,\n                  background: alpha(theme.palette.error.main, 0.05),\n                  minHeight: 150,\n                }}\n              >\n                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, color: 'error.main', mb: 1 }}>\n                  Weaknesses (Internal Negative)\n                </Typography>\n                {student.swotAnalysis.weaknesses.map((weakness, index) => (\n                  <Typography key={index} variant=\"body2\" sx={{ mb: 0.5 }}>\n                    • {weakness}\n                  </Typography>\n                ))}\n              </Box>\n            </Grid>\n            <Grid item xs={6}>\n              <Box\n                sx={{\n                  p: 2,\n                  border: `2px solid ${theme.palette.info.main}`,\n                  borderRadius: 1,\n                  background: alpha(theme.palette.info.main, 0.05),\n                  minHeight: 150,\n                }}\n              >\n                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, color: 'info.main', mb: 1 }}>\n                  Opportunities (External Positive)\n                </Typography>\n                {student.swotAnalysis.opportunities.map((opportunity, index) => (\n                  <Typography key={index} variant=\"body2\" sx={{ mb: 0.5 }}>\n                    • {opportunity}\n                  </Typography>\n                ))}\n              </Box>\n            </Grid>\n            <Grid item xs={6}>\n              <Box\n                sx={{\n                  p: 2,\n                  border: `2px solid ${theme.palette.warning.main}`,\n                  borderRadius: 1,\n                  background: alpha(theme.palette.warning.main, 0.05),\n                  minHeight: 150,\n                }}\n              >\n                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, color: 'warning.main', mb: 1 }}>\n                  Threats (External Negative)\n                </Typography>\n                {student.swotAnalysis.threats.map((threat, index) => (\n                  <Typography key={index} variant=\"body2\" sx={{ mb: 0.5 }}>\n                    • {threat}\n                  </Typography>\n                ))}\n              </Box>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nconst AchievementsTab = ({ student }) => {\n  const theme = useTheme();\n\n  const getAchievementIcon = (type) => {\n    switch (type) {\n      case 'Academic':\n        return <School sx={{ color: theme.palette.primary.main }} />;\n      case 'Behavioral':\n        return <EmojiEvents sx={{ color: theme.palette.secondary.main }} />;\n      default:\n        return <Star sx={{ color: theme.palette.warning.main }} />;\n    }\n  };\n\n  const getAchievementColor = (type) => {\n    switch (type) {\n      case 'Academic':\n        return theme.palette.primary.main;\n      case 'Behavioral':\n        return theme.palette.secondary.main;\n      default:\n        return theme.palette.warning.main;\n    }\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h5\" sx={{ fontWeight: 600, mb: 3 }}>\n        Achievements & Recognition\n      </Typography>\n\n      <Grid container spacing={3}>\n        {student.achievements.map((achievement, index) => (\n          <Grid item xs={12} md={6} key={index}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n            >\n              <Card\n                sx={{\n                  border: `2px solid ${alpha(getAchievementColor(achievement.type), 0.2)}`,\n                  background: `linear-gradient(135deg, ${alpha(getAchievementColor(achievement.type), 0.05)} 0%, ${alpha(getAchievementColor(achievement.type), 0.02)} 100%)`,\n                  transition: 'transform 0.2s ease',\n                  '&:hover': {\n                    transform: 'translateY(-4px)',\n                  },\n                }}\n              >\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>\n                    {getAchievementIcon(achievement.type)}\n                    <Box>\n                      <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                        {achievement.title}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        {new Date(achievement.date).toLocaleDateString('en-IN', {\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric',\n                        })}\n                      </Typography>\n                    </Box>\n                  </Box>\n\n                  <Chip\n                    label={achievement.type}\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: getAchievementColor(achievement.type),\n                      color: 'white',\n                      fontWeight: 500,\n                    }}\n                  />\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Achievement Statistics */}\n      <Card sx={{ mt: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Achievement Statistics\n          </Typography>\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={4}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h3\" color=\"primary.main\" sx={{ fontWeight: 600 }}>\n                  {student.achievements.filter(a => a.type === 'Academic').length}\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                  Academic Awards\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h3\" color=\"secondary.main\" sx={{ fontWeight: 600 }}>\n                  {student.achievements.filter(a => a.type === 'Behavioral').length}\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                  Behavioral Recognition\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h3\" color=\"warning.main\" sx={{ fontWeight: 600 }}>\n                  {student.achievements.length}\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                  Total Achievements\n                </Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default StudentProfile;\n"], "names": ["ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "RadialLinearScale", "BarElement", "Title", "ChartTooltip", "Legend", "Filler", "sampleStudentData", "id", "firstName", "middleName", "lastName", "admissionNumber", "grade", "section", "rollNumber", "board", "dateOfBirth", "gender", "bloodGroup", "profilePhoto", "address", "city", "state", "pincode", "phone", "email", "<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "motherOccupation", "motherPhone", "currentGPA", "attendance", "subjects", "name", "marks", "teacher", "performanceTrends", "month", "gpa", "swotAnalysis", "strengths", "weaknesses", "opportunities", "threats", "achievements", "title", "date", "type", "behavioralScores", "discipline", "teamwork", "leadership", "creativity", "communication", "responsibility", "StudentProfile", "theme", "useTheme", "navigate", "useNavigate", "studentId", "useParams", "activeTab", "setActiveTab", "useState", "student", "setStudent", "loading", "setLoading", "useEffect", "async", "Promise", "resolve", "setTimeout", "error", "fetchStudentData", "jsx", "Box", "sx", "display", "justifyContent", "alignItems", "height", "children", "LinearProgress", "width", "<PERSON><PERSON>", "severity", "performanceChartData", "labels", "map", "trend", "datasets", "label", "data", "borderColor", "palette", "primary", "main", "backgroundColor", "alpha", "fill", "tension", "behavioralRadarData", "Object", "keys", "key", "char<PERSON>t", "toUpperCase", "slice", "values", "secondary", "pointBackgroundColor", "pointBorderColor", "pointHoverBackgroundColor", "pointHoverBorderColor", "subjectPerformanceData", "subject", "_", "index", "borderWidth", "max<PERSON><PERSON><PERSON>", "mx", "p", "jsxRuntimeExports", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "Card", "mb", "overflow", "<PERSON><PERSON><PERSON><PERSON>", "jsxs", "gap", "Avatar", "src", "border", "background", "Person", "fontSize", "Typography", "variant", "fontWeight", "color", "<PERSON><PERSON>", "direction", "spacing", "Chip", "icon", "Star", "IconButton", "onClick", "Edit", "Print", "Share", "<PERSON><PERSON>", "startIcon", "Assessment", "Tabs", "value", "onChange", "event", "newValue", "scrollButtons", "minHeight", "Tab", "School", "EmojiEvents", "x", "PersonalInfoTab", "AcademicPerformanceTab", "SWOTAnalysisTab", "AchievementsTab", "Grid", "container", "item", "xs", "md", "CalendarToday", "LocationOn", "Phone", "Email", "Line", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "scales", "beginAtZero", "min", "max", "Bar", "Radar", "r", "sm", "startsWith", "size", "swotCategories", "items", "success", "info", "warning", "category", "itemIndex", "borderRadius", "mt", "strength", "weakness", "opportunity", "threat", "getAchievementIcon", "getAchievementColor", "achievement", "delay", "transform", "Date", "toLocaleDateString", "year", "day", "textAlign", "filter", "a", "length"], "mappings": "wkBA4DAA,EAAQC,SACNC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAIF,MAAMC,EAAoB,CACxBC,GAAI,EACJC,UAAW,QACXC,WAAY,QACZC,SAAU,QACVC,gBAAiB,aACjBC,MAAO,GACPC,QAAS,IACTC,WAAY,GACZC,MAAO,OACPC,YAAa,aACbC,OAAQ,OACRC,WAAY,KACZC,aAAc,KAGdC,QAAS,uCACTC,KAAM,YACNC,MAAO,YACPC,QAAS,SACTC,MAAO,iBACPC,MAAO,0BAGPC,WAAY,qBACZC,iBAAkB,oBAClBC,YAAa,iBACbC,WAAY,cACZC,iBAAkB,UAClBC,YAAa,iBAGbC,WAAY,IACZC,WAAY,GACZC,SAAU,CACR,CAAEC,KAAM,cAAevB,MAAO,KAAMwB,MAAO,GAAIC,QAAS,eACxD,CAAEF,KAAM,UAAWvB,MAAO,KAAMwB,MAAO,GAAIC,QAAS,aACpD,CAAEF,KAAM,UAAWvB,MAAO,KAAMwB,MAAO,GAAIC,QAAS,eACpD,CAAEF,KAAM,QAASvB,MAAO,KAAMwB,MAAO,GAAIC,QAAS,cAClD,CAAEF,KAAM,iBAAkBvB,MAAO,KAAMwB,MAAO,GAAIC,QAAS,aAC3D,CAAEF,KAAM,SAAUvB,MAAO,KAAMwB,MAAO,GAAIC,QAAS,aAIrDC,kBAAmB,CACjB,CAAEC,MAAO,MAAOC,IAAK,KACrB,CAAED,MAAO,MAAOC,IAAK,KACrB,CAAED,MAAO,MAAOC,IAAK,KACrB,CAAED,MAAO,MAAOC,IAAK,KACrB,CAAED,MAAO,MAAOC,IAAK,KACrB,CAAED,MAAO,MAAOC,IAAK,MAIvBC,aAAc,CACZC,UAAW,CAAC,wBAAyB,yBAA0B,wBAC/DC,WAAY,CAAC,uCAAwC,4BACrDC,cAAe,CAAC,iCAAkC,6BAClDC,QAAS,CAAC,wBAAyB,+BAIrCC,aAAc,CACZ,CAAEC,MAAO,wCAAyCC,KAAM,aAAcC,KAAM,YAC5E,CAAEF,MAAO,4BAA6BC,KAAM,aAAcC,KAAM,cAChE,CAAEF,MAAO,6BAA8BC,KAAM,aAAcC,KAAM,aAInEC,iBAAkB,CAChBC,WAAY,EACZC,SAAU,EACVC,WAAY,EACZC,WAAY,EACZC,cAAe,EACfC,eAAgB,IAIdC,EAAiB,KACrB,MAAMC,EAAQC,IACRC,EAAWC,KACXC,UAAEA,GAAcC,KACfC,EAAWC,GAAgBC,EAAAA,SAAS,IACpCC,EAASC,GAAcF,EAAAA,SAAS,OAChCG,EAASC,GAAcJ,EAAAA,UAAS,GAEvCK,EAAAA,WAAU,KAEiBC,WACvBF,GAAW,GACP,UAEI,IAAIG,SAAQC,GAAWC,WAAWD,EAAS,OACjDN,EAAW9D,SACJsE,GAC4C,CACnD,QACAN,GAAW,EAAK,GAIHO,EAAA,GAChB,CAACf,IAcJ,GAAIO,EAEAS,OAAAA,EAAAA,IAACC,GAAIC,GAAI,CAAEC,QAAS,OAAQC,eAAgB,SAAUC,WAAY,SAAUC,OAAQ,KAClFC,eAACC,EAAe,CAAAN,GAAI,CAAEO,MAAO,SAKnC,IAAKpB,EAEAW,OAAAA,EAAAA,IAAAU,EAAA,CAAMC,SAAS,QAAQJ,SAExB,kEAKJ,MAAMK,EAAuB,CAC3BC,OAAQxB,EAAQ7B,kBAAkBsD,KAAIC,GAASA,EAAMtD,QACrDuD,SAAU,CACR,CACEC,MAAO,YACPC,KAAM7B,EAAQ7B,kBAAkBsD,KAAIC,GAASA,EAAMrD,MACnDyD,YAAavC,EAAMwC,QAAQC,QAAQC,KACnCC,gBAAiBC,EAAM5C,EAAMwC,QAAQC,QAAQC,KAAM,IACnDG,MAAM,EACNC,QAAS,MAKTC,EAAsB,CAC1Bd,OAAQe,OAAOC,KAAKxC,EAAQjB,kBAAkB0C,KAAIgB,GAChDA,EAAIC,OAAO,GAAGC,cAAgBF,EAAIG,MAAM,KAE1CjB,SAAU,CACR,CACEC,MAAO,wBACPC,KAAMU,OAAOM,OAAO7C,EAAQjB,kBAC5B+C,YAAavC,EAAMwC,QAAQe,UAAUb,KACrCC,gBAAiBC,EAAM5C,EAAMwC,QAAQe,UAAUb,KAAM,IACrDc,qBAAsBxD,EAAMwC,QAAQe,UAAUb,KAC9Ce,iBAAkB,OAClBC,0BAA2B,OAC3BC,sBAAuB3D,EAAMwC,QAAQe,UAAUb,QAK/CkB,EAAyB,CAC7B3B,OAAQxB,EAAQjC,SAAS0D,KAAI2B,GAAWA,EAAQpF,OAChD2D,SAAU,CACR,CACEC,MAAO,QACPC,KAAM7B,EAAQjC,SAAS0D,KAAI2B,GAAWA,EAAQnF,QAC9CiE,gBAAiBlC,EAAQjC,SAAS0D,KAAI,CAAC4B,EAAGC,IACxC,OAAgB,GAARA,EAAc,mBAExBxB,YAAa9B,EAAQjC,SAAS0D,KAAI,CAAC4B,EAAGC,IACpC,OAAgB,GAARA,EAAc,mBAExBC,YAAa,KAMjB,cAAC3C,EAAI,CAAAC,GAAI,CAAE2C,SAAU,KAAMC,GAAI,OAAQC,EAAG,GAExCxC,SAAA,CAAAyC,EAAAhD,IAACiD,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,GAAO,IAC9BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAExBjD,SAACyC,EAAAhD,IAAAyD,EAAA,CAAKvD,GAAI,CAAEwD,GAAI,EAAGC,SAAU,WAC3BpD,WAAAP,IAAC4D,EAAY,CAAA1D,GAAI,CAAE6C,EAAG,GACpBxC,SAACsD,EAAAA,KAAA5D,EAAA,CAAIC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUD,eAAgB,gBAAiBsD,GAAI,GACrFnD,SAAA,CAACsD,EAAAA,KAAA5D,EAAA,CAAIC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUyD,IAAK,GACrDvD,SAAA,CAAAyC,EAAAhD,IAAC+D,EAAA,CACCC,IAAK3E,EAAQhD,aACb6D,GAAI,CACFO,MAAO,IACPH,OAAQ,IACR2D,OAAQ,aAAarF,EAAMwC,QAAQC,QAAQC,OAC3C4C,WAAY,2BAA2BtF,EAAMwC,QAAQC,QAAQC,YAAY1C,EAAMwC,QAAQe,UAAUb,cAGnGf,eAAC4D,EAAO,CAAAjE,GAAI,CAAEkE,SAAU,eAGzBnE,EACC,CAAAM,SAAA,CAACsD,EAAAA,KAAAQ,EAAA,CAAWC,QAAQ,KAAKpE,GAAI,CAAEqE,WAAY,IAAKb,GAAI,GACjDnD,SAAA,CAAQlB,EAAA3D,UAAU,IAAE2D,EAAQ1D,WAAW,IAAE0D,EAAQzD,YAEpDiI,EAAAA,KAACQ,EAAW,CAAAC,QAAQ,KAAKE,MAAM,iBAAiBtE,GAAI,CAAEwD,GAAI,GAAKnD,SAAA,CAAA,SACtDlB,EAAQvD,MAAM,cAAYuD,EAAQtD,WAE3C8H,EAAAA,KAACQ,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBtE,GAAI,CAAEwD,GAAI,GAAKnD,SAAA,CAAA,iBACjDlB,EAAQxD,gBAAgB,eAAawD,EAAQrD,cAG9D6H,EAAAA,KAACY,EAAM,CAAAC,UAAU,MAAMC,QAAS,EAAGzE,GAAI,CAAEwD,GAAI,GAC3CnD,SAAA,CAAAyC,EAAAhD,IAAC4E,EAAA,CACC3D,MAAO5B,EAAQpD,MACfuI,MAAM,UACNF,QAAQ,SACRpE,GAAI,CAAEqE,WAAY,OAEpBvB,EAAAhD,IAAC4E,EAAA,CACC3D,MAAO,QAAQ5B,EAAQnC,aACvBsH,MAAM,UACNF,QAAQ,WACRO,WAAOC,EAAK,CAAA,KAEd9B,EAAAhD,IAAC4E,EAAA,CACC3D,MAAO,GAAG5B,EAAQlC,yBAClBqH,MAAOnF,EAAQlC,YAAc,GAAK,UAAYkC,EAAQlC,YAAc,GAAK,UAAY,QACrFmH,QAAQ,sBAMfT,EAAAA,KAAAY,EAAA,CAAMC,UAAU,MAAMC,QAAS,EAC9BpE,SAAA,CAAAP,EAAAA,IAAC+E,GAAWC,QApIA,KACflG,EAAA,uBAAuBE,SAAgB,EAmIIwF,MAAM,UAC5CjE,WAAAP,IAACiF,cAEFF,EAAW,CAAAP,MAAM,UAChBjE,SAAAP,MAACkF,GAAM,WAERH,EAAW,CAAAP,MAAM,UAChBjE,SAAAP,MAACmF,GAAM,KAETnC,EAAAhD,IAACoF,EAAA,CACCd,QAAQ,YACRe,gBAAYC,EAAW,IACvBN,QA5IW,KAChBlG,EAAA,uBAAuBE,SAAgB,EA4IlCkB,GAAI,CACFgE,WAAY,2BAA2BtF,EAAMwC,QAAQC,QAAQC,YAAY1C,EAAMwC,QAAQe,UAAUb,cAEpGf,SAAA,oCAUVkD,EAAK,CAAAvD,GAAI,CAAEwD,GAAI,GACdnD,SAAAyC,EAAAa,KAAC0B,EAAA,CACCC,MAAOtG,EACPuG,SArKgB,CAACC,EAAOC,KAC9BxG,EAAawG,EAAQ,EAqKfrB,QAAQ,aACRsB,cAAc,OACd1F,GAAI,CACF,iBAAkB,CAChB2F,UAAW,GACXtB,WAAY,MAIhBhE,SAAA,OAACuF,GAAIjB,KAAM7E,EAAAA,IAACmE,EAAO,IAAIlD,MAAM,wBAC5B6E,EAAI,CAAAjB,WAAOkB,EAAO,IAAI9E,MAAM,+BAC5B6E,EAAI,CAAAjB,WAAOS,EAAW,IAAIrE,MAAM,wBAChC6E,EAAI,CAAAjB,WAAOmB,EAAY,CAAA,GAAI/E,MAAM,sBAKtC+B,EAAAa,KAACZ,EAAOC,IAAP,CAECC,QAAS,CAAEC,QAAS,EAAG6C,EAAG,IAC1B3C,QAAS,CAAEF,QAAS,EAAG6C,EAAG,GAC1B1C,WAAY,CAAEC,SAAU,IAEvBjD,SAAA,CAAc,IAAArB,GAAMc,EAAAA,IAAAkG,EAAA,CAAgB7G,YACtB,IAAdH,GACC8D,EAAAhD,IAACmG,EAAA,CACC9G,UACAuB,uBACA4B,yBACAb,wBAGW,IAAdzC,GAAoBc,EAAAA,IAAAoG,EAAA,CAAgB/G,YACtB,IAAdH,GAAoBc,EAAAA,IAAAqG,EAAA,CAAgBhH,cAfhCH,KAiBT,EAKEgH,EAAkB,EAAG7G,cACFR,IAGpBgF,EAAAA,KAAAyC,EAAA,CAAKC,WAAS,EAAC5B,QAAS,EACvBpE,SAAA,CAACP,EAAAA,IAAAsG,EAAA,CAAKE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAP,EAAAA,IAACyD,EACC,CAAAlD,WAAAsD,KAACD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,wBACAsD,KAACY,EAAM,CAAAE,QAAS,EACdpE,SAAA,CAACsD,EAAAA,KAAA5D,EAAA,CAAIC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUyD,IAAK,GACrDvD,SAAA,GAACP,IAAA2G,EAAA,CAAcnC,MAAM,iBACpBH,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAAc,mBAChEP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,QAAS/D,WAAQrE,iBAEvC2H,EAAAA,KAAC5D,EAAI,CAAAC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUyD,IAAK,GACrDvD,SAAA,GAACP,IAAAmE,EAAA,CAAOK,MAAM,iBACbH,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAAO,YACzDP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,QAAS/D,WAAQpE,YAEvC0H,EAAAA,KAAC5D,EAAI,CAAAC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUyD,IAAK,GACrDvD,SAAA,CAAAP,MAACqE,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAAY,iBAC9DP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,QAAS/D,WAAQnE,0BAO/C4D,EAAAA,IAACsG,EAAK,CAAAE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAP,EAAAA,IAACyD,EACC,CAAAlD,WAAAsD,KAACD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,0BACAsD,KAACY,EAAM,CAAAE,QAAS,EACdpE,SAAA,CAACsD,EAAAA,KAAA5D,EAAA,CAAIC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUyD,IAAK,GACrDvD,SAAA,GAACP,IAAA4G,EAAA,CAAWpC,MAAM,iBACjBH,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAAQ,aAC1DP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,QAAS/D,WAAQjE,aAEvCuH,EAAAA,KAAC5D,EAAI,CAAAC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUyD,IAAK,GACrDvD,SAAA,GAACP,IAAA6G,EAAA,CAAMrC,MAAM,iBACZH,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAAM,WACxDP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,QAAS/D,WAAQ7D,WAEvCmH,EAAAA,KAAC5D,EAAI,CAAAC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUyD,IAAK,GACrDvD,SAAA,GAACP,IAAA8G,EAAA,CAAMtC,MAAM,iBACZH,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAAM,WACxDP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,QAAS/D,WAAQ5D,uBAO/CqD,IAACsG,GAAKE,MAAI,EAACC,GAAI,GACblG,SAAAP,EAAAA,IAACyD,EACC,CAAAlD,WAAAsD,KAACD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,gCACCsD,EAAAA,KAAAyC,EAAA,CAAKC,WAAS,EAAC5B,QAAS,EACvBpE,SAAA,CAAAsD,OAACyC,GAAKE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAA,CAACP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,YAAYpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,KAAOhE,SAAgB,uBAChFsD,KAACY,EAAM,CAAAE,QAAS,EACdpE,SAAA,GAACsD,KAAAQ,EAAA,CAAWC,QAAQ,QAAQ/D,SAAA,CAAA,SAAOlB,EAAQzC,gBAC3CiH,KAACQ,EAAW,CAAAC,QAAQ,QAAQ/D,SAAA,CAAA,eAAalB,EAAQxC,sBACjDgH,KAACQ,EAAW,CAAAC,QAAQ,QAAQ/D,SAAA,CAAA,UAAQlB,EAAQvC,4BAG/CwJ,EAAK,CAAAE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAA,CAACP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,YAAYpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,KAAOhE,SAAgB,uBAChFsD,KAACY,EAAM,CAAAE,QAAS,EACdpE,SAAA,GAACsD,KAAAQ,EAAA,CAAWC,QAAQ,QAAQ/D,SAAA,CAAA,SAAOlB,EAAQtC,gBAC3C8G,KAACQ,EAAW,CAAAC,QAAQ,QAAQ/D,SAAA,CAAA,eAAalB,EAAQrC,sBACjD6G,KAACQ,EAAW,CAAAC,QAAQ,QAAQ/D,SAAA,CAAA,UAAQlB,EAAQpC,mCAWxDkJ,EAAyB,EAAG9G,UAASuB,uBAAsB4B,yBAAwBb,yBAEpFkC,EAAAA,KAAAyC,EAAA,CAAKC,WAAS,EAAC5B,QAAS,EAEvBpE,SAAA,CAACP,EAAAA,IAAAsG,EAAA,CAAKE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAP,EAAAA,IAACyD,EACC,CAAAlD,WAAAsD,KAACD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,2BACAsD,KAACY,EAAM,CAAAE,QAAS,EACdpE,SAAA,QAACN,EACC,CAAAM,SAAA,CAAAP,MAACqE,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAAW,kBAC7DP,IAAAqE,EAAA,CAAWC,QAAQ,KAAKE,MAAM,eAAetE,GAAI,CAAEqE,WAAY,KAC7DhE,SAAAlB,EAAQnC,uBAGZ+C,EACC,CAAAM,SAAA,CAAAP,MAACqE,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAAU,eAC7DsD,EAAAA,KAACQ,EAAW,CAAAC,QAAQ,KAAKE,MAAM,eAAetE,GAAI,CAAEqE,WAAY,KAC7DhE,SAAA,CAAQlB,EAAAlC,WAAW,iBAGvB8C,EACC,CAAAM,SAAA,CAAAP,MAACqE,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAAU,eAC7DP,EAAAA,IAACqE,EAAW,CAAAC,QAAQ,KAAKE,MAAM,iBAAiBtE,GAAI,CAAEqE,WAAY,KAAOhE,SAEzE,qBAQVP,EAAAA,IAACsG,EAAK,CAAAE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAP,EAAAA,IAACyD,EACC,CAAAlD,WAAAsD,KAACD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,oBACCN,EAAI,CAAAC,GAAI,CAAEI,OAAQ,KACjBC,SAAAyC,EAAAhD,IAAC+G,EAAA,CACC7F,KAAMN,EACNoG,QAAS,CACPC,YAAY,EACZC,qBAAqB,EACrBC,QAAS,CACPC,OAAQ,CACNjH,SAAS,IAGbkH,OAAQ,CACNhE,EAAG,CACDiE,aAAa,EACbC,IAAK,EACLC,IAAK,iBAWrBxH,EAAAA,IAACsG,EAAK,CAAAE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAP,EAAAA,IAACyD,EACC,CAAAlD,WAAAsD,KAACD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,8BACCN,EAAI,CAAAC,GAAI,CAAEI,OAAQ,KACjBC,SAAAyC,EAAAhD,IAACyH,EAAA,CACCvG,KAAMsB,EACNwE,QAAS,CACPC,YAAY,EACZC,qBAAqB,EACrBC,QAAS,CACPC,OAAQ,CACNjH,SAAS,IAGbkH,OAAQ,CACNhE,EAAG,CACDiE,aAAa,EACbE,IAAK,kBAWrBxH,EAAAA,IAACsG,EAAK,CAAAE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAP,EAAAA,IAACyD,EACC,CAAAlD,WAAAsD,KAACD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,gCACCN,EAAI,CAAAC,GAAI,CAAEI,OAAQ,KACjBC,SAAAyC,EAAAhD,IAAC0H,EAAA,CACCxG,KAAMS,EACNqF,QAAS,CACPC,YAAY,EACZC,qBAAqB,EACrBC,QAAS,CACPC,OAAQ,CACNjH,SAAS,IAGbkH,OAAQ,CACNM,EAAG,CACDL,aAAa,EACbE,IAAK,mBAWrBxH,IAACsG,GAAKE,MAAI,EAACC,GAAI,GACblG,SAAAP,EAAAA,IAACyD,EACC,CAAAlD,WAAAsD,KAACD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,oBACCP,EAAAA,IAAAsG,EAAA,CAAKC,WAAS,EAAC5B,QAAS,EACtBpE,SAAAlB,EAAQjC,SAAS0D,KAAI,CAAC2B,EAASE,IAC9B3C,EAAAA,IAACsG,GAAKE,MAAI,EAACC,GAAI,GAAImB,GAAI,EAAGlB,GAAI,EAC5BnG,eAACkD,EAAK,CAAAa,QAAQ,WACZ/D,SAAAsD,EAAAA,KAACD,EAAY,CAAA1D,GAAI,CAAE6C,EAAG,GACpBxC,SAAA,CAACP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,YAAYpE,GAAI,CAAEqE,WAAY,KAC/ChE,SAAAkC,EAAQpF,OAEXwG,EAAAA,KAACQ,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBtE,GAAI,CAAEwD,GAAI,GAAKnD,SAAA,CAAA,YACtDkC,EAAQlF,WAEpBsG,EAAAA,KAAC5D,EAAI,CAAAC,GAAI,CAAEC,QAAS,OAAQC,eAAgB,gBAAiBC,WAAY,UACvEE,SAAA,CAAAyC,EAAAhD,IAAC4E,EAAA,CACC3D,MAAOwB,EAAQ3G,MACf0I,MAAO/B,EAAQ3G,MAAM+L,WAAW,KAAO,UAAY,UACnDC,KAAK,UAEPjE,OAACQ,GAAWC,QAAQ,KAAKpE,GAAI,CAAEqE,WAAY,KACxChE,SAAA,CAAQkC,EAAAnF,MAAM,gBAhBaqF,iBA+BhDyD,EAAkB,EAAG/G,cACzB,MAAMT,EAAQC,IAERkJ,EAAiB,CACrB,CACE9J,MAAO,YACP+J,MAAO3I,EAAQ1B,aAAaC,UAC5B4G,MAAO5F,EAAMwC,QAAQ6G,QAAQ3G,KAC7BuD,KAAM,MAER,CACE5G,MAAO,aACP+J,MAAO3I,EAAQ1B,aAAaE,WAC5B2G,MAAO5F,EAAMwC,QAAQtB,MAAMwB,KAC3BuD,KAAM,MAER,CACE5G,MAAO,gBACP+J,MAAO3I,EAAQ1B,aAAaG,cAC5B0G,MAAO5F,EAAMwC,QAAQ8G,KAAK5G,KAC1BuD,KAAM,MAER,CACE5G,MAAO,UACP+J,MAAO3I,EAAQ1B,aAAaI,QAC5ByG,MAAO5F,EAAMwC,QAAQ+G,QAAQ7G,KAC7BuD,KAAM,MAIV,cACG5E,EACC,CAAAM,SAAA,CAACsD,EAAAA,KAAA5D,EAAA,CAAIC,GAAI,CAAEC,QAAS,OAAQC,eAAgB,gBAAiBC,WAAY,SAAUqD,GAAI,GACrFnD,SAAA,CAACP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,KAAKpE,GAAI,CAAEqE,WAAY,KAAOhE,SAElD,2BACAyC,EAAAhD,IAACoF,EAAA,CACCd,QAAQ,YACRe,gBAAYC,EAAW,IACvBpF,GAAI,CACFgE,WAAY,2BAA2BtF,EAAMwC,QAAQC,QAAQC,YAAY1C,EAAMwC,QAAQe,UAAUb,cAEpGf,SAAA,yBAKF+F,EAAK,CAAAC,WAAS,EAAC5B,QAAS,EACtBpE,WAAeO,KAAI,CAACsH,EAAUzF,UAC5B2D,EAAK,CAAAE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAyC,EAAAhD,IAACyD,EAAA,CACCvD,GAAI,CACFI,OAAQ,OACR2D,OAAQ,aAAazC,EAAM4G,EAAS5D,MAAO,MAC3CN,WAAY,2BAA2B1C,EAAM4G,EAAS5D,MAAO,YAAahD,EAAM4G,EAAS5D,MAAO,cAGlGjE,gBAACqD,EACC,CAAArD,SAAA,CAACsD,EAAAA,KAAA5D,EAAA,CAAIC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUyD,IAAK,EAAGJ,GAAI,GAC5DnD,SAAA,CAACP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,KAAKpE,GAAI,CAAEkE,SAAU,IACtC7D,SAAA6H,EAASvD,OAEZ7B,EAAAhD,IAACqE,EAAA,CACCC,QAAQ,KACRpE,GAAI,CACFqE,WAAY,IACZC,MAAO4D,EAAS5D,OAGjBjE,SAAS6H,EAAAnK,WAId+B,EAAAA,IAACyE,GAAME,QAAS,EACbpE,WAASyH,MAAMlH,KAAI,CAAC0F,EAAM6B,IACzBrF,EAAAhD,IAACC,EAAA,CAECC,GAAI,CACF6C,EAAG,EACHuF,aAAc,EACdpE,WAAY1C,EAAM4G,EAAS5D,MAAO,IAClCP,OAAQ,aAAazC,EAAM4G,EAAS5D,MAAO,OAG7CjE,WAACP,IAAAqE,EAAA,CAAWC,QAAQ,QACjB/D,SACHiG,KAVK6B,aA3Bc1F,OAgDnC3C,EAAAA,IAACyD,GAAKvD,GAAI,CAAEqI,GAAI,GACdhI,gBAACqD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,gBACCsD,EAAAA,KAAAyC,EAAA,CAAKC,WAAS,EAAC5B,QAAS,EACvBpE,SAAA,CAAAP,EAAAA,IAACsG,EAAK,CAAAE,MAAI,EAACC,GAAI,EACblG,SAAAyC,EAAAa,KAAC5D,EAAA,CACCC,GAAI,CACF6C,EAAG,EACHkB,OAAQ,aAAarF,EAAMwC,QAAQ6G,QAAQ3G,OAC3CgH,aAAc,EACdpE,WAAY1C,EAAM5C,EAAMwC,QAAQ6G,QAAQ3G,KAAM,KAC9CuE,UAAW,KAGbtF,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,YAAYpE,GAAI,CAAEqE,WAAY,IAAKC,MAAO,eAAgBd,GAAI,GAAKnD,SAEvF,kCACClB,EAAQ1B,aAAaC,UAAUkD,KAAI,CAAC0H,EAAU7F,IAC5CK,EAAAa,KAAAQ,EAAA,CAAuBC,QAAQ,QAAQpE,GAAI,CAAEwD,GAAI,IAAOnD,SAAA,CAAA,KACpDiI,IADY7F,UAMtB3C,EAAAA,IAAAsG,EAAA,CAAKE,MAAI,EAACC,GAAI,EACblG,SAAAyC,EAAAa,KAAC5D,EAAA,CACCC,GAAI,CACF6C,EAAG,EACHkB,OAAQ,aAAarF,EAAMwC,QAAQtB,MAAMwB,OACzCgH,aAAc,EACdpE,WAAY1C,EAAM5C,EAAMwC,QAAQtB,MAAMwB,KAAM,KAC5CuE,UAAW,KAGbtF,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,YAAYpE,GAAI,CAAEqE,WAAY,IAAKC,MAAO,aAAcd,GAAI,GAAKnD,SAErF,mCACClB,EAAQ1B,aAAaE,WAAWiD,KAAI,CAAC2H,EAAU9F,IAC7CK,EAAAa,KAAAQ,EAAA,CAAuBC,QAAQ,QAAQpE,GAAI,CAAEwD,GAAI,IAAOnD,SAAA,CAAA,KACpDkI,IADY9F,UAMtB3C,EAAAA,IAAAsG,EAAA,CAAKE,MAAI,EAACC,GAAI,EACblG,SAAAyC,EAAAa,KAAC5D,EAAA,CACCC,GAAI,CACF6C,EAAG,EACHkB,OAAQ,aAAarF,EAAMwC,QAAQ8G,KAAK5G,OACxCgH,aAAc,EACdpE,WAAY1C,EAAM5C,EAAMwC,QAAQ8G,KAAK5G,KAAM,KAC3CuE,UAAW,KAGbtF,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,YAAYpE,GAAI,CAAEqE,WAAY,IAAKC,MAAO,YAAad,GAAI,GAAKnD,SAEpF,sCACClB,EAAQ1B,aAAaG,cAAcgD,KAAI,CAAC4H,EAAa/F,IACnDK,EAAAa,KAAAQ,EAAA,CAAuBC,QAAQ,QAAQpE,GAAI,CAAEwD,GAAI,IAAOnD,SAAA,CAAA,KACpDmI,IADY/F,UAMtB3C,EAAAA,IAAAsG,EAAA,CAAKE,MAAI,EAACC,GAAI,EACblG,SAAAyC,EAAAa,KAAC5D,EAAA,CACCC,GAAI,CACF6C,EAAG,EACHkB,OAAQ,aAAarF,EAAMwC,QAAQ+G,QAAQ7G,OAC3CgH,aAAc,EACdpE,WAAY1C,EAAM5C,EAAMwC,QAAQ+G,QAAQ7G,KAAM,KAC9CuE,UAAW,KAGbtF,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,YAAYpE,GAAI,CAAEqE,WAAY,IAAKC,MAAO,eAAgBd,GAAI,GAAKnD,SAEvF,gCACClB,EAAQ1B,aAAaI,QAAQ+C,KAAI,CAAC6H,EAAQhG,IACxCK,EAAAa,KAAAQ,EAAA,CAAuBC,QAAQ,QAAQpE,GAAI,CAAEwD,GAAI,IAAOnD,SAAA,CAAA,KACpDoI,IADYhG,oBAS/B,EAIE0D,EAAkB,EAAGhH,cACzB,MAAMT,EAAQC,IAER+J,EAAsBzK,IAC1B,OAAQA,GACN,IAAK,WACI,aAAC4H,GAAO7F,GAAI,CAAEsE,MAAO5F,EAAMwC,QAAQC,QAAQC,QACpD,IAAK,aACI,aAAC0E,GAAY9F,GAAI,CAAEsE,MAAO5F,EAAMwC,QAAQe,UAAUb,QAC3D,QACS,aAACwD,GAAK5E,GAAI,CAAEsE,MAAO5F,EAAMwC,QAAQ+G,QAAQ7G,QAAQ,EAIxDuH,EAAuB1K,IAC3B,OAAQA,GACN,IAAK,WACI,OAAAS,EAAMwC,QAAQC,QAAQC,KAC/B,IAAK,aACI,OAAA1C,EAAMwC,QAAQe,UAAUb,KACjC,QACS,OAAA1C,EAAMwC,QAAQ+G,QAAQ7G,KAAA,EAInC,cACGrB,EACC,CAAAM,SAAA,CAACP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,KAAKpE,GAAI,CAAEqE,WAAY,IAAKb,GAAI,GAAKnD,SAEzD,qCAEC+F,EAAK,CAAAC,WAAS,EAAC5B,QAAS,EACtBpE,WAAQvC,aAAa8C,KAAI,CAACgI,EAAanG,UACrC2D,EAAK,CAAAE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAyC,EAAAhD,IAACiD,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKuF,MAAe,GAARpG,GAEpCpC,SAAAyC,EAAAhD,IAACyD,EAAA,CACCvD,GAAI,CACF+D,OAAQ,aAAazC,EAAMqH,EAAoBC,EAAY3K,MAAO,MAClE+F,WAAY,2BAA2B1C,EAAMqH,EAAoBC,EAAY3K,MAAO,YAAaqD,EAAMqH,EAAoBC,EAAY3K,MAAO,aAC9IoF,WAAY,sBACZ,UAAW,CACTyF,UAAW,qBAIfzI,gBAACqD,EACC,CAAArD,SAAA,CAACsD,EAAAA,KAAA5D,EAAA,CAAIC,GAAI,CAAEC,QAAS,OAAQE,WAAY,SAAUyD,IAAK,EAAGJ,GAAI,GAC3DnD,SAAA,CAAAqI,EAAmBE,EAAY3K,aAC/B8B,EACC,CAAAM,SAAA,CAACP,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,KAAKpE,GAAI,CAAEqE,WAAY,KACxChE,SAAAuI,EAAY7K,QAEd+B,EAAAA,IAAAqE,EAAA,CAAWC,QAAQ,QAAQE,MAAM,iBAC/BjE,SAAI,IAAA0I,KAAKH,EAAY5K,MAAMgL,mBAAmB,QAAS,CACtDC,KAAM,UACN1L,MAAO,OACP2L,IAAK,oBAMbpG,EAAAhD,IAAC4E,EAAA,CACC3D,MAAO6H,EAAY3K,KACnB2J,KAAK,QACL5H,GAAI,CACFqB,gBAAiBsH,EAAoBC,EAAY3K,MACjDqG,MAAO,QACPD,WAAY,eAvCO5B,OAkDnC3C,EAAAA,IAACyD,GAAKvD,GAAI,CAAEqI,GAAI,GACdhI,gBAACqD,EACC,CAAArD,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKpE,GAAI,CAAEwD,GAAI,EAAGa,WAAY,IAAKC,MAAO,gBAAkBjE,SAEhF,2BACCsD,EAAAA,KAAAyC,EAAA,CAAKC,WAAS,EAAC5B,QAAS,EACvBpE,SAAA,CAAAP,EAAAA,IAACsG,EAAK,CAAAE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAsD,EAAAA,KAAC5D,EAAI,CAAAC,GAAI,CAAEmJ,UAAW,UACpB9I,SAAA,CAAAP,MAACqE,GAAWC,QAAQ,KAAKE,MAAM,eAAetE,GAAI,CAAEqE,WAAY,KAC7DhE,SAAAlB,EAAQrB,aAAasL,QAAOC,GAAgB,aAAXA,EAAEpL,OAAqBqL,eAE1DnF,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAEnD,yBAGHP,EAAAA,IAAAsG,EAAA,CAAKE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAsD,EAAAA,KAAC5D,EAAI,CAAAC,GAAI,CAAEmJ,UAAW,UACpB9I,SAAA,CAAAP,MAACqE,GAAWC,QAAQ,KAAKE,MAAM,iBAAiBtE,GAAI,CAAEqE,WAAY,KAC/DhE,SAAAlB,EAAQrB,aAAasL,QAAOC,GAAgB,eAAXA,EAAEpL,OAAuBqL,eAE5DnF,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAEnD,gCAGHP,EAAAA,IAAAsG,EAAA,CAAKE,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBnG,SAAAsD,EAAAA,KAAC5D,EAAI,CAAAC,GAAI,CAAEmJ,UAAW,UACpB9I,SAAA,GAAAP,IAACqE,EAAW,CAAAC,QAAQ,KAAKE,MAAM,eAAetE,GAAI,CAAEqE,WAAY,KAC7DhE,SAAQlB,EAAArB,aAAawL,eAEvBnF,EAAW,CAAAC,QAAQ,QAAQE,MAAM,iBAAiBjE,SAEnD,sCAMZ"}