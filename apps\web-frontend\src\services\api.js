/**
 * VidyaMitra Platform - API Service Layer
 * 
 * Centralized API service for backend integration with Indian educational context
 */

import axios from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api/v1';
const API_TIMEOUT = 10000; // 10 seconds

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // For development, if backend is not available, use mock data
    if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
      console.warn('Backend not available, using mock data for:', error.config?.url);
      return Promise.resolve({ data: getMockResponse(error.config) });
    }

    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Mock data for development when backend is not available
const getMockResponse = (config) => {
  const url = config?.url || '';
  const method = config?.method || 'GET';

  console.log('Generating mock response for:', method.toUpperCase(), url);

  // Mock authentication
  if (url.includes('/auth/login')) {
    return {
      success: true,
      token: 'mock-jwt-token-' + Date.now(),
      user: {
        id: '1',
        username: 'demo-teacher',
        email: '<EMAIL>',
        role: 'teacher',
        name: 'Mrs. Priya Sharma',
        school_id: 'school1',
        school_name: 'Delhi Public School, Hyderabad',
        assignedClasses: ['class1', 'class2'],
        subjects: ['Mathematics', 'Physics']
      }
    };
  }

  // Mock students data
  if (url.includes('/students')) {
    if (url.includes('/students/') && !url.includes('/students?')) {
      // Single student
      return {
        success: true,
        data: {
          id: '1',
          personalInfo: {
            firstName: 'Sanju',
            lastName: 'Kumar Reddy',
            dateOfBirth: '2008-05-15',
            gender: 'Male',
            motherTongue: 'Telugu'
          },
          academicInfo: {
            rollNumber: '001',
            admissionNumber: 'ADM2024001',
            class: 'Class 10-A',
            board: 'CBSE',
            status: 'Active'
          },
          contactInfo: {
            email: '<EMAIL>',
            phone: '+91 9000000001'
          },
          performance: {
            attendancePercentage: 95,
            averageMarks: 85,
            recentGrades: [
              { subject: 'Mathematics', marks: 88, grade: 'A2' },
              { subject: 'Physics', marks: 82, grade: 'A2' }
            ]
          },
          hasSWOT: true
        }
      };
    } else {
      // Students list
      return {
        success: true,
        data: [
          {
            id: '1',
            personalInfo: {
              firstName: 'Sanju',
              lastName: 'Kumar Reddy',
              dateOfBirth: '2008-05-15',
              gender: 'Male',
              motherTongue: 'Telugu'
            },
            academicInfo: {
              rollNumber: '001',
              admissionNumber: 'ADM2024001',
              class: 'Class 10-A',
              board: 'CBSE',
              status: 'Active'
            },
            performance: {
              attendancePercentage: 95,
              averageMarks: 85
            },
            hasSWOT: true
          },
          {
            id: '2',
            personalInfo: {
              firstName: 'Niraimathi',
              lastName: 'Selvam',
              dateOfBirth: '2008-08-22',
              gender: 'Female',
              motherTongue: 'Tamil'
            },
            academicInfo: {
              rollNumber: '002',
              admissionNumber: 'ADM2024002',
              class: 'Class 10-A',
              board: 'CBSE',
              status: 'Active'
            },
            performance: {
              attendancePercentage: 92,
              averageMarks: 88
            },
            hasSWOT: false
          },
          {
            id: '3',
            personalInfo: {
              firstName: 'Mahesh',
              lastName: 'Reddy',
              dateOfBirth: '2008-03-10',
              gender: 'Male',
              motherTongue: 'Telugu'
            },
            academicInfo: {
              rollNumber: '003',
              admissionNumber: 'ADM2024003',
              class: 'Class 10-B',
              board: 'CBSE',
              status: 'Active'
            },
            performance: {
              attendancePercentage: 89,
              averageMarks: 78
            },
            hasSWOT: true
          }
        ]
      };
    }
  }

  // Mock teacher dashboard
  if (url.includes('/teachers/') && url.includes('/dashboard')) {
    return {
      success: true,
      data: {
        teacher: {
          id: '1',
          name: 'Mrs. Priya Sharma',
          email: '<EMAIL>',
          subjects: ['Mathematics', 'Physics'],
          assignedClasses: ['Class 10-A', 'Class 10-B']
        },
        statistics: {
          totalStudents: 75,
          pendingGrades: 12,
          upcomingTests: 3,
          avgAttendance: 92
        },
        recentPerformance: [
          {
            studentId: '1',
            studentName: 'Sanju Kumar Reddy',
            class: 'Class 10-A',
            subject: 'Mathematics',
            grade: 'A2',
            marks: 88,
            percentage: 88,
            attendance: 95,
            status: 'Good'
          },
          {
            studentId: '2',
            studentName: 'Niraimathi Selvam',
            class: 'Class 10-A',
            subject: 'Physics',
            grade: 'A1',
            marks: 92,
            percentage: 92,
            attendance: 92,
            status: 'Excellent'
          }
        ],
        charts: {
          weeklyAttendance: [
            { date: '2024-12-02', attendance: 95 },
            { date: '2024-12-03', attendance: 92 },
            { date: '2024-12-04', attendance: 88 },
            { date: '2024-12-05', attendance: 94 },
            { date: '2024-12-06', attendance: 91 }
          ],
          gradeDistribution: [
            { grade: 'A1', count: 15 },
            { grade: 'A2', count: 25 },
            { grade: 'B1', count: 20 },
            { grade: 'B2', count: 10 },
            { grade: 'C1', count: 5 }
          ]
        }
      }
    };
  }

  // Default mock response
  return {
    success: true,
    message: 'Mock response - Backend not available',
    data: []
  };
};

// Authentication API
export const authAPI = {
  login: async (credentials) => {
    const response = await apiClient.post('/auth/login', credentials);
    return response.data;
  },
  
  logout: async () => {
    const response = await apiClient.post('/auth/logout');
    localStorage.removeItem('authToken');
    return response.data;
  },
  
  refreshToken: async () => {
    const response = await apiClient.post('/auth/refresh');
    return response.data;
  },
  
  verifyToken: async () => {
    const response = await apiClient.get('/auth/verify');
    return response.data;
  },
};

// Students API
export const studentsAPI = {
  // Get all students with pagination and filters
  getStudents: async (params = {}) => {
    const response = await apiClient.get('/students', { params });
    return response.data;
  },
  
  // Get student by ID
  getStudent: async (studentId) => {
    const response = await apiClient.get(`/students/${studentId}`);
    return response.data;
  },
  
  // Create new student
  createStudent: async (studentData) => {
    const response = await apiClient.post('/students', studentData);
    return response.data;
  },
  
  // Update student
  updateStudent: async (studentId, studentData) => {
    const response = await apiClient.put(`/students/${studentId}`, studentData);
    return response.data;
  },
  
  // Delete student
  deleteStudent: async (studentId) => {
    const response = await apiClient.delete(`/students/${studentId}`);
    return response.data;
  },
  
  // Get student performance data
  getStudentPerformance: async (studentId, params = {}) => {
    const response = await apiClient.get(`/students/${studentId}/performance`, { params });
    return response.data;
  },
  
  // Upload student photo
  uploadStudentPhoto: async (studentId, photoFile) => {
    const formData = new FormData();
    formData.append('photo', photoFile);
    const response = await apiClient.post(`/students/${studentId}/photo`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  },
};

// SWOT Analysis API
export const swotAPI = {
  // Get SWOT analysis for student
  getStudentSWOT: async (studentId) => {
    const response = await apiClient.get(`/swot/student/${studentId}`);
    return response.data;
  },
  
  // Create/Update SWOT analysis
  createSWOTAnalysis: async (swotData) => {
    const response = await apiClient.post('/swot/analysis', swotData);
    return response.data;
  },
  
  // Get SWOT templates for Indian context
  getSWOTTemplates: async () => {
    const response = await apiClient.get('/swot/templates');
    return response.data;
  },
  
  // Get class-wise SWOT summary
  getClassSWOTSummary: async (classId) => {
    const response = await apiClient.get(`/swot/class/${classId}/summary`);
    return response.data;
  },
  
  // Generate SWOT recommendations
  generateSWOTRecommendations: async (studentId) => {
    const response = await apiClient.post(`/swot/student/${studentId}/recommendations`);
    return response.data;
  },
};

// Attendance API
export const attendanceAPI = {
  // Get attendance for class and date
  getAttendance: async (classId, date, subject = null) => {
    const params = { date };
    if (subject) params.subject = subject;
    const response = await apiClient.get(`/attendance/class/${classId}`, { params });
    return response.data;
  },
  
  // Mark attendance
  markAttendance: async (attendanceData) => {
    const response = await apiClient.post('/attendance/mark', attendanceData);
    return response.data;
  },
  
  // Get student attendance history
  getStudentAttendance: async (studentId, params = {}) => {
    const response = await apiClient.get(`/attendance/student/${studentId}`, { params });
    return response.data;
  },
  
  // Get attendance statistics
  getAttendanceStats: async (classId, params = {}) => {
    const response = await apiClient.get(`/attendance/class/${classId}/stats`, { params });
    return response.data;
  },
  
  // Generate attendance report
  generateAttendanceReport: async (reportParams) => {
    const response = await apiClient.post('/attendance/report', reportParams);
    return response.data;
  },
};

// Grades API
export const gradesAPI = {
  // Get grades for class and subject
  getGrades: async (classId, subject, assessment = null) => {
    const params = { subject };
    if (assessment) params.assessment = assessment;
    const response = await apiClient.get(`/grades/class/${classId}`, { params });
    return response.data;
  },
  
  // Submit grades
  submitGrades: async (gradesData) => {
    const response = await apiClient.post('/grades/submit', gradesData);
    return response.data;
  },
  
  // Get student grade history
  getStudentGrades: async (studentId, params = {}) => {
    const response = await apiClient.get(`/grades/student/${studentId}`, { params });
    return response.data;
  },
  
  // Get grade statistics
  getGradeStats: async (classId, subject, params = {}) => {
    const response = await apiClient.get(`/grades/class/${classId}/subject/${subject}/stats`, { params });
    return response.data;
  },
  
  // Convert marks to Indian grading system
  convertToIndianGrade: async (marks, board = 'CBSE') => {
    const response = await apiClient.post('/grades/convert', { marks, board });
    return response.data;
  },
};

// Classes API
export const classesAPI = {
  // Get all classes
  getClasses: async () => {
    const response = await apiClient.get('/classes');
    return response.data;
  },
  
  // Get class details
  getClass: async (classId) => {
    const response = await apiClient.get(`/classes/${classId}`);
    return response.data;
  },
  
  // Get students in class
  getClassStudents: async (classId) => {
    const response = await apiClient.get(`/classes/${classId}/students`);
    return response.data;
  },
  
  // Get class performance summary
  getClassPerformance: async (classId, params = {}) => {
    const response = await apiClient.get(`/classes/${classId}/performance`, { params });
    return response.data;
  },
};

// Teachers API
export const teachersAPI = {
  // Get teacher dashboard data
  getDashboardData: async (teacherId) => {
    const response = await apiClient.get(`/teachers/${teacherId}/dashboard`);
    return response.data;
  },
  
  // Get teacher's classes
  getTeacherClasses: async (teacherId) => {
    const response = await apiClient.get(`/teachers/${teacherId}/classes`);
    return response.data;
  },
  
  // Get teacher's subjects
  getTeacherSubjects: async (teacherId) => {
    const response = await apiClient.get(`/teachers/${teacherId}/subjects`);
    return response.data;
  },
};

// Reports API
export const reportsAPI = {
  // Generate student report
  generateStudentReport: async (studentId, reportType, params = {}) => {
    const response = await apiClient.post(`/reports/student/${studentId}`, {
      reportType,
      ...params,
    });
    return response.data;
  },
  
  // Generate class report
  generateClassReport: async (classId, reportType, params = {}) => {
    const response = await apiClient.post(`/reports/class/${classId}`, {
      reportType,
      ...params,
    });
    return response.data;
  },
  
  // Get available report templates
  getReportTemplates: async () => {
    const response = await apiClient.get('/reports/templates');
    return response.data;
  },
  
  // Download report
  downloadReport: async (reportId) => {
    const response = await apiClient.get(`/reports/${reportId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

// Analytics API
export const analyticsAPI = {
  // Get dashboard analytics
  getDashboardAnalytics: async (params = {}) => {
    const response = await apiClient.get('/analytics/dashboard', { params });
    return response.data;
  },
  
  // Get performance trends
  getPerformanceTrends: async (params = {}) => {
    const response = await apiClient.get('/analytics/performance-trends', { params });
    return response.data;
  },
  
  // Get attendance trends
  getAttendanceTrends: async (params = {}) => {
    const response = await apiClient.get('/analytics/attendance-trends', { params });
    return response.data;
  },
  
  // Get SWOT analytics
  getSWOTAnalytics: async (params = {}) => {
    const response = await apiClient.get('/analytics/swot', { params });
    return response.data;
  },
};

// Utility functions
export const apiUtils = {
  // Handle API errors
  handleError: (error) => {
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data.message || 'An error occurred',
        status: error.response.status,
        data: error.response.data,
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        message: 'Network error. Please check your connection.',
        status: 0,
      };
    } else {
      // Something else happened
      return {
        message: error.message || 'An unexpected error occurred',
        status: -1,
      };
    }
  },
  
  // Format API response
  formatResponse: (response) => {
    return {
      success: true,
      data: response.data,
      message: response.message || 'Operation successful',
    };
  },
  
  // Create query string from params
  createQueryString: (params) => {
    const searchParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined) {
        searchParams.append(key, params[key]);
      }
    });
    return searchParams.toString();
  },
};

export default apiClient;
