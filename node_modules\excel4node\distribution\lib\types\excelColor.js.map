{"version": 3, "file": "excelColor.js", "names": ["items", "_this", "opts", "Object", "keys", "for<PERSON>ach", "k", "push", "prototype", "validate", "val", "toLowerCase", "undefined", "name", "hasOwnProperty", "TypeError", "join", "getColor", "length", "test", "toUpperCase", "substr", "module", "exports"], "sources": ["../../../source/lib/types/excelColor.js"], "sourcesContent": ["function items() {\n    // subset of §20.1.10.48 ST_PresetColorVal (Preset Color Value)\n    this['aqua'] = 'FF33CCCC';\n    this['black'] = 'FF000000';\n    this['blue'] = 'FF0000FF';\n    this['blue-gray'] = 'FF666699';\n    this['bright green'] = 'FF00FF00';\n    this['brown'] = 'FF993300';\n    this['dark blue'] = 'FF000080';\n    this['dark green'] = 'FF003300';\n    this['dark red'] = 'FF800000';\n    this['dark teal'] = 'FF003366';\n    this['dark yellow'] = 'FF808000';\n    this['gold'] = 'FFFFCC00';\n    this['gray-25'] = 'FFC0C0C0';\n    this['gray-40'] = 'FF969696';\n    this['gray-50'] = 'FF808080';\n    this['gray-80'] = 'FF333333';\n    this['green'] = 'FF008000';\n    this['indigo'] = 'FF333399';\n    this['lavender'] = 'FFCC99FF';\n    this['light blue'] = 'FF3366FF';\n    this['light green'] = 'FFCCFFCC';\n    this['light orange'] = 'FFFF9900';\n    this['light turquoise'] = 'FFCCFFFF';\n    this['light yellow'] = 'FFFFFF99';\n    this['lime'] = 'FF99CC00';\n    this['olive green'] = 'FF333300';\n    this['orange'] = 'FFFF6600';\n    this['pale blue'] = 'FF99CCFF';\n    this['pink'] = 'FFFF00FF';\n    this['plum'] = 'FF993366';\n    this['red'] = 'FFFF0000';\n    this['rose'] = 'FFFF99CC';\n    this['sea green'] = 'FF339966';\n    this['sky blue'] = 'FF00CCFF';\n    this['tan'] = 'FFFFCC99';\n    this['teal'] = 'FF008080';\n    this['turquoise'] = 'FF00FFFF';\n    this['violet'] = 'FF800080';\n    this['white'] = 'FFFFFFFF';\n    this['yellow'] = 'FFFFFF00';\n\n    this.opts = [];\n    Object.keys(this).forEach((k) => {\n        if (typeof this[k] === 'string') {\n            this.opts.push(k);\n        }\n    });\n}\n\n\nitems.prototype.validate = function (val) {\n    if (this[val.toLowerCase()] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError('Invalid value for ST_PresetColorVal; Value must be one of ' + this.opts.join(', '));\n    } else {\n        return true;\n    }\n};\n\nitems.prototype.getColor = function (val) {\n    // check for RGB, RGBA or Excel Color Names and return RGBA\n\n    if (typeof this[val.toLowerCase()] === 'string') {\n        // val was a named color that matches predefined list. return corresponding color\n        return this[val.toLowerCase()];\n    } else if (val.length === 8 && /^[a-fA-F0-9()]+$/.test(val)) {\n        // val is already a properly formatted color string, return upper case version of itself\n        return val.toUpperCase();\n    } else if (val.length === 6 && /^[a-fA-F0-9()]+$/.test(val)) {\n        // val is color code without Alpha, add it and return\n        return 'FF' + val.toUpperCase();\n    } else if (val.length === 7 && val.substr(0, 1) === '#' && /^[a-fA-F0-9()]+$/.test(val.substr(1))) {\n        // val was sent as html style hex code, remove # and add alpha\n        return 'FF' + val.substr(1).toUpperCase();\n    } else {\n        // I don't know what this is, return valid color and console.log error\n        throw new TypeError('valid color options are html style hex codes, ARGB strings or these colors by name: %s', this.opts.join(', '));\n    }\n};\n\nmodule.exports = new items();"], "mappings": ";;AAAA,SAASA,KAAKA,CAAA,EAAG;EAAA,IAAAC,KAAA;EACb;EACA,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU;EACzB,IAAI,CAAC,OAAO,CAAC,GAAG,UAAU;EAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU;EACzB,IAAI,CAAC,WAAW,CAAC,GAAG,UAAU;EAC9B,IAAI,CAAC,cAAc,CAAC,GAAG,UAAU;EACjC,IAAI,CAAC,OAAO,CAAC,GAAG,UAAU;EAC1B,IAAI,CAAC,WAAW,CAAC,GAAG,UAAU;EAC9B,IAAI,CAAC,YAAY,CAAC,GAAG,UAAU;EAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU;EAC7B,IAAI,CAAC,WAAW,CAAC,GAAG,UAAU;EAC9B,IAAI,CAAC,aAAa,CAAC,GAAG,UAAU;EAChC,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU;EACzB,IAAI,CAAC,SAAS,CAAC,GAAG,UAAU;EAC5B,IAAI,CAAC,SAAS,CAAC,GAAG,UAAU;EAC5B,IAAI,CAAC,SAAS,CAAC,GAAG,UAAU;EAC5B,IAAI,CAAC,SAAS,CAAC,GAAG,UAAU;EAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,UAAU;EAC1B,IAAI,CAAC,QAAQ,CAAC,GAAG,UAAU;EAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU;EAC7B,IAAI,CAAC,YAAY,CAAC,GAAG,UAAU;EAC/B,IAAI,CAAC,aAAa,CAAC,GAAG,UAAU;EAChC,IAAI,CAAC,cAAc,CAAC,GAAG,UAAU;EACjC,IAAI,CAAC,iBAAiB,CAAC,GAAG,UAAU;EACpC,IAAI,CAAC,cAAc,CAAC,GAAG,UAAU;EACjC,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU;EACzB,IAAI,CAAC,aAAa,CAAC,GAAG,UAAU;EAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,UAAU;EAC3B,IAAI,CAAC,WAAW,CAAC,GAAG,UAAU;EAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU;EACzB,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU;EACzB,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU;EACxB,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU;EACzB,IAAI,CAAC,WAAW,CAAC,GAAG,UAAU;EAC9B,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU;EAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU;EACxB,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU;EACzB,IAAI,CAAC,WAAW,CAAC,GAAG,UAAU;EAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,UAAU;EAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,UAAU;EAC1B,IAAI,CAAC,QAAQ,CAAC,GAAG,UAAU;EAE3B,IAAI,CAACC,IAAI,GAAG,EAAE;EACdC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAACC,CAAC,EAAK;IAC7B,IAAI,OAAOL,KAAI,CAACK,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC7BL,KAAI,CAACC,IAAI,CAACK,IAAI,CAACD,CAAC,CAAC;IACrB;EACJ,CAAC,CAAC;AACN;AAGAN,KAAK,CAACQ,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACtC,IAAI,IAAI,CAACA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;IACvC,IAAIV,IAAI,GAAG,EAAE;IACb,KAAK,IAAIW,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BX,IAAI,CAACK,IAAI,CAACM,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIE,SAAS,CAAC,4DAA4D,GAAG,IAAI,CAACb,IAAI,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5G,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDhB,KAAK,CAACQ,SAAS,CAACS,QAAQ,GAAG,UAAUP,GAAG,EAAE;EACtC;;EAEA,IAAI,OAAO,IAAI,CAACA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IAC7C;IACA,OAAO,IAAI,CAACD,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC;EAClC,CAAC,MAAM,IAAID,GAAG,CAACQ,MAAM,KAAK,CAAC,IAAI,kBAAkB,CAACC,IAAI,CAACT,GAAG,CAAC,EAAE;IACzD;IACA,OAAOA,GAAG,CAACU,WAAW,CAAC,CAAC;EAC5B,CAAC,MAAM,IAAIV,GAAG,CAACQ,MAAM,KAAK,CAAC,IAAI,kBAAkB,CAACC,IAAI,CAACT,GAAG,CAAC,EAAE;IACzD;IACA,OAAO,IAAI,GAAGA,GAAG,CAACU,WAAW,CAAC,CAAC;EACnC,CAAC,MAAM,IAAIV,GAAG,CAACQ,MAAM,KAAK,CAAC,IAAIR,GAAG,CAACW,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,IAAI,kBAAkB,CAACF,IAAI,CAACT,GAAG,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAC/F;IACA,OAAO,IAAI,GAAGX,GAAG,CAACW,MAAM,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC,CAAC;EAC7C,CAAC,MAAM;IACH;IACA,MAAM,IAAIL,SAAS,CAAC,wFAAwF,EAAE,IAAI,CAACb,IAAI,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC;EACvI;AACJ,CAAC;AAEDM,MAAM,CAACC,OAAO,GAAG,IAAIvB,KAAK,CAAC,CAAC"}