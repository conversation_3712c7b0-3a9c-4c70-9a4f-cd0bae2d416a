{"version": 3, "file": "PDFPage.d.ts", "sourceRoot": "", "sources": ["../../src/api/PDFPage.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAO,iBAAuB;AAiB5C,OAAO,WAAW,sBAA4B;AAC9C,OAAO,eAAe,0BAAgC;AACtD,OAAO,OAAO,kBAAwB;AACtC,OAAO,QAAQ,mBAAyB;AACxC,OAAO,EACL,wBAAwB,EACxB,yBAAyB,EACzB,uBAAuB,EACvB,sBAAsB,EACtB,sBAAsB,EACtB,2BAA2B,EAC3B,wBAAwB,EACxB,qBAAqB,EACrB,sBAAsB,EAEvB,yBAA+B;AAChC,OAAO,EAAW,QAAQ,EAAa,oBAA0B;AAEjE,OAAO,EAIL,WAAW,EACX,WAAW,EACX,MAAM,EAGP,gBAAiB;AAclB;;GAEG;AACH,MAAM,CAAC,OAAO,OAAO,OAAO;IAC1B;;;;;;;;;;OAUG;IACH,MAAM,CAAC,EAAE,aAAc,WAAW,OAAO,MAAM,OAAO,WAAW,aAC/B;IAElC;;;;;;;;OAQG;IACH,MAAM,CAAC,MAAM,QAAS,WAAW,aAM/B;IAEF,wDAAwD;IACxD,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC;IAE3B,sEAAsE;IACtE,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC;IAErB,+CAA+C;IAC/C,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC;IAE1B,OAAO,CAAC,OAAO,CAAC,CAAU;IAC1B,OAAO,CAAC,IAAI,CAAC,CAAU;IACvB,OAAO,CAAC,QAAQ,CAAM;IACtB,OAAO,CAAC,SAAS,CAAyB;IAC1C,OAAO,CAAC,UAAU,CAAM;IACxB,OAAO,CAAC,CAAC,CAAK;IACd,OAAO,CAAC,CAAC,CAAK;IACd,OAAO,CAAC,aAAa,CAAC,CAAmB;IACzC,OAAO,CAAC,gBAAgB,CAAC,CAAS;IAElC,OAAO;IAUP;;;;;;;;;;;;OAYG;IACH,WAAW,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI;IAMlC;;;;;;;OAOG;IACH,WAAW,IAAI,QAAQ;IAKvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACH,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IA+B5C;;;;;;;;;;;OAWG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAK7B;;;;;;;;;;;OAWG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAK/B;;;;;;;;;;;;;;;;OAgBG;IACH,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAStE;;;;;;;;;;;;;;;;OAgBG;IACH,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IASrE;;;;;;;;;;;;;;;;OAgBG;IACH,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAStE;;;;;;;;;;;;;;;;OAgBG;IACH,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IASrE;;;;;;;;;;;;;;;;OAgBG;IACH,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IASpE;;;;;;;;;;OAUG;IACH,OAAO,IAAI;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAK5C;;;;;;;;;OASG;IACH,QAAQ,IAAI,MAAM;IAIlB;;;;;;;;;OASG;IACH,SAAS,IAAI,MAAM;IAInB;;;;;;;;;;;;;;;;OAgBG;IACH,WAAW,IAAI;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAKtE;;;;;;;;;;;;;;;OAeG;IACH,UAAU,IAAI;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAKrE;;;;;;;;;;;;;;;;OAgBG;IACH,WAAW,IAAI;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAKtE;;;;;;;;;;;;;;;OAeG;IACH,UAAU,IAAI;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAKrE;;;;;;;;;;;;;OAaG;IACH,SAAS,IAAI;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAKpE;;;;;;;;;;;;;;;;OAgBG;IACH,gBAAgB,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAmB5C;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAQjC;;;;;;;;;;;;;;;;;OAiBG;IACH,YAAY,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAgBxC;;;;;;;;;;;;;;;OAeG;IACH,gBAAgB,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;IAarC;;;;;;;;;;;;;;;;OAgBG;IACH,aAAa,IAAI,IAAI;IAMrB;;;;;;;;;;;;;;;;;;;OAmBG;IACH,OAAO,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAO5B;;;;;;;;;;;;;OAaG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAKnC;;;;;;;;;;;;;;;OAeG;IACH,YAAY,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;IAKpC;;;;;;;;;;;;;;;OAeG;IACH,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAKvC;;;;;;OAMG;IACH,WAAW,IAAI;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE;IAIvC;;;;;;OAMG;IACH,IAAI,IAAI,MAAM;IAId;;;;;;OAMG;IACH,IAAI,IAAI,MAAM;IAId;;;;;;;;;;;;;;OAcG;IACH,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAOlC;;;;;;;;;;;;OAYG;IACH,QAAQ,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAKjC;;;;;;;;;;;;OAYG;IACH,MAAM,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAK/B;;;;;;;;;;;;OAYG;IACH,QAAQ,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAKjC;;;;;;;;;;;;OAYG;IACH,SAAS,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAKlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,aAAa,CAAC,GAAG,QAAQ,EAAE,WAAW,EAAE,GAAG,IAAI;IAM/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,GAAE,sBAA2B,GAAG,IAAI;IA0DlE;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,GAAE,uBAA4B,GAAG,IAAI;IAmCvE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,QAAQ,CACN,YAAY,EAAE,eAAe,EAC7B,OAAO,GAAE,sBAA2B,GACnC,IAAI;IAwDP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCG;IACH,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,GAAE,qBAA0B,GAAG,IAAI;IA2DpE;;;;;;;;;;;;;;OAcG;IACH,QAAQ,CAAC,OAAO,EAAE,sBAAsB,GAAG,IAAI;IA2C/C;;;;;;;;;;;;;;;;;;;OAmBG;IACH,aAAa,CAAC,OAAO,GAAE,2BAAgC,GAAG,IAAI;IAgE9D;;;;;;;;;;;;;;;;;;OAkBG;IACH,UAAU,CAAC,OAAO,GAAE,wBAA6B,GAAG,IAAI;IAMxD;;;;;;;;;;;;;;;;;;OAkBG;IACH,WAAW,CAAC,OAAO,GAAE,yBAA8B,GAAG,IAAI;IA2D1D;;;;;;;;;;;;;;;;;OAiBG;IACH,UAAU,CAAC,OAAO,GAAE,wBAA6B,GAAG,IAAI;IAMxD,OAAO,CAAC,cAAc;IAatB,OAAO,CAAC,OAAO;IAQf,OAAO,CAAC,SAAS;IAKjB,OAAO,CAAC,gBAAgB;IAQxB,OAAO,CAAC,mBAAmB;IAM3B,OAAO,CAAC,uBAAuB;IA2B/B,OAAO,CAAC,UAAU;CAenB"}