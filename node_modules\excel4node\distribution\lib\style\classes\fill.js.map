{"version": 3, "file": "fill.js", "names": ["types", "require", "xmlbuilder", "CTColor", "Stop", "opts", "position", "_classCallCheck", "color", "_createClass", "key", "value", "toObject", "obj", "undefined", "Fill", "_this", "indexOf", "type", "TypeError", "bottom", "degree", "left", "right", "top", "stops", "Array", "for<PERSON>ach", "s", "i", "push", "bgColor", "fgColor", "patternType", "fillPattern", "validate", "stop", "addToXMLele", "fXML", "pFill", "ele", "att", "module", "exports"], "sources": ["../../../../source/lib/style/classes/fill.js"], "sourcesContent": ["const types = require('../../types/index.js');\nconst xmlbuilder = require('xmlbuilder');\nconst CTColor = require('./ctColor.js');\n\nclass Stop { //§18.8.38\n    /** \n     * @class Stop\n     * @desc Stops for Gradient fills\n     * @param {Object} opts Options for Stop\n     * @param {String} opts.color Color of Stop\n     * @param {Number} opts.position Order of Stop with first stop being 0\n     * @returns {Stop}\n     */\n    constructor(opts, position) {\n        this.color = new CTColor(opts.color);\n        this.position = position;\n    }\n\n    /** \n     * @func Stop.toObject\n     * @desc Converts the Stop instance to a javascript object\n     * @returns {Object}\n     */\n    toObject() {\n        let obj = {};\n        this.color !== undefined ? obj.color = this.color.toObject() : null;\n        this.position !== undefined ? obj.position = this.position : null;\n        return obj;\n    }\n}\n\nclass Fill { //§18.8.20 fill (Fill)\n\n    /** \n     * @class Fill\n     * @desc Excel Fill \n     * @param {Object} opts\n     * @param {String} opts.type Type of Excel fill (gradient or pattern)\n     * @param {Number} opts.bottom If Gradient fill, the position of the bottom edge of the inner rectange as a percentage in decimal form. (must be between 0 and 1)\n     * @param {Number} opts.top If Gradient fill, the position of the top edge of the inner rectange as a percentage in decimal form. (must be between 0 and 1)\n     * @param {Number} opts.left If Gradient fill, the position of the left edge of the inner rectange as a percentage in decimal form. (must be between 0 and 1)\n     * @param {Number} opts.right If Gradient fill, the position of the right edge of the inner rectange as a percentage in decimal form. (must be between 0 and 1)\n     * @param {Number} opts.degree Angle of the Gradient\n     * @param {Array.Stop} opts.stops Array of position stops for gradient\n     * @returns {Fill}\n     */\n    constructor(opts) {\n\n        if (['gradient', 'pattern', 'none'].indexOf(opts.type) >= 0) {\n            this.type = opts.type;\n        } else {\n            throw new TypeError('Fill type must be one of gradient, pattern or none.');\n        }\n\n        switch (this.type) {\n            case 'gradient': //§18.8.24\n                if (opts.bottom !== undefined) {\n                    if (opts.bottom < 0 || opts.bottom > 1) {\n                        throw new TypeError('Values for gradient fill bottom attribute must be a decimal between 0 and 1');\n                    } else {\n                        this.bottom = opts.bottom;\n                    }\n                }\n\n                if (opts.degree !== undefined) {\n                    if (typeof opts.degree === 'number') {\n                        this.degree = opts.degree;\n                    } else {\n                        throw new TypeError('Values of gradient fill degree must be of type number.');\n                    }\n                }\n\n\n                if (opts.left !== undefined) {\n                    if (opts.left < 0 || opts.left > 1) {\n                        throw new TypeError('Values for gradient fill left attribute must be a decimal between 0 and 1');\n                    } else {\n                        this.left = opts.left;\n                    }\n                }\n\n                if (opts.right !== undefined) {\n                    if (opts.right < 0 || opts.right > 1) {\n                        throw new TypeError('Values for gradient fill right attribute must be a decimal between 0 and 1');\n                    } else {\n                        this.right = opts.right;\n                    }\n                }\n\n                if (opts.top !== undefined) {\n                    if (opts.top < 0 || opts.top > 1) {\n                        throw new TypeError('Values for gradient fill top attribute must be a decimal between 0 and 1');\n                    } else {\n                        this.top = opts.top;\n                    }\n                }\n\n                if (opts.stops !== undefined) {\n                    if (opts.stops instanceof Array) {\n                        opts.stops.forEach((s, i) => {\n                            this.stops.push(new Stop(s, i));\n                        });\n                    } else {\n                        throw new TypeError('Stops for gradient fills must be sent as an Array');\n                    }\n                }\n\n                break;\n\n            case 'pattern': //§18.8.32\n                if (opts.bgColor !== undefined) {\n                    this.bgColor = new CTColor(opts.bgColor);\n                }\n\n                if (opts.fgColor !== undefined) {\n                    this.fgColor = new CTColor(opts.fgColor);\n                }\n\n                if (opts.patternType !== undefined) {\n                    types.fillPattern.validate(opts.patternType) === true ? this.patternType = opts.patternType : null;\n                }\n                break;\n\n            case 'none':\n                this.patternType = 'none';\n                break;\n        }\n    }\n\n    /** \n     * @func Fill.toObject\n     * @desc Converts the Fill instance to a javascript object\n     * @returns {Object}\n     */\n    toObject() {\n        let obj = {};\n\n        this.type !== undefined ? obj.type = this.type : null;\n        this.bottom !== undefined ? obj.bottom = this.bottom : null;\n        this.degree !== undefined ? obj.degree = this.degree : null;\n        this.left !== undefined ? obj.left = this.left : null;\n        this.right !== undefined ? obj.right = this.right : null;\n        this.top !== undefined ? obj.top = this.top : null;\n        this.bgColor !== undefined ? obj.bgColor = this.bgColor.toObject() : null;\n        this.fgColor !== undefined ? obj.fgColor = this.fgColor.toObject() : null;\n        this.patternType !== undefined ? obj.patternType = this.patternType : null;\n\n        if (this.stops !== undefined) {\n            obj.stop = [];\n            this.stops.forEach((s) => {\n                obj.stops.push(s.toObject());\n            });\n        }\n\n        return obj;\n    }\n\n    /**\n     * @alias Fill.addToXMLele\n     * @desc When generating Workbook output, attaches style to the styles xml file\n     * @func Fill.addToXMLele\n     * @param {xmlbuilder.Element} ele Element object of the xmlbuilder module\n     */\n    addToXMLele(fXML) {\n        let pFill = fXML.ele('patternFill').att('patternType', this.patternType);\n\n        if (this.fgColor instanceof CTColor) {\n            pFill.ele('fgColor').att(this.fgColor.type, this.fgColor[this.fgColor.type]);\n        }\n\n        if (this.bgColor instanceof CTColor) {\n            pFill.ele('bgColor').att(this.bgColor.type, this.bgColor[this.bgColor.type]);\n        }\n    }\n}\n\nmodule.exports = Fill;"], "mappings": ";;;;;AAAA,IAAMA,KAAK,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAMC,UAAU,GAAGD,OAAO,CAAC,YAAY,CAAC;AACxC,IAAME,OAAO,GAAGF,OAAO,CAAC,cAAc,CAAC;AAAC,IAElCG,IAAI;EAAG;EACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,KAAYC,IAAI,EAAEC,QAAQ,EAAE;IAAAC,eAAA,OAAAH,IAAA;IACxB,IAAI,CAACI,KAAK,GAAG,IAAIL,OAAO,CAACE,IAAI,CAACG,KAAK,CAAC;IACpC,IAAI,CAACF,QAAQ,GAAGA,QAAQ;EAC5B;;EAEA;AACJ;AACA;AACA;AACA;EAJIG,YAAA,CAAAL,IAAA;IAAAM,GAAA;IAAAC,KAAA,EAKA,SAAAC,SAAA,EAAW;MACP,IAAIC,GAAG,GAAG,CAAC,CAAC;MACZ,IAAI,CAACL,KAAK,KAAKM,SAAS,GAAGD,GAAG,CAACL,KAAK,GAAG,IAAI,CAACA,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,IAAI;MACnE,IAAI,CAACN,QAAQ,KAAKQ,SAAS,GAAGD,GAAG,CAACP,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAI;MACjE,OAAOO,GAAG;IACd;EAAC;EAAA,OAAAT,IAAA;AAAA;AAAA,IAGCW,IAAI;EAAG;;EAET;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,KAAYV,IAAI,EAAE;IAAA,IAAAW,KAAA;IAAAT,eAAA,OAAAQ,IAAA;IAEd,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC,CAACE,OAAO,CAACZ,IAAI,CAACa,IAAI,CAAC,IAAI,CAAC,EAAE;MACzD,IAAI,CAACA,IAAI,GAAGb,IAAI,CAACa,IAAI;IACzB,CAAC,MAAM;MACH,MAAM,IAAIC,SAAS,CAAC,qDAAqD,CAAC;IAC9E;IAEA,QAAQ,IAAI,CAACD,IAAI;MACb,KAAK,UAAU;QAAE;QACb,IAAIb,IAAI,CAACe,MAAM,KAAKN,SAAS,EAAE;UAC3B,IAAIT,IAAI,CAACe,MAAM,GAAG,CAAC,IAAIf,IAAI,CAACe,MAAM,GAAG,CAAC,EAAE;YACpC,MAAM,IAAID,SAAS,CAAC,6EAA6E,CAAC;UACtG,CAAC,MAAM;YACH,IAAI,CAACC,MAAM,GAAGf,IAAI,CAACe,MAAM;UAC7B;QACJ;QAEA,IAAIf,IAAI,CAACgB,MAAM,KAAKP,SAAS,EAAE;UAC3B,IAAI,OAAOT,IAAI,CAACgB,MAAM,KAAK,QAAQ,EAAE;YACjC,IAAI,CAACA,MAAM,GAAGhB,IAAI,CAACgB,MAAM;UAC7B,CAAC,MAAM;YACH,MAAM,IAAIF,SAAS,CAAC,wDAAwD,CAAC;UACjF;QACJ;QAGA,IAAId,IAAI,CAACiB,IAAI,KAAKR,SAAS,EAAE;UACzB,IAAIT,IAAI,CAACiB,IAAI,GAAG,CAAC,IAAIjB,IAAI,CAACiB,IAAI,GAAG,CAAC,EAAE;YAChC,MAAM,IAAIH,SAAS,CAAC,2EAA2E,CAAC;UACpG,CAAC,MAAM;YACH,IAAI,CAACG,IAAI,GAAGjB,IAAI,CAACiB,IAAI;UACzB;QACJ;QAEA,IAAIjB,IAAI,CAACkB,KAAK,KAAKT,SAAS,EAAE;UAC1B,IAAIT,IAAI,CAACkB,KAAK,GAAG,CAAC,IAAIlB,IAAI,CAACkB,KAAK,GAAG,CAAC,EAAE;YAClC,MAAM,IAAIJ,SAAS,CAAC,4EAA4E,CAAC;UACrG,CAAC,MAAM;YACH,IAAI,CAACI,KAAK,GAAGlB,IAAI,CAACkB,KAAK;UAC3B;QACJ;QAEA,IAAIlB,IAAI,CAACmB,GAAG,KAAKV,SAAS,EAAE;UACxB,IAAIT,IAAI,CAACmB,GAAG,GAAG,CAAC,IAAInB,IAAI,CAACmB,GAAG,GAAG,CAAC,EAAE;YAC9B,MAAM,IAAIL,SAAS,CAAC,0EAA0E,CAAC;UACnG,CAAC,MAAM;YACH,IAAI,CAACK,GAAG,GAAGnB,IAAI,CAACmB,GAAG;UACvB;QACJ;QAEA,IAAInB,IAAI,CAACoB,KAAK,KAAKX,SAAS,EAAE;UAC1B,IAAIT,IAAI,CAACoB,KAAK,YAAYC,KAAK,EAAE;YAC7BrB,IAAI,CAACoB,KAAK,CAACE,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;cACzBb,KAAI,CAACS,KAAK,CAACK,IAAI,CAAC,IAAI1B,IAAI,CAACwB,CAAC,EAAEC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC;UACN,CAAC,MAAM;YACH,MAAM,IAAIV,SAAS,CAAC,mDAAmD,CAAC;UAC5E;QACJ;QAEA;MAEJ,KAAK,SAAS;QAAE;QACZ,IAAId,IAAI,CAAC0B,OAAO,KAAKjB,SAAS,EAAE;UAC5B,IAAI,CAACiB,OAAO,GAAG,IAAI5B,OAAO,CAACE,IAAI,CAAC0B,OAAO,CAAC;QAC5C;QAEA,IAAI1B,IAAI,CAAC2B,OAAO,KAAKlB,SAAS,EAAE;UAC5B,IAAI,CAACkB,OAAO,GAAG,IAAI7B,OAAO,CAACE,IAAI,CAAC2B,OAAO,CAAC;QAC5C;QAEA,IAAI3B,IAAI,CAAC4B,WAAW,KAAKnB,SAAS,EAAE;UAChCd,KAAK,CAACkC,WAAW,CAACC,QAAQ,CAAC9B,IAAI,CAAC4B,WAAW,CAAC,KAAK,IAAI,GAAG,IAAI,CAACA,WAAW,GAAG5B,IAAI,CAAC4B,WAAW,GAAG,IAAI;QACtG;QACA;MAEJ,KAAK,MAAM;QACP,IAAI,CAACA,WAAW,GAAG,MAAM;QACzB;IACR;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EAJIxB,YAAA,CAAAM,IAAA;IAAAL,GAAA;IAAAC,KAAA,EAKA,SAAAC,SAAA,EAAW;MACP,IAAIC,GAAG,GAAG,CAAC,CAAC;MAEZ,IAAI,CAACK,IAAI,KAAKJ,SAAS,GAAGD,GAAG,CAACK,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI;MACrD,IAAI,CAACE,MAAM,KAAKN,SAAS,GAAGD,GAAG,CAACO,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI;MAC3D,IAAI,CAACC,MAAM,KAAKP,SAAS,GAAGD,GAAG,CAACQ,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI;MAC3D,IAAI,CAACC,IAAI,KAAKR,SAAS,GAAGD,GAAG,CAACS,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI;MACrD,IAAI,CAACC,KAAK,KAAKT,SAAS,GAAGD,GAAG,CAACU,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI;MACxD,IAAI,CAACC,GAAG,KAAKV,SAAS,GAAGD,GAAG,CAACW,GAAG,GAAG,IAAI,CAACA,GAAG,GAAG,IAAI;MAClD,IAAI,CAACO,OAAO,KAAKjB,SAAS,GAAGD,GAAG,CAACkB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACnB,QAAQ,CAAC,CAAC,GAAG,IAAI;MACzE,IAAI,CAACoB,OAAO,KAAKlB,SAAS,GAAGD,GAAG,CAACmB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACpB,QAAQ,CAAC,CAAC,GAAG,IAAI;MACzE,IAAI,CAACqB,WAAW,KAAKnB,SAAS,GAAGD,GAAG,CAACoB,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAI;MAE1E,IAAI,IAAI,CAACR,KAAK,KAAKX,SAAS,EAAE;QAC1BD,GAAG,CAACuB,IAAI,GAAG,EAAE;QACb,IAAI,CAACX,KAAK,CAACE,OAAO,CAAC,UAACC,CAAC,EAAK;UACtBf,GAAG,CAACY,KAAK,CAACK,IAAI,CAACF,CAAC,CAAChB,QAAQ,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC;MACN;MAEA,OAAOC,GAAG;IACd;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAH,GAAA;IAAAC,KAAA,EAMA,SAAA0B,YAAYC,IAAI,EAAE;MACd,IAAIC,KAAK,GAAGD,IAAI,CAACE,GAAG,CAAC,aAAa,CAAC,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACR,WAAW,CAAC;MAExE,IAAI,IAAI,CAACD,OAAO,YAAY7B,OAAO,EAAE;QACjCoC,KAAK,CAACC,GAAG,CAAC,SAAS,CAAC,CAACC,GAAG,CAAC,IAAI,CAACT,OAAO,CAACd,IAAI,EAAE,IAAI,CAACc,OAAO,CAAC,IAAI,CAACA,OAAO,CAACd,IAAI,CAAC,CAAC;MAChF;MAEA,IAAI,IAAI,CAACa,OAAO,YAAY5B,OAAO,EAAE;QACjCoC,KAAK,CAACC,GAAG,CAAC,SAAS,CAAC,CAACC,GAAG,CAAC,IAAI,CAACV,OAAO,CAACb,IAAI,EAAE,IAAI,CAACa,OAAO,CAAC,IAAI,CAACA,OAAO,CAACb,IAAI,CAAC,CAAC;MAChF;IACJ;EAAC;EAAA,OAAAH,IAAA;AAAA;AAGL2B,MAAM,CAACC,OAAO,GAAG5B,IAAI"}