import{u as e,j as s,B as a,h as r,G as i,d as n,f as t,F as l,w as o,x as d,M as c,l as x,i as h,ab as m,a3 as j,a4 as p,P as u,Q as b,e as g,ac as v,A as y,R as f,_ as C,L as A}from"./mui-core-BBO2DoRL.js";import{r as S}from"./vendor-CeOqOr8o.js";import{u as R}from"./routing-B6PnZiBG.js";import{m as W,b as k,C as w,f as N,ad as P,t as $,E as _}from"./mui-icons-BXhTkfAe.js";import{m as B}from"./animation-BJm6nf7i.js";import"./utils-misc-NFmYzBmQ.js";const Q=[{id:"progress_report",name:"Student Progress Report",description:"Comprehensive academic and behavioral progress report",icon:W,color:"primary"},{id:"report_card",name:"Report Card",description:"Traditional report card with grades and attendance",icon:k,color:"success"},{id:"swot_report",name:"SWOT Analysis Report",description:"Detailed SWOT analysis with recommendations",icon:W,color:"info"},{id:"attendance_report",name:"Attendance Report",description:"Monthly attendance summary and patterns",icon:w,color:"warning"}],T=[{id:1,name:"Sanju Kumar Reddy",class:"10-A",rollNumber:1},{id:2,name:"Niraimathi Selvam",class:"10-A",rollNumber:2},{id:3,name:"Mahesh Reddy",class:"10-B",rollNumber:3},{id:4,name:"Ravi Teja Sharma",class:"9-A",rollNumber:4},{id:5,name:"Ankitha Patel",class:"10-A",rollNumber:5},{id:6,name:"Sirisha Nair",class:"10-B",rollNumber:6},{id:7,name:"Priya Agarwal",class:"9-A",rollNumber:7}],G=()=>{const k=e();R();const[w,G]=S.useState(""),[I,D]=S.useState(""),[F,M]=S.useState([]),[O,z]=S.useState(""),[E,L]=S.useState({grades:!0,attendance:!0,behavior:!0,swot:!1,parentComments:!1}),[H,K]=S.useState(!1),[U,Y]=S.useState(0),q=e=>s=>{L((a=>({...a,[e]:s.target.checked})))},J=I?T.filter((e=>e.class===I)):T,V=Q.find((e=>e.id===w));return s.jsxs(a,{sx:{maxWidth:1200,mx:"auto",p:3},children:[s.jsx(B.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(a,{sx:{mb:4},children:[s.jsx(r,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${k.palette.primary.main} 0%, ${k.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Report Generation"}),s.jsx(r,{variant:"body1",color:"text.secondary",children:"Generate comprehensive student reports with Indian educational context"})]})}),s.jsxs(i,{container:!0,spacing:3,children:[s.jsxs(i,{item:!0,xs:12,md:8,children:[s.jsx(n,{sx:{mb:3},children:s.jsxs(t,{children:[s.jsx(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Select Report Template"}),s.jsx(i,{container:!0,spacing:2,children:Q.map((e=>s.jsx(i,{item:!0,xs:12,sm:6,children:s.jsx(n,{sx:{cursor:"pointer",border:w===e.id?`2px solid ${k.palette[e.color].main}`:"1px solid",borderColor:w===e.id?`${e.color}.main`:"divider",transition:"all 0.2s ease","&:hover":{transform:"translateY(-2px)",boxShadow:k.shadows[4]}},onClick:()=>G(e.id),children:s.jsxs(t,{children:[s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:2,mb:1},children:[s.jsx(e.icon,{color:e.color}),s.jsx(r,{variant:"subtitle1",sx:{fontWeight:600},children:e.name})]}),s.jsx(r,{variant:"body2",color:"text.secondary",children:e.description})]})})},e.id)))})]})}),s.jsx(n,{sx:{mb:3},children:s.jsxs(t,{children:[s.jsx(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Report Configuration"}),s.jsxs(i,{container:!0,spacing:3,children:[s.jsx(i,{item:!0,xs:12,md:4,children:s.jsxs(l,{fullWidth:!0,children:[s.jsx(o,{children:"Class"}),s.jsxs(d,{value:I,onChange:e=>D(e.target.value),label:"Class",children:[s.jsx(c,{value:"",children:"All Classes"}),s.jsx(c,{value:"9-A",children:"Class 9-A"}),s.jsx(c,{value:"9-B",children:"Class 9-B"}),s.jsx(c,{value:"10-A",children:"Class 10-A"}),s.jsx(c,{value:"10-B",children:"Class 10-B"})]})]})}),s.jsx(i,{item:!0,xs:12,md:4,children:s.jsxs(l,{fullWidth:!0,children:[s.jsx(o,{children:"Report Period"}),s.jsxs(d,{value:O,onChange:e=>z(e.target.value),label:"Report Period",children:[s.jsx(c,{value:"Q1_2024",children:"Q1 2024-2025"}),s.jsx(c,{value:"Q2_2024",children:"Q2 2024-2025"}),s.jsx(c,{value:"Q3_2024",children:"Q3 2024-2025"}),s.jsx(c,{value:"Q4_2024",children:"Q4 2024-2025"}),s.jsx(c,{value:"ANNUAL_2024",children:"Annual 2024-2025"})]})]})}),s.jsx(i,{item:!0,xs:12,md:4,children:s.jsxs(x,{direction:"row",spacing:1,children:[s.jsx(h,{variant:"outlined",size:"small",onClick:()=>{const e=T.filter((e=>!I||e.class===I));M(e.map((e=>e.id)))},children:"Select All"}),s.jsx(h,{variant:"outlined",size:"small",onClick:()=>{M([])},children:"Deselect All"})]})})]})]})}),s.jsx(n,{sx:{mb:3},children:s.jsxs(t,{children:[s.jsx(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Include in Report"}),s.jsx(m,{children:s.jsxs(i,{container:!0,spacing:2,children:[s.jsx(i,{item:!0,xs:12,sm:6,children:s.jsx(j,{control:s.jsx(p,{checked:E.grades,onChange:q("grades")}),label:"Academic Grades"})}),s.jsx(i,{item:!0,xs:12,sm:6,children:s.jsx(j,{control:s.jsx(p,{checked:E.attendance,onChange:q("attendance")}),label:"Attendance Record"})}),s.jsx(i,{item:!0,xs:12,sm:6,children:s.jsx(j,{control:s.jsx(p,{checked:E.behavior,onChange:q("behavior")}),label:"Behavioral Assessment"})}),s.jsx(i,{item:!0,xs:12,sm:6,children:s.jsx(j,{control:s.jsx(p,{checked:E.swot,onChange:q("swot")}),label:"SWOT Analysis"})}),s.jsx(i,{item:!0,xs:12,sm:6,children:s.jsx(j,{control:s.jsx(p,{checked:E.parentComments,onChange:q("parentComments")}),label:"Parent Comments Section"})})]})})]})}),s.jsx(n,{children:s.jsxs(t,{children:[s.jsxs(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:["Select Students (",F.length," selected)"]}),s.jsx(u,{sx:{maxHeight:300,overflow:"auto"},children:J.map((e=>s.jsxs(b,{sx:{cursor:"pointer",borderRadius:1,mb:1,border:F.includes(e.id)?`2px solid ${k.palette.primary.main}`:"1px solid",borderColor:F.includes(e.id)?"primary.main":"divider",background:F.includes(e.id)?g(k.palette.primary.main,.05):"transparent"},onClick:()=>{return s=e.id,void M((e=>e.includes(s)?e.filter((e=>e!==s)):[...e,s]));var s},children:[s.jsx(v,{children:s.jsx(y,{sx:{bgcolor:F.includes(e.id)?"primary.main":"grey.400"},children:e.name.charAt(0)})}),s.jsx(f,{primary:e.name,secondary:`Class ${e.class} - Roll No. ${e.rollNumber}`}),F.includes(e.id)&&s.jsx(N,{color:"primary"})]},e.id)))})]})})]}),s.jsx(i,{item:!0,xs:12,md:4,children:s.jsx(n,{sx:{position:"sticky",top:24},children:s.jsxs(t,{children:[s.jsx(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Report Summary"}),V&&s.jsxs(C,{severity:"info",sx:{mb:2},children:[s.jsx(r,{variant:"subtitle2",sx:{fontWeight:600},children:V.name}),s.jsx(r,{variant:"body2",children:V.description})]}),s.jsxs(x,{spacing:2,sx:{mb:3},children:[s.jsxs(a,{children:[s.jsx(r,{variant:"body2",color:"text.secondary",children:"Selected Students:"}),s.jsx(r,{variant:"h6",sx:{fontWeight:600},children:F.length})]}),s.jsxs(a,{children:[s.jsx(r,{variant:"body2",color:"text.secondary",children:"Class Filter:"}),s.jsx(r,{variant:"body1",children:I||"All Classes"})]}),s.jsxs(a,{children:[s.jsx(r,{variant:"body2",color:"text.secondary",children:"Report Period:"}),s.jsx(r,{variant:"body1",children:O||"Not selected"})]})]}),H&&s.jsxs(a,{sx:{mb:3},children:[s.jsxs(r,{variant:"body2",sx:{mb:1},children:["Generating Reports... ",U,"%"]}),s.jsx(A,{variant:"determinate",value:U})]}),s.jsxs(x,{spacing:2,children:[s.jsx(h,{variant:"contained",fullWidth:!0,startIcon:s.jsx(W,{}),onClick:async()=>{if(w&&0!==F.length){K(!0),Y(0);try{for(let e=0;e<=100;e+=10)await new Promise((e=>setTimeout(e,200))),Y(e);alert(`Successfully generated ${F.length} reports!`)}catch(e){}finally{K(!1),Y(0)}}else alert("Please select a template and at least one student")},disabled:!w||0===F.length||H,sx:{background:`linear-gradient(135deg, ${k.palette.primary.main} 0%, ${k.palette.secondary.main} 100%)`},children:H?"Generating...":"Generate Reports"}),s.jsx(h,{variant:"outlined",fullWidth:!0,startIcon:s.jsx(P,{}),disabled:!w,children:"Preview Template"}),s.jsx(h,{variant:"outlined",fullWidth:!0,startIcon:s.jsx($,{}),disabled:0===F.length,children:"Download All"}),s.jsx(h,{variant:"outlined",fullWidth:!0,startIcon:s.jsx(_,{}),disabled:0===F.length,children:"Email to Parents"})]})]})})})]})]})};export{G as default};
//# sourceMappingURL=ReportGeneration-WXxmrtL8.js.map
