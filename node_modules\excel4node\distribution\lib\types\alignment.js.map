{"version": 3, "file": "alignment.js", "names": ["horizontalAlignments", "_this", "opts", "for<PERSON>ach", "o", "i", "verticalAlignments", "_this2", "readingOrders", "prototype", "validate", "val", "undefined", "name", "hasOwnProperty", "push", "TypeError", "concat", "join", "module", "exports", "vertical", "horizontal", "readingOrder"], "sources": ["../../../source/lib/types/alignment.js"], "sourcesContent": ["function horizontalAlignments() {\n    this.opts = [ // §18.18.40 ST_HorizontalAlignment (Horizontal Alignment Type)\n        'center', \n        'centerContinuous', \n        'distributed', \n        'fill', \n        'general', \n        'justify', \n        'left', \n        'right'\n    ];\n    this.opts.forEach((o, i) => {\n        this[o] = i + 1;\n    });\n}\n\nfunction verticalAlignments() {\n    this.opts = [ //§18.18.88 ST_VerticalAlignment (Vertical Alignment Types)\n        'bottom', \n        'center', \n        'distributed', \n        'justify', \n        'top'\n    ];\n    this.opts.forEach((o, i) => {\n        this[o] = i + 1;\n    });\n}\n\nfunction readingOrders() {\n    this['contextDependent'] = 0;\n    this['leftToRight'] = 1;\n    this['rightToLeft'] = 2;\n    this.opts = ['contextDependent', 'leftToRight', 'rightToLeft'];\n}\n\nhorizontalAlignments.prototype.validate = function (val) {\n    if (this[val] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError(`Invalid value for alignment.horizontal ${val}; Value must be one of ${this.opts.join(', ')}`);\n    } else {\n        return true;\n    }\n};\n\nverticalAlignments.prototype.validate = function (val) {\n    if (this[val] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError(`Invalid value for alignment.vertical ${val}; Value must be one of ${this.opts.join(', ')}`);\n    } else {\n        return true;\n    }\n};\n\nreadingOrders.prototype.validate = function (val) {\n    if (this[val] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError(`Invalid value for alignment.readingOrder ${val}; Value must be one of ${this.opts.join(', ')}`);\n    } else {\n        return true;\n    }\n};\n\nmodule.exports.vertical = new verticalAlignments();\nmodule.exports.horizontal = new horizontalAlignments();\nmodule.exports.readingOrder = new readingOrders();"], "mappings": ";;AAAA,SAASA,oBAAoBA,CAAA,EAAG;EAAA,IAAAC,KAAA;EAC5B,IAAI,CAACC,IAAI,GAAG;EAAE;EACV,QAAQ,EACR,kBAAkB,EAClB,aAAa,EACb,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,OAAO,CACV;EACD,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACxBJ,KAAI,CAACG,CAAC,CAAC,GAAGC,CAAC,GAAG,CAAC;EACnB,CAAC,CAAC;AACN;AAEA,SAASC,kBAAkBA,CAAA,EAAG;EAAA,IAAAC,MAAA;EAC1B,IAAI,CAACL,IAAI,GAAG;EAAE;EACV,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,SAAS,EACT,KAAK,CACR;EACD,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACxBE,MAAI,CAACH,CAAC,CAAC,GAAGC,CAAC,GAAG,CAAC;EACnB,CAAC,CAAC;AACN;AAEA,SAASG,aAAaA,CAAA,EAAG;EACrB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;EAC5B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;EACvB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;EACvB,IAAI,CAACN,IAAI,GAAG,CAAC,kBAAkB,EAAE,aAAa,EAAE,aAAa,CAAC;AAClE;AAEAF,oBAAoB,CAACS,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACrD,IAAI,IAAI,CAACA,GAAG,CAAC,KAAKC,SAAS,EAAE;IACzB,IAAIV,IAAI,GAAG,EAAE;IACb,KAAK,IAAIW,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BX,IAAI,CAACa,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIG,SAAS,2CAAAC,MAAA,CAA2CN,GAAG,6BAAAM,MAAA,CAA0B,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;EACtH,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDZ,kBAAkB,CAACG,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACnD,IAAI,IAAI,CAACA,GAAG,CAAC,KAAKC,SAAS,EAAE;IACzB,IAAIV,IAAI,GAAG,EAAE;IACb,KAAK,IAAIW,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BX,IAAI,CAACa,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIG,SAAS,yCAAAC,MAAA,CAAyCN,GAAG,6BAAAM,MAAA,CAA0B,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;EACpH,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDV,aAAa,CAACC,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EAC9C,IAAI,IAAI,CAACA,GAAG,CAAC,KAAKC,SAAS,EAAE;IACzB,IAAIV,IAAI,GAAG,EAAE;IACb,KAAK,IAAIW,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BX,IAAI,CAACa,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIG,SAAS,6CAAAC,MAAA,CAA6CN,GAAG,6BAAAM,MAAA,CAA0B,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;EACxH,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,CAACC,QAAQ,GAAG,IAAIf,kBAAkB,CAAC,CAAC;AAClDa,MAAM,CAACC,OAAO,CAACE,UAAU,GAAG,IAAItB,oBAAoB,CAAC,CAAC;AACtDmB,MAAM,CAACC,OAAO,CAACG,YAAY,GAAG,IAAIf,aAAa,CAAC,CAAC"}