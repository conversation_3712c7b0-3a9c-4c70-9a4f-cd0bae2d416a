{"version": 3, "file": "utils.js", "names": ["types", "require", "_bitXOR", "a", "b", "max<PERSON><PERSON><PERSON>", "length", "padString", "i", "String", "substr", "response", "generateRId", "text", "possible", "char<PERSON>t", "Math", "floor", "random", "_rotateBinary", "bin", "_getHashForChar", "char", "hash", "charCode", "charCodeAt", "hashBin", "parseInt", "toString", "char<PERSON>in", "nextHash", "getHashOfPassword", "str", "curHash", "curH<PERSON><PERSON><PERSON>", "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "saltBin", "firstXOR", "final<PERSON><PERSON><PERSON><PERSON>", "finalHash", "toUpperCase", "slice", "getExcelAlpha", "colNum", "remaining", "aCharCode", "columnName", "mod", "fromCharCode", "getExcelCellRef", "row<PERSON>um", "getExcelRowCol", "numeric", "split", "filter", "el", "alpha", "row", "col", "reduce", "index", "arr", "pow", "getExcelTS", "date", "thisDt", "Date", "getTime", "epoch", "legacyLeapDate", "diff2", "ts", "parseFloat", "toFixed", "sortCellRefs", "aAtt", "bAtt", "arrayIntersectSafe", "Array", "ai", "bi", "result", "push", "TypeError", "getAllCellsInExcelRange", "range", "cells", "cell1props", "cell2props", "getAllCellsInNumericRange", "row1", "col1", "row2", "col2", "j", "sort", "boolToInt", "bool", "module", "exports"], "sources": ["../../source/lib/utils.js"], "sourcesContent": ["let types = require('./types/index.js');\n\nlet _bitXOR = (a, b) => {\n    let maxLength = a.length > b.length ? a.length : b.length;\n\n    let padString = '';\n    for (let i = 0; i < maxLength; i++) {\n        padString += '0';\n    }\n\n    a = String(padString + a).substr(-maxLength);\n    b = String(padString + b).substr(-maxLength);\n\n    let response = '';\n    for (let i = 0; i < a.length; i++) {\n        response += a[i] === b[i] ? 0 : 1;\n    }\n    return response;\n};\n\nlet generateRId = () => {\n    let text = 'R';\n    let possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    for (let i = 0; i < 16; i++) {\n        text += possible.charAt(Math.floor(Math.random() * possible.length));\n    }\n    return text;\n};\n\nlet _rotateBinary = (bin) => {\n    return bin.substr(1, bin.length - 1) + bin.substr(0, 1);\n};\n\nlet _getHashForChar = (char, hash) => {\n    hash = hash ? hash : '0000';\n    let charCode = char.charCodeAt(0);\n    let hashBin = parseInt(hash, 16).toString(2);\n    let charBin = parseInt(charCode, 10).toString(2);\n    hashBin = String('000000000000000' + hashBin).substr(-15);\n    charBin = String('000000000000000' + charBin).substr(-15);\n    let nextHash = _bitXOR(hashBin, charBin);\n    nextHash = _rotateBinary(nextHash);\n    nextHash = parseInt(nextHash, 2).toString(16);\n\n    return nextHash;\n};\n\n//  http://www.openoffice.org/sc/excelfileformat.pdf section 4.18.4\nlet getHashOfPassword = (str) => {\n    let curHash = '0000';\n    for (let i = str.length - 1; i >= 0; i--) {\n        curHash = _getHashForChar(str[i], curHash);\n    }\n    let curHashBin = parseInt(curHash, 16).toString(2);\n    let charCountBin = parseInt(str.length, 10).toString(2);\n    let saltBin = parseInt('CE4B', 16).toString(2);\n\n    let firstXOR = _bitXOR(curHashBin, charCountBin);\n    let finalHashBin = _bitXOR(firstXOR, saltBin);\n    let finalHash = String('0000' + parseInt(finalHashBin, 2).toString(16).toUpperCase()).slice(-4);\n\n    return finalHash;\n};\n\n/**\n * Translates a column number into the Alpha equivalent used by Excel\n * @function getExcelAlpha\n * @param {Number} colNum Column number that is to be transalated\n * @returns {String} The Excel alpha representation of the column number\n * @example\n * // returns B\n * getExcelAlpha(2);\n */\nlet getExcelAlpha = (colNum) => {\n    let remaining = colNum;\n    let aCharCode = 65;\n    let columnName = '';\n    while (remaining > 0) {\n        let mod = (remaining - 1) % 26;\n        columnName = String.fromCharCode(aCharCode + mod) + columnName;\n        remaining = (remaining - 1 - mod) / 26;\n    }\n    return columnName;\n};\n\n/**\n * Translates a column number into the Alpha equivalent used by Excel\n * @function getExcelAlpha\n * @param {Number} rowNum Row number that is to be transalated\n * @param {Number} colNum Column number that is to be transalated\n * @returns {String} The Excel alpha representation of the column number\n * @example\n * // returns B1\n * getExcelCellRef(1, 2);\n */\nlet getExcelCellRef = (rowNum, colNum) => {\n    let remaining = colNum;\n    let aCharCode = 65;\n    let columnName = '';\n    while (remaining > 0) {\n        let mod = (remaining - 1) % 26;\n        columnName = String.fromCharCode(aCharCode + mod) + columnName;\n        remaining = (remaining - 1 - mod) / 26;\n    }\n    return columnName + rowNum;\n};\n\n/**\n * Translates a Excel cell represenation into row and column numerical equivalents\n * @function getExcelRowCol\n * @param {String} str Excel cell representation\n * @returns {Object} Object keyed with row and col\n * @example\n * // returns {row: 2, col: 3}\n * getExcelRowCol('C2')\n */\nlet getExcelRowCol = (str) => {\n    let numeric = str.split(/\\D/).filter(function (el) {\n        return el !== '';\n    })[0];\n    let alpha = str.split(/\\d/).filter(function (el) {\n        return el !== '';\n    })[0];\n    let row = parseInt(numeric, 10);\n    let col = alpha.toUpperCase().split('').reduce(function (a, b, index, arr) {\n        return a + (b.charCodeAt(0) - 64) * Math.pow(26, arr.length - index - 1);\n    }, 0);\n    return { row: row, col: col };\n};\n\n/**\n * Translates a date into Excel timestamp\n * @function getExcelTS\n * @param {Date} date Date to translate\n * @returns {Number} Excel timestamp\n * @example\n * // returns 29810.958333333332\n * getExcelTS(new Date('08/13/1981'));\n */\nlet getExcelTS = (date) => {\n\n    let thisDt = new Date(date);\n    thisDt = new Date(thisDt.getTime() + 24 * 60 * 60 * 1000);\n\n    let epoch = new Date('1900-01-01T00:00:00.0000Z');\n\n    // Handle legacy leap year offset as described in  §18.17.4.1\n    const legacyLeapDate = new Date('1900-02-28T23:59:59.999Z');\n    if (thisDt - legacyLeapDate > 0) {\n        thisDt = new Date(thisDt.getTime() + 24 * 60 * 60 * 1000);\n    }\n\n    // Get milliseconds between date sent to function and epoch\n    let diff2 = thisDt.getTime() - epoch.getTime();\n\n    let ts = diff2 / (1000 * 60 * 60 * 24);\n\n    return parseFloat(ts.toFixed(8));\n};\n\nlet sortCellRefs = (a, b) => {\n    let aAtt = getExcelRowCol(a);\n    let bAtt = getExcelRowCol(b);\n    if (aAtt.col === bAtt.col) {\n        return aAtt.row - bAtt.row;\n    } else {\n        return aAtt.col - bAtt.col;\n    }\n};\n\nlet arrayIntersectSafe = (a, b) => {\n\n    if (a instanceof Array && b instanceof Array) {\n        var ai = 0, bi = 0;\n        var result = new Array();\n\n        while (ai < a.length && bi < b.length) {\n            if (a[ai] < b[bi]) {\n                ai++;\n            } else if (a[ai] > b[bi]) {\n                bi++;\n            } else {\n                result.push(a[ai]);\n                ai++;\n                bi++;\n            }\n        }\n        return result;\n    } else {\n        throw new TypeError('Both variables sent to arrayIntersectSafe must be arrays');\n    }\n};\n\nlet getAllCellsInExcelRange = (range) => {\n    var cells = range.split(':');\n    var cell1props = getExcelRowCol(cells[0]);\n    var cell2props = getExcelRowCol(cells[1]);\n    return getAllCellsInNumericRange(cell1props.row, cell1props.col, cell2props.row, cell2props.col);\n};\n\nlet getAllCellsInNumericRange = (row1, col1, row2, col2) => {\n    var response = [];\n    row2 = row2 ? row2 : row1;\n    col2 = col2 ? col2 : col1;\n    for (var i = row1; i <= row2; i++) {\n        for (var j = col1; j <= col2; j++) {\n            response.push(getExcelAlpha(j) + i);\n        }\n    }\n    return response.sort(sortCellRefs);\n};\n\nlet boolToInt = (bool) => {\n    if (bool === true) {\n        return 1;\n    }\n    if (bool === false) {\n        return 0;\n    }\n    if (parseInt(bool) === 1) {\n        return 1;\n    }\n    if (parseInt(bool) === 0) {\n        return 0;\n    }\n    throw new TypeError('Value sent to boolToInt must be true, false, 1 or 0');\n};\n\n/*\n * Helper Functions\n */\n\nmodule.exports = {\n    generateRId,\n    getHashOfPassword,\n    getExcelAlpha,\n    getExcelCellRef,\n    getExcelRowCol,\n    getExcelTS,\n    sortCellRefs,\n    arrayIntersectSafe,\n    getAllCellsInExcelRange,\n    getAllCellsInNumericRange,\n    boolToInt\n};\n"], "mappings": ";;AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEvC,IAAIC,OAAO,GAAG,SAAVA,OAAOA,CAAIC,CAAC,EAAEC,CAAC,EAAK;EACpB,IAAIC,SAAS,GAAGF,CAAC,CAACG,MAAM,GAAGF,CAAC,CAACE,MAAM,GAAGH,CAAC,CAACG,MAAM,GAAGF,CAAC,CAACE,MAAM;EAEzD,IAAIC,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,EAAEG,CAAC,EAAE,EAAE;IAChCD,SAAS,IAAI,GAAG;EACpB;EAEAJ,CAAC,GAAGM,MAAM,CAACF,SAAS,GAAGJ,CAAC,CAAC,CAACO,MAAM,CAAC,CAACL,SAAS,CAAC;EAC5CD,CAAC,GAAGK,MAAM,CAACF,SAAS,GAAGH,CAAC,CAAC,CAACM,MAAM,CAAC,CAACL,SAAS,CAAC;EAE5C,IAAIM,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIH,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,EAAC,EAAE,EAAE;IAC/BG,QAAQ,IAAIR,CAAC,CAACK,EAAC,CAAC,KAAKJ,CAAC,CAACI,EAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EACrC;EACA,OAAOG,QAAQ;AACnB,CAAC;AAED,IAAIC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;EACpB,IAAIC,IAAI,GAAG,GAAG;EACd,IAAIC,QAAQ,GAAG,gEAAgE;EAC/E,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IACzBK,IAAI,IAAIC,QAAQ,CAACC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,QAAQ,CAACR,MAAM,CAAC,CAAC;EACxE;EACA,OAAOO,IAAI;AACf,CAAC;AAED,IAAIM,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAK;EACzB,OAAOA,GAAG,CAACV,MAAM,CAAC,CAAC,EAAEU,GAAG,CAACd,MAAM,GAAG,CAAC,CAAC,GAAGc,GAAG,CAACV,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,IAAIW,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAI,EAAEC,IAAI,EAAK;EAClCA,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG,MAAM;EAC3B,IAAIC,QAAQ,GAAGF,KAAI,CAACG,UAAU,CAAC,CAAC,CAAC;EACjC,IAAIC,OAAO,GAAGC,QAAQ,CAACJ,IAAI,EAAE,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;EAC5C,IAAIC,OAAO,GAAGF,QAAQ,CAACH,QAAQ,EAAE,EAAE,CAAC,CAACI,QAAQ,CAAC,CAAC,CAAC;EAChDF,OAAO,GAAGjB,MAAM,CAAC,iBAAiB,GAAGiB,OAAO,CAAC,CAAChB,MAAM,CAAC,CAAC,EAAE,CAAC;EACzDmB,OAAO,GAAGpB,MAAM,CAAC,iBAAiB,GAAGoB,OAAO,CAAC,CAACnB,MAAM,CAAC,CAAC,EAAE,CAAC;EACzD,IAAIoB,QAAQ,GAAG5B,OAAO,CAACwB,OAAO,EAAEG,OAAO,CAAC;EACxCC,QAAQ,GAAGX,aAAa,CAACW,QAAQ,CAAC;EAClCA,QAAQ,GAAGH,QAAQ,CAACG,QAAQ,EAAE,CAAC,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC;EAE7C,OAAOE,QAAQ;AACnB,CAAC;;AAED;AACA,IAAIC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,GAAG,EAAK;EAC7B,IAAIC,OAAO,GAAG,MAAM;EACpB,KAAK,IAAIzB,CAAC,GAAGwB,GAAG,CAAC1B,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACtCyB,OAAO,GAAGZ,eAAe,CAACW,GAAG,CAACxB,CAAC,CAAC,EAAEyB,OAAO,CAAC;EAC9C;EACA,IAAIC,UAAU,GAAGP,QAAQ,CAACM,OAAO,EAAE,EAAE,CAAC,CAACL,QAAQ,CAAC,CAAC,CAAC;EAClD,IAAIO,YAAY,GAAGR,QAAQ,CAACK,GAAG,CAAC1B,MAAM,EAAE,EAAE,CAAC,CAACsB,QAAQ,CAAC,CAAC,CAAC;EACvD,IAAIQ,OAAO,GAAGT,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;EAE9C,IAAIS,QAAQ,GAAGnC,OAAO,CAACgC,UAAU,EAAEC,YAAY,CAAC;EAChD,IAAIG,YAAY,GAAGpC,OAAO,CAACmC,QAAQ,EAAED,OAAO,CAAC;EAC7C,IAAIG,SAAS,GAAG9B,MAAM,CAAC,MAAM,GAAGkB,QAAQ,CAACW,YAAY,EAAE,CAAC,CAAC,CAACV,QAAQ,CAAC,EAAE,CAAC,CAACY,WAAW,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAE/F,OAAOF,SAAS;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,MAAM,EAAK;EAC5B,IAAIC,SAAS,GAAGD,MAAM;EACtB,IAAIE,SAAS,GAAG,EAAE;EAClB,IAAIC,UAAU,GAAG,EAAE;EACnB,OAAOF,SAAS,GAAG,CAAC,EAAE;IAClB,IAAIG,GAAG,GAAG,CAACH,SAAS,GAAG,CAAC,IAAI,EAAE;IAC9BE,UAAU,GAAGrC,MAAM,CAACuC,YAAY,CAACH,SAAS,GAAGE,GAAG,CAAC,GAAGD,UAAU;IAC9DF,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,GAAGG,GAAG,IAAI,EAAE;EAC1C;EACA,OAAOD,UAAU;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,MAAM,EAAEP,MAAM,EAAK;EACtC,IAAIC,SAAS,GAAGD,MAAM;EACtB,IAAIE,SAAS,GAAG,EAAE;EAClB,IAAIC,UAAU,GAAG,EAAE;EACnB,OAAOF,SAAS,GAAG,CAAC,EAAE;IAClB,IAAIG,GAAG,GAAG,CAACH,SAAS,GAAG,CAAC,IAAI,EAAE;IAC9BE,UAAU,GAAGrC,MAAM,CAACuC,YAAY,CAACH,SAAS,GAAGE,GAAG,CAAC,GAAGD,UAAU;IAC9DF,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,GAAGG,GAAG,IAAI,EAAE;EAC1C;EACA,OAAOD,UAAU,GAAGI,MAAM;AAC9B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAG,SAAjBA,cAAcA,CAAInB,GAAG,EAAK;EAC1B,IAAIoB,OAAO,GAAGpB,GAAG,CAACqB,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,UAAUC,EAAE,EAAE;IAC/C,OAAOA,EAAE,KAAK,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,IAAIC,KAAK,GAAGxB,GAAG,CAACqB,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,UAAUC,EAAE,EAAE;IAC7C,OAAOA,EAAE,KAAK,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,IAAIE,GAAG,GAAG9B,QAAQ,CAACyB,OAAO,EAAE,EAAE,CAAC;EAC/B,IAAIM,GAAG,GAAGF,KAAK,CAAChB,WAAW,CAAC,CAAC,CAACa,KAAK,CAAC,EAAE,CAAC,CAACM,MAAM,CAAC,UAAUxD,CAAC,EAAEC,CAAC,EAAEwD,KAAK,EAAEC,GAAG,EAAE;IACvE,OAAO1D,CAAC,GAAG,CAACC,CAAC,CAACqB,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIT,IAAI,CAAC8C,GAAG,CAAC,EAAE,EAAED,GAAG,CAACvD,MAAM,GAAGsD,KAAK,GAAG,CAAC,CAAC;EAC5E,CAAC,EAAE,CAAC,CAAC;EACL,OAAO;IAAEH,GAAG,EAAEA,GAAG;IAAEC,GAAG,EAAEA;EAAI,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;EAEvB,IAAIC,MAAM,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;EAC3BC,MAAM,GAAG,IAAIC,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAEzD,IAAIC,KAAK,GAAG,IAAIF,IAAI,CAAC,2BAA2B,CAAC;;EAEjD;EACA,IAAMG,cAAc,GAAG,IAAIH,IAAI,CAAC,0BAA0B,CAAC;EAC3D,IAAID,MAAM,GAAGI,cAAc,GAAG,CAAC,EAAE;IAC7BJ,MAAM,GAAG,IAAIC,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC7D;;EAEA;EACA,IAAIG,KAAK,GAAGL,MAAM,CAACE,OAAO,CAAC,CAAC,GAAGC,KAAK,CAACD,OAAO,CAAC,CAAC;EAE9C,IAAII,EAAE,GAAGD,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EAEtC,OAAOE,UAAU,CAACD,EAAE,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC;AAED,IAAIC,YAAY,GAAG,SAAfA,YAAYA,CAAIvE,CAAC,EAAEC,CAAC,EAAK;EACzB,IAAIuE,IAAI,GAAGxB,cAAc,CAAChD,CAAC,CAAC;EAC5B,IAAIyE,IAAI,GAAGzB,cAAc,CAAC/C,CAAC,CAAC;EAC5B,IAAIuE,IAAI,CAACjB,GAAG,KAAKkB,IAAI,CAAClB,GAAG,EAAE;IACvB,OAAOiB,IAAI,CAAClB,GAAG,GAAGmB,IAAI,CAACnB,GAAG;EAC9B,CAAC,MAAM;IACH,OAAOkB,IAAI,CAACjB,GAAG,GAAGkB,IAAI,CAAClB,GAAG;EAC9B;AACJ,CAAC;AAED,IAAImB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAI1E,CAAC,EAAEC,CAAC,EAAK;EAE/B,IAAID,CAAC,YAAY2E,KAAK,IAAI1E,CAAC,YAAY0E,KAAK,EAAE;IAC1C,IAAIC,EAAE,GAAG,CAAC;MAAEC,EAAE,GAAG,CAAC;IAClB,IAAIC,MAAM,GAAG,IAAIH,KAAK,CAAC,CAAC;IAExB,OAAOC,EAAE,GAAG5E,CAAC,CAACG,MAAM,IAAI0E,EAAE,GAAG5E,CAAC,CAACE,MAAM,EAAE;MACnC,IAAIH,CAAC,CAAC4E,EAAE,CAAC,GAAG3E,CAAC,CAAC4E,EAAE,CAAC,EAAE;QACfD,EAAE,EAAE;MACR,CAAC,MAAM,IAAI5E,CAAC,CAAC4E,EAAE,CAAC,GAAG3E,CAAC,CAAC4E,EAAE,CAAC,EAAE;QACtBA,EAAE,EAAE;MACR,CAAC,MAAM;QACHC,MAAM,CAACC,IAAI,CAAC/E,CAAC,CAAC4E,EAAE,CAAC,CAAC;QAClBA,EAAE,EAAE;QACJC,EAAE,EAAE;MACR;IACJ;IACA,OAAOC,MAAM;EACjB,CAAC,MAAM;IACH,MAAM,IAAIE,SAAS,CAAC,0DAA0D,CAAC;EACnF;AACJ,CAAC;AAED,IAAIC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIC,KAAK,EAAK;EACrC,IAAIC,KAAK,GAAGD,KAAK,CAAChC,KAAK,CAAC,GAAG,CAAC;EAC5B,IAAIkC,UAAU,GAAGpC,cAAc,CAACmC,KAAK,CAAC,CAAC,CAAC,CAAC;EACzC,IAAIE,UAAU,GAAGrC,cAAc,CAACmC,KAAK,CAAC,CAAC,CAAC,CAAC;EACzC,OAAOG,yBAAyB,CAACF,UAAU,CAAC9B,GAAG,EAAE8B,UAAU,CAAC7B,GAAG,EAAE8B,UAAU,CAAC/B,GAAG,EAAE+B,UAAU,CAAC9B,GAAG,CAAC;AACpG,CAAC;AAED,IAAI+B,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAIC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAK;EACxD,IAAIlF,QAAQ,GAAG,EAAE;EACjBiF,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAGF,IAAI;EACzBG,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAGF,IAAI;EACzB,KAAK,IAAInF,CAAC,GAAGkF,IAAI,EAAElF,CAAC,IAAIoF,IAAI,EAAEpF,CAAC,EAAE,EAAE;IAC/B,KAAK,IAAIsF,CAAC,GAAGH,IAAI,EAAEG,CAAC,IAAID,IAAI,EAAEC,CAAC,EAAE,EAAE;MAC/BnF,QAAQ,CAACuE,IAAI,CAACxC,aAAa,CAACoD,CAAC,CAAC,GAAGtF,CAAC,CAAC;IACvC;EACJ;EACA,OAAOG,QAAQ,CAACoF,IAAI,CAACrB,YAAY,CAAC;AACtC,CAAC;AAED,IAAIsB,SAAS,GAAG,SAAZA,SAASA,CAAIC,IAAI,EAAK;EACtB,IAAIA,IAAI,KAAK,IAAI,EAAE;IACf,OAAO,CAAC;EACZ;EACA,IAAIA,IAAI,KAAK,KAAK,EAAE;IAChB,OAAO,CAAC;EACZ;EACA,IAAItE,QAAQ,CAACsE,IAAI,CAAC,KAAK,CAAC,EAAE;IACtB,OAAO,CAAC;EACZ;EACA,IAAItE,QAAQ,CAACsE,IAAI,CAAC,KAAK,CAAC,EAAE;IACtB,OAAO,CAAC;EACZ;EACA,MAAM,IAAId,SAAS,CAAC,qDAAqD,CAAC;AAC9E,CAAC;;AAED;AACA;AACA;;AAEAe,MAAM,CAACC,OAAO,GAAG;EACbvF,WAAW,EAAXA,WAAW;EACXmB,iBAAiB,EAAjBA,iBAAiB;EACjBW,aAAa,EAAbA,aAAa;EACbO,eAAe,EAAfA,eAAe;EACfE,cAAc,EAAdA,cAAc;EACdY,UAAU,EAAVA,UAAU;EACVW,YAAY,EAAZA,YAAY;EACZG,kBAAkB,EAAlBA,kBAAkB;EAClBO,uBAAuB,EAAvBA,uBAAuB;EACvBK,yBAAyB,EAAzBA,yBAAyB;EACzBO,SAAS,EAATA;AACJ,CAAC"}