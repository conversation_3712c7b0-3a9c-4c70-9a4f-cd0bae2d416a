{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src/core/errors.ts"], "names": [], "mappings": "AACA,OAAO,SAAS,4BAAmC;AAGnD,qBAAa,yBAA0B,SAAQ,KAAK;gBACtC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM;CAIlD;AAED,qBAAa,uBAAwB,SAAQ,KAAK;gBACpC,SAAS,EAAE,MAAM;CAI9B;AAED,qBAAa,yBAA0B,SAAQ,KAAK;gBACtC,QAAQ,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG;CAa/C;AAED,qBAAa,wBAAyB,SAAQ,KAAK;gBACrC,QAAQ,EAAE,MAAM;CAI7B;AAED,qBAAa,YAAa,SAAQ,KAAK;gBACzB,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM;CAIlD;AAED,qBAAa,mBAAoB,SAAQ,KAAK;gBAChC,GAAG,CAAC,EAAE,SAAS;CAI5B;AAED,qBAAa,iCAAkC,SAAQ,KAAK;;CAK3D;AAED,qBAAa,2BAA4B,SAAQ,KAAK;gBACxC,MAAM,EAAE,GAAG;CAKxB;AAED,qBAAa,mCAAoC,SAAQ,KAAK;;CAK7D;AAED,qBAAa,2BAA4B,SAAQ,KAAK;gBACxC,IAAI,EAAE,MAAM;CAIzB;AAED,qBAAa,yBAA0B,SAAQ,KAAK;gBACtC,KAAK,EAAE,MAAM;CAI1B;AAED,qBAAa,uBAAwB,SAAQ,KAAK;gBACpC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;CAI/C;AAED,qBAAa,oBAAqB,SAAQ,KAAK;gBACjC,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;CAInD;AAED,qBAAa,qBAAsB,SAAQ,KAAK;gBAClC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;CAIpD;AAED,qBAAa,0BAA2B,SAAQ,KAAK;;CAKpD;AAED,qBAAa,qBAAsB,SAAQ,KAAK;;CAK/C;AAED,qBAAa,mBAAoB,SAAQ,KAAK;gBAChC,SAAS,EAAE,MAAM;CAI9B;AAED,qBAAa,sBAAuB,SAAQ,KAAK;gBACnC,SAAS,EAAE,MAAM;CAI9B;AAED,4BAA4B;AAE5B,MAAM,WAAW,QAAQ;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,qBAAa,kBAAmB,SAAQ,KAAK;gBAC/B,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM;CAMzC;AAED,qBAAa,eAAgB,SAAQ,KAAK;gBAC5B,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM;CAM3C;AAED,qBAAa,sBAAuB,SAAQ,eAAe;gBAC7C,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM;CAIpE;AAED,qBAAa,qBAAsB,SAAQ,eAAe;gBAC5C,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM;CAIxC;AAED,qBAAa,4BAA6B,SAAQ,eAAe;gBACnD,GAAG,EAAE,QAAQ;CAI1B;AAED,qBAAa,qBAAsB,SAAQ,eAAe;gBAC5C,GAAG,EAAE,QAAQ;CAI1B;AAED,qBAAa,0BAA2B,SAAQ,eAAe;gBACjD,GAAG,EAAE,QAAQ;CAI1B;AAED,qBAAa,kBAAmB,SAAQ,eAAe;gBACzC,GAAG,EAAE,QAAQ;CAI1B;AAED,qBAAa,qBAAsB,SAAQ,eAAe;gBAC5C,GAAG,EAAE,QAAQ;CAI1B;AAED,qBAAa,mBAAoB,SAAQ,eAAe;gBAC1C,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE;CAI7C"}