{"version": 3, "file": "emu.js", "names": ["EMU", "val", "_classCallCheck", "_value", "value", "_createClass", "key", "get", "set", "undefined", "parseInt", "re", "RegExp", "test", "measure", "parseFloat", "exec", "unit", "TypeError", "toInt", "toInch", "toCM", "module", "exports"], "sources": ["../../../source/lib/classes/emu.js"], "sourcesContent": ["class EMU {\n    \n    /** \n     * The EMU was created in order to be able to evenly divide in both English and Metric units\n     * @class EMU\n     * @param {String} Number of EMUs or string representation of length in mm, cm or in. i.e. '10.5mm'\n     * @property {Number} value Number of EMUs\n     * @returns {EMU} Number of EMUs \n     */\n    constructor(val) {\n        this._value;\n        this.value = val;\n    }\n\n    get value() {\n        return this._value;\n    }\n\n    set value(val) {\n        if (val === undefined) {\n            this._value = 0;\n        } else if (typeof val === 'number') {\n            this._value = val ? parseInt(val) : 0; \n        } else if (typeof val === 'string') {\n            let re = new RegExp('[0-9]+(\\.[0-9]+)?(mm|cm|in)');\n            if (re.test(val) === true) {\n                let measure = parseFloat(/[0-9]+(\\.[0-9]+)?/.exec(val)[0]);\n                let unit = /(mm|cm|in)/.exec(val)[0];\n\n                switch (unit) {\n                case 'mm':\n                    this._value = parseInt(measure * 36000);\n                    break;\n\n                case 'cm':\n                    this._value = parseInt(measure * 360000);\n                    break;\n\n                case 'in':\n                    this._value = parseInt(measure * 914400);\n                    break;\n                }\n            } else {\n                throw new TypeError('EMUs must be specified as whole integer EMUs or Floats immediately followed by unit of measure in cm, mm, or in. i.e. \"1.5in\"');\n            }\n        }        \n    }\n\n    /**\n     * @alias EMU.toInt\n     * @desc Returns the number of EMUs as integer\n     * @func EMU.toInt\n     * @returns {Number} Number of EMUs\n     */\n    toInt() {\n        return this._value;\n    }\n\n    /**\n     * @alias EMU.toInch\n     * @desc Returns the number of Inches for the EMUs\n     * @func EMU.toInch\n     * @returns {Number} Number of Inches for the EMUs\n     */\n    toInch() {\n        return this._value / 914400;\n    }\n\n    /**\n     * @alias EMU.toCM\n     * @desc Returns the number of Centimeters for the EMUs\n     * @func EMU.toCM\n     * @returns {Number} Number of Centimeters for the EMUs\n     */\n    toCM() {\n        return this._value / 360000;\n    }\n}\n\nmodule.exports = EMU;\n\n/*\nM.4.1.1 EMU Unit of Measurement\n\n1 emu  = 1/914400 in = 1/360000 cm\n\nThroughout ECMA-376, the EMU is used as a unit of measurement for length. An EMU is defined as follows:\nThe EMU was created in order to be able to evenly divide in both English and Metric units, in order to \navoid rounding errors during the calculation. The usage of EMUs also facilitates a more seamless system \nswitch and interoperability between different locales utilizing different units of measurement. \nEMUs define an integer based, high precision coordinate system.\n*/"], "mappings": ";;;;;IAAMA,GAAG;EAEL;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,IAAYC,GAAG,EAAE;IAAAC,eAAA,OAAAF,GAAA;IACb,IAAI,CAACG,MAAM;IACX,IAAI,CAACC,KAAK,GAAGH,GAAG;EACpB;EAACI,YAAA,CAAAL,GAAA;IAAAM,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAY;MACR,OAAO,IAAI,CAACJ,MAAM;IACtB,CAAC;IAAAK,GAAA,EAED,SAAAA,IAAUP,GAAG,EAAE;MACX,IAAIA,GAAG,KAAKQ,SAAS,EAAE;QACnB,IAAI,CAACN,MAAM,GAAG,CAAC;MACnB,CAAC,MAAM,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACE,MAAM,GAAGF,GAAG,GAAGS,QAAQ,CAACT,GAAG,CAAC,GAAG,CAAC;MACzC,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAChC,IAAIU,EAAE,GAAG,IAAIC,MAAM,CAAC,6BAA6B,CAAC;QAClD,IAAID,EAAE,CAACE,IAAI,CAACZ,GAAG,CAAC,KAAK,IAAI,EAAE;UACvB,IAAIa,OAAO,GAAGC,UAAU,CAAC,mBAAmB,CAACC,IAAI,CAACf,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D,IAAIgB,IAAI,GAAG,YAAY,CAACD,IAAI,CAACf,GAAG,CAAC,CAAC,CAAC,CAAC;UAEpC,QAAQgB,IAAI;YACZ,KAAK,IAAI;cACL,IAAI,CAACd,MAAM,GAAGO,QAAQ,CAACI,OAAO,GAAG,KAAK,CAAC;cACvC;YAEJ,KAAK,IAAI;cACL,IAAI,CAACX,MAAM,GAAGO,QAAQ,CAACI,OAAO,GAAG,MAAM,CAAC;cACxC;YAEJ,KAAK,IAAI;cACL,IAAI,CAACX,MAAM,GAAGO,QAAQ,CAACI,OAAO,GAAG,MAAM,CAAC;cACxC;UACJ;QACJ,CAAC,MAAM;UACH,MAAM,IAAII,SAAS,CAAC,+HAA+H,CAAC;QACxJ;MACJ;IACJ;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAZ,GAAA;IAAAF,KAAA,EAMA,SAAAe,MAAA,EAAQ;MACJ,OAAO,IAAI,CAAChB,MAAM;IACtB;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAG,GAAA;IAAAF,KAAA,EAMA,SAAAgB,OAAA,EAAS;MACL,OAAO,IAAI,CAACjB,MAAM,GAAG,MAAM;IAC/B;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAG,GAAA;IAAAF,KAAA,EAMA,SAAAiB,KAAA,EAAO;MACH,OAAO,IAAI,CAAClB,MAAM,GAAG,MAAM;IAC/B;EAAC;EAAA,OAAAH,GAAA;AAAA;AAGLsB,MAAM,CAACC,OAAO,GAAGvB,GAAG;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}