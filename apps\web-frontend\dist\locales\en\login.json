{"loginTitle": "Sign In to <PERSON><PERSON>a<PERSON>it<PERSON>", "usernameLabel": "Username", "passwordLabel": "Password", "signInButton": "Sign In", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "noAccount": "Don't have an account?", "signUp": "Sign Up", "welcomeBack": "Welcome back!", "loginSuccess": "Login successful. Redirecting...", "invalidCredentials": "Invalid username or password.", "accountLocked": "Your account has been locked. Please contact administrator.", "accountDisabled": "Your account has been disabled. Please contact support.", "sessionTimeout": "Your session has timed out. Please login again.", "loginRequired": "Please login to access this page.", "logoutSuccess": "You have been logged out successfully.", "passwordReset": "Password Reset", "resetPasswordInstructions": "Enter your email address and we'll send you a link to reset your password.", "resetPasswordButton": "Send Reset Link", "resetPasswordSuccess": "Password reset link has been sent to your email.", "resetPasswordError": "Failed to send password reset link. Please try again.", "newPassword": "New Password", "confirmPassword": "Confirm Password", "changePassword": "Change Password", "passwordChanged": "Password changed successfully.", "passwordChangeError": "Failed to change password. Please try again.", "currentPassword": "Current Password", "passwordRequirements": "Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.", "termsAndConditions": "Terms and Conditions", "privacyPolicy": "Privacy Policy", "agreeToTerms": "I agree to the Terms and Conditions and Privacy Policy", "mustAgreeToTerms": "You must agree to the terms and conditions to continue.", "contactSupport": "Contact Support", "technicalSupport": "For technical support, please contact your school administrator.", "loginHelp": "Need help logging in?", "schoolCode": "School Code", "selectRole": "Select Role", "teacher": "Teacher", "parent": "Parent", "admin": "Administrator", "staff": "Staff", "student": "Student"}