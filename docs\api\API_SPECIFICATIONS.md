# VidyaMitra Platform - API Specifications

## Overview

This document defines the RESTful API endpoints for the VidyaMitra platform backend, designed to support the comprehensive frontend implementation with Indian educational context.

## Base Configuration

- **Base URL**: `https://api.vidyamitra.edu/v1`
- **Authentication**: JWT <PERSON>
- **Content-Type**: `application/json`
- **Rate Limiting**: 1000 requests/hour per user
- **API Version**: v1.0.0

## Authentication Endpoints

### POST /auth/login
Login user and return JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "role": "teacher"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "USR001",
      "name": "<PERSON><PERSON> <PERSON>",
      "email": "<EMAIL>",
      "role": "teacher",
      "schoolId": "SCH001",
      "permissions": ["read_students", "write_grades", "generate_reports"]
    },
    "expiresIn": "24h"
  }
}
```

### POST /auth/refresh
Refresh JWT token.

### POST /auth/logout
Logout user and invalidate token.

## Student Management Endpoints

### GET /students
Get all students with filtering and pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `grade`: Filter by grade (e.g., "10th")
- `board`: Filter by board (cbse, icse, state, ib)
- `school`: Filter by school ID
- `search`: Search by name or ID

**Response:**
```json
{
  "success": true,
  "data": {
    "students": [
      {
        "id": "STU001",
        "name": "Sanju Kumar",
        "grade": "10th",
        "section": "A",
        "board": "cbse",
        "schoolId": "SCH001",
        "academicLevel": 85,
        "region": "Telangana",
        "parentContact": "+91 9876543210",
        "languages": ["Telugu", "Hindi", "English"],
        "dateOfBirth": "2009-03-15",
        "address": "Plot 123, Jubilee Hills, Hyderabad, Telangana - 500033"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 100,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### GET /students/:id
Get individual student details.

### POST /students
Create new student record.

### PUT /students/:id
Update student information.

### DELETE /students/:id
Delete student record.

## SWOT Analysis Endpoints

### GET /students/:id/swot
Get SWOT analysis for a student.

**Response:**
```json
{
  "success": true,
  "data": {
    "studentId": "STU001",
    "strengths": [
      {
        "item": "Strong analytical and logical thinking",
        "description": "Student demonstrates excellent ability to break down complex problems",
        "impact": "high",
        "evidence": "Observed through academic performance (85%) and teacher feedback"
      }
    ],
    "weaknesses": [
      {
        "item": "Hesitation in public speaking",
        "description": "Student needs to build confidence in presentations",
        "impact": "medium",
        "improvementPlan": "Join debate club and practice presentations"
      }
    ],
    "opportunities": [
      {
        "item": "Advanced courses in STEM subjects",
        "description": "Opportunity to excel in Science, Technology, Engineering, and Mathematics",
        "potential": "high",
        "actionSteps": "1. Consult with teachers 2. Enroll in advanced classes 3. Join study groups"
      }
    ],
    "threats": [
      {
        "item": "Intense competition for engineering and medical seats",
        "description": "High competition in entrance exams may create stress",
        "severity": "medium",
        "mitigationStrategy": "Focus on consistent preparation, explore alternative career paths"
      }
    ],
    "lastUpdated": "2024-12-15T10:30:00Z",
    "confidence": 87,
    "culturalContext": "Indian Educational System",
    "boardSpecific": "CBSE curriculum emphasizes conceptual understanding and application-based learning"
  }
}
```

### POST /students/:id/swot
Create or update SWOT analysis.

### GET /swot/cultural
Get cultural SWOT analysis patterns.

## Analytics Endpoints

### GET /analytics/dashboard
Get comprehensive analytics data.

**Response:**
```json
{
  "success": true,
  "data": {
    "totalStudents": 1200,
    "averagePerformance": 84,
    "averageAttendance": 93,
    "topPerformers": 156,
    "boardDistribution": {
      "cbse": 450,
      "icse": 320,
      "state": 380,
      "ib": 50
    },
    "performanceTrends": [
      {
        "month": "Jun",
        "average": 82,
        "cbse": 85,
        "icse": 88,
        "state": 78
      }
    ],
    "subjectAnalysis": [
      {
        "subject": "Mathematics",
        "averageScore": 85,
        "boardComparison": {
          "cbse": 87,
          "icse": 89,
          "state": 81
        }
      }
    ]
  }
}
```

### GET /analytics/performance
Get performance analytics with filters.

### GET /analytics/attendance
Get attendance analytics.

### GET /analytics/trends
Get trend analysis data.

## Reports Endpoints

### GET /reports/templates
Get available report templates.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "comprehensive",
      "name": "Comprehensive Student Report",
      "description": "Complete academic and behavioral analysis",
      "category": "Student",
      "boardSpecific": true,
      "fields": ["Academic Performance", "Attendance", "SWOT Analysis"],
      "formats": ["PDF", "Excel"],
      "estimatedTime": "2-3 minutes"
    }
  ]
}
```

### POST /reports/generate
Generate a new report.

**Request Body:**
```json
{
  "templateId": "comprehensive",
  "studentIds": ["STU001", "STU002"],
  "format": "PDF",
  "parameters": {
    "startDate": "2024-06-01",
    "endDate": "2024-12-15",
    "includeCharts": true,
    "language": "en"
  }
}
```

### GET /reports/:id
Get generated report details.

### GET /reports/:id/download
Download generated report file.

## Attendance Endpoints

### GET /students/:id/attendance
Get student attendance records.

### POST /attendance/bulk
Bulk attendance entry.

### GET /attendance/summary
Get attendance summary by class/grade.

## Grades Endpoints

### GET /students/:id/grades
Get student grade records.

### POST /grades
Enter new grades.

### PUT /grades/:id
Update grade entry.

## Schools Endpoints

### GET /schools
Get all schools.

### GET /schools/:id
Get school details.

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-12-15T10:30:00Z"
}
```

## Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `429`: Rate Limited
- `500`: Internal Server Error

## Rate Limiting

- **Standard Users**: 1000 requests/hour
- **Premium Users**: 5000 requests/hour
- **Admin Users**: 10000 requests/hour

## Pagination

All list endpoints support pagination:

```json
{
  "pagination": {
    "currentPage": 1,
    "totalPages": 10,
    "totalItems": 200,
    "itemsPerPage": 20,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Filtering and Sorting

Query parameters for filtering:
- `filter[field]=value`
- `sort=field:asc|desc`
- `include=relatedResource`

## Webhooks

Available webhook events:
- `student.created`
- `student.updated`
- `grade.entered`
- `report.generated`
- `swot.completed`

## Security

- JWT tokens expire in 24 hours
- Refresh tokens expire in 30 days
- All sensitive data encrypted at rest
- HTTPS required for all endpoints
- CORS configured for frontend domains
- Input validation and sanitization
- SQL injection protection
- Rate limiting per user/IP

## Implementation Priority

### Phase 4.1 - Core APIs (Week 1-2)
1. Authentication endpoints
2. Student management CRUD
3. Basic analytics
4. Error handling and validation

### Phase 4.2 - Advanced Features (Week 3-4)
1. SWOT analysis endpoints
2. Report generation
3. Attendance and grades
4. Advanced analytics

### Phase 4.3 - Optimization (Week 5-6)
1. Performance optimization
2. Caching implementation
3. Real-time features (WebSocket)
4. Comprehensive testing
