<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="VidyaMitra - Student SWOT Analysis Platform for Indian Schools" />
    <meta name="keywords" content="education, SWOT analysis, Indian schools, student management, CBSE, ICSE, state boards" />
    <meta name="author" content="VidyaMitra Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://vidyamitra.vercel.app/" />
    <meta property="og:title" content="VidyaMitra - Student SWOT Analysis Platform" />
    <meta property="og:description" content="Comprehensive SWOT analysis platform for Indian schools with AI-powered insights" />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://vidyamitra.vercel.app/" />
    <meta property="twitter:title" content="VidyaMitra - Student SWOT Analysis Platform" />
    <meta property="twitter:description" content="Comprehensive SWOT analysis platform for Indian schools with AI-powered insights" />
    <meta property="twitter:image" content="/og-image.png" />

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts for better Indic script support -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,400&family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,400&display=swap" rel="stylesheet" />
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    
    <title>VidyaMitra - Student SWOT Analysis Platform</title>
    
    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#2E5BA8" />
    
    <!-- PWA manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Apple touch icon -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    
    <!-- Loading styles to prevent FOUC (Flash of Unstyled Content) -->
    <style>
      #root {
        min-height: 100vh;
      }
      
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #2E5BA8 0%, #FF9933 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-family: 'Noto Sans', 'Roboto', Arial, sans-serif;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      .loading-subtext {
        font-size: 14px;
        opacity: 0.8;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-CdYvGdFC.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-CeOqOr8o.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-misc-NFmYzBmQ.js">
    <link rel="modulepreload" crossorigin href="/assets/mui-core-BBO2DoRL.js">
    <link rel="modulepreload" crossorigin href="/assets/routing-B6PnZiBG.js">
    <link rel="modulepreload" crossorigin href="/assets/i18n-DWU17bW_.js">
    <link rel="modulepreload" crossorigin href="/assets/animation-BJm6nf7i.js">
    <link rel="modulepreload" crossorigin href="/assets/mui-icons-BXhTkfAe.js">
    <link rel="modulepreload" crossorigin href="/assets/charts-chartjs-Dl1vZNhv.js">
    <link rel="stylesheet" crossorigin href="/assets/index-CGGAVQXx.css">
  </head>
  <body>
    <div id="root">
      <!-- Loading screen shown while React app loads -->
      <div class="loading-screen" id="loading-screen">
        <div class="loading-spinner"></div>
        <div class="loading-text">VidyaMitra</div>
        <div class="loading-subtext">Loading your education platform...</div>
      </div>
    </div>
    
    <!-- Hide loading screen once React takes over -->
    <script>
      window.addEventListener('DOMContentLoaded', () => {
        // Hide loading screen after a short delay to ensure React has mounted
        setTimeout(() => {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.3s ease-out';
            setTimeout(() => {
              loadingScreen.remove();
            }, 300);
          }
        }, 1000);
      });
    </script>
    
  </body>
</html>
