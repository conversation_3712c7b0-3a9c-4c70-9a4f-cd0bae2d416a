/**
 * VidyaMitra Platform - Comprehensive Mock Data System
 * 
 * Complete mock data for Indian educational context including:
 * - Extended student profiles with authentic Indian names
 * - Multiple schools in Hyderabad/Telangana region
 * - Teacher and parent profiles
 * - Academic performance data aligned with Indian boards
 * - Cultural and regional context
 */

import { studentProfiles, schools, teachers, parents } from './studentData.js';

// Extended student profiles with more authentic Indian names
export const extendedStudentProfiles = [
  ...studentProfiles,
  {
    id: 'STU006',
    name: '<PERSON><PERSON><PERSON>',
    grade: '9th',
    section: 'B',
    board: 'state',
    rollNumber: 'KL2024006',
    academicLevel: 82,
    schoolId: 'SCH002',
    region: 'Kerala',
    parentContact: '+91 9876543215',
    email: '<EMAIL>',
    dateOfBirth: '2010-09-18',
    address: 'House 34, Marine Drive, Kochi, Kerala - 682031',
    fatherName: 'Mr. <PERSON><PERSON>',
    motherName: 'Mrs. <PERSON>',
    guardian: 'Mother',
    bloodGroup: 'B+',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Malayalam',
    languages: ['Malayalam', 'English', 'Hindi', 'Tamil'],
    previousSchool: 'Kendriya Vidyalaya',
    admissionDate: '2023-06-12',
    studentPhoto: '/uploads/student-photos/sirisha-nair.jpg'
  },
  {
    id: 'STU007',
    name: 'Priya Agarwal',
    grade: '11th',
    section: 'B',
    board: 'cbse',
    rollNumber: 'CBSE2024007',
    academicLevel: 89,
    schoolId: 'SCH001',
    region: 'Rajasthan',
    parentContact: '+91 9876543216',
    email: '<EMAIL>',
    dateOfBirth: '2008-12-03',
    address: 'Villa 78, Civil Lines, Jaipur, Rajasthan - 302006',
    fatherName: 'Mr. Anil Agarwal',
    motherName: 'Mrs. Sunita Agarwal',
    guardian: 'Father',
    bloodGroup: 'A-',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Hindi',
    languages: ['Hindi', 'English', 'Rajasthani'],
    previousSchool: 'Maharaja Sawai Man Singh Vidyalaya',
    admissionDate: '2021-06-18',
    studentPhoto: '/uploads/student-photos/priya-agarwal.jpg'
  },
  {
    id: 'STU008',
    name: 'Arjun Singh',
    grade: '10th',
    section: 'A',
    board: 'cbse',
    rollNumber: 'CBSE2024008',
    academicLevel: 76,
    schoolId: 'SCH001',
    region: 'Punjab',
    parentContact: '+91 9876543217',
    email: '<EMAIL>',
    dateOfBirth: '2009-04-25',
    address: 'SCO 45, Sector 17, Chandigarh - 160017',
    fatherName: 'Mr. Harpreet Singh',
    motherName: 'Mrs. Simran Kaur',
    guardian: 'Father',
    bloodGroup: 'O+',
    religion: 'Sikh',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Punjabi',
    languages: ['Punjabi', 'Hindi', 'English'],
    previousSchool: 'DAV Public School',
    admissionDate: '2022-06-20',
    studentPhoto: '/uploads/student-photos/arjun-singh.jpg'
  },
  {
    id: 'STU009',
    name: 'Kavya Menon',
    grade: '12th',
    section: 'C',
    board: 'icse',
    rollNumber: 'ICSE2024009',
    academicLevel: 91,
    schoolId: 'SCH003',
    region: 'Kerala',
    parentContact: '+91 9876543218',
    email: '<EMAIL>',
    dateOfBirth: '2007-08-14',
    address: 'TC 12/456, Vazhuthacaud, Thiruvananthapuram, Kerala - 695014',
    fatherName: 'Mr. Rajeev Menon',
    motherName: 'Mrs. Divya Menon',
    guardian: 'Father',
    bloodGroup: 'AB-',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Malayalam',
    languages: ['Malayalam', 'English', 'Hindi', 'French'],
    previousSchool: 'Loyola School',
    admissionDate: '2020-06-10',
    studentPhoto: '/uploads/student-photos/kavya-menon.jpg'
  },
  {
    id: 'STU010',
    name: 'Rohit Gupta',
    grade: '9th',
    section: 'A',
    board: 'state',
    rollNumber: 'UP2024010',
    academicLevel: 84,
    schoolId: 'SCH002',
    region: 'Uttar Pradesh',
    parentContact: '+91 9876543219',
    email: '<EMAIL>',
    dateOfBirth: '2010-02-28',
    address: '123/45, Gomti Nagar, Lucknow, Uttar Pradesh - 226010',
    fatherName: 'Mr. Vinod Gupta',
    motherName: 'Mrs. Rekha Gupta',
    guardian: 'Father',
    bloodGroup: 'B-',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Hindi',
    languages: ['Hindi', 'English', 'Urdu'],
    previousSchool: 'City Montessori School',
    admissionDate: '2023-06-15',
    studentPhoto: '/uploads/student-photos/rohit-gupta.jpg'
  },
  {
    id: 'STU011',
    name: 'Ananya Krishnan',
    grade: '11th',
    section: 'A',
    board: 'cbse',
    rollNumber: 'CBSE2024011',
    academicLevel: 87,
    schoolId: 'SCH001',
    region: 'Tamil Nadu',
    parentContact: '+91 9876543220',
    email: '<EMAIL>',
    dateOfBirth: '2008-06-15',
    address: 'Flat 23, T. Nagar, Chennai, Tamil Nadu - 600017',
    fatherName: 'Mr. Krishnan Iyer',
    motherName: 'Mrs. Priya Krishnan',
    guardian: 'Father',
    bloodGroup: 'A+',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Tamil',
    languages: ['Tamil', 'English', 'Hindi', 'Sanskrit'],
    previousSchool: 'Padma Seshadri Bala Bhavan',
    admissionDate: '2021-06-22',
    studentPhoto: '/uploads/student-photos/ananya-krishnan.jpg'
  },
  {
    id: 'STU012',
    name: 'Vikram Joshi',
    grade: '10th',
    section: 'B',
    board: 'icse',
    rollNumber: 'ICSE2024012',
    academicLevel: 79,
    schoolId: 'SCH003',
    region: 'Maharashtra',
    parentContact: '+91 9876543221',
    email: '<EMAIL>',
    dateOfBirth: '2009-09-12',
    address: 'Bungalow 56, Pune, Maharashtra - 411001',
    fatherName: 'Mr. Sunil Joshi',
    motherName: 'Mrs. Manjusha Joshi',
    guardian: 'Father',
    bloodGroup: 'O+',
    religion: 'Hindu',
    caste: 'General',
    nationality: 'Indian',
    motherTongue: 'Marathi',
    languages: ['Marathi', 'Hindi', 'English'],
    previousSchool: 'Fergusson College',
    admissionDate: '2022-06-18',
    studentPhoto: '/uploads/student-photos/vikram-joshi.jpg'
  }
];

// Academic calendar events for Indian schools
export const academicCalendar = [
  {
    id: 'AC001',
    title: 'Academic Year Begins',
    date: '2024-06-01',
    type: 'academic',
    description: 'New academic session starts for all classes'
  },
  {
    id: 'AC002',
    title: 'Independence Day Celebration',
    date: '2024-08-15',
    type: 'national',
    description: 'National holiday with flag hoisting ceremony'
  },
  {
    id: 'AC003',
    title: 'Ganesh Chaturthi',
    date: '2024-09-07',
    type: 'cultural',
    description: 'Cultural celebration with eco-friendly practices'
  },
  {
    id: 'AC004',
    title: 'First Term Examinations',
    date: '2024-09-15',
    type: 'examination',
    description: 'First term examinations for all classes'
  },
  {
    id: 'AC005',
    title: 'Dussehra Holidays',
    date: '2024-10-12',
    type: 'holiday',
    description: 'Festival holidays for Dussehra celebration'
  },
  {
    id: 'AC006',
    title: 'Diwali Celebration',
    date: '2024-11-01',
    type: 'cultural',
    description: 'Festival of lights celebration in school'
  },
  {
    id: 'AC007',
    title: 'Annual Sports Day',
    date: '2024-11-15',
    type: 'sports',
    description: 'Inter-house sports competition and cultural events'
  },
  {
    id: 'AC008',
    title: 'Second Term Examinations',
    date: '2024-12-10',
    type: 'examination',
    description: 'Second term examinations for all classes'
  },
  {
    id: 'AC009',
    title: 'Winter Break',
    date: '2024-12-25',
    type: 'holiday',
    description: 'Winter vacation for students and staff'
  },
  {
    id: 'AC010',
    title: 'Republic Day Celebration',
    date: '2025-01-26',
    type: 'national',
    description: 'National holiday with parade and cultural programs'
  },
  {
    id: 'AC011',
    title: 'Annual Day Celebration',
    date: '2025-02-14',
    type: 'cultural',
    description: 'Annual cultural program showcasing student talents'
  },
  {
    id: 'AC012',
    title: 'Holi Celebration',
    date: '2025-03-14',
    type: 'cultural',
    description: 'Festival of colors celebration'
  },
  {
    id: 'AC013',
    title: 'Final Examinations',
    date: '2025-03-20',
    type: 'examination',
    description: 'Annual final examinations for all classes'
  }
];

// Export all data
export {
  schools,
  teachers,
  parents,
  extendedStudentProfiles as studentProfiles
};

export default {
  schools,
  teachers,
  parents,
  studentProfiles: extendedStudentProfiles,
  academicCalendar
};
