{"version": 3, "file": "GradeEntry-C2wSA142.js", "sources": ["../../src/components/Grades/GradeEntry.jsx"], "sourcesContent": ["/**\n * <PERSON>idyaMitra Platform - Grade Entry Component\n * \n * Grade entry interface for teachers with Indian grading system support\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  Button,\n  Avatar,\n  TextField,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Stack,\n  Alert,\n  Chip,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Save,\n  Print,\n  Download,\n  Grade,\n  Assessment,\n  TrendingUp,\n  TrendingDown,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\n\n// Sample grade data with Indian student names\nconst gradeData = [\n  { id: 1, name: '<PERSON><PERSON>', rollNumber: 1, previousGrade: 'A1', currentMarks: 95, maxMarks: 100 },\n  { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>', rollNumber: 2, previousGrade: 'A2', currentMarks: 88, maxMarks: 100 },\n  { id: 3, name: '<PERSON><PERSON><PERSON>', rollNumber: 3, previousGrade: 'B1', currentMarks: 82, maxMarks: 100 },\n  { id: 4, name: '<PERSON>', rollNumber: 4, previousGrade: 'A1', currentMarks: 92, maxMarks: 100 },\n  { id: 5, name: 'Ankitha Patel', rollNumber: 5, previousGrade: 'A2', currentMarks: 85, maxMarks: 100 },\n  { id: 6, name: 'Sirisha Nair', rollNumber: 6, previousGrade: 'A1', currentMarks: 96, maxMarks: 100 },\n  { id: 7, name: 'Priya Agarwal', rollNumber: 7, previousGrade: 'B2', currentMarks: 78, maxMarks: 100 },\n];\n\n// Indian grading system\nconst getGradeFromMarks = (marks, maxMarks) => {\n  const percentage = (marks / maxMarks) * 100;\n  if (percentage >= 91) return 'A1';\n  if (percentage >= 81) return 'A2';\n  if (percentage >= 71) return 'B1';\n  if (percentage >= 61) return 'B2';\n  if (percentage >= 51) return 'C1';\n  if (percentage >= 41) return 'C2';\n  if (percentage >= 33) return 'D';\n  return 'E';\n};\n\nconst getGradeColor = (grade) => {\n  switch (grade) {\n    case 'A1':\n    case 'A2':\n      return 'success';\n    case 'B1':\n    case 'B2':\n      return 'info';\n    case 'C1':\n    case 'C2':\n      return 'warning';\n    case 'D':\n    case 'E':\n      return 'error';\n    default:\n      return 'default';\n  }\n};\n\nconst GradeEntry = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const [selectedClass, setSelectedClass] = useState('10-A');\n  const [selectedSubject, setSelectedSubject] = useState('Mathematics');\n  const [selectedTest, setSelectedTest] = useState('Unit Test 1');\n  const [grades, setGrades] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    // Initialize grades state\n    const initialGrades = {};\n    gradeData.forEach(student => {\n      initialGrades[student.id] = {\n        marks: student.currentMarks,\n        maxMarks: student.maxMarks,\n      };\n    });\n    setGrades(initialGrades);\n  }, []);\n\n  const handleMarksChange = (studentId, marks) => {\n    setGrades(prev => ({\n      ...prev,\n      [studentId]: {\n        ...prev[studentId],\n        marks: parseInt(marks) || 0,\n      }\n    }));\n  };\n\n  const handleMaxMarksChange = (studentId, maxMarks) => {\n    setGrades(prev => ({\n      ...prev,\n      [studentId]: {\n        ...prev[studentId],\n        maxMarks: parseInt(maxMarks) || 100,\n      }\n    }));\n  };\n\n  const handleSaveGrades = async () => {\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      alert('Grades saved successfully!');\n    } catch (error) {\n      console.error('Error saving grades:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateStatistics = () => {\n    const allMarks = Object.values(grades).map(g => (g.marks / g.maxMarks) * 100);\n    const average = allMarks.reduce((sum, mark) => sum + mark, 0) / allMarks.length;\n    const highest = Math.max(...allMarks);\n    const lowest = Math.min(...allMarks);\n    \n    return {\n      average: average.toFixed(1),\n      highest: highest.toFixed(1),\n      lowest: lowest.toFixed(1),\n      totalStudents: allMarks.length,\n    };\n  };\n\n  const stats = calculateStatistics();\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Grade Entry\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Enter and manage student grades with Indian grading system\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Controls */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Grid container spacing={3} alignItems=\"center\">\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Class</InputLabel>\n                <Select\n                  value={selectedClass}\n                  onChange={(e) => setSelectedClass(e.target.value)}\n                  label=\"Class\"\n                >\n                  <MenuItem value=\"9-A\">Class 9-A</MenuItem>\n                  <MenuItem value=\"9-B\">Class 9-B</MenuItem>\n                  <MenuItem value=\"10-A\">Class 10-A</MenuItem>\n                  <MenuItem value=\"10-B\">Class 10-B</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Subject</InputLabel>\n                <Select\n                  value={selectedSubject}\n                  onChange={(e) => setSelectedSubject(e.target.value)}\n                  label=\"Subject\"\n                >\n                  <MenuItem value=\"Mathematics\">Mathematics</MenuItem>\n                  <MenuItem value=\"Science\">Science</MenuItem>\n                  <MenuItem value=\"English\">English</MenuItem>\n                  <MenuItem value=\"Hindi\">Hindi</MenuItem>\n                  <MenuItem value=\"Social Studies\">Social Studies</MenuItem>\n                  <MenuItem value=\"Telugu\">Telugu</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Test/Assessment</InputLabel>\n                <Select\n                  value={selectedTest}\n                  onChange={(e) => setSelectedTest(e.target.value)}\n                  label=\"Test/Assessment\"\n                >\n                  <MenuItem value=\"Unit Test 1\">Unit Test 1</MenuItem>\n                  <MenuItem value=\"Unit Test 2\">Unit Test 2</MenuItem>\n                  <MenuItem value=\"Mid Term\">Mid Term Exam</MenuItem>\n                  <MenuItem value=\"Final Term\">Final Term Exam</MenuItem>\n                  <MenuItem value=\"Assignment\">Assignment</MenuItem>\n                  <MenuItem value=\"Project\">Project Work</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <Stack direction=\"row\" spacing={2}>\n                <Button\n                  variant=\"contained\"\n                  startIcon={<Save />}\n                  onClick={handleSaveGrades}\n                  loading={loading}\n                  sx={{\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  Save Grades\n                </Button>\n              </Stack>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Statistics */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {stats.average}%\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Class Average\n                  </Typography>\n                </Box>\n                <Assessment sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {stats.highest}%\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Highest Score\n                  </Typography>\n                </Box>\n                <TrendingUp sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {stats.lowest}%\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Lowest Score\n                  </Typography>\n                </Box>\n                <TrendingDown sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {stats.totalStudents}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Total Students\n                  </Typography>\n                </Box>\n                <Grade sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Grade Entry Table */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Grade Entry - {selectedClass} | {selectedSubject} | {selectedTest}\n          </Typography>\n\n          <TableContainer component={Paper} variant=\"outlined\">\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Roll No.</TableCell>\n                  <TableCell>Student Name</TableCell>\n                  <TableCell>Previous Grade</TableCell>\n                  <TableCell>Marks Obtained</TableCell>\n                  <TableCell>Max Marks</TableCell>\n                  <TableCell>Percentage</TableCell>\n                  <TableCell>Current Grade</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {gradeData.map((student) => {\n                  const currentGrade = grades[student.id];\n                  const percentage = currentGrade ? (currentGrade.marks / currentGrade.maxMarks) * 100 : 0;\n                  const grade = currentGrade ? getGradeFromMarks(currentGrade.marks, currentGrade.maxMarks) : 'E';\n                  \n                  return (\n                    <TableRow key={student.id} hover>\n                      <TableCell>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                          {student.rollNumber}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                          <Avatar sx={{ width: 32, height: 32 }}>\n                            {student.name.charAt(0)}\n                          </Avatar>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                            {student.name}\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={student.previousGrade}\n                          color={getGradeColor(student.previousGrade)}\n                          size=\"small\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <TextField\n                          size=\"small\"\n                          type=\"number\"\n                          value={currentGrade?.marks || ''}\n                          onChange={(e) => handleMarksChange(student.id, e.target.value)}\n                          sx={{ width: 80 }}\n                          inputProps={{ min: 0, max: currentGrade?.maxMarks || 100 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <TextField\n                          size=\"small\"\n                          type=\"number\"\n                          value={currentGrade?.maxMarks || 100}\n                          onChange={(e) => handleMaxMarksChange(student.id, e.target.value)}\n                          sx={{ width: 80 }}\n                          inputProps={{ min: 1 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {percentage.toFixed(1)}%\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={grade}\n                          color={getGradeColor(grade)}\n                          size=\"small\"\n                        />\n                      </TableCell>\n                    </TableRow>\n                  );\n                })}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default GradeEntry;\n"], "names": ["gradeData", "id", "name", "rollNumber", "previousGrade", "currentMarks", "maxMarks", "getGradeColor", "grade", "GradeEntry", "theme", "useTheme", "useNavigate", "selectedClass", "setSelectedClass", "useState", "selectedSubject", "setSelectedSubject", "selectedTest", "setSelectedTest", "grades", "setGrades", "loading", "setLoading", "useEffect", "initialGrades", "for<PERSON>ach", "student", "marks", "stats", "allMarks", "Object", "values", "map", "g", "average", "reduce", "sum", "mark", "length", "highest", "Math", "max", "lowest", "min", "toFixed", "totalStudents", "calculateStatistics", "Box", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "jsxRuntimeExports", "jsx", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "mb", "Typography", "variant", "fontWeight", "background", "palette", "primary", "main", "secondary", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "color", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "container", "spacing", "alignItems", "item", "xs", "md", "jsxs", "FormControl", "fullWidth", "InputLabel", "Select", "value", "onChange", "e", "target", "label", "MenuItem", "<PERSON><PERSON>", "direction", "<PERSON><PERSON>", "startIcon", "Save", "onClick", "async", "Promise", "resolve", "setTimeout", "alert", "error", "dark", "display", "justifyContent", "Assessment", "fontSize", "success", "TrendingUp", "warning", "TrendingDown", "info", "Grade", "TableContainer", "component", "Paper", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "currentGrade", "percentage", "getGradeFromMarks", "hover", "gap", "Avatar", "width", "height", "char<PERSON>t", "Chip", "size", "TextField", "type", "handleMarksChange", "studentId", "prev", "parseInt", "inputProps", "handleMaxMarksChange"], "mappings": "gbA8CA,MAAMA,EAAY,CAChB,CAAEC,GAAI,EAAGC,KAAM,oBAAqBC,WAAY,EAAGC,cAAe,KAAMC,aAAc,GAAIC,SAAU,KACpG,CAAEL,GAAI,EAAGC,KAAM,oBAAqBC,WAAY,EAAGC,cAAe,KAAMC,aAAc,GAAIC,SAAU,KACpG,CAAEL,GAAI,EAAGC,KAAM,eAAgBC,WAAY,EAAGC,cAAe,KAAMC,aAAc,GAAIC,SAAU,KAC/F,CAAEL,GAAI,EAAGC,KAAM,mBAAoBC,WAAY,EAAGC,cAAe,KAAMC,aAAc,GAAIC,SAAU,KACnG,CAAEL,GAAI,EAAGC,KAAM,gBAAiBC,WAAY,EAAGC,cAAe,KAAMC,aAAc,GAAIC,SAAU,KAChG,CAAEL,GAAI,EAAGC,KAAM,eAAgBC,WAAY,EAAGC,cAAe,KAAMC,aAAc,GAAIC,SAAU,KAC/F,CAAEL,GAAI,EAAGC,KAAM,gBAAiBC,WAAY,EAAGC,cAAe,KAAMC,aAAc,GAAIC,SAAU,MAgB5FC,EAAiBC,IACrB,OAAQA,GACN,IAAK,KACL,IAAK,KACI,MAAA,UACT,IAAK,KACL,IAAK,KACI,MAAA,OACT,IAAK,KACL,IAAK,KACI,MAAA,UACT,IAAK,IACL,IAAK,IACI,MAAA,QACT,QACS,MAAA,UAAA,EAIPC,EAAa,KACjB,MAAMC,EAAQC,IACeC,IAC7B,MAAOC,EAAeC,GAAoBC,EAAAA,SAAS,SAC5CC,EAAiBC,GAAsBF,EAAAA,SAAS,gBAChDG,EAAcC,GAAmBJ,EAAAA,SAAS,gBAC1CK,EAAQC,GAAaN,EAAAA,SAAS,CAAA,IAC9BO,EAASC,GAAcR,EAAAA,UAAS,GAEvCS,EAAAA,WAAU,KAER,MAAMC,EAAgB,CAAC,EACbzB,EAAA0B,SAAmBC,IACbF,EAAAE,EAAQ1B,IAAM,CAC1B2B,MAAOD,EAAQtB,aACfC,SAAUqB,EAAQrB,SACpB,IAEFe,EAAUI,EAAa,GACtB,IAEG,MA+CAI,EAdsB,MACpB,MAAAC,EAAWC,OAAOC,OAAOZ,GAAQa,KAAIC,GAAMA,EAAEN,MAAQM,EAAE5B,SAAY,MACnE6B,EAAUL,EAASM,QAAO,CAACC,EAAKC,IAASD,EAAMC,GAAM,GAAKR,EAASS,OACnEC,EAAUC,KAAKC,OAAOZ,GACtBa,EAASF,KAAKG,OAAOd,GAEpB,MAAA,CACLK,QAASA,EAAQU,QAAQ,GACzBL,QAASA,EAAQK,QAAQ,GACzBF,OAAQA,EAAOE,QAAQ,GACvBC,cAAehB,EAASS,OAC1B,EAGYQ,GAGZ,cAACC,EAAI,CAAAC,GAAI,CAAEC,SAAU,KAAMC,GAAI,OAAQC,EAAG,GAExCC,SAAA,CAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,GAAO,IAC9BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAExBV,gBAACL,EAAI,CAAAC,GAAI,CAAEe,GAAI,GACbX,SAAA,CAAAC,EAAAC,IAACU,EAAA,CACCC,QAAQ,KACRjB,GAAI,CACFkB,WAAY,IACZH,GAAI,EACJI,WAAY,2BAA2B1D,EAAM2D,QAAQC,QAAQC,YAAY7D,EAAM2D,QAAQG,UAAUD,aACjGE,qBAAsB,OACtBC,oBAAqB,cACrBC,eAAgB,QAEnBtB,SAAA,sBAGAY,EAAW,CAAAC,QAAQ,QAAQU,MAAM,iBAAiBvB,SAEnD,0EAKHwB,EAAK,CAAA5B,GAAI,CAAEe,GAAI,GACdX,SAAAC,EAAAC,IAACuB,EACC,CAAAzB,gBAAC0B,GAAKC,WAAS,EAACC,QAAS,EAAGC,WAAW,SACrC7B,SAAA,GAACE,IAAAwB,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAiC,EAAAA,KAACC,EAAY,CAAAC,WAAS,EACpBnC,SAAA,GAAAE,IAACkC,GAAWpC,SAAK,UACjBC,EAAAgC,KAACI,EAAA,CACCC,MAAO9E,EACP+E,SAAWC,GAAM/E,EAAiB+E,EAAEC,OAAOH,OAC3CI,MAAM,QAEN1C,SAAA,CAACE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,MAAMtC,SAAS,cAC9BE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,MAAMtC,SAAS,cAC9BE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,OAAOtC,SAAU,eAChCE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,OAAOtC,SAAU,yBAIvCE,IAACwB,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAiC,EAAAA,KAACC,EAAY,CAAAC,WAAS,EACpBnC,SAAA,GAAAE,IAACkC,GAAWpC,SAAO,YACnBC,EAAAgC,KAACI,EAAA,CACCC,MAAO3E,EACP4E,SAAWC,GAAM5E,EAAmB4E,EAAEC,OAAOH,OAC7CI,MAAM,UAEN1C,SAAA,CAACE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,cAActC,SAAW,gBACxCE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,UAAUtC,SAAO,YAChCE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,UAAUtC,SAAO,YAChCE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,QAAQtC,SAAK,UAC5BE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,iBAAiBtC,SAAc,mBAC9CE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,SAAStC,SAAM,qBAIrCE,IAACwB,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAiC,EAAAA,KAACC,EAAY,CAAAC,WAAS,EACpBnC,SAAA,GAAAE,IAACkC,GAAWpC,SAAe,oBAC3BC,EAAAgC,KAACI,EAAA,CACCC,MAAOzE,EACP0E,SAAWC,GAAM1E,EAAgB0E,EAAEC,OAAOH,OAC1CI,MAAM,kBAEN1C,SAAA,CAACE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,cAActC,SAAW,gBACxCE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,cAActC,SAAW,gBACxCE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,WAAWtC,SAAa,kBACvCE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,aAAatC,SAAe,oBAC3CE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,aAAatC,SAAU,eACtCE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,UAAUtC,SAAY,yBAI3CE,EAAAA,IAAAwB,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAACC,EAAAC,IAAA0C,EAAA,CAAMC,UAAU,MAAMjB,QAAS,EAC9B5B,SAAAC,EAAAC,IAAC4C,EAAA,CACCjC,QAAQ,YACRkC,gBAAYC,EAAK,IACjBC,QAnHSC,UACvBhF,GAAW,GACP,UAEI,IAAIiF,SAAQC,GAAWC,WAAWD,EAAS,OACjDE,MAAM,oCACCC,GACoC,CAC3C,QACArF,GAAW,EAAK,GA2GJD,UACA2B,GAAI,CACFmB,WAAY,2BAA2B1D,EAAM2D,QAAQC,QAAQC,YAAY7D,EAAM2D,QAAQG,UAAUD,cAEpGlB,SAAA,2BAUXiC,EAAAA,KAACP,EAAK,CAAAC,WAAS,EAACC,QAAS,EAAGhC,GAAI,CAAEe,GAAI,GACpCX,SAAA,CAAAE,MAACwB,GAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAC,EAAAC,IAACsB,EAAA,CACC5B,GAAI,CACFmB,WAAY,2BAA2B1D,EAAM2D,QAAQC,QAAQC,YAAY7D,EAAM2D,QAAQC,QAAQuC,aAC/FjC,MAAO,SAGTvB,SAACC,EAAAC,IAAAuB,EAAA,CACCzB,WAACiC,KAAAtC,EAAA,CAAIC,GAAI,CAAE6D,QAAS,OAAQ5B,WAAY,SAAU6B,eAAgB,iBAChE1D,SAAA,QAACL,EACC,CAAAK,SAAA,CAAAiC,OAACrB,GAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAA,CAAMxB,EAAAM,QAAQ,OAEjBoB,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,qBAEFE,MAACyD,GAAW/D,GAAI,CAAEgE,SAAU,GAAItD,QAAS,qBAKhDoB,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAC,EAAAC,IAACsB,EAAA,CACC5B,GAAI,CACFmB,WAAY,2BAA2B1D,EAAM2D,QAAQ6C,QAAQ3C,YAAY7D,EAAM2D,QAAQ6C,QAAQL,aAC/FjC,MAAO,SAGTvB,SAACC,EAAAC,IAAAuB,EAAA,CACCzB,WAACiC,KAAAtC,EAAA,CAAIC,GAAI,CAAE6D,QAAS,OAAQ5B,WAAY,SAAU6B,eAAgB,iBAChE1D,SAAA,QAACL,EACC,CAAAK,SAAA,CAAAiC,OAACrB,GAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAA,CAAMxB,EAAAW,QAAQ,OAEjBe,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,qBAEFE,MAAC4D,GAAWlE,GAAI,CAAEgE,SAAU,GAAItD,QAAS,qBAKhDoB,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAC,EAAAC,IAACsB,EAAA,CACC5B,GAAI,CACFmB,WAAY,2BAA2B1D,EAAM2D,QAAQ+C,QAAQ7C,YAAY7D,EAAM2D,QAAQ+C,QAAQP,aAC/FjC,MAAO,SAGTvB,SAACC,EAAAC,IAAAuB,EAAA,CACCzB,WAACiC,KAAAtC,EAAA,CAAIC,GAAI,CAAE6D,QAAS,OAAQ5B,WAAY,SAAU6B,eAAgB,iBAChE1D,SAAA,QAACL,EACC,CAAAK,SAAA,CAAAiC,OAACrB,GAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAA,CAAMxB,EAAAc,OAAO,OAEhBY,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,oBAEFE,MAAC8D,GAAapE,GAAI,CAAEgE,SAAU,GAAItD,QAAS,qBAKlDoB,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAC,EAAAC,IAACsB,EAAA,CACC5B,GAAI,CACFmB,WAAY,2BAA2B1D,EAAM2D,QAAQiD,KAAK/C,YAAY7D,EAAM2D,QAAQiD,KAAKT,aACzFjC,MAAO,SAGTvB,SAACC,EAAAC,IAAAuB,EAAA,CACCzB,WAACiC,KAAAtC,EAAA,CAAIC,GAAI,CAAE6D,QAAS,OAAQ5B,WAAY,SAAU6B,eAAgB,iBAChE1D,SAAA,QAACL,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAAxB,EAAMiB,gBAETS,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,sBAEFE,MAACgE,GAAMtE,GAAI,CAAEgE,SAAU,GAAItD,QAAS,kBAQ9CJ,EAAAA,IAACsB,EACC,CAAAxB,SAAAiC,EAAAA,KAACR,EACC,CAAAzB,SAAA,CAACiC,EAAAA,KAAArB,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKS,MAAO,gBAAkBvB,SAAA,CAAA,iBAC/DxC,EAAc,MAAIG,EAAgB,MAAIE,WAGtDsG,EAAe,CAAAC,UAAWC,EAAOxD,QAAQ,WACxCb,gBAACsE,EACC,CAAAtE,SAAA,CAACE,EAAAA,IAAAqE,EAAA,CACCvE,gBAACwE,EACC,CAAAxE,SAAA,GAAAE,IAACuE,GAAUzE,SAAQ,eACnBE,IAACuE,GAAUzE,SAAY,mBACvBE,IAACuE,GAAUzE,SAAc,qBACzBE,IAACuE,GAAUzE,SAAc,qBACzBE,IAACuE,GAAUzE,SAAS,gBACpBE,IAACuE,GAAUzE,SAAU,iBACrBE,IAACuE,GAAUzE,SAAa,uBAG3BE,EAAAA,IAAAwE,EAAA,CACE1E,SAAUrD,EAAAiC,KAAKN,IACR,MAAAqG,EAAe5G,EAAOO,EAAQ1B,IAC9BgI,EAAaD,EAAgBA,EAAapG,MAAQoG,EAAa1H,SAAY,IAAM,EACjFE,EAAQwH,EA7TN,EAACpG,EAAOtB,KAC1B,MAAA2H,EAAcrG,EAAQtB,EAAY,IACpC,OAAA2H,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,IACtB,GAAA,EAoTsCC,CAAkBF,EAAapG,MAAOoG,EAAa1H,UAAY,IAG1F,SAAAgF,KAACuC,EAA0B,CAAAM,OAAK,EAC9B9E,SAAA,CAAAE,EAAAA,IAACuE,EACC,CAAAzE,SAAAC,EAAAC,IAACU,EAAW,CAAAC,QAAQ,QAAQjB,GAAI,CAAEkB,WAAY,KAC3Cd,SAAQ1B,EAAAxB,iBAGZoD,IAAAuE,EAAA,CACCzE,SAACiC,EAAAA,KAAAtC,EAAA,CAAIC,GAAI,CAAE6D,QAAS,OAAQ5B,WAAY,SAAUkD,IAAK,GACrD/E,SAAA,GAAAE,IAAC8E,EAAO,CAAApF,GAAI,CAAEqF,MAAO,GAAIC,OAAQ,IAC9BlF,SAAQ1B,EAAAzB,KAAKsI,OAAO,KAEvBjF,EAAAA,IAACU,EAAW,CAAAC,QAAQ,QAAQjB,GAAI,CAAEkB,WAAY,KAC3Cd,SAAA1B,EAAQzB,kBAId4H,EACC,CAAAzE,SAAAC,EAAAC,IAACkF,EAAA,CACC1C,MAAOpE,EAAQvB,cACfwE,MAAOrE,EAAcoB,EAAQvB,eAC7BsI,KAAK,kBAGRZ,EACC,CAAAzE,SAAAC,EAAAC,IAACoF,EAAA,CACCD,KAAK,QACLE,KAAK,SACLjD,aAAOqC,WAAcpG,QAAS,GAC9BgE,SAAWC,IAAMgD,OAxSdC,EAwSgCnH,EAAQ1B,GAxS7B2B,EAwSiCiE,EAAEC,OAAOH,WAvS9EtE,GAAmB0H,IAAA,IACdA,EACHD,CAACA,GAAY,IACRC,EAAKD,GACRlH,MAAOoH,SAASpH,IAAU,OALN,IAACkH,EAAWlH,CAwS+C,EAC7DqB,GAAI,CAAEqF,MAAO,IACbW,WAAY,CAAErG,IAAK,EAAGF,KAAK,MAAAsF,OAAA,EAAAA,EAAc1H,WAAY,eAGxDwH,EACC,CAAAzE,SAAAC,EAAAC,IAACoF,EAAA,CACCD,KAAK,QACLE,KAAK,SACLjD,aAAOqC,WAAc1H,WAAY,IACjCsF,SAAWC,IAAMqD,OAxSXJ,EAwSgCnH,EAAQ1B,GAxS7BK,EAwSiCuF,EAAEC,OAAOH,WAvSjFtE,GAAmB0H,IAAA,IACdA,EACHD,CAACA,GAAY,IACRC,EAAKD,GACRxI,SAAU0I,SAAS1I,IAAa,SALT,IAACwI,EAAWxI,CAwS+C,EAChE2C,GAAI,CAAEqF,MAAO,IACbW,WAAY,CAAErG,IAAK,SAGvBW,IAACuE,EACC,CAAAzE,WAAAiC,KAACrB,EAAW,CAAAC,QAAQ,QAAQjB,GAAI,CAAEkB,WAAY,KAC3Cd,SAAA,CAAA4E,EAAWpF,QAAQ,GAAG,eAG1BiF,EACC,CAAAzE,SAAAC,EAAAC,IAACkF,EAAA,CACC1C,MAAOvF,EACPoE,MAAOrE,EAAcC,GACrBkI,KAAK,cApDI/G,EAAQ1B,GAuDvB,kBAQhB"}