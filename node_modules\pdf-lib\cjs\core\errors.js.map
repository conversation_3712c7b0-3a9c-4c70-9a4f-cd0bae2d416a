{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/core/errors.ts"], "names": [], "mappings": ";;;;AAEA,kCAA0C;AAE1C;IAA+C,qDAAK;IAClD,mCAAY,SAAiB,EAAE,UAAkB;QAAjD,iBAGC;QAFC,IAAM,GAAG,GAAG,YAAU,SAAS,SAAI,UAAU,uBAAoB,CAAC;QAClE,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,gCAAC;AAAD,CAAC,AALD,CAA+C,KAAK,GAKnD;AALY,8DAAyB;AAOtC;IAA6C,mDAAK;IAChD,iCAAY,SAAiB;QAA7B,iBAGC;QAFC,IAAM,GAAG,GAAG,sBAAoB,SAAS,oCAAiC,CAAC;QAC3E,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,8BAAC;AAAD,CAAC,AALD,CAA6C,KAAK,GAKjD;AALY,0DAAuB;AAOpC;IAA+C,qDAAK;IAClD,mCAAY,QAAqB,EAAE,MAAW;QAA9C,iBAYC;QAXC,IAAM,IAAI,GAAG,UAAC,CAAM,6BAAK,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,yCAAI,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,WAAW,0CAAE,IAAI,GAAA,CAAC;QAEzD,IAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC3C,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;YACpB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAErB,IAAM,GAAG,GACP,0BAAwB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,OAAI;aACtD,0BAAuB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAE,CAAA,CAAC;QAE1D,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,gCAAC;AAAD,CAAC,AAdD,CAA+C,KAAK,GAcnD;AAdY,8DAAyB;AAgBtC;IAA8C,oDAAK;IACjD,kCAAY,QAAgB;QAA5B,iBAGC;QAFC,IAAM,GAAG,GAAM,QAAQ,mCAAgC,CAAC;QACxD,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,+BAAC;AAAD,CAAC,AALD,CAA8C,KAAK,GAKlD;AALY,4DAAwB;AAOrC;IAAkC,wCAAK;IACrC,sBAAY,SAAiB,EAAE,UAAkB;QAAjD,iBAGC;QAFC,IAAM,GAAG,GAAG,iBAAe,SAAS,SAAI,UAAU,sBAAmB,CAAC;QACtE,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,mBAAC;AAAD,CAAC,AALD,CAAkC,KAAK,GAKtC;AALY,oCAAY;AAOzB;IAAyC,+CAAK;IAC5C,6BAAY,GAAe;QAA3B,iBAGC;QAFC,IAAM,GAAG,GAAG,0BAAwB,GAAG,MAAG,CAAC;QAC3C,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,0BAAC;AAAD,CAAC,AALD,CAAyC,KAAK,GAK7C;AALY,kDAAmB;AAOhC;IAAuD,6DAAK;IAC1D;QAAA,iBAGC;QAFC,IAAM,GAAG,GAAG,wCAAwC,CAAC;QACrD,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,wCAAC;AAAD,CAAC,AALD,CAAuD,KAAK,GAK3D;AALY,8EAAiC;AAO9C;IAAiD,uDAAK;IACpD,qCAAY,MAAW;;QAAvB,iBAIC;QAHC,IAAM,UAAU,qBAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,0CAAE,IAAI,mCAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,mCAAI,MAAM,CAAC;QACtE,IAAM,GAAG,GAAG,+BAA6B,UAAY,CAAC;QACtD,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,kCAAC;AAAD,CAAC,AAND,CAAiD,KAAK,GAMrD;AANY,kEAA2B;AAQxC;IAAyD,+DAAK;IAC5D;QAAA,iBAGC;QAFC,IAAM,GAAG,GAAG,8IAAgJ,CAAC;QAC7J,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,0CAAC;AAAD,CAAC,AALD,CAAyD,KAAK,GAK7D;AALY,kFAAmC;AAOhD;IAAiD,uDAAK;IACpD,qCAAY,IAAY;QAAxB,iBAGC;QAFC,IAAM,GAAG,GAAG,wCAAsC,IAAI,8DAA2D,CAAC;QAClH,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,kCAAC;AAAD,CAAC,AALD,CAAiD,KAAK,GAKrD;AALY,kEAA2B;AAOxC;IAA+C,qDAAK;IAClD,mCAAY,KAAa;QAAzB,iBAGC;QAFC,IAAM,GAAG,GAAG,4BAAyB,KAAK,oEAAgE,CAAC;QAC3G,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,gCAAC;AAAD,CAAC,AALD,CAA+C,KAAK,GAKnD;AALY,8DAAyB;AAOtC;IAA6C,mDAAK;IAChD,iCAAY,WAAmB,EAAE,KAAa;QAA9C,iBAGC;QAFC,IAAM,GAAG,GAAG,gDAA8C,WAAW,iCAA4B,KAAO,CAAC;QACzG,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,8BAAC;AAAD,CAAC,AALD,CAA6C,KAAK,GAKjD;AALY,0DAAuB;AAOpC;IAA0C,gDAAK;IAC7C,8BAAY,WAAmB,EAAE,SAAiB;QAAlD,iBAGC;QAFC,IAAM,GAAG,GAAG,eAAa,SAAS,wBAAmB,WAAW,yFAAsF,CAAC;QACvJ,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,2BAAC;AAAD,CAAC,AALD,CAA0C,KAAK,GAK9C;AALY,oDAAoB;AAOjC;IAA2C,iDAAK;IAC9C,+BAAY,KAAa,EAAE,GAAW,EAAE,GAAW;QAAnD,iBAGC;QAFC,IAAM,GAAG,GAAG,8BAA4B,GAAG,qBAAgB,GAAG,2BAAsB,KAAO,CAAC;QAC5F,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,4BAAC;AAAD,CAAC,AALD,CAA2C,KAAK,GAK/C;AALY,sDAAqB;AAOlC;IAAgD,sDAAK;IACnD;QAAA,iBAGC;QAFC,IAAM,GAAG,GAAG,sCAAsC,CAAC;QACnD,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,iCAAC;AAAD,CAAC,AALD,CAAgD,KAAK,GAKpD;AALY,gEAA0B;AAOvC;IAA2C,iDAAK;IAC9C;QAAA,iBAGC;QAFC,IAAM,GAAG,GAAG,6DAA6D,CAAC;QAC1E,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,4BAAC;AAAD,CAAC,AALD,CAA2C,KAAK,GAK/C;AALY,sDAAqB;AAOlC;IAAyC,+CAAK;IAC5C,6BAAY,SAAiB;QAA7B,iBAGC;QAFC,IAAM,GAAG,GAAG,wDAAsD,SAAW,CAAC;QAC9E,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,0BAAC;AAAD,CAAC,AALD,CAAyC,KAAK,GAK7C;AALY,kDAAmB;AAOhC;IAA4C,kDAAK;IAC/C,gCAAY,SAAiB;QAA7B,iBAGC;QAFC,IAAM,GAAG,GAAG,2CAAyC,SAAW,CAAC;QACjE,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,6BAAC;AAAD,CAAC,AALD,CAA4C,KAAK,GAKhD;AALY,wDAAsB;AAenC;IAAwC,8CAAK;IAC3C,4BAAY,GAAa,EAAE,KAAa;QAAxC,iBAKC;QAJC,IAAM,GAAG,GACP,yBAAyB;aACzB,WAAS,GAAG,CAAC,IAAI,aAAQ,GAAG,CAAC,MAAM,gBAAW,GAAG,CAAC,MAAM,aAAO,KAAK,OAAG,CAAA,CAAC;QAC1E,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,yBAAC;AAAD,CAAC,AAPD,CAAwC,KAAK,GAO5C;AAPY,gDAAkB;AAS/B;IAAqC,2CAAK;IACxC,yBAAY,GAAa,EAAE,OAAe;QAA1C,iBAKC;QAJC,IAAM,GAAG,GACP,+BAA+B;aAC/B,WAAS,GAAG,CAAC,IAAI,aAAQ,GAAG,CAAC,MAAM,gBAAW,GAAG,CAAC,MAAM,WAAM,OAAS,CAAA,CAAC;QAC1E,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,sBAAC;AAAD,CAAC,AAPD,CAAqC,KAAK,GAOzC;AAPY,0CAAe;AAS5B;IAA4C,kDAAe;IACzD,gCAAY,GAAa,EAAE,YAAoB,EAAE,UAAkB;QAAnE,iBAGC;QAFC,IAAM,GAAG,GAAG,8BAA4B,YAAY,6BAAwB,UAAY,CAAC;QACzF,QAAA,kBAAM,GAAG,EAAE,GAAG,CAAC,SAAC;;IAClB,CAAC;IACH,6BAAC;AAAD,CAAC,AALD,CAA4C,eAAe,GAK1D;AALY,wDAAsB;AAOnC;IAA2C,iDAAe;IACxD,+BAAY,GAAa,EAAE,IAAY;QAAvC,iBAGC;QAFC,IAAM,GAAG,GAAG,kEAAgE,IAAM,CAAC;QACnF,QAAA,kBAAM,GAAG,EAAE,GAAG,CAAC,SAAC;;IAClB,CAAC;IACH,4BAAC;AAAD,CAAC,AALD,CAA2C,eAAe,GAKzD;AALY,sDAAqB;AAOlC;IAAkD,wDAAe;IAC/D,sCAAY,GAAa;QAAzB,iBAGC;QAFC,IAAM,GAAG,GAAG,oCAAoC,CAAC;QACjD,QAAA,kBAAM,GAAG,EAAE,GAAG,CAAC,SAAC;;IAClB,CAAC;IACH,mCAAC;AAAD,CAAC,AALD,CAAkD,eAAe,GAKhE;AALY,oEAA4B;AAOzC;IAA2C,iDAAe;IACxD,+BAAY,GAAa;QAAzB,iBAGC;QAFC,IAAM,GAAG,GAAG,4BAA4B,CAAC;QACzC,QAAA,kBAAM,GAAG,EAAE,GAAG,CAAC,SAAC;;IAClB,CAAC;IACH,4BAAC;AAAD,CAAC,AALD,CAA2C,eAAe,GAKzD;AALY,sDAAqB;AAOlC;IAAgD,sDAAe;IAC7D,oCAAY,GAAa;QAAzB,iBAGC;QAFC,IAAM,GAAG,GAAG,kEAAkE,CAAC;QAC/E,QAAA,kBAAM,GAAG,EAAE,GAAG,CAAC,SAAC;;IAClB,CAAC;IACH,iCAAC;AAAD,CAAC,AALD,CAAgD,eAAe,GAK9D;AALY,gEAA0B;AAOvC;IAAwC,8CAAe;IACrD,4BAAY,GAAa;QAAzB,iBAGC;QAFC,IAAM,GAAG,GAAG,gBAAgB,CAAC;QAC7B,QAAA,kBAAM,GAAG,EAAE,GAAG,CAAC,SAAC;;IAClB,CAAC;IACH,yBAAC;AAAD,CAAC,AALD,CAAwC,eAAe,GAKtD;AALY,gDAAkB;AAO/B;IAA2C,iDAAe;IACxD,+BAAY,GAAa;QAAzB,iBAGC;QAFC,IAAM,GAAG,GAAG,qBAAqB,CAAC;QAClC,QAAA,kBAAM,GAAG,EAAE,GAAG,CAAC,SAAC;;IAClB,CAAC;IACH,4BAAC;AAAD,CAAC,AALD,CAA2C,eAAe,GAKzD;AALY,sDAAqB;AAOlC;IAAyC,+CAAe;IACtD,6BAAY,GAAa,EAAE,OAAiB;QAA5C,iBAGC;QAFC,IAAM,GAAG,GAAG,oCAAkC,qBAAa,CAAC,OAAO,CAAC,MAAG,CAAC;QACxE,QAAA,kBAAM,GAAG,EAAE,GAAG,CAAC,SAAC;;IAClB,CAAC;IACH,0BAAC;AAAD,CAAC,AALD,CAAyC,eAAe,GAKvD;AALY,kDAAmB"}