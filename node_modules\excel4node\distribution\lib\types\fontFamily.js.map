{"version": 3, "file": "fontFamily.js", "names": ["items", "_this", "opts", "for<PERSON>ach", "o", "i", "prototype", "validate", "val", "TypeError", "concat", "join", "toLowerCase", "undefined", "name", "hasOwnProperty", "push", "module", "exports"], "sources": ["../../../source/lib/types/fontFamily.js"], "sourcesContent": ["function items() {\n    this.opts = [//§18.8.18 family (Font Family)\n        'n/a', \n        'roman', \n        'swiss', \n        'modern', \n        'script', \n        'decorative'\n    ];\n    this.opts.forEach((o, i) => {\n        this[o] = i;\n    });\n}\n\n\nitems.prototype.validate = function (val) {\n    if (typeof val !== 'string') {\n        throw new TypeError(`Invalid value for Font Family ${val}; Value must be one of ${this.opts.join(', ')}`);\n    }\n\n    if (this[val.toLowerCase()] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError(`Invalid value for Font Family ${val}; Value must be one of ${this.opts.join(', ')}`);\n    } else {\n        return true;\n    }\n};\n\nmodule.exports = new items();"], "mappings": ";;AAAA,SAASA,KAAKA,CAAA,EAAG;EAAA,IAAAC,KAAA;EACb,IAAI,CAACC,IAAI,GAAG;EAAC;EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,YAAY,CACf;EACD,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACxBJ,KAAI,CAACG,CAAC,CAAC,GAAGC,CAAC;EACf,CAAC,CAAC;AACN;AAGAL,KAAK,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACtC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzB,MAAM,IAAIC,SAAS,kCAAAC,MAAA,CAAkCF,GAAG,6BAAAE,MAAA,CAA0B,IAAI,CAACR,IAAI,CAACS,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;EAC7G;EAEA,IAAI,IAAI,CAACH,GAAG,CAACI,WAAW,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;IACvC,IAAIX,IAAI,GAAG,EAAE;IACb,KAAK,IAAIY,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BZ,IAAI,CAACc,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIL,SAAS,kCAAAC,MAAA,CAAkCF,GAAG,6BAAAE,MAAA,CAA0B,IAAI,CAACR,IAAI,CAACS,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;EAC7G,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDM,MAAM,CAACC,OAAO,GAAG,IAAIlB,KAAK,CAAC,CAAC"}