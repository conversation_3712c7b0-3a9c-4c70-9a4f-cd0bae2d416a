{"version": 3, "file": "ReportGeneration-WXxmrtL8.js", "sources": ["../../src/components/Reports/ReportGeneration.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Report Generation Component\n * \n * Automated report card generation with Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Checkbox,\n  FormControlLabel,\n  FormGroup,\n  Stack,\n  Alert,\n  LinearProgress,\n  Chip,\n  Avatar,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Print,\n  Download,\n  Email,\n  Preview,\n  Assessment,\n  School,\n  CalendarToday,\n  CheckCircle,\n  Schedule,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\n\n// Sample report templates\nconst reportTemplates = [\n  {\n    id: 'progress_report',\n    name: 'Student Progress Report',\n    description: 'Comprehensive academic and behavioral progress report',\n    icon: Assessment,\n    color: 'primary',\n  },\n  {\n    id: 'report_card',\n    name: 'Report Card',\n    description: 'Traditional report card with grades and attendance',\n    icon: School,\n    color: 'success',\n  },\n  {\n    id: 'swot_report',\n    name: 'SWOT Analysis Report',\n    description: 'Detailed SWOT analysis with recommendations',\n    icon: Assessment,\n    color: 'info',\n  },\n  {\n    id: 'attendance_report',\n    name: 'Attendance Report',\n    description: 'Monthly attendance summary and patterns',\n    icon: CalendarToday,\n    color: 'warning',\n  },\n];\n\n// Sample students for report generation\nconst students = [\n  { id: 1, name: 'Sanju Kumar Reddy', class: '10-A', rollNumber: 1 },\n  { id: 2, name: 'Niraimathi Selvam', class: '10-A', rollNumber: 2 },\n  { id: 3, name: 'Mahesh Reddy', class: '10-B', rollNumber: 3 },\n  { id: 4, name: 'Ravi Teja Sharma', class: '9-A', rollNumber: 4 },\n  { id: 5, name: 'Ankitha Patel', class: '10-A', rollNumber: 5 },\n  { id: 6, name: 'Sirisha Nair', class: '10-B', rollNumber: 6 },\n  { id: 7, name: 'Priya Agarwal', class: '9-A', rollNumber: 7 },\n];\n\nconst ReportGeneration = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const [selectedTemplate, setSelectedTemplate] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [selectedStudents, setSelectedStudents] = useState([]);\n  const [reportPeriod, setReportPeriod] = useState('');\n  const [includeOptions, setIncludeOptions] = useState({\n    grades: true,\n    attendance: true,\n    behavior: true,\n    swot: false,\n    parentComments: false,\n  });\n  const [generating, setGenerating] = useState(false);\n  const [progress, setProgress] = useState(0);\n\n  const handleStudentSelection = (studentId) => {\n    setSelectedStudents(prev => \n      prev.includes(studentId)\n        ? prev.filter(id => id !== studentId)\n        : [...prev, studentId]\n    );\n  };\n\n  const handleSelectAllStudents = () => {\n    const classStudents = students.filter(student => \n      selectedClass ? student.class === selectedClass : true\n    );\n    setSelectedStudents(classStudents.map(student => student.id));\n  };\n\n  const handleDeselectAllStudents = () => {\n    setSelectedStudents([]);\n  };\n\n  const handleIncludeOptionChange = (option) => (event) => {\n    setIncludeOptions(prev => ({\n      ...prev,\n      [option]: event.target.checked,\n    }));\n  };\n\n  const handleGenerateReports = async () => {\n    if (!selectedTemplate || selectedStudents.length === 0) {\n      alert('Please select a template and at least one student');\n      return;\n    }\n\n    setGenerating(true);\n    setProgress(0);\n\n    try {\n      // Simulate report generation progress\n      for (let i = 0; i <= 100; i += 10) {\n        await new Promise(resolve => setTimeout(resolve, 200));\n        setProgress(i);\n      }\n\n      alert(`Successfully generated ${selectedStudents.length} reports!`);\n    } catch (error) {\n      console.error('Error generating reports:', error);\n    } finally {\n      setGenerating(false);\n      setProgress(0);\n    }\n  };\n\n  const filteredStudents = selectedClass \n    ? students.filter(student => student.class === selectedClass)\n    : students;\n\n  const selectedTemplate_obj = reportTemplates.find(template => template.id === selectedTemplate);\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Report Generation\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Generate comprehensive student reports with Indian educational context\n          </Typography>\n        </Box>\n      </motion.div>\n\n      <Grid container spacing={3}>\n        {/* Configuration Panel */}\n        <Grid item xs={12} md={8}>\n          {/* Template Selection */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Select Report Template\n              </Typography>\n              <Grid container spacing={2}>\n                {reportTemplates.map((template) => (\n                  <Grid item xs={12} sm={6} key={template.id}>\n                    <Card\n                      sx={{\n                        cursor: 'pointer',\n                        border: selectedTemplate === template.id ? `2px solid ${theme.palette[template.color].main}` : '1px solid',\n                        borderColor: selectedTemplate === template.id ? `${template.color}.main` : 'divider',\n                        transition: 'all 0.2s ease',\n                        '&:hover': {\n                          transform: 'translateY(-2px)',\n                          boxShadow: theme.shadows[4],\n                        },\n                      }}\n                      onClick={() => setSelectedTemplate(template.id)}\n                    >\n                      <CardContent>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                          <template.icon color={template.color} />\n                          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600 }}>\n                            {template.name}\n                          </Typography>\n                        </Box>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          {template.description}\n                        </Typography>\n                      </CardContent>\n                    </Card>\n                  </Grid>\n                ))}\n              </Grid>\n            </CardContent>\n          </Card>\n\n          {/* Class and Period Selection */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Report Configuration\n              </Typography>\n              <Grid container spacing={3}>\n                <Grid item xs={12} md={4}>\n                  <FormControl fullWidth>\n                    <InputLabel>Class</InputLabel>\n                    <Select\n                      value={selectedClass}\n                      onChange={(e) => setSelectedClass(e.target.value)}\n                      label=\"Class\"\n                    >\n                      <MenuItem value=\"\">All Classes</MenuItem>\n                      <MenuItem value=\"9-A\">Class 9-A</MenuItem>\n                      <MenuItem value=\"9-B\">Class 9-B</MenuItem>\n                      <MenuItem value=\"10-A\">Class 10-A</MenuItem>\n                      <MenuItem value=\"10-B\">Class 10-B</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Grid>\n                <Grid item xs={12} md={4}>\n                  <FormControl fullWidth>\n                    <InputLabel>Report Period</InputLabel>\n                    <Select\n                      value={reportPeriod}\n                      onChange={(e) => setReportPeriod(e.target.value)}\n                      label=\"Report Period\"\n                    >\n                      <MenuItem value=\"Q1_2024\">Q1 2024-2025</MenuItem>\n                      <MenuItem value=\"Q2_2024\">Q2 2024-2025</MenuItem>\n                      <MenuItem value=\"Q3_2024\">Q3 2024-2025</MenuItem>\n                      <MenuItem value=\"Q4_2024\">Q4 2024-2025</MenuItem>\n                      <MenuItem value=\"ANNUAL_2024\">Annual 2024-2025</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Grid>\n                <Grid item xs={12} md={4}>\n                  <Stack direction=\"row\" spacing={1}>\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      onClick={handleSelectAllStudents}\n                    >\n                      Select All\n                    </Button>\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      onClick={handleDeselectAllStudents}\n                    >\n                      Deselect All\n                    </Button>\n                  </Stack>\n                </Grid>\n              </Grid>\n            </CardContent>\n          </Card>\n\n          {/* Include Options */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Include in Report\n              </Typography>\n              <FormGroup>\n                <Grid container spacing={2}>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.grades}\n                          onChange={handleIncludeOptionChange('grades')}\n                        />\n                      }\n                      label=\"Academic Grades\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.attendance}\n                          onChange={handleIncludeOptionChange('attendance')}\n                        />\n                      }\n                      label=\"Attendance Record\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.behavior}\n                          onChange={handleIncludeOptionChange('behavior')}\n                        />\n                      }\n                      label=\"Behavioral Assessment\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.swot}\n                          onChange={handleIncludeOptionChange('swot')}\n                        />\n                      }\n                      label=\"SWOT Analysis\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.parentComments}\n                          onChange={handleIncludeOptionChange('parentComments')}\n                        />\n                      }\n                      label=\"Parent Comments Section\"\n                    />\n                  </Grid>\n                </Grid>\n              </FormGroup>\n            </CardContent>\n          </Card>\n\n          {/* Student Selection */}\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Select Students ({selectedStudents.length} selected)\n              </Typography>\n              <List sx={{ maxHeight: 300, overflow: 'auto' }}>\n                {filteredStudents.map((student) => (\n                  <ListItem\n                    key={student.id}\n                    sx={{\n                      cursor: 'pointer',\n                      borderRadius: 1,\n                      mb: 1,\n                      border: selectedStudents.includes(student.id) ? `2px solid ${theme.palette.primary.main}` : '1px solid',\n                      borderColor: selectedStudents.includes(student.id) ? 'primary.main' : 'divider',\n                      background: selectedStudents.includes(student.id) ? alpha(theme.palette.primary.main, 0.05) : 'transparent',\n                    }}\n                    onClick={() => handleStudentSelection(student.id)}\n                  >\n                    <ListItemAvatar>\n                      <Avatar sx={{ bgcolor: selectedStudents.includes(student.id) ? 'primary.main' : 'grey.400' }}>\n                        {student.name.charAt(0)}\n                      </Avatar>\n                    </ListItemAvatar>\n                    <ListItemText\n                      primary={student.name}\n                      secondary={`Class ${student.class} - Roll No. ${student.rollNumber}`}\n                    />\n                    {selectedStudents.includes(student.id) && (\n                      <CheckCircle color=\"primary\" />\n                    )}\n                  </ListItem>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Summary Panel */}\n        <Grid item xs={12} md={4}>\n          <Card sx={{ position: 'sticky', top: 24 }}>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Report Summary\n              </Typography>\n\n              {selectedTemplate_obj && (\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 600 }}>\n                    {selectedTemplate_obj.name}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    {selectedTemplate_obj.description}\n                  </Typography>\n                </Alert>\n              )}\n\n              <Stack spacing={2} sx={{ mb: 3 }}>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Selected Students:\n                  </Typography>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                    {selectedStudents.length}\n                  </Typography>\n                </Box>\n\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Class Filter:\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    {selectedClass || 'All Classes'}\n                  </Typography>\n                </Box>\n\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Report Period:\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    {reportPeriod || 'Not selected'}\n                  </Typography>\n                </Box>\n              </Stack>\n\n              {generating && (\n                <Box sx={{ mb: 3 }}>\n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    Generating Reports... {progress}%\n                  </Typography>\n                  <LinearProgress variant=\"determinate\" value={progress} />\n                </Box>\n              )}\n\n              <Stack spacing={2}>\n                <Button\n                  variant=\"contained\"\n                  fullWidth\n                  startIcon={<Assessment />}\n                  onClick={handleGenerateReports}\n                  disabled={!selectedTemplate || selectedStudents.length === 0 || generating}\n                  sx={{\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  {generating ? 'Generating...' : 'Generate Reports'}\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  fullWidth\n                  startIcon={<Preview />}\n                  disabled={!selectedTemplate}\n                >\n                  Preview Template\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  fullWidth\n                  startIcon={<Download />}\n                  disabled={selectedStudents.length === 0}\n                >\n                  Download All\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  fullWidth\n                  startIcon={<Email />}\n                  disabled={selectedStudents.length === 0}\n                >\n                  Email to Parents\n                </Button>\n              </Stack>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default ReportGeneration;\n"], "names": ["reportTemplates", "id", "name", "description", "icon", "Assessment", "color", "School", "CalendarToday", "students", "class", "rollNumber", "ReportGeneration", "theme", "useTheme", "useNavigate", "selectedTemplate", "setSelectedTemplate", "useState", "selectedClass", "setSelectedClass", "selectedStudents", "setSelectedStudents", "reportPeriod", "setReportPeriod", "includeOptions", "setIncludeOptions", "grades", "attendance", "behavior", "swot", "parentComments", "generating", "setGenerating", "progress", "setProgress", "handleIncludeOptionChange", "option", "event", "prev", "target", "checked", "filteredStudents", "filter", "student", "selectedTemplate_obj", "find", "template", "Box", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "jsxRuntimeExports", "jsx", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "mb", "Typography", "variant", "fontWeight", "background", "palette", "primary", "main", "secondary", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "jsxs", "Grid", "container", "spacing", "item", "xs", "md", "Card", "<PERSON><PERSON><PERSON><PERSON>", "map", "sm", "cursor", "border", "borderColor", "transform", "boxShadow", "shadows", "onClick", "display", "alignItems", "gap", "FormControl", "fullWidth", "InputLabel", "Select", "value", "onChange", "e", "label", "MenuItem", "<PERSON><PERSON>", "direction", "<PERSON><PERSON>", "size", "classStudents", "FormGroup", "FormControlLabel", "control", "Checkbox", "length", "List", "maxHeight", "overflow", "ListItem", "borderRadius", "includes", "alpha", "handleStudentSelection", "studentId", "ListItemAvatar", "Avatar", "bgcolor", "char<PERSON>t", "ListItemText", "CheckCircle", "position", "top", "<PERSON><PERSON>", "severity", "LinearProgress", "startIcon", "async", "i", "Promise", "resolve", "setTimeout", "alert", "error", "disabled", "Preview", "Download", "Email"], "mappings": "ucAgDA,MAAMA,EAAkB,CACtB,CACEC,GAAI,kBACJC,KAAM,0BACNC,YAAa,wDACbC,KAAMC,EACNC,MAAO,WAET,CACEL,GAAI,cACJC,KAAM,cACNC,YAAa,qDACbC,KAAMG,EACND,MAAO,WAET,CACEL,GAAI,cACJC,KAAM,uBACNC,YAAa,8CACbC,KAAMC,EACNC,MAAO,QAET,CACEL,GAAI,oBACJC,KAAM,oBACNC,YAAa,0CACbC,KAAMI,EACNF,MAAO,YAKLG,EAAW,CACf,CAAER,GAAI,EAAGC,KAAM,oBAAqBQ,MAAO,OAAQC,WAAY,GAC/D,CAAEV,GAAI,EAAGC,KAAM,oBAAqBQ,MAAO,OAAQC,WAAY,GAC/D,CAAEV,GAAI,EAAGC,KAAM,eAAgBQ,MAAO,OAAQC,WAAY,GAC1D,CAAEV,GAAI,EAAGC,KAAM,mBAAoBQ,MAAO,MAAOC,WAAY,GAC7D,CAAEV,GAAI,EAAGC,KAAM,gBAAiBQ,MAAO,OAAQC,WAAY,GAC3D,CAAEV,GAAI,EAAGC,KAAM,eAAgBQ,MAAO,OAAQC,WAAY,GAC1D,CAAEV,GAAI,EAAGC,KAAM,gBAAiBQ,MAAO,MAAOC,WAAY,IAGtDC,EAAmB,KACvB,MAAMC,EAAQC,IACeC,IAC7B,MAAOC,EAAkBC,GAAuBC,EAAAA,SAAS,KAClDC,EAAeC,GAAoBF,EAAAA,SAAS,KAC5CG,EAAkBC,GAAuBJ,EAAAA,SAAS,KAClDK,EAAcC,GAAmBN,EAAAA,SAAS,KAC1CO,EAAgBC,GAAqBR,WAAS,CACnDS,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,MAAM,EACNC,gBAAgB,KAEXC,EAAYC,GAAiBf,EAAAA,UAAS,IACtCgB,EAAUC,GAAejB,EAAAA,SAAS,GAqBnCkB,EAA6BC,GAAYC,IAC7CZ,GAA2Ba,IAAA,IACtBA,EACHF,CAACA,GAASC,EAAME,OAAOC,WACvB,EA4BEC,EAAmBvB,EACrBV,EAASkC,WAAkBC,EAAQlC,QAAUS,IAC7CV,EAEEoC,EAAuB7C,EAAgB8C,MAAiBC,GAAAA,EAAS9C,KAAOe,IAG5E,cAACgC,EAAI,CAAAC,GAAI,CAAEC,SAAU,KAAMC,GAAI,OAAQC,EAAG,GAExCC,SAAA,CAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,GAAO,IAC9BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAExBV,gBAACL,EAAI,CAAAC,GAAI,CAAEe,GAAI,GACbX,SAAA,CAAAC,EAAAC,IAACU,EAAA,CACCC,QAAQ,KACRjB,GAAI,CACFkB,WAAY,IACZH,GAAI,EACJI,WAAY,2BAA2BvD,EAAMwD,QAAQC,QAAQC,YAAY1D,EAAMwD,QAAQG,UAAUD,aACjGE,qBAAsB,OACtBC,oBAAqB,cACrBC,eAAgB,QAEnBtB,SAAA,4BAGAY,EAAW,CAAAC,QAAQ,QAAQ5D,MAAM,iBAAiB+C,SAEnD,gFAIHuB,EAAAA,KAAAC,EAAA,CAAKC,WAAS,EAACC,QAAS,EAEvB1B,SAAA,CAAAuB,OAACC,GAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAErB7B,SAAA,CAAAE,EAAAA,IAAC4B,GAAKlC,GAAI,CAAEe,GAAI,GACdX,gBAAC+B,EACC,CAAA/B,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAK7D,MAAO,gBAAkB+C,SAEhF,iCACCwB,EAAK,CAAAC,WAAS,EAACC,QAAS,EACtB1B,SAAgBrD,EAAAqF,KAAKtC,SACnB8B,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIK,GAAI,EACrBjC,SAAAC,EAAAC,IAAC4B,EAAA,CACClC,GAAI,CACFsC,OAAQ,UACRC,OAAQxE,IAAqB+B,EAAS9C,GAAK,aAAaY,EAAMwD,QAAQtB,EAASzC,OAAOiE,OAAS,YAC/FkB,YAAazE,IAAqB+B,EAAS9C,GAAK,GAAG8C,EAASzC,aAAe,UAC3EwD,WAAY,gBACZ,UAAW,CACT4B,UAAW,mBACXC,UAAW9E,EAAM+E,QAAQ,KAG7BC,QAAS,IAAM5E,EAAoB8B,EAAS9C,IAE5CoD,gBAAC+B,EACC,CAAA/B,SAAA,CAACuB,EAAAA,KAAA5B,EAAA,CAAIC,GAAI,CAAE6C,QAAS,OAAQC,WAAY,SAAUC,IAAK,EAAGhC,GAAI,GAC5DX,SAAA,CAAAE,EAAAA,IAACR,EAAS3C,KAAT,CAAcE,MAAOyC,EAASzC,QAC/BiD,EAAAA,IAACU,EAAW,CAAAC,QAAQ,YAAYjB,GAAI,CAAEkB,WAAY,KAC/Cd,SAAAN,EAAS7C,gBAGb+D,EAAW,CAAAC,QAAQ,QAAQ5D,MAAM,iBAC/B+C,WAASlD,oBAtBa4C,EAAS9C,aAiChDsD,EAAAA,IAAC4B,GAAKlC,GAAI,CAAEe,GAAI,GACdX,gBAAC+B,EACC,CAAA/B,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAK7D,MAAO,gBAAkB+C,SAEhF,yBACCuB,EAAAA,KAAAC,EAAA,CAAKC,WAAS,EAACC,QAAS,EACvB1B,SAAA,GAACE,IAAAsB,EAAA,CAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB7B,SAAAuB,EAAAA,KAACqB,EAAY,CAAAC,WAAS,EACpB7C,SAAA,GAAAE,IAAC4C,GAAW9C,SAAK,UACjBC,EAAAsB,KAACwB,EAAA,CACCC,MAAOlF,EACPmF,SAAWC,GAAMnF,EAAiBmF,EAAE/D,OAAO6D,OAC3CG,MAAM,QAENnD,SAAA,CAACE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,GAAGhD,SAAW,gBAC7BE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,MAAMhD,SAAS,cAC9BE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,MAAMhD,SAAS,cAC9BE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,OAAOhD,SAAU,eAChCE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,OAAOhD,SAAU,yBAIvCE,IAACsB,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB7B,SAAAuB,EAAAA,KAACqB,EAAY,CAAAC,WAAS,EACpB7C,SAAA,GAAAE,IAAC4C,GAAW9C,SAAa,kBACzBC,EAAAsB,KAACwB,EAAA,CACCC,MAAO9E,EACP+E,SAAWC,GAAM/E,EAAgB+E,EAAE/D,OAAO6D,OAC1CG,MAAM,gBAENnD,SAAA,CAACE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,UAAUhD,SAAY,iBACrCE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,UAAUhD,SAAY,iBACrCE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,UAAUhD,SAAY,iBACrCE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,UAAUhD,SAAY,iBACrCE,EAAAA,IAAAkD,EAAA,CAASJ,MAAM,cAAchD,SAAgB,+BAInDE,IAAAsB,EAAA,CAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB7B,SAACuB,EAAAA,KAAA8B,EAAA,CAAMC,UAAU,MAAM5B,QAAS,EAC9B1B,SAAA,CAAAC,EAAAC,IAACqD,EAAA,CACC1C,QAAQ,WACR2C,KAAK,QACLhB,QApKY,KAC9B,MAAMiB,EAAgBrG,EAASkC,QAC7BC,IAAAzB,GAAgByB,EAAQlC,QAAUS,IAEpCG,EAAoBwF,EAAczB,KAAezC,GAAAA,EAAQ3C,KAAG,EAiK3CoD,SAAA,eAGDC,EAAAC,IAACqD,EAAA,CACC1C,QAAQ,WACR2C,KAAK,QACLhB,QApKc,KAChCvE,EAAoB,GAAE,EAoKL+B,SAAA,8BAUXE,EAAAA,IAAC4B,GAAKlC,GAAI,CAAEe,GAAI,GACdX,gBAAC+B,EACC,CAAA/B,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAK7D,MAAO,gBAAkB+C,SAEhF,4BACC0D,EACC,CAAA1D,SAAAuB,OAACC,GAAKC,WAAS,EAACC,QAAS,EACvB1B,SAAA,CAAAE,MAACsB,GAAKG,MAAI,EAACC,GAAI,GAAIK,GAAI,EACrBjC,SAAAC,EAAAC,IAACyD,EAAA,CACCC,QACE3D,EAAAC,IAAC2D,EAAA,CACCzE,QAAShB,EAAeE,OACxB2E,SAAUlE,EAA0B,YAGxCoE,MAAM,4BAGT3B,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIK,GAAI,EACrBjC,SAAAC,EAAAC,IAACyD,EAAA,CACCC,QACE3D,EAAAC,IAAC2D,EAAA,CACCzE,QAAShB,EAAeG,WACxB0E,SAAUlE,EAA0B,gBAGxCoE,MAAM,8BAGT3B,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIK,GAAI,EACrBjC,SAAAC,EAAAC,IAACyD,EAAA,CACCC,QACE3D,EAAAC,IAAC2D,EAAA,CACCzE,QAAShB,EAAeI,SACxByE,SAAUlE,EAA0B,cAGxCoE,MAAM,kCAGT3B,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIK,GAAI,EACrBjC,SAAAC,EAAAC,IAACyD,EAAA,CACCC,QACE3D,EAAAC,IAAC2D,EAAA,CACCzE,QAAShB,EAAeK,KACxBwE,SAAUlE,EAA0B,UAGxCoE,MAAM,0BAGT3B,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIK,GAAI,EACrBjC,SAAAC,EAAAC,IAACyD,EAAA,CACCC,QACE3D,EAAAC,IAAC2D,EAAA,CACCzE,QAAShB,EAAeM,eACxBuE,SAAUlE,EAA0B,oBAGxCoE,MAAM,wCASlBjD,EAAAA,IAAC4B,EACC,CAAA9B,SAAAuB,EAAAA,KAACQ,EACC,CAAA/B,SAAA,CAACuB,EAAAA,KAAAX,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAK7D,MAAO,gBAAkB+C,SAAA,CAAA,oBAC5DhC,EAAiB8F,OAAO,kBAE3C5D,IAAA6D,EAAA,CAAKnE,GAAI,CAAEoE,UAAW,IAAKC,SAAU,QACnCjE,SAAAX,EAAiB2C,KAAKzC,GACrBU,EAAAsB,KAAC2C,EAAA,CAECtE,GAAI,CACFsC,OAAQ,UACRiC,aAAc,EACdxD,GAAI,EACJwB,OAAQnE,EAAiBoG,SAAS7E,EAAQ3C,IAAM,aAAaY,EAAMwD,QAAQC,QAAQC,OAAS,YAC5FkB,YAAapE,EAAiBoG,SAAS7E,EAAQ3C,IAAM,eAAiB,UACtEmE,WAAY/C,EAAiBoG,SAAS7E,EAAQ3C,IAAMyH,EAAM7G,EAAMwD,QAAQC,QAAQC,KAAM,KAAQ,eAEhGsB,QAAS,KAAM8B,OAnRDC,EAmRwBhF,EAAQ3C,QAlR9DqB,GACEiB,GAAAA,EAAKkF,SAASG,GACVrF,EAAKI,QAAa1C,GAAAA,IAAO2H,IACzB,IAAIrF,EAAMqF,KAJa,IAACA,CAmRkC,EAEhDvE,SAAA,CAAAE,EAAAA,IAACsE,GACCxE,SAACE,MAAAuE,EAAA,CAAO7E,GAAI,CAAE8E,QAAS1G,EAAiBoG,SAAS7E,EAAQ3C,IAAM,eAAiB,YAC7EoD,SAAAT,EAAQ1C,KAAK8H,OAAO,OAGzB1E,EAAAC,IAAC0E,EAAA,CACC3D,QAAS1B,EAAQ1C,KACjBsE,UAAW,SAAS5B,EAAQlC,oBAAoBkC,EAAQjC,eAEzDU,EAAiBoG,SAAS7E,EAAQ3C,KAChCsD,EAAAA,IAAA2E,EAAA,CAAY5H,MAAM,cArBhBsC,EAAQ3C,sBA+BxB4E,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB7B,eAAC8B,EAAK,CAAAlC,GAAI,CAAEkF,SAAU,SAAUC,IAAK,IACnC/E,gBAAC+B,EACC,CAAA/B,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAK7D,MAAO,gBAAkB+C,SAEhF,mBAECR,UACEwF,EAAM,CAAAC,SAAS,OAAOrF,GAAI,CAAEe,GAAI,GAC/BX,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,YAAYjB,GAAI,CAAEkB,WAAY,KAC/Cd,SAAAR,EAAqB3C,OAEvBqD,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QACjBb,WAAqBlD,iBAK5ByE,OAAC8B,GAAM3B,QAAS,EAAG9B,GAAI,CAAEe,GAAI,GAC3BX,SAAA,QAACL,EACC,CAAAK,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQ5D,MAAM,iBAAiB+C,SAEnD,uBACAE,EAAAA,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAAhC,EAAiB8F,mBAIrBnE,EACC,CAAAK,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQ5D,MAAM,iBAAiB+C,SAEnD,kBACCE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QACjBb,YAAiB,0BAIrBL,EACC,CAAAK,SAAA,CAAAE,MAACU,EAAW,CAAAC,QAAQ,QAAQ5D,MAAM,iBAAiB+C,SAEnD,mBACCE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QACjBb,YAAgB,uBAKtBrB,GACE4C,EAAAA,KAAA5B,EAAA,CAAIC,GAAI,CAAEe,GAAI,GACbX,SAAA,CAAAuB,OAACX,GAAWC,QAAQ,QAAQjB,GAAI,CAAEe,GAAI,GAAKX,SAAA,CAAA,yBAClBnB,EAAS,OAEjCqB,EAAAA,IAAAgF,EAAA,CAAerE,QAAQ,cAAcmC,MAAOnE,SAIjD0C,KAAC8B,EAAM,CAAA3B,QAAS,EACd1B,SAAA,CAAAC,EAAAC,IAACqD,EAAA,CACC1C,QAAQ,YACRgC,WAAS,EACTsC,gBAAYnI,EAAW,IACvBwF,QA5Uc4C,UAC5B,GAAKzH,GAAgD,IAA5BK,EAAiB8F,OAA1C,CAKAlF,GAAc,GACdE,EAAY,GAER,IAEF,IAAA,IAASuG,EAAI,EAAGA,GAAK,IAAKA,GAAK,SACvB,IAAIC,SAAQC,GAAWC,WAAWD,EAAS,OACjDzG,EAAYuG,GAGRI,MAAA,0BAA0BzH,EAAiB8F,yBAC1C4B,GACyC,CAChD,QACA9G,GAAc,GACdE,EAAY,EAAC,CAlBb,MADA2G,MAAM,oDAmBO,EAwTDE,UAAWhI,GAAgD,IAA5BK,EAAiB8F,QAAgBnF,EAChEiB,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMwD,QAAQC,QAAQC,YAAY1D,EAAMwD,QAAQG,UAAUD,cAGlGlB,WAAa,gBAAkB,qBAGlCC,EAAAC,IAACqD,EAAA,CACC1C,QAAQ,WACRgC,WAAS,EACTsC,gBAAYS,EAAQ,IACpBD,UAAWhI,EACZqC,SAAA,qBAIDC,EAAAC,IAACqD,EAAA,CACC1C,QAAQ,WACRgC,WAAS,EACTsC,gBAAYU,EAAS,IACrBF,SAAsC,IAA5B3H,EAAiB8F,OAC5B9D,SAAA,iBAIDC,EAAAC,IAACqD,EAAA,CACC1C,QAAQ,WACRgC,WAAS,EACTsC,gBAAYW,EAAM,IAClBH,SAAsC,IAA5B3H,EAAiB8F,OAC5B9D,SAAA,oCAQb"}