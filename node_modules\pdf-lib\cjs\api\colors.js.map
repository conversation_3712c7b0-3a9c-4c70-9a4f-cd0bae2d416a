{"version": 3, "file": "colors.js", "sourceRoot": "", "sources": ["../../src/api/colors.ts"], "names": [], "mappings": ";;;AAAA,yCAO2B;AAC3B,kCAA+C;AAE/C,IAAY,UAIX;AAJD,WAAY,UAAU;IACpB,qCAAuB,CAAA;IACvB,yBAAW,CAAA;IACX,2BAAa,CAAA;AACf,CAAC,EAJW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAIrB;AAwBY,QAAA,SAAS,GAAG,UAAC,IAAY;IACpC,mBAAW,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACpC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,MAAA,EAAE,CAAC;AAC9C,CAAC,CAAC;AAEW,QAAA,GAAG,GAAG,UAAC,GAAW,EAAE,KAAa,EAAE,IAAY;IAC1D,mBAAW,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,mBAAW,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAClC,mBAAW,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAChC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,GAAG,EAAE,GAAG,KAAA,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAE,CAAC;AACpD,CAAC,CAAC;AAEW,QAAA,IAAI,GAAG,UAClB,IAAY,EACZ,OAAe,EACf,MAAc,EACd,GAAW;IAEX,mBAAW,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAChC,mBAAW,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,mBAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACpC,mBAAW,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,MAAM,QAAA,EAAE,GAAG,KAAA,EAAE,CAAC;AAC/D,CAAC,CAAC;AAEM,IAAA,SAAS,GAAgB,UAAU,UAA1B,EAAE,GAAG,GAAW,UAAU,IAArB,EAAE,IAAI,GAAK,UAAU,KAAf,CAAgB;AAE5C,kBAAkB;AACL,QAAA,eAAe,GAAG,UAAC,KAAY;IACxC,OAAA,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,oCAAwB,CAAC,KAAK,CAAC,IAAI,CAAC;QACjE,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAO,CAAC,CAAC,8BAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC;YACnF,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAM,CAAC,CAAC,+BAAmB,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC;gBACpG,CAAC,CAAC,aAAK,CAAC,oBAAkB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAG,CAAC;AAHhD,CAGgD,CAAC;AAErD,kBAAkB;AACL,QAAA,gBAAgB,GAAG,UAAC,KAAY;IACzC,OAAA,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,qCAAyB,CAAC,KAAK,CAAC,IAAI,CAAC;QAClE,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAO,CAAC,CAAC,+BAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC;YACpF,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAM,CAAC,CAAC,gCAAoB,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC;gBACrG,CAAC,CAAC,aAAK,CAAC,oBAAkB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAG,CAAC;AAHhD,CAGgD,CAAC;AAErD,kBAAkB;AACL,QAAA,iBAAiB,GAAG,UAAC,KAAgB,EAAE,KAAS;IAAT,sBAAA,EAAA,SAAS;IAAK,OAAA,CAC9D,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,MAAK,CAAC,CAAC,CAAC,CAAC,iBAAS,CAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CACjB;QACH,CAAC,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,MAAK,CAAC,CAAC,CAAC,CAAC,WAAG,CACvB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,EAChB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,EAChB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CACjB;YACH,CAAC,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,MAAK,CAAC,CAAC,CAAC,CAAC,YAAI,CACxB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,EAChB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,EAChB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,EAChB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CACjB;gBACH,CAAC,CAAC,SAAS,CACZ;AAhBiE,CAgBjE,CAAC;AAEF,kBAAkB;AACL,QAAA,iBAAiB,GAAG,UAAC,KAAY;IAC1C,OAAA,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;QACzC,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAO,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC;YACjE,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC;gBACjF,CAAC,CAAC,aAAK,CAAC,oBAAkB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAG,CAAC;AAHhD,CAGgD,CAAC"}