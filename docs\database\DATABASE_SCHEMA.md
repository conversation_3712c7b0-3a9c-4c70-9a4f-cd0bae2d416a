# VidyaMitra Platform - Database Schema Design

## Overview

PostgreSQL database schema designed for the VidyaMitra platform, optimized for Indian educational context with support for multiple boards, regional data, and comprehensive student analytics.

## Database Configuration

- **Database**: PostgreSQL 15+
- **Character Set**: UTF-8 (for multilingual support)
- **Timezone**: Asia/Kolkata
- **Collation**: en_IN.UTF-8

## Core Tables

### 1. Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    role user_role NOT NULL,
    school_id UUID REFERENCES schools(id),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE user_role AS ENUM ('principal', 'teacher', 'student', 'parent', 'admin');
```

### 2. Schools Table
```sql
CREATE TABLE schools (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    school_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    board education_board NOT NULL,
    location VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    pincode VARCHAR(10),
    principal_id UUID REFERENCES users(id),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(255),
    website VARCHAR(255),
    established_year INTEGER,
    total_students INTEGER DEFAULT 0,
    total_teachers INTEGER DEFAULT 0,
    facilities TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE education_board AS ENUM ('cbse', 'icse', 'state', 'ib');
```

### 3. Students Table
```sql
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    grade VARCHAR(10) NOT NULL,
    section VARCHAR(10),
    board education_board NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    roll_number VARCHAR(20),
    date_of_birth DATE NOT NULL,
    gender gender_type,
    blood_group VARCHAR(5),
    religion VARCHAR(50),
    caste VARCHAR(50),
    nationality VARCHAR(50) DEFAULT 'Indian',
    mother_tongue VARCHAR(50),
    languages TEXT[],
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    pincode VARCHAR(10),
    father_name VARCHAR(255),
    mother_name VARCHAR(255),
    guardian VARCHAR(255),
    parent_contact VARCHAR(20),
    parent_email VARCHAR(255),
    emergency_contact VARCHAR(20),
    academic_level DECIMAL(5,2),
    admission_date DATE,
    previous_school VARCHAR(255),
    medical_info JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE gender_type AS ENUM ('male', 'female', 'other');
```

### 4. Teachers Table
```sql
CREATE TABLE teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    teacher_id VARCHAR(20) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id) NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    subject VARCHAR(100),
    qualification VARCHAR(255),
    experience_years INTEGER,
    specialization TEXT[],
    classes_assigned TEXT[],
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5. Academic Performance Table
```sql
CREATE TABLE academic_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) NOT NULL,
    subject VARCHAR(100) NOT NULL,
    assessment_type assessment_type NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    max_score DECIMAL(5,2) NOT NULL,
    grade VARCHAR(5),
    assessment_date DATE NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    term VARCHAR(20),
    teacher_id UUID REFERENCES teachers(id),
    remarks TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE assessment_type AS ENUM ('unit_test', 'mid_term', 'final_exam', 'assignment', 'project', 'practical');
```

### 6. Attendance Table
```sql
CREATE TABLE attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) NOT NULL,
    date DATE NOT NULL,
    status attendance_status NOT NULL,
    period INTEGER,
    subject VARCHAR(100),
    teacher_id UUID REFERENCES teachers(id),
    remarks TEXT,
    marked_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, date, period)
);

CREATE TYPE attendance_status AS ENUM ('present', 'absent', 'late', 'excused');
```

### 7. SWOT Analysis Table
```sql
CREATE TABLE swot_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) NOT NULL,
    strengths JSONB NOT NULL,
    weaknesses JSONB NOT NULL,
    opportunities JSONB NOT NULL,
    threats JSONB NOT NULL,
    cultural_context VARCHAR(100),
    board_specific_insights TEXT,
    confidence_score INTEGER CHECK (confidence_score >= 0 AND confidence_score <= 100),
    recommendations TEXT[],
    conducted_by UUID REFERENCES teachers(id),
    conducted_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 8. Reports Table
```sql
CREATE TABLE reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_id VARCHAR(20) UNIQUE NOT NULL,
    type report_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    student_id UUID REFERENCES students(id),
    class_id VARCHAR(20),
    school_id UUID REFERENCES schools(id),
    generated_by UUID REFERENCES users(id) NOT NULL,
    format report_format NOT NULL,
    parameters JSONB,
    file_path VARCHAR(500),
    file_size INTEGER,
    status report_status DEFAULT 'pending',
    generated_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE report_type AS ENUM ('comprehensive', 'academic', 'attendance', 'swot', 'class_performance', 'parent_communication');
CREATE TYPE report_format AS ENUM ('pdf', 'excel', 'csv');
CREATE TYPE report_status AS ENUM ('pending', 'processing', 'completed', 'failed', 'expired');
```

### 9. Behavioral Records Table
```sql
CREATE TABLE behavioral_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) NOT NULL,
    incident_date DATE NOT NULL,
    type VARCHAR(100) NOT NULL,
    category behavior_category NOT NULL,
    severity behavior_severity NOT NULL,
    description TEXT NOT NULL,
    action_taken TEXT,
    reported_by UUID REFERENCES teachers(id),
    parent_notified BOOLEAN DEFAULT false,
    follow_up_required BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE behavior_category AS ENUM ('academic', 'cultural', 'improvement', 'disciplinary');
CREATE TYPE behavior_severity AS ENUM ('positive', 'neutral', 'improvement_needed', 'serious');
```

### 10. Extracurricular Activities Table
```sql
CREATE TABLE extracurricular_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) NOT NULL,
    activity VARCHAR(255) NOT NULL,
    category activity_category NOT NULL,
    level activity_level NOT NULL,
    duration VARCHAR(50),
    frequency VARCHAR(100),
    achievements TEXT[],
    skills_learned TEXT[],
    mentor_id UUID REFERENCES teachers(id),
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE activity_category AS ENUM ('sports', 'cultural', 'academic', 'social');
CREATE TYPE activity_level AS ENUM ('beginner', 'intermediate', 'advanced');
```

## Indexes for Performance

```sql
-- User indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_school_id ON users(school_id);

-- Student indexes
CREATE INDEX idx_students_school_id ON students(school_id);
CREATE INDEX idx_students_grade ON students(grade);
CREATE INDEX idx_students_board ON students(board);
CREATE INDEX idx_students_name ON students(name);
CREATE INDEX idx_students_student_id ON students(student_id);

-- Performance indexes
CREATE INDEX idx_academic_performance_student_id ON academic_performance(student_id);
CREATE INDEX idx_academic_performance_date ON academic_performance(assessment_date);
CREATE INDEX idx_academic_performance_subject ON academic_performance(subject);

-- Attendance indexes
CREATE INDEX idx_attendance_student_id ON attendance(student_id);
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_attendance_status ON attendance(status);

-- SWOT indexes
CREATE INDEX idx_swot_student_id ON swot_analysis(student_id);
CREATE INDEX idx_swot_date ON swot_analysis(conducted_date);

-- Reports indexes
CREATE INDEX idx_reports_student_id ON reports(student_id);
CREATE INDEX idx_reports_type ON reports(type);
CREATE INDEX idx_reports_status ON reports(status);
CREATE INDEX idx_reports_generated_by ON reports(generated_by);
```

## Views for Common Queries

```sql
-- Student performance summary view
CREATE VIEW student_performance_summary AS
SELECT 
    s.id,
    s.name,
    s.grade,
    s.board,
    s.school_id,
    AVG(ap.score) as average_score,
    COUNT(ap.id) as total_assessments,
    MAX(ap.assessment_date) as last_assessment_date
FROM students s
LEFT JOIN academic_performance ap ON s.id = ap.student_id
WHERE s.is_active = true
GROUP BY s.id, s.name, s.grade, s.board, s.school_id;

-- Attendance summary view
CREATE VIEW attendance_summary AS
SELECT 
    s.id as student_id,
    s.name,
    COUNT(a.id) as total_days,
    COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_days,
    COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_days,
    ROUND(
        (COUNT(CASE WHEN a.status = 'present' THEN 1 END) * 100.0 / COUNT(a.id)), 2
    ) as attendance_percentage
FROM students s
LEFT JOIN attendance a ON s.id = a.student_id
WHERE s.is_active = true
GROUP BY s.id, s.name;
```

## Data Migration Scripts

```sql
-- Insert sample schools
INSERT INTO schools (school_code, name, board, location, city, state, pincode) VALUES
('VVS001', 'Vidya Vikas High School', 'cbse', 'Banjara Hills', 'Hyderabad', 'Telangana', '500034'),
('TMS002', 'Telangana Model School', 'state', 'Jubilee Hills', 'Hyderabad', 'Telangana', '500033'),
('ICS003', 'International Cambridge School', 'icse', 'Gachibowli', 'Hyderabad', 'Telangana', '500032');

-- Insert sample users
INSERT INTO users (email, password_hash, name, role) VALUES
('<EMAIL>', '$2b$12$...', 'Dr. Rajesh Kumar', 'principal'),
('<EMAIL>', '$2b$12$...', 'Mrs. Priya Sharma', 'teacher'),
('<EMAIL>', '$2b$12$...', 'Sanju Kumar', 'student');
```

## Backup and Maintenance

- **Daily backups** at 2:00 AM IST
- **Weekly full backups** on Sundays
- **Monthly archive** to cold storage
- **Point-in-time recovery** enabled
- **Automated vacuum** and analyze
- **Index maintenance** weekly
