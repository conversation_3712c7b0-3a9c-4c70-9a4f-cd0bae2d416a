{"version": 3, "file": "definedNameCollection.js", "names": ["DefinedName", "opts", "_classCallCheck", "refForm<PERSON>", "undefined", "name", "comment", "customMenu", "description", "help", "statusBar", "localSheetId", "hidden", "vbProcedure", "xlm", "functionGroupId", "shortcut<PERSON><PERSON>", "publishToServer", "workbookParameter", "_createClass", "key", "value", "addToXMLele", "ele", "d<PERSON>le", "att", "text", "DefinedNameCollection", "items", "get", "length", "addDefinedName", "item", "<PERSON><PERSON><PERSON><PERSON>", "push", "dnEle", "for<PERSON>ach", "dn", "module", "exports"], "sources": ["../../../source/lib/classes/definedNameCollection.js"], "sourcesContent": ["class DefinedName { //§18.2.5 definedName (Defined Name)\n    constructor(opts) {\n        opts.refFormula !== undefined ? this.refFormula = opts.refFormula : null; \n        opts.name !== undefined ? this.name = opts.name : null;\n        opts.comment !== undefined ? this.comment = opts.comment : null; \n        opts.customMenu !== undefined ? this.customMenu = opts.customMenu : null; \n        opts.description !== undefined ? this.description = opts.description : null; \n        opts.help !== undefined ? this.help = opts.help : null; \n        opts.statusBar !== undefined ? this.statusBar = opts.statusBar : null; \n        opts.localSheetId !== undefined ? this.localSheetId = opts.localSheetId : null; \n        opts.hidden !== undefined ? this.hidden = opts.hidden : null; \n        opts['function'] !== undefined ? this['function'] = opts['function'] : null; \n        opts.vbProcedure !== undefined ? this.vbProcedure = opts.vbProcedure : null; \n        opts.xlm !== undefined ? this.xlm = opts.xlm : null; \n        opts.functionGroupId !== undefined ? this.functionGroupId = opts.functionGroupId : null; \n        opts.shortcutKey !== undefined ? this.shortcutKey = opts.shortcutKey : null; \n        opts.publishToServer !== undefined ? this.publishToServer = opts.publishToServer : null; \n        opts.workbookParameter !== undefined ? this.workbookParameter = opts.workbookParameter : null; \n    }\n\n    addToXMLele(ele) {\n        let dEle = ele.ele('definedName');\n        this.comment !== undefined ? dEle.att('comment', this.comment) : null; \n        this.customMenu !== undefined ? dEle.att('customMenu', this.customMenu) : null; \n        this.description !== undefined ? dEle.att('description', this.description) : null; \n        this.help !== undefined ? dEle.att('help', this.help) : null; \n        this.statusBar !== undefined ? dEle.att('statusBar', this.statusBar) : null; \n        this.hidden !== undefined ? dEle.att('hidden', this.hidden) : null; \n        this.localSheetId !== undefined ? dEle.att('localSheetId', this.localSheetId) : null; \n        this.name !== undefined ? dEle.att('name', this.name) : null;\n        this['function'] !== undefined ? dEle.att('function', this['function']) : null; \n        this.vbProcedure !== undefined ? dEle.att('vbProcedure', this.vbProcedure) : null; \n        this.xlm !== undefined ? dEle.att('xlm', this.xlm) : null; \n        this.functionGroupId !== undefined ? dEle.att('functionGroupId', this.functionGroupId) : null; \n        this.shortcutKey !== undefined ? dEle.att('shortcutKey', this.shortcutKey) : null; \n        this.publishToServer !== undefined ? dEle.att('publishToServer', this.publishToServer) : null; \n        this.workbookParameter !== undefined ? dEle.att('workbookParameter', this.workbookParameter) : null; \n\n        this.refFormula !== undefined ? dEle.text(this.refFormula) : null;\n    }\n}\n\n\nclass DefinedNameCollection { // §18.2.6 definedNames (Defined Names)\n    constructor() {\n        this.items = [];\n    }\n\n    get length() {\n        return this.items.length;\n    }\n\n    get isEmpty() {\n        if (this.items.length === 0) {\n            return true;\n        } else {\n            return false;\n        }\n    }\n\n    addDefinedName(opts) {\n        let item = new DefinedName(opts);\n        let newLength = this.items.push(item);\n        return this.items[newLength - 1];\n    }\n\n    addToXMLele(ele) {\n        let dnEle = ele.ele('definedNames');\n        this.items.forEach((dn) => {\n            dn.addToXMLele(dnEle);\n        });\n    }\n}\nmodule.exports = DefinedNameCollection;"], "mappings": ";;;;;IAAMA,WAAW;EAAG;EAChB,SAAAA,YAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,WAAA;IACdC,IAAI,CAACE,UAAU,KAAKC,SAAS,GAAG,IAAI,CAACD,UAAU,GAAGF,IAAI,CAACE,UAAU,GAAG,IAAI;IACxEF,IAAI,CAACI,IAAI,KAAKD,SAAS,GAAG,IAAI,CAACC,IAAI,GAAGJ,IAAI,CAACI,IAAI,GAAG,IAAI;IACtDJ,IAAI,CAACK,OAAO,KAAKF,SAAS,GAAG,IAAI,CAACE,OAAO,GAAGL,IAAI,CAACK,OAAO,GAAG,IAAI;IAC/DL,IAAI,CAACM,UAAU,KAAKH,SAAS,GAAG,IAAI,CAACG,UAAU,GAAGN,IAAI,CAACM,UAAU,GAAG,IAAI;IACxEN,IAAI,CAACO,WAAW,KAAKJ,SAAS,GAAG,IAAI,CAACI,WAAW,GAAGP,IAAI,CAACO,WAAW,GAAG,IAAI;IAC3EP,IAAI,CAACQ,IAAI,KAAKL,SAAS,GAAG,IAAI,CAACK,IAAI,GAAGR,IAAI,CAACQ,IAAI,GAAG,IAAI;IACtDR,IAAI,CAACS,SAAS,KAAKN,SAAS,GAAG,IAAI,CAACM,SAAS,GAAGT,IAAI,CAACS,SAAS,GAAG,IAAI;IACrET,IAAI,CAACU,YAAY,KAAKP,SAAS,GAAG,IAAI,CAACO,YAAY,GAAGV,IAAI,CAACU,YAAY,GAAG,IAAI;IAC9EV,IAAI,CAACW,MAAM,KAAKR,SAAS,GAAG,IAAI,CAACQ,MAAM,GAAGX,IAAI,CAACW,MAAM,GAAG,IAAI;IAC5DX,IAAI,CAAC,UAAU,CAAC,KAAKG,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAGH,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI;IAC3EA,IAAI,CAACY,WAAW,KAAKT,SAAS,GAAG,IAAI,CAACS,WAAW,GAAGZ,IAAI,CAACY,WAAW,GAAG,IAAI;IAC3EZ,IAAI,CAACa,GAAG,KAAKV,SAAS,GAAG,IAAI,CAACU,GAAG,GAAGb,IAAI,CAACa,GAAG,GAAG,IAAI;IACnDb,IAAI,CAACc,eAAe,KAAKX,SAAS,GAAG,IAAI,CAACW,eAAe,GAAGd,IAAI,CAACc,eAAe,GAAG,IAAI;IACvFd,IAAI,CAACe,WAAW,KAAKZ,SAAS,GAAG,IAAI,CAACY,WAAW,GAAGf,IAAI,CAACe,WAAW,GAAG,IAAI;IAC3Ef,IAAI,CAACgB,eAAe,KAAKb,SAAS,GAAG,IAAI,CAACa,eAAe,GAAGhB,IAAI,CAACgB,eAAe,GAAG,IAAI;IACvFhB,IAAI,CAACiB,iBAAiB,KAAKd,SAAS,GAAG,IAAI,CAACc,iBAAiB,GAAGjB,IAAI,CAACiB,iBAAiB,GAAG,IAAI;EACjG;EAACC,YAAA,CAAAnB,WAAA;IAAAoB,GAAA;IAAAC,KAAA,EAED,SAAAC,YAAYC,GAAG,EAAE;MACb,IAAIC,IAAI,GAAGD,GAAG,CAACA,GAAG,CAAC,aAAa,CAAC;MACjC,IAAI,CAACjB,OAAO,KAAKF,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACnB,OAAO,CAAC,GAAG,IAAI;MACrE,IAAI,CAACC,UAAU,KAAKH,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAClB,UAAU,CAAC,GAAG,IAAI;MAC9E,IAAI,CAACC,WAAW,KAAKJ,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACjB,WAAW,CAAC,GAAG,IAAI;MACjF,IAAI,CAACC,IAAI,KAAKL,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAChB,IAAI,CAAC,GAAG,IAAI;MAC5D,IAAI,CAACC,SAAS,KAAKN,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAACf,SAAS,CAAC,GAAG,IAAI;MAC3E,IAAI,CAACE,MAAM,KAAKR,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACb,MAAM,CAAC,GAAG,IAAI;MAClE,IAAI,CAACD,YAAY,KAAKP,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACd,YAAY,CAAC,GAAG,IAAI;MACpF,IAAI,CAACN,IAAI,KAAKD,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACpB,IAAI,CAAC,GAAG,IAAI;MAC5D,IAAI,CAAC,UAAU,CAAC,KAAKD,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI;MAC9E,IAAI,CAACZ,WAAW,KAAKT,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACZ,WAAW,CAAC,GAAG,IAAI;MACjF,IAAI,CAACC,GAAG,KAAKV,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,KAAK,EAAE,IAAI,CAACX,GAAG,CAAC,GAAG,IAAI;MACzD,IAAI,CAACC,eAAe,KAAKX,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACV,eAAe,CAAC,GAAG,IAAI;MAC7F,IAAI,CAACC,WAAW,KAAKZ,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACT,WAAW,CAAC,GAAG,IAAI;MACjF,IAAI,CAACC,eAAe,KAAKb,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACR,eAAe,CAAC,GAAG,IAAI;MAC7F,IAAI,CAACC,iBAAiB,KAAKd,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACP,iBAAiB,CAAC,GAAG,IAAI;MAEnG,IAAI,CAACf,UAAU,KAAKC,SAAS,GAAGoB,IAAI,CAACE,IAAI,CAAC,IAAI,CAACvB,UAAU,CAAC,GAAG,IAAI;IACrE;EAAC;EAAA,OAAAH,WAAA;AAAA;AAAA,IAIC2B,qBAAqB;EAAG;EAC1B,SAAAA,sBAAA,EAAc;IAAAzB,eAAA,OAAAyB,qBAAA;IACV,IAAI,CAACC,KAAK,GAAG,EAAE;EACnB;EAACT,YAAA,CAAAQ,qBAAA;IAAAP,GAAA;IAAAS,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,OAAO,IAAI,CAACD,KAAK,CAACE,MAAM;IAC5B;EAAC;IAAAV,GAAA;IAAAS,GAAA,EAED,SAAAA,IAAA,EAAc;MACV,IAAI,IAAI,CAACD,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;QACzB,OAAO,IAAI;MACf,CAAC,MAAM;QACH,OAAO,KAAK;MAChB;IACJ;EAAC;IAAAV,GAAA;IAAAC,KAAA,EAED,SAAAU,eAAe9B,IAAI,EAAE;MACjB,IAAI+B,IAAI,GAAG,IAAIhC,WAAW,CAACC,IAAI,CAAC;MAChC,IAAIgC,SAAS,GAAG,IAAI,CAACL,KAAK,CAACM,IAAI,CAACF,IAAI,CAAC;MACrC,OAAO,IAAI,CAACJ,KAAK,CAACK,SAAS,GAAG,CAAC,CAAC;IACpC;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAAC,YAAYC,GAAG,EAAE;MACb,IAAIY,KAAK,GAAGZ,GAAG,CAACA,GAAG,CAAC,cAAc,CAAC;MACnC,IAAI,CAACK,KAAK,CAACQ,OAAO,CAAC,UAACC,EAAE,EAAK;QACvBA,EAAE,CAACf,WAAW,CAACa,KAAK,CAAC;MACzB,CAAC,CAAC;IACN;EAAC;EAAA,OAAAR,qBAAA;AAAA;AAELW,MAAM,CAACC,OAAO,GAAGZ,qBAAqB"}