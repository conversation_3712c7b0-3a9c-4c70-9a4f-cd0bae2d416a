{"version": 3, "file": "TeacherDashboard-D8R1Yo_8.js", "sources": ["../../src/components/Dashboard/TeacherDashboard.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Teacher Dashboard Component\n *\n * Comprehensive teacher dashboard with grade entry, student progress tracking,\n * and class management features for Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  Button,\n  Avatar,\n  Chip,\n  LinearProgress,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Stack,\n  Alert,\n  Fab,\n  useTheme,\n  alpha,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Badge,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  Divider,\n} from '@mui/material';\nimport {\n  School,\n  People,\n  Assessment,\n  TrendingUp,\n  Add,\n  Edit,\n  Visibility,\n  NotificationImportant,\n  CalendarToday,\n  Assignment,\n  Grade,\n  EmojiEvents,\n  Warning,\n  Notifications,\n  Search,\n  Schedule,\n  ArrowForward,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { <PERSON>, Doughnut, Bar, Pie } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  Tooltip as ChartTooltip,\n  Legend,\n  Filler,\n} from 'chart.js';\nimport { useTranslation } from 'react-i18next';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  ChartTooltip,\n  Legend,\n  Filler\n);\n\n// Sample teacher data with Indian context\nconst teacherData = {\n  name: 'Mrs. Priya Sharma',\n  employeeId: 'TCH001',\n  subjects: ['Mathematics', 'Physics'],\n  classes: [\n    { grade: 10, section: 'A', students: 35, board: 'CBSE' },\n    { grade: 10, section: 'B', students: 32, board: 'CBSE' },\n    { grade: 9, section: 'A', students: 38, board: 'CBSE' },\n  ],\n  totalStudents: 105,\n  pendingGrades: 12,\n  upcomingTests: 3,\n  recentActivity: [\n    { type: 'grade', message: 'Graded Mathematics test for Class 10-A', time: '2 hours ago' },\n    { type: 'attendance', message: 'Marked attendance for Class 9-A', time: '4 hours ago' },\n    { type: 'swot', message: 'Updated SWOT analysis for Sanju Reddy', time: '1 day ago' },\n  ],\n};\n\n// Sample student performance data with Indian names\nconst studentPerformanceData = [\n  { id: 1, name: 'Sanju Kumar Reddy', class: '10-A', subject: 'Mathematics', lastGrade: 'A1', attendance: 95, status: 'Excellent' },\n  { id: 2, name: 'Niraimathi Selvam', class: '10-A', subject: 'Mathematics', lastGrade: 'A2', attendance: 92, status: 'Good' },\n  { id: 3, name: 'Mahesh Reddy', class: '10-B', subject: 'Mathematics', lastGrade: 'B1', attendance: 88, status: 'Average' },\n  { id: 4, name: 'Ravi Teja Sharma', class: '9-A', subject: 'Physics', lastGrade: 'A1', attendance: 94, status: 'Excellent' },\n  { id: 5, name: 'Ankitha Patel', class: '10-A', subject: 'Mathematics', lastGrade: 'A2', attendance: 90, status: 'Good' },\n  { id: 6, name: 'Sirisha Nair', class: '10-B', subject: 'Physics', lastGrade: 'A1', attendance: 96, status: 'Excellent' },\n  { id: 7, name: 'Priya Agarwal', class: '9-A', subject: 'Mathematics', lastGrade: 'B2', attendance: 85, status: 'Average' },\n];\n\n// Class Overview Card Component\nconst ClassOverviewCard = ({ classData, loading }) => {\n  const theme = useTheme();\n\n  if (loading) {\n    return (\n      <Card sx={{ height: '100%' }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Class Overview</Typography>\n          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n            <LinearProgress />\n            <LinearProgress />\n            <LinearProgress />\n          </Box>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Class Overview\n        </Typography>\n        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Students:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>{classData.totalStudents}</Typography>\n          </Box>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Avg GPA:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>\n              {classData.avgGPA}\n            </Typography>\n          </Box>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Attendance:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.success.main }}>\n              {classData.attendance}%\n            </Typography>\n          </Box>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Performance Distribution Chart Component\nconst PerformanceDistributionCard = ({ performanceData, loading }) => {\n  if (loading) {\n    return (\n      <Card sx={{ height: '100%' }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Performance Distribution</Typography>\n          <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            <LinearProgress sx={{ width: '80%' }} />\n          </Box>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const chartData = {\n    labels: ['A', 'B', 'C', 'D', 'F'],\n    datasets: [\n      {\n        label: 'Students',\n        data: [performanceData.A, performanceData.B, performanceData.C, performanceData.D, performanceData.F],\n        backgroundColor: [\n          '#4CAF50',\n          '#2196F3',\n          '#FF9800',\n          '#FF5722',\n          '#F44336'\n        ],\n        borderWidth: 0,\n        borderRadius: 4,\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false,\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: {\n          display: false,\n        },\n      },\n      x: {\n        grid: {\n          display: false,\n        },\n      },\n    },\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Performance Distribution\n        </Typography>\n        <Box sx={{ height: 200 }}>\n          <Bar data={chartData} options={chartOptions} />\n        </Box>\n        <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>\n          {Object.entries(performanceData).map(([grade, count]) => (\n            <Box key={grade} sx={{ textAlign: 'center' }}>\n              <Typography variant=\"body2\" color=\"text.secondary\">{grade}:</Typography>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>{count}</Typography>\n            </Box>\n          ))}\n        </Box>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Attendance Calendar Heat Map Component\nconst AttendanceCalendarCard = ({ attendanceData, loading }) => {\n  if (loading) {\n    return (\n      <Card sx={{ height: '100%' }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Attendance</Typography>\n          <Box sx={{ height: 150, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            <LinearProgress sx={{ width: '80%' }} />\n          </Box>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  // Simplified calendar view for demo\n  const calendarDays = Array.from({ length: 30 }, (_, i) => ({\n    day: i + 1,\n    status: Math.random() > 0.1 ? 'present' : Math.random() > 0.5 ? 'absent' : 'late'\n  }));\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'present': return '#4CAF50';\n      case 'absent': return '#F44336';\n      case 'late': return '#FF9800';\n      default: return '#E0E0E0';\n    }\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Attendance\n        </Typography>\n        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 1, mb: 2 }}>\n          {calendarDays.slice(0, 21).map((day) => (\n            <Box\n              key={day.day}\n              sx={{\n                width: 24,\n                height: 24,\n                borderRadius: 1,\n                backgroundColor: getStatusColor(day.status),\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '0.75rem',\n                color: 'white',\n                fontWeight: 500\n              }}\n            >\n              {day.day}\n            </Box>\n          ))}\n        </Box>\n        <Box sx={{ display: 'flex', gap: 2, fontSize: '0.75rem' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n            <Box sx={{ width: 12, height: 12, backgroundColor: '#4CAF50', borderRadius: 1 }} />\n            <Typography variant=\"caption\">Present</Typography>\n          </Box>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n            <Box sx={{ width: 12, height: 12, backgroundColor: '#FF9800', borderRadius: 1 }} />\n            <Typography variant=\"caption\">Late</Typography>\n          </Box>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n            <Box sx={{ width: 12, height: 12, backgroundColor: '#F44336', borderRadius: 1 }} />\n            <Typography variant=\"caption\">Absent</Typography>\n          </Box>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Behavior Incidents Card Component\nconst BehaviorIncidentsCard = ({ behaviorData, loading }) => {\n  if (loading) {\n    return (\n      <Card sx={{ height: '100%' }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Behavior Incidents</Typography>\n          <LinearProgress />\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const chartData = {\n    labels: ['Positive', 'Negative'],\n    datasets: [\n      {\n        data: [behaviorData.positive, behaviorData.negative],\n        backgroundColor: ['#4CAF50', '#FF5722'],\n        borderWidth: 0,\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n      },\n    },\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Behavior Incidents\n        </Typography>\n        <Box sx={{ height: 150 }}>\n          <Pie data={chartData} options={chartOptions} />\n        </Box>\n        <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>\n          <Box sx={{ textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Positive:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#4CAF50' }}>\n              {behaviorData.positive}\n            </Typography>\n          </Box>\n          <Box sx={{ textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Negative:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#FF5722' }}>\n              {behaviorData.negative}\n            </Typography>\n          </Box>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Students Requiring Attention Component\nconst StudentsRequiringAttention = ({ studentsData, loading }) => {\n  if (loading) {\n    return (\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Students Requiring Attention</Typography>\n          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n            <LinearProgress />\n            <LinearProgress />\n            <LinearProgress />\n          </Box>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Students Requiring Attention\n        </Typography>\n        <List>\n          {studentsData.map((student, index) => (\n            <React.Fragment key={student.id}>\n              <ListItem\n                sx={{\n                  px: 0,\n                  '&:hover': {\n                    backgroundColor: alpha('#000', 0.04),\n                    borderRadius: 1,\n                  },\n                }}\n              >\n                <ListItemAvatar>\n                  <Avatar sx={{ bgcolor: student.severity === 'high' ? 'error.main' : 'warning.main' }}>\n                    {student.severity === 'high' ? <Warning /> : <Schedule />}\n                  </Avatar>\n                </ListItemAvatar>\n                <ListItemText\n                  primary={\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                      {student.name}\n                    </Typography>\n                  }\n                  secondary={student.reason}\n                />\n                <Button\n                  size=\"small\"\n                  endIcon={<ArrowForward />}\n                  sx={{ textTransform: 'none' }}\n                >\n                  View\n                </Button>\n              </ListItem>\n              {index < studentsData.length - 1 && <Divider />}\n            </React.Fragment>\n          ))}\n        </List>\n      </CardContent>\n    </Card>\n  );\n};\n\nconst TeacherDashboard = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { t } = useTranslation(['common', 'dashboard']);\n  const [selectedClass, setSelectedClass] = useState(teacherData.classes[0]);\n  const [loading, setLoading] = useState(false);\n\n  // Chart data for class performance\n  const classPerformanceData = {\n    labels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],\n    datasets: [\n      {\n        label: 'Grade Distribution',\n        data: [12, 15, 8, 6, 3, 1],\n        backgroundColor: [\n          theme.palette.success.main,\n          theme.palette.success.light,\n          theme.palette.info.main,\n          theme.palette.info.light,\n          theme.palette.warning.main,\n          theme.palette.error.main,\n        ],\n        borderWidth: 2,\n        borderColor: '#fff',\n      },\n    ],\n  };\n\n  // Attendance trend data\n  const attendanceTrendData = {\n    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n    datasets: [\n      {\n        label: 'Class 10-A',\n        data: [95, 92, 94, 96, 93, 89],\n        borderColor: theme.palette.primary.main,\n        backgroundColor: alpha(theme.palette.primary.main, 0.1),\n        fill: true,\n        tension: 0.4,\n      },\n      {\n        label: 'Class 10-B',\n        data: [88, 90, 87, 91, 89, 85],\n        borderColor: theme.palette.secondary.main,\n        backgroundColor: alpha(theme.palette.secondary.main, 0.1),\n        fill: true,\n        tension: 0.4,\n      },\n    ],\n  };\n\n  const handleGradeEntry = () => {\n    navigate('/dashboard/grades');\n  };\n\n  const handleAttendance = () => {\n    navigate('/dashboard/attendance');\n  };\n\n  const handleStudentProfile = (studentId) => {\n    navigate(`/dashboard/students/${studentId}`);\n  };\n\n  const handleSWOTAnalysis = (studentId) => {\n    navigate(`/dashboard/students/${studentId}/swot`);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Excellent':\n        return 'success';\n      case 'Good':\n        return 'info';\n      case 'Average':\n        return 'warning';\n      case 'Needs Attention':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Teacher Dashboard\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Welcome back, {teacherData.name}! Manage your classes and track student progress.\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Quick Stats */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n          >\n            <Card\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'transform 0.2s ease',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                },\n              }}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                      {teacherData.totalStudents}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      Total Students\n                    </Typography>\n                  </Box>\n                  <People sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            <Card\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'transform 0.2s ease',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                },\n              }}\n              onClick={handleGradeEntry}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                      {teacherData.pendingGrades}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      Pending Grades\n                    </Typography>\n                  </Box>\n                  <Grade sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n          >\n            <Card\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'transform 0.2s ease',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                },\n              }}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                      {teacherData.upcomingTests}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      Upcoming Tests\n                    </Typography>\n                  </Box>\n                  <Assignment sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            <Card\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'transform 0.2s ease',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                },\n              }}\n              onClick={handleAttendance}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                      92%\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      Avg Attendance\n                    </Typography>\n                  </Box>\n                  <CalendarToday sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </Grid>\n      </Grid>\n\n      {/* Quick Actions */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Quick Actions\n          </Typography>\n          <Stack direction=\"row\" spacing={2} flexWrap=\"wrap\" useFlexGap>\n            <Button\n              variant=\"contained\"\n              startIcon={<Grade />}\n              onClick={handleGradeEntry}\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              }}\n            >\n              Enter Grades\n            </Button>\n            <Button\n              variant=\"outlined\"\n              startIcon={<CalendarToday />}\n              onClick={handleAttendance}\n            >\n              Mark Attendance\n            </Button>\n            <Button\n              variant=\"outlined\"\n              startIcon={<Assessment />}\n              onClick={() => navigate('/dashboard/swot/wizard')}\n            >\n              SWOT Analysis\n            </Button>\n            <Button\n              variant=\"outlined\"\n              startIcon={<Assignment />}\n              onClick={() => navigate('/dashboard/reports/generate')}\n            >\n              Generate Reports\n            </Button>\n            <Button\n              variant=\"outlined\"\n              startIcon={<Add />}\n              onClick={() => navigate('/dashboard/students/register')}\n            >\n              Add Student\n            </Button>\n          </Stack>\n        </CardContent>\n      </Card>\n\n      {/* Charts and Analytics */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={8}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Weekly Attendance Trends\n              </Typography>\n              <Box sx={{ height: 300 }}>\n                <Line\n                  data={attendanceTrendData}\n                  options={{\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        position: 'top',\n                      },\n                    },\n                    scales: {\n                      y: {\n                        beginAtZero: false,\n                        min: 80,\n                        max: 100,\n                      },\n                    },\n                  }}\n                />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Grade Distribution\n              </Typography>\n              <Box sx={{ height: 300 }}>\n                <Doughnut\n                  data={classPerformanceData}\n                  options={{\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        position: 'bottom',\n                      },\n                    },\n                  }}\n                />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Student Performance Table */}\n      <Card>\n        <CardContent>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n              Recent Student Performance\n            </Typography>\n            <Button\n              variant=\"outlined\"\n              size=\"small\"\n              onClick={() => navigate('/dashboard/students')}\n            >\n              View All Students\n            </Button>\n          </Box>\n\n          <TableContainer component={Paper} variant=\"outlined\">\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Student Name</TableCell>\n                  <TableCell>Class</TableCell>\n                  <TableCell>Subject</TableCell>\n                  <TableCell>Last Grade</TableCell>\n                  <TableCell>Attendance</TableCell>\n                  <TableCell>Status</TableCell>\n                  <TableCell>Actions</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {studentPerformanceData.map((student) => (\n                  <TableRow key={student.id} hover>\n                    <TableCell>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                        <Avatar sx={{ width: 32, height: 32 }}>\n                          {student.name.charAt(0)}\n                        </Avatar>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {student.name}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>{student.class}</TableCell>\n                    <TableCell>{student.subject}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={student.lastGrade}\n                        color={student.lastGrade.startsWith('A') ? 'success' : 'warning'}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        <LinearProgress\n                          variant=\"determinate\"\n                          value={student.attendance}\n                          sx={{ width: 60, height: 6, borderRadius: 3 }}\n                          color={student.attendance >= 90 ? 'success' : student.attendance >= 75 ? 'warning' : 'error'}\n                        />\n                        <Typography variant=\"body2\">{student.attendance}%</Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={student.status}\n                        color={getStatusColor(student.status)}\n                        size=\"small\"\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Stack direction=\"row\" spacing={1}>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleStudentProfile(student.id)}\n                          color=\"primary\"\n                        >\n                          <Visibility />\n                        </IconButton>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleSWOTAnalysis(student.id)}\n                          color=\"secondary\"\n                        >\n                          <Assessment />\n                        </IconButton>\n                      </Stack>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </CardContent>\n      </Card>\n\n      {/* Floating Action Button */}\n      <Fab\n        color=\"primary\"\n        sx={{\n          position: 'fixed',\n          bottom: 24,\n          right: 24,\n          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n        }}\n        onClick={() => navigate('/dashboard/students/register')}\n      >\n        <Add />\n      </Fab>\n    </Box>\n  );\n};\n\nexport default TeacherDashboard;\n"], "names": ["ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "ArcElement", "Title", "ChartTooltip", "Legend", "Filler", "teacher<PERSON><PERSON>", "grade", "section", "students", "board", "studentPerformanceData", "id", "name", "class", "subject", "lastGrade", "attendance", "status", "TeacherDashboard", "theme", "useTheme", "navigate", "useNavigate", "t", "useTranslation", "selectedClass", "setSelectedClass", "useState", "loading", "setLoading", "classPerformanceData", "labels", "datasets", "label", "data", "backgroundColor", "palette", "success", "main", "light", "info", "warning", "error", "borderWidth", "borderColor", "attendanceTrendData", "primary", "alpha", "fill", "tension", "secondary", "handleGradeEntry", "handleAttendance", "getStatusColor", "Box", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "jsxRuntimeExports", "jsx", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "mb", "Typography", "variant", "fontWeight", "background", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "jsxs", "color", "Grid", "container", "spacing", "item", "xs", "sm", "md", "delay", "Card", "dark", "cursor", "transform", "<PERSON><PERSON><PERSON><PERSON>", "display", "alignItems", "justifyContent", "People", "fontSize", "onClick", "Grade", "Assignment", "CalendarToday", "<PERSON><PERSON>", "direction", "flexWrap", "useFlexGap", "<PERSON><PERSON>", "startIcon", "Assessment", "Add", "height", "Line", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "scales", "beginAtZero", "min", "max", "Doughnut", "size", "TableContainer", "component", "Paper", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "map", "student", "hover", "gap", "Avatar", "width", "char<PERSON>t", "Chip", "startsWith", "LinearProgress", "value", "borderRadius", "IconButton", "handleStudentProfile", "studentId", "Visibility", "handleSWOTAnalysis", "Fab", "bottom", "right"], "mappings": "ylBAgFAA,EAAQC,SACNC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAIF,MAAMC,EACE,oBADFA,EAIK,CACP,CAAEC,MAAO,GAAIC,QAAS,IAAKC,SAAU,GAAIC,MAAO,QAChD,CAAEH,MAAO,GAAIC,QAAS,IAAKC,SAAU,GAAIC,MAAO,QAChD,CAAEH,MAAO,EAAGC,QAAS,IAAKC,SAAU,GAAIC,MAAO,SAP7CJ,EASW,IATXA,EAUW,GAVXA,EAWW,EASXK,EAAyB,CAC7B,CAAEC,GAAI,EAAGC,KAAM,oBAAqBC,MAAO,OAAQC,QAAS,cAAeC,UAAW,KAAMC,WAAY,GAAIC,OAAQ,aACpH,CAAEN,GAAI,EAAGC,KAAM,oBAAqBC,MAAO,OAAQC,QAAS,cAAeC,UAAW,KAAMC,WAAY,GAAIC,OAAQ,QACpH,CAAEN,GAAI,EAAGC,KAAM,eAAgBC,MAAO,OAAQC,QAAS,cAAeC,UAAW,KAAMC,WAAY,GAAIC,OAAQ,WAC/G,CAAEN,GAAI,EAAGC,KAAM,mBAAoBC,MAAO,MAAOC,QAAS,UAAWC,UAAW,KAAMC,WAAY,GAAIC,OAAQ,aAC9G,CAAEN,GAAI,EAAGC,KAAM,gBAAiBC,MAAO,OAAQC,QAAS,cAAeC,UAAW,KAAMC,WAAY,GAAIC,OAAQ,QAChH,CAAEN,GAAI,EAAGC,KAAM,eAAgBC,MAAO,OAAQC,QAAS,UAAWC,UAAW,KAAMC,WAAY,GAAIC,OAAQ,aAC3G,CAAEN,GAAI,EAAGC,KAAM,gBAAiBC,MAAO,MAAOC,QAAS,cAAeC,UAAW,KAAMC,WAAY,GAAIC,OAAQ,YA6U3GC,EAAmB,KACvB,MAAMC,EAAQC,IACRC,EAAWC,KACXC,EAAEA,GAAMC,EAAe,CAAC,SAAU,eACjCC,EAAeC,GAAoBC,WAAStB,EAAoB,KAChEuB,EAASC,GAAcF,EAAAA,UAAS,GAGjCG,EAAuB,CAC3BC,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,MACvCC,SAAU,CACR,CACEC,MAAO,qBACPC,KAAM,CAAC,GAAI,GAAI,EAAG,EAAG,EAAG,GACxBC,gBAAiB,CACfhB,EAAMiB,QAAQC,QAAQC,KACtBnB,EAAMiB,QAAQC,QAAQE,MACtBpB,EAAMiB,QAAQI,KAAKF,KACnBnB,EAAMiB,QAAQI,KAAKD,MACnBpB,EAAMiB,QAAQK,QAAQH,KACtBnB,EAAMiB,QAAQM,MAAMJ,MAEtBK,YAAa,EACbC,YAAa,UAMbC,EAAsB,CAC1Bd,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,OAC5CC,SAAU,CACR,CACEC,MAAO,aACPC,KAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAC3BU,YAAazB,EAAMiB,QAAQU,QAAQR,KACnCH,gBAAiBY,EAAM5B,EAAMiB,QAAQU,QAAQR,KAAM,IACnDU,MAAM,EACNC,QAAS,IAEX,CACEhB,MAAO,aACPC,KAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAC3BU,YAAazB,EAAMiB,QAAQc,UAAUZ,KACrCH,gBAAiBY,EAAM5B,EAAMiB,QAAQc,UAAUZ,KAAM,IACrDU,MAAM,EACNC,QAAS,MAKTE,EAAmB,KACvB9B,EAAS,oBAAmB,EAGxB+B,EAAmB,KACvB/B,EAAS,wBAAuB,EAW5BgC,EAAkBpC,IACtB,OAAQA,GACN,IAAK,YACI,MAAA,UACT,IAAK,OACI,MAAA,OACT,IAAK,UACI,MAAA,UACT,IAAK,kBACI,MAAA,QACT,QACS,MAAA,UAAA,EAKX,cAACqC,EAAI,CAAAC,GAAI,CAAEC,SAAU,KAAMC,GAAI,OAAQC,EAAG,GAExCC,SAAA,CAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,GAAO,IAC9BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAExBV,gBAACL,EAAI,CAAAC,GAAI,CAAEe,GAAI,GACbX,SAAA,CAAAC,EAAAC,IAACU,EAAA,CACCC,QAAQ,KACRjB,GAAI,CACFkB,WAAY,IACZH,GAAI,EACJI,WAAY,2BAA2BvD,EAAMiB,QAAQU,QAAQR,YAAYnB,EAAMiB,QAAQc,UAAUZ,aACjGqC,qBAAsB,OACtBC,oBAAqB,cACrBC,eAAgB,QAEnBlB,SAAA,sBAGAmB,EAAAA,KAAAP,EAAA,CAAWC,QAAQ,QAAQO,MAAM,iBAAiBpB,SAAA,CAAA,iBAClCtD,EAAiB,4DAMtCyE,EAAAA,KAACE,EAAK,CAAAC,WAAS,EAACC,QAAS,EAAG3B,GAAI,CAAEe,GAAI,GACpCX,SAAA,CAACE,EAAAA,IAAAmB,EAAA,CAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAC5B3B,SAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKkB,MAAO,IAEpC5B,SAAAC,EAAAC,IAAC2B,EAAA,CACCjC,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMiB,QAAQU,QAAQR,YAAYnB,EAAMiB,QAAQU,QAAQ2C,aAC/FV,MAAO,QACPW,OAAQ,UACRtB,WAAY,sBACZ,UAAW,CACTuB,UAAW,qBAIfhC,SAACC,EAAAC,IAAA+B,EAAA,CACCjC,WAACmB,KAAAxB,EAAA,CAAIC,GAAI,CAAEsC,QAAS,OAAQC,WAAY,SAAUC,eAAgB,iBAChEpC,SAAA,QAACL,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAAtD,IAEHwD,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,sBAEFE,MAACmC,GAAOzC,GAAI,CAAE0C,SAAU,GAAIhC,QAAS,iBAO/CJ,EAAAA,IAACmB,GAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAC5B3B,SAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKkB,MAAO,IAEpC5B,SAAAC,EAAAC,IAAC2B,EAAA,CACCjC,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMiB,QAAQC,QAAQC,YAAYnB,EAAMiB,QAAQC,QAAQoD,aAC/FV,MAAO,QACPW,OAAQ,UACRtB,WAAY,sBACZ,UAAW,CACTuB,UAAW,qBAGfO,QAAS/C,EAETQ,SAACC,EAAAC,IAAA+B,EAAA,CACCjC,WAACmB,KAAAxB,EAAA,CAAIC,GAAI,CAAEsC,QAAS,OAAQC,WAAY,SAAUC,eAAgB,iBAChEpC,SAAA,QAACL,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAAtD,IAEHwD,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,sBAEFE,MAACsC,GAAM5C,GAAI,CAAE0C,SAAU,GAAIhC,QAAS,iBAO9CJ,EAAAA,IAACmB,GAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAC5B3B,SAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKkB,MAAO,IAEpC5B,SAAAC,EAAAC,IAAC2B,EAAA,CACCjC,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMiB,QAAQK,QAAQH,YAAYnB,EAAMiB,QAAQK,QAAQgD,aAC/FV,MAAO,QACPW,OAAQ,UACRtB,WAAY,sBACZ,UAAW,CACTuB,UAAW,qBAIfhC,SAACC,EAAAC,IAAA+B,EAAA,CACCjC,WAACmB,KAAAxB,EAAA,CAAIC,GAAI,CAAEsC,QAAS,OAAQC,WAAY,SAAUC,eAAgB,iBAChEpC,SAAA,QAACL,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAAtD,IAEHwD,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,sBAEFE,MAACuC,GAAW7C,GAAI,CAAE0C,SAAU,GAAIhC,QAAS,iBAOnDJ,EAAAA,IAACmB,GAAKG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAC5B3B,SAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKkB,MAAO,IAEpC5B,SAAAC,EAAAC,IAAC2B,EAAA,CACCjC,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMiB,QAAQI,KAAKF,YAAYnB,EAAMiB,QAAQI,KAAKiD,aACzFV,MAAO,QACPW,OAAQ,UACRtB,WAAY,sBACZ,UAAW,CACTuB,UAAW,qBAGfO,QAAS9C,EAETO,SAACC,EAAAC,IAAA+B,EAAA,CACCjC,WAACmB,KAAAxB,EAAA,CAAIC,GAAI,CAAEsC,QAAS,OAAQC,WAAY,SAAUC,eAAgB,iBAChEpC,SAAA,QAACL,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KAAOd,SAElD,QACAE,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,sBAEFE,MAACwC,GAAc9C,GAAI,CAAE0C,SAAU,GAAIhC,QAAS,oBASxDJ,EAAAA,IAAC2B,GAAKjC,GAAI,CAAEe,GAAI,GACdX,gBAACiC,EACC,CAAAjC,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKM,MAAO,gBAAkBpB,SAEhF,kBACAmB,EAAAA,KAACwB,GAAMC,UAAU,MAAMrB,QAAS,EAAGsB,SAAS,OAAOC,YAAU,EAC3D9C,SAAA,CAAAC,EAAAC,IAAC6C,EAAA,CACClC,QAAQ,YACRmC,gBAAYR,EAAM,IAClBD,QAAS/C,EACTI,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMiB,QAAQU,QAAQR,YAAYnB,EAAMiB,QAAQc,UAAUZ,cAEpGqB,SAAA,iBAGDC,EAAAC,IAAC6C,EAAA,CACClC,QAAQ,WACRmC,gBAAYN,EAAc,IAC1BH,QAAS9C,EACVO,SAAA,oBAGDC,EAAAC,IAAC6C,EAAA,CACClC,QAAQ,WACRmC,gBAAYC,EAAW,IACvBV,QAAS,IAAM7E,EAAS,0BACzBsC,SAAA,kBAGDC,EAAAC,IAAC6C,EAAA,CACClC,QAAQ,WACRmC,gBAAYP,EAAW,IACvBF,QAAS,IAAM7E,EAAS,+BACzBsC,SAAA,qBAGDC,EAAAC,IAAC6C,EAAA,CACClC,QAAQ,WACRmC,gBAAYE,EAAI,IAChBX,QAAS,IAAM7E,EAAS,gCACzBsC,SAAA,wBAQPmB,EAAAA,KAACE,EAAK,CAAAC,WAAS,EAACC,QAAS,EAAG3B,GAAI,CAAEe,GAAI,GACpCX,SAAA,CAACE,EAAAA,IAAAmB,EAAA,CAAKG,MAAI,EAACC,GAAI,GAAIE,GAAI,EACrB3B,SAAAE,EAAAA,IAAC2B,EACC,CAAA7B,WAAAmB,KAACc,EACC,CAAAjC,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKM,MAAO,gBAAkBpB,SAEhF,mCACCL,EAAI,CAAAC,GAAI,CAAEuD,OAAQ,KACjBnD,SAAAC,EAAAC,IAACkD,EAAA,CACC7E,KAAMW,EACNmE,QAAS,CACPC,YAAY,EACZC,qBAAqB,EACrBC,QAAS,CACPC,OAAQ,CACNC,SAAU,QAGdC,OAAQ,CACNpD,EAAG,CACDqD,aAAa,EACbC,IAAK,GACLC,IAAK,kBAUrB5D,EAAAA,IAACmB,EAAK,CAAAG,MAAI,EAACC,GAAI,GAAIE,GAAI,EACrB3B,SAAAE,EAAAA,IAAC2B,EACC,CAAA7B,WAAAmB,KAACc,EACC,CAAAjC,SAAA,GAAAE,IAACU,EAAW,CAAAC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKM,MAAO,gBAAkBpB,SAEhF,6BACCL,EAAI,CAAAC,GAAI,CAAEuD,OAAQ,KACjBnD,SAAAC,EAAAC,IAAC6D,EAAA,CACCxF,KAAMJ,EACNkF,QAAS,CACPC,YAAY,EACZC,qBAAqB,EACrBC,QAAS,CACPC,OAAQ,CACNC,SAAU,0BAY5BxD,EAAAA,IAAC2B,EACC,CAAA7B,SAAAmB,EAAAA,KAACc,EACC,CAAAjC,SAAA,CAACmB,EAAAA,KAAAxB,EAAA,CAAIC,GAAI,CAAEsC,QAAS,OAAQE,eAAgB,gBAAiBD,WAAY,SAAUxB,GAAI,GACrFX,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,IAAKM,MAAO,gBAAkBpB,SAEzE,+BACAC,EAAAC,IAAC6C,EAAA,CACClC,QAAQ,WACRmD,KAAK,QACLzB,QAAS,IAAM7E,EAAS,uBACzBsC,SAAA,+BAKFiE,EAAe,CAAAC,UAAWC,EAAOtD,QAAQ,WACxCb,gBAACoE,EACC,CAAApE,SAAA,CAACE,EAAAA,IAAAmE,EAAA,CACCrE,gBAACsE,EACC,CAAAtE,SAAA,GAAAE,IAACqE,GAAUvE,SAAY,mBACvBE,IAACqE,GAAUvE,SAAK,YAChBE,IAACqE,GAAUvE,SAAO,cAClBE,IAACqE,GAAUvE,SAAU,iBACrBE,IAACqE,GAAUvE,SAAU,iBACrBE,IAACqE,GAAUvE,SAAM,aACjBE,IAACqE,GAAUvE,SAAO,iBAGtBE,EAAAA,IAACsE,GACExE,SAAuBjD,EAAA0H,KAAKC,KAC3BvD,KAACmD,EAA0B,CAAAK,OAAK,EAC9B3E,SAAA,GAACE,IAAAqE,EAAA,CACCvE,SAACmB,EAAAA,KAAAxB,EAAA,CAAIC,GAAI,CAAEsC,QAAS,OAAQC,WAAY,SAAUyC,IAAK,GACrD5E,SAAA,GAAAE,IAAC2E,EAAO,CAAAjF,GAAI,CAAEkF,MAAO,GAAI3B,OAAQ,IAC9BnD,SAAQ0E,EAAAzH,KAAK8H,OAAO,KAEvB7E,EAAAA,IAACU,EAAW,CAAAC,QAAQ,QAAQjB,GAAI,CAAEkB,WAAY,KAC3Cd,SAAA0E,EAAQzH,cAIfiD,IAACqE,EAAW,CAAAvE,SAAA0E,EAAQxH,UACpBgD,IAACqE,EAAW,CAAAvE,SAAA0E,EAAQvH,gBACnBoH,EACC,CAAAvE,SAAAC,EAAAC,IAAC8E,EAAA,CACC1G,MAAOoG,EAAQtH,UACfgE,MAAOsD,EAAQtH,UAAU6H,WAAW,KAAO,UAAY,UACvDjB,KAAK,cAGR9D,IAAAqE,EAAA,CACCvE,SAACmB,EAAAA,KAAAxB,EAAA,CAAIC,GAAI,CAAEsC,QAAS,OAAQC,WAAY,SAAUyC,IAAK,GACrD5E,SAAA,CAAAC,EAAAC,IAACgF,EAAA,CACCrE,QAAQ,cACRsE,MAAOT,EAAQrH,WACfuC,GAAI,CAAEkF,MAAO,GAAI3B,OAAQ,EAAGiC,aAAc,GAC1ChE,MAAOsD,EAAQrH,YAAc,GAAK,UAAYqH,EAAQrH,YAAc,GAAK,UAAY,YAEvF8D,KAACP,EAAW,CAAAC,QAAQ,QAASb,SAAA,CAAQ0E,EAAArH,WAAW,kBAGnDkH,EACC,CAAAvE,SAAAC,EAAAC,IAAC8E,EAAA,CACC1G,MAAOoG,EAAQpH,OACf8D,MAAO1B,EAAegF,EAAQpH,QAC9B0G,KAAK,QACLnD,QAAQ,qBAGX0D,EACC,CAAAvE,SAAAmB,OAACwB,GAAMC,UAAU,MAAMrB,QAAS,EAC9BvB,SAAA,CAAAC,EAAAC,IAACmF,EAAA,CACCrB,KAAK,QACLzB,QAAS,KAAM+C,OArXTC,EAqX8Bb,EAAQ1H,QApXzDU,EAAA,uBAAuB6H,KADL,IAACA,CAqXwC,EAC9CnE,MAAM,UAENpB,eAACwF,EAAW,CAAA,KAEdvF,EAAAC,IAACmF,EAAA,CACCrB,KAAK,QACLzB,QAAS,KAAMkD,OAxXXF,EAwX8Bb,EAAQ1H,QAvXvDU,EAAA,uBAAuB6H,UADP,IAACA,CAwXwC,EAC5CnE,MAAM,YAENpB,eAACiD,EAAW,CAAA,YArDLyB,EAAQ1H,kBAkEnCiD,EAAAC,IAACwF,EAAA,CACCtE,MAAM,UACNxB,GAAI,CACF8D,SAAU,QACViC,OAAQ,GACRC,MAAO,GACP7E,WAAY,2BAA2BvD,EAAMiB,QAAQU,QAAQR,YAAYnB,EAAMiB,QAAQc,UAAUZ,cAEnG4D,QAAS,IAAM7E,EAAS,gCAExBsC,eAACkD,EAAI,CAAA,OAET"}