{"version": 3, "file": "mediaCollection.js", "names": ["fs", "require", "MediaCollection", "_classCallCheck", "items", "_createClass", "key", "value", "add", "item", "accessSync", "<PERSON>_<PERSON>", "push", "length", "get", "module", "exports"], "sources": ["../../../source/lib/workbook/mediaCollection.js"], "sourcesContent": ["const fs = require('fs');\n\nclass MediaCollection {\n    constructor() {\n        this.items = [];\n    }\n\n    add(item) {\n        if (typeof item === 'string') {\n            fs.accessSync(item, fs.R_OK);\n        }\n\n        this.items.push(item);\n        return this.items.length;\n    }\n\n    get isEmpty() {\n        if (this.items.length === 0) {\n            return true;\n        } else {\n            return false;\n        }\n    }\n}\n\nmodule.exports = MediaCollection;"], "mappings": ";;;;;AAAA,IAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;AAAC,IAEnBC,eAAe;EACjB,SAAAA,gBAAA,EAAc;IAAAC,eAAA,OAAAD,eAAA;IACV,IAAI,CAACE,KAAK,GAAG,EAAE;EACnB;EAACC,YAAA,CAAAH,eAAA;IAAAI,GAAA;IAAAC,KAAA,EAED,SAAAC,IAAIC,IAAI,EAAE;MACN,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC1BT,EAAE,CAACU,UAAU,CAACD,IAAI,EAAET,EAAE,CAACW,IAAI,CAAC;MAChC;MAEA,IAAI,CAACP,KAAK,CAACQ,IAAI,CAACH,IAAI,CAAC;MACrB,OAAO,IAAI,CAACL,KAAK,CAACS,MAAM;IAC5B;EAAC;IAAAP,GAAA;IAAAQ,GAAA,EAED,SAAAA,IAAA,EAAc;MACV,IAAI,IAAI,CAACV,KAAK,CAACS,MAAM,KAAK,CAAC,EAAE;QACzB,OAAO,IAAI;MACf,CAAC,MAAM;QACH,OAAO,KAAK;MAChB;IACJ;EAAC;EAAA,OAAAX,eAAA;AAAA;AAGLa,MAAM,CAACC,OAAO,GAAGd,eAAe"}