{"version": 3, "file": "paperSize.js", "names": ["items", "_this", "LETTER_PAPER", "LETTER_SMALL_PAPER", "TABLOID_PAPER", "LEDGER_PAPER", "LEGAL_PAPER", "STATEMENT_PAPER", "EXECUTIVE_PAPER", "A3_PAPER", "A4_PAPER", "A4_SMALL_PAPER", "A5_PAPER", "B4_PAPER", "B5_PAPER", "FOLIO_PAPER", "QUARTO_PAPER", "STANDARD_PAPER_10_BY_14_IN", "STANDARD_PAPER_11_BY_17_IN", "NOTE_PAPER", "NUMBER_9_ENVELOPE", "NUMBER_10_ENVELOPE", "NUMBER_11_ENVELOPE", "NUMBER_12_ENVELOPE", "NUMBER_14_ENVELOPE", "C_PAPER", "D_PAPER", "E_PAPER", "DL_PAPER", "C5_ENVELOPE", "C3_ENVELOPE", "C4_ENVELOPE", "C6_ENVELOPE", "C65_ENVELOPE", "B4_ENVELOPE", "B5_ENVELOPE", "B6_ENVELOPE", "ITALY_ENVELOPE", "MONARCH_ENVELOPE", "SIX_THREE_QUARTERS_ENVELOPE", "US_STANDARD_FANFOLD", "GERMAN_STANDARD_FANFOLD", "GERMAN_LEGAL_FANFOLD", "ISO_B4", "JAPANESE_DOUBLE_POSTCARD", "STANDARD_PAPER_9_BY_11_IN", "STANDARD_PAPER_10_BY_11_IN", "STANDARD_PAPER_15_BY_11_IN", "INVITE_ENVELOPE", "LETTER_EXTRA_PAPER", "LEGAL_EXTRA_PAPER", "TABLOID_EXTRA_PAPER", "A4_EXTRA_PAPER", "LETTER_TRANSVERSE_PAPER", "A4_TRANSVERSE_PAPER", "LETTER_EXTRA_TRANSVERSE_PAPER", "SUPER_A_SUPER_A_A4_PAPER", "SUPER_B_SUPER_B_A3_PAPER", "LETTER_PLUS_PAPER", "A4_PLUS_PAPER", "A5_TRANSVERSE_PAPER", "JIS_B5_TRANSVERSE_PAPER", "A3_EXTRA_PAPER", "A5_EXTRA_PAPER", "ISO_B5_EXTRA_PAPER", "A2_PAPER", "A3_TRANSVERSE_PAPER", "A3_EXTRA_TRANSVERSE_PAPER", "opts", "Object", "keys", "for<PERSON>ach", "k", "push", "prototype", "validate", "val", "toUpperCase", "undefined", "name", "hasOwnProperty", "TypeError", "join", "module", "exports"], "sources": ["../../../source/lib/types/paperSize.js"], "sourcesContent": ["function items() { // As defined in §18.3.1.63 pageSetup (Page Setup Settings)\n    this.LETTER_PAPER = 1; // Letter paper (8.5 in. by 11 in.)\n    this.LETTER_SMALL_PAPER = 2; // Letter small paper (8.5 in. by 11 in.)\n    this.TABLOID_PAPER = 3; // Tabloid paper (11 in. by 17 in.)\n    this.LEDGER_PAPER = 4; // Ledger paper (17 in. by 11 in.)\n    this.LEGAL_PAPER = 5; // Legal paper (8.5 in. by 14 in.)\n    this.STATEMENT_PAPER = 6; // Statement paper (5.5 in. by 8.5 in.)\n    this.EXECUTIVE_PAPER = 7; // Executive paper (7.25 in. by 10.5 in.)\n    this.A3_PAPER = 8; // A3 paper (297 mm by 420 mm)\n    this.A4_PAPER = 9; // A4 paper (210 mm by 297 mm)\n    this.A4_SMALL_PAPER = 10; // A4 small paper (210 mm by 297 mm)\n    this.A5_PAPER = 11; // A5 paper (148 mm by 210 mm)\n    this.B4_PAPER = 12; // B4 paper (250 mm by 353 mm)\n    this.B5_PAPER = 13; // B5 paper (176 mm by 250 mm)\n    this.FOLIO_PAPER = 14; // Folio paper (8.5 in. by 13 in.)\n    this.QUARTO_PAPER = 15; // Quarto paper (215 mm by 275 mm)\n    this.STANDARD_PAPER_10_BY_14_IN = 16; // Standard paper (10 in. by 14 in.)\n    this.STANDARD_PAPER_11_BY_17_IN = 17; // Standard paper (11 in. by 17 in.)\n    this.NOTE_PAPER = 18; // Note paper (8.5 in. by 11 in.)\n    this.NUMBER_9_ENVELOPE = 19; // #9 envelope (3.875 in. by 8.875 in.)\n    this.NUMBER_10_ENVELOPE = 20; // #10 envelope (4.125 in. by 9.5 in.)\n    this.NUMBER_11_ENVELOPE = 21; // #11 envelope (4.5 in. by 10.375 in.)\n    this.NUMBER_12_ENVELOPE = 22; // #12 envelope (4.75 in. by 11 in.)\n    this.NUMBER_14_ENVELOPE = 23; // #14 envelope (5 in. by 11.5 in.)\n    this.C_PAPER = 24; // C paper (17 in. by 22 in.)\n    this.D_PAPER = 25; // D paper (22 in. by 34 in.)\n    this.E_PAPER = 26; // E paper (34 in. by 44 in.)\n    this.DL_PAPER = 27; // DL envelope (110 mm by 220 mm)\n    this.C5_ENVELOPE = 28; // C5 envelope (162 mm by 229 mm)\n    this.C3_ENVELOPE = 29; // C3 envelope (324 mm by 458 mm)\n    this.C4_ENVELOPE = 30; // C4 envelope (229 mm by 324 mm)\n    this.C6_ENVELOPE = 31; // C6 envelope (114 mm by 162 mm)\n    this.C65_ENVELOPE = 32; // C65 envelope (114 mm by 229 mm)\n    this.B4_ENVELOPE = 33; // B4 envelope (250 mm by 353 mm)\n    this.B5_ENVELOPE = 34; // B5 envelope (176 mm by 250 mm)\n    this.B6_ENVELOPE = 35; // B6 envelope (176 mm by 125 mm)\n    this.ITALY_ENVELOPE = 36; // Italy envelope (110 mm by 230 mm)\n    this.MONARCH_ENVELOPE = 37; // Monarch envelope (3.875 in. by 7.5 in.).\n    this.SIX_THREE_QUARTERS_ENVELOPE = 38; // 6 3/4 envelope (3.625 in. by 6.5 in.)\n    this.US_STANDARD_FANFOLD = 39; // US standard fanfold (14.875 in. by 11 in.)\n    this.GERMAN_STANDARD_FANFOLD = 40; // German standard fanfold (8.5 in. by 12 in.)\n    this.GERMAN_LEGAL_FANFOLD = 41; // German legal fanfold (8.5 in. by 13 in.)\n    this.ISO_B4 = 42; // ISO B4 (250 mm by 353 mm)\n    this.JAPANESE_DOUBLE_POSTCARD = 43; // Japanese double postcard (200 mm by 148 mm)\n    this.STANDARD_PAPER_9_BY_11_IN = 44; // Standard paper (9 in. by 11 in.)\n    this.STANDARD_PAPER_10_BY_11_IN = 45; // Standard paper (10 in. by 11 in.)\n    this.STANDARD_PAPER_15_BY_11_IN = 46; // Standard paper (15 in. by 11 in.)\n    this.INVITE_ENVELOPE = 47; // Invite envelope (220 mm by 220 mm)\n    this.LETTER_EXTRA_PAPER = 50; // Letter extra paper (9.275 in. by 12 in.)\n    this.LEGAL_EXTRA_PAPER = 51; // Legal extra paper (9.275 in. by 15 in.)\n    this.TABLOID_EXTRA_PAPER = 52; // Tabloid extra paper (11.69 in. by 18 in.)\n    this.A4_EXTRA_PAPER = 53; // A4 extra paper (236 mm by 322 mm)\n    this.LETTER_TRANSVERSE_PAPER = 54; // Letter transverse paper (8.275 in. by 11 in.)\n    this.A4_TRANSVERSE_PAPER = 55; // A4 transverse paper (210 mm by 297 mm)\n    this.LETTER_EXTRA_TRANSVERSE_PAPER = 56; // Letter extra transverse paper (9.275 in. by 12 in.)\n    this.SUPER_A_SUPER_A_A4_PAPER = 57; // SuperA/SuperA/A4 paper (227 mm by 356 mm)\n    this.SUPER_B_SUPER_B_A3_PAPER = 58; // SuperB/SuperB/A3 paper (305 mm by 487 mm)\n    this.LETTER_PLUS_PAPER = 59; // Letter plus paper (8.5 in. by 12.69 in.)\n    this.A4_PLUS_PAPER = 60; // A4 plus paper (210 mm by 330 mm)\n    this.A5_TRANSVERSE_PAPER = 61; // A5 transverse paper (148 mm by 210 mm)\n    this.JIS_B5_TRANSVERSE_PAPER = 62; // JIS B5 transverse paper (182 mm by 257 mm)\n    this.A3_EXTRA_PAPER = 63; // A3 extra paper (322 mm by 445 mm)\n    this.A5_EXTRA_PAPER = 64; // A5 extra paper (174 mm by 235 mm)\n    this.ISO_B5_EXTRA_PAPER = 65; // ISO B5 extra paper (201 mm by 276 mm)\n    this.A2_PAPER = 66; // A2 paper (420 mm by 594 mm)\n    this.A3_TRANSVERSE_PAPER = 67; // A3 transverse paper (297 mm by 420 mm)\n    this.A3_EXTRA_TRANSVERSE_PAPER = 68; // A3 extra transverse paper (322 mm by 445 mm)\n\n    this.opts = [];\n    Object.keys(this).forEach((k) => {\n        if (typeof this[k] === 'number') {\n            this.opts.push(k);\n        }\n    });\n}\n\n\nitems.prototype.validate = function (val) {\n    if (this[val.toUpperCase()] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError('Invalid value for PAPER_SIZE; Value must be one of ' + this.opts.join(', '));\n    } else {\n        return true;\n    }\n};\n\nmodule.exports = new items();"], "mappings": ";;AAAA,SAASA,KAAKA,CAAA,EAAG;EAAA,IAAAC,KAAA;EAAE;EACf,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC;EACvB,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC,CAAC;EAC7B,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC,CAAC;EACxB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC;EACvB,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC,CAAC;EACtB,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC,CAAC;EAC1B,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC,CAAC;EAC1B,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,CAAC;EACnB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,CAAC;EACnB,IAAI,CAACC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC1B,IAAI,CAACC,QAAQ,GAAG,EAAE,CAAC,CAAC;EACpB,IAAI,CAACC,QAAQ,GAAG,EAAE,CAAC,CAAC;EACpB,IAAI,CAACC,QAAQ,GAAG,EAAE,CAAC,CAAC;EACpB,IAAI,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC;EACvB,IAAI,CAACC,YAAY,GAAG,EAAE,CAAC,CAAC;EACxB,IAAI,CAACC,0BAA0B,GAAG,EAAE,CAAC,CAAC;EACtC,IAAI,CAACC,0BAA0B,GAAG,EAAE,CAAC,CAAC;EACtC,IAAI,CAACC,UAAU,GAAG,EAAE,CAAC,CAAC;EACtB,IAAI,CAACC,iBAAiB,GAAG,EAAE,CAAC,CAAC;EAC7B,IAAI,CAACC,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC9B,IAAI,CAACC,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC9B,IAAI,CAACC,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC9B,IAAI,CAACC,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC9B,IAAI,CAACC,OAAO,GAAG,EAAE,CAAC,CAAC;EACnB,IAAI,CAACC,OAAO,GAAG,EAAE,CAAC,CAAC;EACnB,IAAI,CAACC,OAAO,GAAG,EAAE,CAAC,CAAC;EACnB,IAAI,CAACC,QAAQ,GAAG,EAAE,CAAC,CAAC;EACpB,IAAI,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC;EACvB,IAAI,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC;EACvB,IAAI,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC;EACvB,IAAI,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC;EACvB,IAAI,CAACC,YAAY,GAAG,EAAE,CAAC,CAAC;EACxB,IAAI,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC;EACvB,IAAI,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC;EACvB,IAAI,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC;EACvB,IAAI,CAACC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC1B,IAAI,CAACC,gBAAgB,GAAG,EAAE,CAAC,CAAC;EAC5B,IAAI,CAACC,2BAA2B,GAAG,EAAE,CAAC,CAAC;EACvC,IAAI,CAACC,mBAAmB,GAAG,EAAE,CAAC,CAAC;EAC/B,IAAI,CAACC,uBAAuB,GAAG,EAAE,CAAC,CAAC;EACnC,IAAI,CAACC,oBAAoB,GAAG,EAAE,CAAC,CAAC;EAChC,IAAI,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC;EAClB,IAAI,CAACC,wBAAwB,GAAG,EAAE,CAAC,CAAC;EACpC,IAAI,CAACC,yBAAyB,GAAG,EAAE,CAAC,CAAC;EACrC,IAAI,CAACC,0BAA0B,GAAG,EAAE,CAAC,CAAC;EACtC,IAAI,CAACC,0BAA0B,GAAG,EAAE,CAAC,CAAC;EACtC,IAAI,CAACC,eAAe,GAAG,EAAE,CAAC,CAAC;EAC3B,IAAI,CAACC,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC9B,IAAI,CAACC,iBAAiB,GAAG,EAAE,CAAC,CAAC;EAC7B,IAAI,CAACC,mBAAmB,GAAG,EAAE,CAAC,CAAC;EAC/B,IAAI,CAACC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC1B,IAAI,CAACC,uBAAuB,GAAG,EAAE,CAAC,CAAC;EACnC,IAAI,CAACC,mBAAmB,GAAG,EAAE,CAAC,CAAC;EAC/B,IAAI,CAACC,6BAA6B,GAAG,EAAE,CAAC,CAAC;EACzC,IAAI,CAACC,wBAAwB,GAAG,EAAE,CAAC,CAAC;EACpC,IAAI,CAACC,wBAAwB,GAAG,EAAE,CAAC,CAAC;EACpC,IAAI,CAACC,iBAAiB,GAAG,EAAE,CAAC,CAAC;EAC7B,IAAI,CAACC,aAAa,GAAG,EAAE,CAAC,CAAC;EACzB,IAAI,CAACC,mBAAmB,GAAG,EAAE,CAAC,CAAC;EAC/B,IAAI,CAACC,uBAAuB,GAAG,EAAE,CAAC,CAAC;EACnC,IAAI,CAACC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC1B,IAAI,CAACC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC9B,IAAI,CAACC,QAAQ,GAAG,EAAE,CAAC,CAAC;EACpB,IAAI,CAACC,mBAAmB,GAAG,EAAE,CAAC,CAAC;EAC/B,IAAI,CAACC,yBAAyB,GAAG,EAAE,CAAC,CAAC;;EAErC,IAAI,CAACC,IAAI,GAAG,EAAE;EACdC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAACC,CAAC,EAAK;IAC7B,IAAI,OAAOvE,KAAI,CAACuE,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC7BvE,KAAI,CAACmE,IAAI,CAACK,IAAI,CAACD,CAAC,CAAC;IACrB;EACJ,CAAC,CAAC;AACN;AAGAxE,KAAK,CAAC0E,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACtC,IAAI,IAAI,CAACA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;IACvC,IAAIV,IAAI,GAAG,EAAE;IACb,KAAK,IAAIW,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BX,IAAI,CAACK,IAAI,CAACM,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIE,SAAS,CAAC,qDAAqD,GAAG,IAAI,CAACb,IAAI,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC;EACrG,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,IAAIpF,KAAK,CAAC,CAAC"}