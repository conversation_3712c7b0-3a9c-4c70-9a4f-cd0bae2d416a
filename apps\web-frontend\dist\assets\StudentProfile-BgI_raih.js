import{u as e,j as s,B as a,L as i,_ as n,e as r,d as t,f as o,A as c,h as l,l as d,m as x,I as h,i as m,q as j,r as p,G as g}from"./mui-core-BBO2DoRL.js";import{r as y}from"./vendor-CeOqOr8o.js";import{e as b,f as u,g as v,C as f,a as A,L as w,P as W,b as k,R as S,B as P,p as C,c as I,d as $,i as N}from"./charts-chartjs-Dl1vZNhv.js";import{u as B,a as O}from"./routing-B6PnZiBG.js";import{m as R}from"./animation-BJm6nf7i.js";import{d as T,x as M,o as G,v as D,u as H,m as E,b as z,a6 as J,C as L,z as F,y as Z,E as K}from"./mui-icons-BXhTkfAe.js";import"./utils-misc-NFmYzBmQ.js";f.register(A,w,W,k,S,P,C,I,$,N);const U={id:1,firstName:"Sanju",middleName:"Kumar",lastName:"Reddy",admissionNumber:"VMS2024001",grade:10,section:"A",rollNumber:15,board:"CBSE",dateOfBirth:"2008-05-15",gender:"Male",bloodGroup:"B+",profilePhoto:null,address:"H.No 12-34, Jubilee Hills, Hyderabad",city:"Hyderabad",state:"Telangana",pincode:"500033",phone:"+91 **********",email:"<EMAIL>",fatherName:"Rajesh Kumar Reddy",fatherOccupation:"Software Engineer",fatherPhone:"+91 **********",motherName:"Priya Reddy",motherOccupation:"Teacher",motherPhone:"+91 **********",currentGPA:8.7,attendance:92,subjects:[{name:"Mathematics",grade:"A1",marks:95,teacher:"Mrs. Sharma"},{name:"Science",grade:"A1",marks:92,teacher:"Mr. Patel"},{name:"English",grade:"A2",marks:88,teacher:"Ms. Johnson"},{name:"Hindi",grade:"A1",marks:94,teacher:"Mrs. Gupta"},{name:"Social Studies",grade:"A2",marks:86,teacher:"Mr. Singh"},{name:"Telugu",grade:"A1",marks:96,teacher:"Mrs. Rao"}],performanceTrends:[{month:"Apr",gpa:8.2},{month:"May",gpa:8.4},{month:"Jun",gpa:8.6},{month:"Jul",gpa:8.5},{month:"Aug",gpa:8.7},{month:"Sep",gpa:8.8}],swotAnalysis:{strengths:["Strong in Mathematics","Good leadership skills","Excellent attendance"],weaknesses:["Needs improvement in English writing","Shy in group discussions"],opportunities:["Science Olympiad participation","Student council elections"],threats:["Increased competition","Time management challenges"]},achievements:[{title:"Mathematics Olympiad - District Level",date:"2024-03-15",type:"Academic"},{title:"Best Student of the Month",date:"2024-02-28",type:"Behavioral"},{title:"Science Fair - First Prize",date:"2024-01-20",type:"Academic"}],behavioralScores:{discipline:9,teamwork:8,leadership:9,creativity:7,communication:6,responsibility:9}},q=()=>{const g=e(),b=B(),{studentId:u}=O(),[v,f]=y.useState(0),[A,w]=y.useState(null),[W,k]=y.useState(!0);y.useEffect((()=>{(async()=>{k(!0);try{await new Promise((e=>setTimeout(e,1e3))),w(U)}catch(e){}finally{k(!1)}})()}),[u]);if(W)return s.jsx(a,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:400},children:s.jsx(i,{sx:{width:300}})});if(!A)return s.jsx(n,{severity:"error",children:"Student not found. Please check the student ID and try again."});const S={labels:A.performanceTrends.map((e=>e.month)),datasets:[{label:"GPA Trend",data:A.performanceTrends.map((e=>e.gpa)),borderColor:g.palette.primary.main,backgroundColor:r(g.palette.primary.main,.1),fill:!0,tension:.4}]},P={labels:Object.keys(A.behavioralScores).map((e=>e.charAt(0).toUpperCase()+e.slice(1))),datasets:[{label:"Behavioral Assessment",data:Object.values(A.behavioralScores),borderColor:g.palette.secondary.main,backgroundColor:r(g.palette.secondary.main,.2),pointBackgroundColor:g.palette.secondary.main,pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:g.palette.secondary.main}]},C={labels:A.subjects.map((e=>e.name)),datasets:[{label:"Marks",data:A.subjects.map((e=>e.marks)),backgroundColor:A.subjects.map(((e,s)=>`hsl(${60*s%360}, 70%, 60%)`)),borderColor:A.subjects.map(((e,s)=>`hsl(${60*s%360}, 70%, 50%)`)),borderWidth:2}]};return s.jsxs(a,{sx:{maxWidth:1400,mx:"auto",p:3},children:[s.jsx(R.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsx(t,{sx:{mb:3,overflow:"visible"},children:s.jsx(o,{sx:{p:4},children:s.jsxs(a,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:3},children:[s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:3},children:[s.jsx(c,{src:A.profilePhoto,sx:{width:120,height:120,border:`4px solid ${g.palette.primary.main}`,background:`linear-gradient(135deg, ${g.palette.primary.main} 0%, ${g.palette.secondary.main} 100%)`},children:s.jsx(T,{sx:{fontSize:60}})}),s.jsxs(a,{children:[s.jsxs(l,{variant:"h4",sx:{fontWeight:600,mb:1},children:[A.firstName," ",A.middleName," ",A.lastName]}),s.jsxs(l,{variant:"h6",color:"text.secondary",sx:{mb:1},children:["Class ",A.grade," - Section ",A.section]}),s.jsxs(l,{variant:"body1",color:"text.secondary",sx:{mb:2},children:["Admission No: ",A.admissionNumber," | Roll No: ",A.rollNumber]}),s.jsxs(d,{direction:"row",spacing:1,sx:{mb:2},children:[s.jsx(x,{label:A.board,color:"primary",variant:"filled",sx:{fontWeight:500}}),s.jsx(x,{label:`GPA: ${A.currentGPA}`,color:"success",variant:"outlined",icon:s.jsx(M,{})}),s.jsx(x,{label:`${A.attendance}% Attendance`,color:A.attendance>=90?"success":A.attendance>=75?"warning":"error",variant:"outlined"})]})]})]}),s.jsxs(d,{direction:"row",spacing:1,children:[s.jsx(h,{onClick:()=>{b(`/dashboard/students/${u}/edit`)},color:"primary",children:s.jsx(G,{})}),s.jsx(h,{color:"primary",children:s.jsx(D,{})}),s.jsx(h,{color:"primary",children:s.jsx(H,{})}),s.jsx(m,{variant:"contained",startIcon:s.jsx(E,{}),onClick:()=>{b(`/dashboard/students/${u}/swot`)},sx:{background:`linear-gradient(135deg, ${g.palette.primary.main} 0%, ${g.palette.secondary.main} 100%)`},children:"SWOT Analysis"})]})]})})})}),s.jsx(t,{sx:{mb:3},children:s.jsxs(j,{value:v,onChange:(e,s)=>{f(s)},variant:"scrollable",scrollButtons:"auto",sx:{"& .MuiTab-root":{minHeight:64,fontWeight:500}},children:[s.jsx(p,{icon:s.jsx(T,{}),label:"Personal Info"}),s.jsx(p,{icon:s.jsx(z,{}),label:"Academic Performance"}),s.jsx(p,{icon:s.jsx(E,{}),label:"SWOT Analysis"}),s.jsx(p,{icon:s.jsx(J,{}),label:"Achievements"})]})}),s.jsxs(R.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:[0===v&&s.jsx(V,{student:A}),1===v&&s.jsx(Y,{student:A,performanceChartData:S,subjectPerformanceData:C,behavioralRadarData:P}),2===v&&s.jsx(_,{student:A}),3===v&&s.jsx(Q,{student:A})]},v)]})},V=({student:i})=>(e(),s.jsxs(g,{container:!0,spacing:3,children:[s.jsx(g,{item:!0,xs:12,md:6,children:s.jsx(t,{children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Basic Information"}),s.jsxs(d,{spacing:2,children:[s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(L,{color:"action"}),s.jsx(l,{variant:"body2",color:"text.secondary",children:"Date of Birth:"}),s.jsx(l,{variant:"body2",children:i.dateOfBirth})]}),s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(T,{color:"action"}),s.jsx(l,{variant:"body2",color:"text.secondary",children:"Gender:"}),s.jsx(l,{variant:"body2",children:i.gender})]}),s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(l,{variant:"body2",color:"text.secondary",children:"Blood Group:"}),s.jsx(l,{variant:"body2",children:i.bloodGroup})]})]})]})})}),s.jsx(g,{item:!0,xs:12,md:6,children:s.jsx(t,{children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Contact Information"}),s.jsxs(d,{spacing:2,children:[s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(F,{color:"action"}),s.jsx(l,{variant:"body2",color:"text.secondary",children:"Address:"}),s.jsx(l,{variant:"body2",children:i.address})]}),s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(Z,{color:"action"}),s.jsx(l,{variant:"body2",color:"text.secondary",children:"Phone:"}),s.jsx(l,{variant:"body2",children:i.phone})]}),s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(K,{color:"action"}),s.jsx(l,{variant:"body2",color:"text.secondary",children:"Email:"}),s.jsx(l,{variant:"body2",children:i.email})]})]})]})})}),s.jsx(g,{item:!0,xs:12,children:s.jsx(t,{children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Parent/Guardian Information"}),s.jsxs(g,{container:!0,spacing:3,children:[s.jsxs(g,{item:!0,xs:12,md:6,children:[s.jsx(l,{variant:"subtitle2",sx:{mb:1,fontWeight:500},children:"Father's Details"}),s.jsxs(d,{spacing:1,children:[s.jsxs(l,{variant:"body2",children:["Name: ",i.fatherName]}),s.jsxs(l,{variant:"body2",children:["Occupation: ",i.fatherOccupation]}),s.jsxs(l,{variant:"body2",children:["Phone: ",i.fatherPhone]})]})]}),s.jsxs(g,{item:!0,xs:12,md:6,children:[s.jsx(l,{variant:"subtitle2",sx:{mb:1,fontWeight:500},children:"Mother's Details"}),s.jsxs(d,{spacing:1,children:[s.jsxs(l,{variant:"body2",children:["Name: ",i.motherName]}),s.jsxs(l,{variant:"body2",children:["Occupation: ",i.motherOccupation]}),s.jsxs(l,{variant:"body2",children:["Phone: ",i.motherPhone]})]})]})]})]})})})]})),Y=({student:e,performanceChartData:i,subjectPerformanceData:n,behavioralRadarData:r})=>s.jsxs(g,{container:!0,spacing:3,children:[s.jsx(g,{item:!0,xs:12,md:4,children:s.jsx(t,{children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Performance Overview"}),s.jsxs(d,{spacing:2,children:[s.jsxs(a,{children:[s.jsx(l,{variant:"body2",color:"text.secondary",children:"Current GPA"}),s.jsx(l,{variant:"h4",color:"primary.main",sx:{fontWeight:600},children:e.currentGPA})]}),s.jsxs(a,{children:[s.jsx(l,{variant:"body2",color:"text.secondary",children:"Attendance"}),s.jsxs(l,{variant:"h4",color:"success.main",sx:{fontWeight:600},children:[e.attendance,"%"]})]}),s.jsxs(a,{children:[s.jsx(l,{variant:"body2",color:"text.secondary",children:"Class Rank"}),s.jsx(l,{variant:"h4",color:"secondary.main",sx:{fontWeight:600},children:"3rd"})]})]})]})})}),s.jsx(g,{item:!0,xs:12,md:8,children:s.jsx(t,{children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"GPA Trend"}),s.jsx(a,{sx:{height:300},children:s.jsx(b,{data:i,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!1,min:7,max:10}}}})})]})})}),s.jsx(g,{item:!0,xs:12,md:8,children:s.jsx(t,{children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Subject Performance"}),s.jsx(a,{sx:{height:300},children:s.jsx(u,{data:n,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,max:100}}}})})]})})}),s.jsx(g,{item:!0,xs:12,md:4,children:s.jsx(t,{children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Behavioral Assessment"}),s.jsx(a,{sx:{height:300},children:s.jsx(v,{data:r,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{r:{beginAtZero:!0,max:10}}}})})]})})}),s.jsx(g,{item:!0,xs:12,children:s.jsx(t,{children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Subject Details"}),s.jsx(g,{container:!0,spacing:2,children:e.subjects.map(((e,i)=>s.jsx(g,{item:!0,xs:12,sm:6,md:4,children:s.jsx(t,{variant:"outlined",children:s.jsxs(o,{sx:{p:2},children:[s.jsx(l,{variant:"subtitle1",sx:{fontWeight:600},children:e.name}),s.jsxs(l,{variant:"body2",color:"text.secondary",sx:{mb:1},children:["Teacher: ",e.teacher]}),s.jsxs(a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s.jsx(x,{label:e.grade,color:e.grade.startsWith("A")?"success":"warning",size:"small"}),s.jsxs(l,{variant:"h6",sx:{fontWeight:600},children:[e.marks,"%"]})]})]})})},i)))})]})})})]}),_=({student:i})=>{const n=e(),c=[{title:"Strengths",items:i.swotAnalysis.strengths,color:n.palette.success.main,icon:"💪"},{title:"Weaknesses",items:i.swotAnalysis.weaknesses,color:n.palette.error.main,icon:"⚠️"},{title:"Opportunities",items:i.swotAnalysis.opportunities,color:n.palette.info.main,icon:"🚀"},{title:"Threats",items:i.swotAnalysis.threats,color:n.palette.warning.main,icon:"⚡"}];return s.jsxs(a,{children:[s.jsxs(a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[s.jsx(l,{variant:"h5",sx:{fontWeight:600},children:"SWOT Analysis Overview"}),s.jsx(m,{variant:"contained",startIcon:s.jsx(E,{}),sx:{background:`linear-gradient(135deg, ${n.palette.primary.main} 0%, ${n.palette.secondary.main} 100%)`},children:"Update SWOT"})]}),s.jsx(g,{container:!0,spacing:3,children:c.map(((e,i)=>s.jsx(g,{item:!0,xs:12,md:6,children:s.jsx(t,{sx:{height:"100%",border:`2px solid ${r(e.color,.2)}`,background:`linear-gradient(135deg, ${r(e.color,.05)} 0%, ${r(e.color,.02)} 100%)`},children:s.jsxs(o,{children:[s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[s.jsx(l,{variant:"h2",sx:{fontSize:24},children:e.icon}),s.jsx(l,{variant:"h6",sx:{fontWeight:600,color:e.color},children:e.title})]}),s.jsx(d,{spacing:1,children:e.items.map(((i,n)=>s.jsx(a,{sx:{p:2,borderRadius:1,background:r(e.color,.1),border:`1px solid ${r(e.color,.2)}`},children:s.jsx(l,{variant:"body2",children:i})},n)))})]})})},i)))}),s.jsx(t,{sx:{mt:3},children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"SWOT Matrix"}),s.jsxs(g,{container:!0,spacing:2,children:[s.jsx(g,{item:!0,xs:6,children:s.jsxs(a,{sx:{p:2,border:`2px solid ${n.palette.success.main}`,borderRadius:1,background:r(n.palette.success.main,.05),minHeight:150},children:[s.jsx(l,{variant:"subtitle1",sx:{fontWeight:600,color:"success.main",mb:1},children:"Strengths (Internal Positive)"}),i.swotAnalysis.strengths.map(((e,a)=>s.jsxs(l,{variant:"body2",sx:{mb:.5},children:["• ",e]},a)))]})}),s.jsx(g,{item:!0,xs:6,children:s.jsxs(a,{sx:{p:2,border:`2px solid ${n.palette.error.main}`,borderRadius:1,background:r(n.palette.error.main,.05),minHeight:150},children:[s.jsx(l,{variant:"subtitle1",sx:{fontWeight:600,color:"error.main",mb:1},children:"Weaknesses (Internal Negative)"}),i.swotAnalysis.weaknesses.map(((e,a)=>s.jsxs(l,{variant:"body2",sx:{mb:.5},children:["• ",e]},a)))]})}),s.jsx(g,{item:!0,xs:6,children:s.jsxs(a,{sx:{p:2,border:`2px solid ${n.palette.info.main}`,borderRadius:1,background:r(n.palette.info.main,.05),minHeight:150},children:[s.jsx(l,{variant:"subtitle1",sx:{fontWeight:600,color:"info.main",mb:1},children:"Opportunities (External Positive)"}),i.swotAnalysis.opportunities.map(((e,a)=>s.jsxs(l,{variant:"body2",sx:{mb:.5},children:["• ",e]},a)))]})}),s.jsx(g,{item:!0,xs:6,children:s.jsxs(a,{sx:{p:2,border:`2px solid ${n.palette.warning.main}`,borderRadius:1,background:r(n.palette.warning.main,.05),minHeight:150},children:[s.jsx(l,{variant:"subtitle1",sx:{fontWeight:600,color:"warning.main",mb:1},children:"Threats (External Negative)"}),i.swotAnalysis.threats.map(((e,a)=>s.jsxs(l,{variant:"body2",sx:{mb:.5},children:["• ",e]},a)))]})})]})]})})]})},Q=({student:i})=>{const n=e(),c=e=>{switch(e){case"Academic":return s.jsx(z,{sx:{color:n.palette.primary.main}});case"Behavioral":return s.jsx(J,{sx:{color:n.palette.secondary.main}});default:return s.jsx(M,{sx:{color:n.palette.warning.main}})}},d=e=>{switch(e){case"Academic":return n.palette.primary.main;case"Behavioral":return n.palette.secondary.main;default:return n.palette.warning.main}};return s.jsxs(a,{children:[s.jsx(l,{variant:"h5",sx:{fontWeight:600,mb:3},children:"Achievements & Recognition"}),s.jsx(g,{container:!0,spacing:3,children:i.achievements.map(((e,i)=>s.jsx(g,{item:!0,xs:12,md:6,children:s.jsx(R.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*i},children:s.jsx(t,{sx:{border:`2px solid ${r(d(e.type),.2)}`,background:`linear-gradient(135deg, ${r(d(e.type),.05)} 0%, ${r(d(e.type),.02)} 100%)`,transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},children:s.jsxs(o,{children:[s.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[c(e.type),s.jsxs(a,{children:[s.jsx(l,{variant:"h6",sx:{fontWeight:600},children:e.title}),s.jsx(l,{variant:"body2",color:"text.secondary",children:new Date(e.date).toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})})]})]}),s.jsx(x,{label:e.type,size:"small",sx:{backgroundColor:d(e.type),color:"white",fontWeight:500}})]})})})},i)))}),s.jsx(t,{sx:{mt:3},children:s.jsxs(o,{children:[s.jsx(l,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Achievement Statistics"}),s.jsxs(g,{container:!0,spacing:3,children:[s.jsx(g,{item:!0,xs:12,md:4,children:s.jsxs(a,{sx:{textAlign:"center"},children:[s.jsx(l,{variant:"h3",color:"primary.main",sx:{fontWeight:600},children:i.achievements.filter((e=>"Academic"===e.type)).length}),s.jsx(l,{variant:"body1",color:"text.secondary",children:"Academic Awards"})]})}),s.jsx(g,{item:!0,xs:12,md:4,children:s.jsxs(a,{sx:{textAlign:"center"},children:[s.jsx(l,{variant:"h3",color:"secondary.main",sx:{fontWeight:600},children:i.achievements.filter((e=>"Behavioral"===e.type)).length}),s.jsx(l,{variant:"body1",color:"text.secondary",children:"Behavioral Recognition"})]})}),s.jsx(g,{item:!0,xs:12,md:4,children:s.jsxs(a,{sx:{textAlign:"center"},children:[s.jsx(l,{variant:"h3",color:"warning.main",sx:{fontWeight:600},children:i.achievements.length}),s.jsx(l,{variant:"body1",color:"text.secondary",children:"Total Achievements"})]})})]})]})})]})};export{q as default};
//# sourceMappingURL=StudentProfile-BgI_raih.js.map
