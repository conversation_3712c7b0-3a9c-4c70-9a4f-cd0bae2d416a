{"version": 3, "file": "PDFDocument.js", "sourceRoot": "", "sources": ["../../src/api/PDFDocument.ts"], "names": [], "mappings": ";AACA,OAAO,EACL,iBAAiB,EACjB,yBAAyB,EACzB,gBAAgB,EAChB,gCAAgC,GACjC,iBAAuB;AACxB,OAAO,eAAe,0BAAgC;AACtD,OAAO,OAAO,kBAAwB;AACtC,OAAO,QAAQ,mBAAyB;AACxC,OAAO,OAAO,kBAAwB;AACtC,OAAO,OAAO,uBAA6B;AAC3C,OAAO,EAAE,SAAS,EAAE,gBAAsB;AAE1C,OAAO,EACL,kBAAkB,EAClB,wBAAwB,EACxB,YAAY,EAEZ,mCAAmC,EACnC,UAAU,EACV,UAAU,EACV,OAAO,EACP,YAAY,EACZ,OAAO,EACP,eAAe,EACf,eAAe,EACf,WAAW,EACX,WAAW,EACX,SAAS,EACT,eAAe,EACf,SAAS,EACT,SAAS,EACT,WAAW,EACX,oBAAoB,EACpB,yBAAyB,GAC1B,gBAAiB;AAClB,OAAO,EACL,WAAW,GAQZ,6BAAmC;AAKpC,OAAO,EACL,QAAQ,EACR,wBAAwB,EACxB,iBAAiB,EACjB,WAAW,EACX,KAAK,EACL,0BAA0B,EAC1B,cAAc,EACd,cAAc,EACd,YAAY,EACZ,KAAK,EACL,YAAY,GACb,iBAAkB;AACnB,OAAO,YAAY,EAAE,EAAE,cAAc,EAAE,uCAAwC;AAC/E,OAAO,eAAe,0BAAgC;AACtD,OAAO,aAAa,wBAA8B;AAClD,OAAO,kBAAkB,6CAA8C;AAEvE;;GAEG;AACH;IAuHE,qBACE,OAAmB,EACnB,gBAAyB,EACzB,cAAuB;QAHzB,iBAwBC;QAtCD,uDAAuD;QACvD,sBAAiB,GAAa,CAAC,GAAG,CAAC,CAAC;QAiqC5B,iBAAY,GAAG;YACrB,IAAM,KAAK,GAAc,EAAE,CAAC;YAC5B,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,QAAQ,CAAC,UAAC,IAAI,EAAE,GAAG;gBACtC,IAAI,IAAI,YAAY,WAAW,EAAE;oBAC/B,IAAI,IAAI,GAAG,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAClC,IAAI,CAAC,IAAI,EAAE;wBACT,IAAI,GAAG,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,KAAI,CAAC,CAAC;wBACnC,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;qBAC9B;oBACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAClB;YACH,CAAC,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;QAEM,oBAAe,GAAG;YACxB,IAAM,QAAQ,GAAG,KAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YACpD,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAI,CAAC,CAAC;QACpC,CAAC,CAAC;QAjqCA,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;QAC3D,QAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;QAE5D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAe,CAAC;QACtE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEjE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACzD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QAEtB,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,iBAAiB,EAAE,CAAC;QAEzE,IAAI,cAAc;YAAE,IAAI,CAAC,cAAc,EAAE,CAAC;IAC5C,CAAC;IA9ID;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmDG;IACU,gBAAI,GAAjB,UACE,GAAsC,EACtC,OAAyB;QAAzB,wBAAA,EAAA,YAAyB;;;;;;wBAGvB,KAKE,OAAO,iBALe,EAAxB,gBAAgB,mBAAG,KAAK,KAAA,EACxB,KAIE,OAAO,WAJoB,EAA7B,UAAU,mBAAG,WAAW,CAAC,IAAI,KAAA,EAC7B,KAGE,OAAO,qBAHmB,EAA5B,oBAAoB,mBAAG,KAAK,KAAA,EAC5B,KAEE,OAAO,eAFY,EAArB,cAAc,mBAAG,IAAI,KAAA,EACrB,KACE,OAAO,WADS,EAAlB,UAAU,mBAAG,KAAK,KAAA,CACR;wBAEZ,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;wBAC1D,QAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;wBAC5D,QAAQ,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;wBAC/C,QAAQ,CAAC,oBAAoB,EAAE,sBAAsB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;wBAE9D,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;wBAChB,qBAAM,SAAS,CAAC,mBAAmB,CACjD,KAAK,EACL,UAAU,EACV,oBAAoB,EACpB,UAAU,CACX,CAAC,aAAa,EAAE,EAAA;;wBALX,OAAO,GAAG,SAKC;wBACjB,sBAAO,IAAI,WAAW,CAAC,OAAO,EAAE,gBAAgB,EAAE,cAAc,CAAC,EAAC;;;;KACnE;IAED;;;OAGG;IACU,kBAAM,GAAnB,UAAoB,OAA2B;QAA3B,wBAAA,EAAA,YAA2B;;;;gBACrC,KAA0B,OAAO,eAAZ,EAArB,cAAc,mBAAG,IAAI,KAAA,CAAa;gBAEpC,OAAO,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;gBAC9B,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAC5C,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACzC,OAAO,GAAG,UAAU,CAAC,mBAAmB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;gBACrE,OAAO,CAAC,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAErD,sBAAO,IAAI,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,cAAc,CAAC,EAAC;;;KACxD;IAmDD;;;;;;;;;;;;;;;;;OAiBG;IACH,qCAAe,GAAf,UAAgB,OAAgB;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,6BAAO,GAAP;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QACrC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,CAAC,IAAI,CACV,2EAA2E,CAC5E,CAAC;YACF,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;OAOG;IACH,8BAAQ,GAAR;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,KAAK;YAAE,OAAO,SAAS,CAAC;QAC7B,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAClC,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC5B,CAAC;IAED;;;;;;;OAOG;IACH,+BAAS,GAAT;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM;YAAE,OAAO,SAAS,CAAC;QAC9B,0BAA0B,CAAC,MAAM,CAAC,CAAC;QACnC,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACH,gCAAU,GAAV;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO;YAAE,OAAO,SAAS,CAAC;QAC/B,0BAA0B,CAAC,OAAO,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACH,iCAAW,GAAX;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ;YAAE,OAAO,SAAS,CAAC;QAChC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;QACrC,OAAO,QAAQ,CAAC,UAAU,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;;;OAOG;IACH,gCAAU,GAAV;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO;YAAE,OAAO,SAAS,CAAC;QAC/B,0BAA0B,CAAC,OAAO,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACH,iCAAW,GAAX;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ;YAAE,OAAO,SAAS,CAAC;QAChC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;QACrC,OAAO,QAAQ,CAAC,UAAU,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;;;;OAQG;IACH,qCAAe,GAAf;QACE,IAAM,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACrE,IAAI,CAAC,YAAY;YAAE,OAAO,SAAS,CAAC;QACpC,0BAA0B,CAAC,YAAY,CAAC,CAAC;QACzC,OAAO,YAAY,CAAC,UAAU,EAAE,CAAC;IACnC,CAAC;IAED;;;;;;;;;OASG;IACH,yCAAmB,GAAnB;QACE,IAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,gBAAgB;YAAE,OAAO,SAAS,CAAC;QACxC,0BAA0B,CAAC,gBAAgB,CAAC,CAAC;QAC7C,OAAO,gBAAgB,CAAC,UAAU,EAAE,CAAC;IACvC,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,8BAAQ,GAAR,UAAS,KAAa,EAAE,OAAyB;QAC/C,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACrC,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAE1D,0EAA0E;QAC1E,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,oBAAoB,EAAE;YACjC,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,4BAA4B,EAAE,CAAC;YAC1D,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SAChC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,+BAAS,GAAT,UAAU,MAAc;QACtB,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvC,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;OAOG;IACH,gCAAU,GAAV,UAAW,OAAe;QACxB,QAAQ,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxC,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;;;OAOG;IACH,iCAAW,GAAX,UAAY,QAAkB;QAC5B,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACxC,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;IAED;;;;;;;OAOG;IACH,gCAAU,GAAV,UAAW,OAAe;QACxB,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACzC,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;;;OAOG;IACH,iCAAW,GAAX,UAAY,QAAgB;QAC1B,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1C,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;;;;OASG;IACH,iCAAW,GAAX,UAAY,QAAgB;QAC1B,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3C,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;OAOG;IACH,qCAAe,GAAf,UAAgB,YAAkB;QAChC,QAAQ,CAAC,YAAY,EAAE,cAAc,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;QACzD,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;QACvC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;OAQG;IACH,yCAAmB,GAAnB,UAAoB,gBAAsB;QACxC,QAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;QACjE,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;OAMG;IACH,kCAAY,GAAZ;QACE,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS;YAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;QAC1E,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;;;;;;;;OAWG;IACH,8BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAED;;;;;;;;OAQG;IACH,6BAAO,GAAP,UAAQ,KAAa;QACnB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACjD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,oCAAc,GAAd;QACE,OAAO,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;;;;;OAUG;IACH,gCAAU,GAAV,UAAW,KAAa;QACtB,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC;YAAE,MAAM,IAAI,gCAAgC,EAAE,CAAC;QACvE,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACH,6BAAO,GAAP,UAAQ,IAAiC;QACvC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,gCAAU,GAAV,UAAW,KAAa,EAAE,IAAiC;QACzD,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;QAC1C,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAChC,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC;YACvD,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC5B,IAAI,CAAC,OAAO,OAAZ,IAAI,EAAY,IAAI,EAAE;SACvB;aAAM,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE;YAC5B,MAAM,IAAI,gBAAgB,EAAE,CAAC;SAC9B;QAED,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAE5B,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;QAE/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACG,+BAAS,GAAf,UAAgB,MAAmB,EAAE,OAAiB;;;;;;wBACpD,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;wBAC3D,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;wBACtC,qBAAM,MAAM,CAAC,KAAK,EAAE,EAAA;;wBAApB,SAAoB,CAAC;wBACf,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;wBAC3D,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;wBAC7B,WAAW,GAAc,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBACzD,KAAS,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;4BAClD,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;4BACjC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;4BACvC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;4BAC9C,WAAW,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;yBACtD;wBACD,sBAAO,WAAW,EAAC;;;;KACpB;IAED;;;;;;;;;;;;;OAaG;IACG,0BAAI,GAAV;;;;;4BACkB,qBAAM,WAAW,CAAC,MAAM,EAAE,EAAA;;wBAApC,OAAO,GAAG,SAA0B;wBACrB,qBAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,EAAA;;wBAAnE,YAAY,GAAG,SAAoD;wBAEzE,KAAS,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;4BAC7D,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;yBACpC;wBAED,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,SAAS,EAAE;4BAClC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAG,CAAC,CAAC;yBACtC;wBACD,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,SAAS,EAAE;4BACxC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAG,CAAC,CAAC;yBAClD;wBACD,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,EAAE;4BACnC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAG,CAAC,CAAC;yBACxC;wBACD,IAAI,IAAI,CAAC,mBAAmB,EAAE,KAAK,SAAS,EAAE;4BAC5C,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,EAAG,CAAC,CAAC;yBAC1D;wBACD,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,EAAE;4BACpC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAG,CAAC,CAAC;yBAC1C;wBACD,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,EAAE;4BACnC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAG,CAAC,CAAC;yBACxC;wBACD,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE;4BACjC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAG,CAAC,CAAC;yBACpC;wBACD,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;wBAEnD,sBAAO,OAAO,EAAC;;;;KAChB;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,mCAAa,GAAb,UAAc,IAAY,EAAE,MAAc;QACxC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEvC,IAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEtD,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACnC,IAAM,UAAU,GAAG,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsDG;IACG,4BAAM,GAAZ,UACE,UAA6C,EAC7C,IAAY,EACZ,OAA+B;QAA/B,wBAAA,EAAA,YAA+B;;;;gBAE/B,QAAQ,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;gBACxE,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACnC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC5D,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAClE,iBAAiB,CAAC,OAAO,CAAC,YAAY,EAAE,sBAAsB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;gBACxE,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,EAAE,0BAA0B,EAAE;oBACtE,IAAI;iBACL,CAAC,CAAC;gBACH,wBAAwB,CACtB,OAAO,CAAC,cAAc,EACtB,wBAAwB,EACxB,cAAc,CACf,CAAC;gBAEI,KAAK,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;gBACjC,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBAElD,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC7B,YAAY,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAC7D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;;;KACvC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACG,+BAAS,GAAf,UACE,IAAuD,EACvD,OAA8B;QAA9B,wBAAA,EAAA,YAA8B;;;;;;wBAEtB,KAAyC,OAAO,OAAlC,EAAd,MAAM,mBAAG,KAAK,KAAA,EAAE,UAAU,GAAe,OAAO,WAAtB,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;wBAEzD,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;wBAC5D,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;6BAGpC,cAAc,CAAC,IAAI,CAAC,EAApB,wBAAoB;wBACtB,QAAQ,GAAG,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;;;6BAC7C,0BAA0B,CAAC,IAAI,CAAC,EAAhC,wBAAgC;wBACnC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;wBAC3B,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;6BAC1B,MAAM,EAAN,wBAAM;wBACb,qBAAM,wBAAwB,CAAC,GAAG,CAChC,OAAO,EACP,KAAK,EACL,UAAU,EACV,QAAQ,CACT,EAAA;;wBALD,KAAA,SAKC,CAAA;;4BACD,qBAAM,kBAAkB,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAA;;wBAAlE,KAAA,SAAkE,CAAA;;;wBAPtE,QAAQ,KAO8D,CAAC;;4BAEvE,MAAM,IAAI,SAAS,CACjB,2EAA2E,CAC5E,CAAC;;wBAGE,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;wBAC7B,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;wBAChD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBAEzB,sBAAO,OAAO,EAAC;;;;KAChB;IAED;;;;;;;;;;OAUG;IACH,uCAAiB,GAAjB,UAAkB,IAAmB,EAAE,UAAmB;QACxD,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACzB,MAAM,IAAI,SAAS,CAAC,4CAA4C,CAAC,CAAC;SACnE;QAED,IAAM,QAAQ,GAAG,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAE5D,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACnC,IAAM,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzB,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACG,8BAAQ,GAAd,UAAe,GAAsC;;;;;;wBACnD,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;wBACpD,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;wBACf,qBAAM,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAA;;wBAAxC,QAAQ,GAAG,SAA6B;wBACxC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;wBAC7B,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;wBAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAC3B,sBAAO,QAAQ,EAAC;;;;KACjB;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACG,8BAAQ,GAAd,UAAe,GAAsC;;;;;;wBACnD,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;wBACpD,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;wBACf,qBAAM,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAA;;wBAAvC,QAAQ,GAAG,SAA4B;wBACvC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;wBAC7B,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;wBAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAC3B,sBAAO,QAAQ,EAAC;;;;KACjB;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACG,8BAAQ,GAAd,UACE,GAAoD,EACpD,OAAuB;QAAvB,wBAAA,EAAA,WAAqB,CAAC,CAAC;;;;;;wBAEvB,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE;4BACnB,QAAQ;4BACR,UAAU;4BACV,WAAW;4BACX,CAAC,WAAW,EAAE,aAAa,CAAC;yBAC7B,CAAC,CAAC;wBACH,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;6BAGpC,CAAA,GAAG,YAAY,WAAW,CAAA,EAA1B,wBAA0B;wBAAG,KAAA,GAAG,CAAA;;4BAAG,qBAAM,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAA3B,KAAA,SAA2B,CAAA;;;wBAD1D,MAAM,KACoD;wBAE1D,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;wBAE1D,sBAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAC;;;;KAClC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACG,+BAAS,GAAf,UACE,IAAa,EACb,WAA6B,EAC7B,oBAA2C;;;;;;wBAE3C,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;wBACxB,qBAAM,IAAI,CAAC,UAAU,CAC1C,CAAC,IAAI,CAAC,EACN,CAAC,WAAW,CAAC,EACb,CAAC,oBAAoB,CAAC,CACvB,EAAA;;wBAJM,YAAY,GAAI,CAAA,SAItB,CAAA,GAJkB;wBAKnB,sBAAO,YAAY,EAAC;;;;KACrB;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACG,gCAAU,GAAhB,UACE,KAAgB,EAChB,aAAmD,EACnD,sBAAiE;QADjE,8BAAA,EAAA,kBAAmD;QACnD,uCAAA,EAAA,2BAAiE;;;;;;;wBAEjE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;4BAAE,sBAAO,EAAE,EAAC;wBAElC,yCAAyC;wBACzC,KAAS,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;4BACpD,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;4BACtB,QAAQ,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;4BAChC,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gCACnD,MAAM,IAAI,mCAAmC,EAAE,CAAC;6BACjD;yBACF;wBAEK,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;wBAChC,aAAa,GACjB,OAAO,KAAK,IAAI,CAAC,OAAO;4BACtB,CAAC,CAAC,UAAC,CAAc,IAAK,OAAA,CAAC,EAAD,CAAC;4BACvB,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;wBAEhD,aAAa,GAAG,IAAI,KAAK,CAAkB,KAAK,CAAC,MAAM,CAAC,CAAC;wBACtD,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM;;;6BAAE,CAAA,GAAG,GAAG,GAAG,CAAA;wBACvC,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;wBACtC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;wBACzB,MAAM,GAAG,sBAAsB,CAAC,GAAG,CAAC,CAAC;wBAE1B,qBAAM,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAEvD,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;wBACnC,aAAa,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;;;wBARd,GAAG,EAAE,CAAA;;;wBAWtD,CAAA,KAAA,IAAI,CAAC,aAAa,CAAA,CAAC,IAAI,WAAI,aAAa,EAAE;wBAE1C,sBAAO,aAAa,EAAC;;;;KACtB;IAED;;;;;;;;;OASG;IACG,2BAAK,GAAX;;;;4BACE,qBAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAA;;wBAA/B,SAA+B,CAAC;wBAChC,qBAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAA;;wBAAhC,SAAgC,CAAC;wBACjC,qBAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,EAAA;;wBAAvC,SAAuC,CAAC;wBACxC,qBAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,EAAA;;wBAAvC,SAAuC,CAAC;wBACxC,qBAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAA;;wBAArC,SAAqC,CAAC;;;;;KACvC;IAED;;;;;;;;;;;;;;;OAeG;IACG,0BAAI,GAAV,UAAW,OAAyB;QAAzB,wBAAA,EAAA,YAAyB;;;;;;wBAEhC,KAIE,OAAO,iBAJc,EAAvB,gBAAgB,mBAAG,IAAI,KAAA,EACvB,KAGE,OAAO,eAHY,EAArB,cAAc,mBAAG,IAAI,KAAA,EACrB,KAEE,OAAO,eAFU,EAAnB,cAAc,mBAAG,EAAE,KAAA,EACnB,KACE,OAAO,uBADoB,EAA7B,sBAAsB,mBAAG,IAAI,KAAA,CACnB;wBAEZ,QAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;wBAC5D,QAAQ,CAAC,cAAc,EAAE,gBAAgB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;wBACxD,QAAQ,CAAC,cAAc,EAAE,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACvD,QAAQ,CAAC,sBAAsB,EAAE,wBAAwB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;wBAExE,IAAI,cAAc,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC;4BAAE,IAAI,CAAC,OAAO,EAAE,CAAC;wBAEhE,IAAI,sBAAsB,EAAE;4BACpB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;4BACvC,IAAI,IAAI;gCAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC;yBACzC;wBAED,qBAAM,IAAI,CAAC,KAAK,EAAE,EAAA;;wBAAlB,SAAkB,CAAC;wBAEb,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC;wBAC9D,sBAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,iBAAiB,EAAE,EAAC;;;;KAC5E;IAED;;;;;;;;;;;;;;OAcG;IACG,kCAAY,GAAlB,UAAmB,OAA+B;QAA/B,wBAAA,EAAA,YAA+B;;;;;;wBACxC,KAAqC,OAAO,QAA7B,EAAf,OAAO,mBAAG,KAAK,KAAA,EAAK,YAAY,UAAK,OAAO,EAA9C,WAAoC,CAAF,CAAa;wBACrD,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;wBAC5B,qBAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAA;;wBAArC,KAAK,GAAG,SAA6B;wBACrC,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;wBACrC,sBAAO,OAAO,CAAC,CAAC,CAAC,iCAA+B,MAAQ,CAAC,CAAC,CAAC,MAAM,EAAC;;;;KACnE;IAED,8CAAwB,GAAxB,UAAyB,GAAW;QAClC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACtD,IAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YACxB,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAEvC,IAAI,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,OAAO,CAAC,GAAG,OAAM,SAAS,EAAE;gBAC3C,OAAO,IAAI,CAAC;aACb;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEa,8BAAQ,GAAtB,UAAuB,WAAyB;;;;;;wBACrC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,MAAM;;;6BAAE,CAAA,GAAG,GAAG,GAAG,CAAA;wBACnD,qBAAM,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAA;;wBAA9B,SAA8B,CAAC;;;wBADsB,GAAG,EAAE,CAAA;;;;;;KAG7D;IAEO,oCAAc,GAAtB;QACE,IAAM,MAAM,GAAG,8CAA8C,CAAC;QAC9D,IAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEhC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACzB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;YAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;YAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;IAEO,iCAAW,GAAnB;QACE,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACxE,IAAI,YAAY,YAAY,OAAO;YAAE,OAAO,YAAY,CAAC;QAEzD,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAE/D,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,mCAAa,GAArB;QACE,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,yBAAyB,EAAE,CAAC;QACzD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAqBH,kBAAC;AAAD,CAAC,AA9xCD,IA8xCC;;AAED,mDAAmD;AACnD,SAAS,0BAA0B,CACjC,SAAoB;IAEpB,IACE,CAAC,CAAC,SAAS,YAAY,YAAY,CAAC;QACpC,CAAC,CAAC,SAAS,YAAY,SAAS,CAAC,EACjC;QACA,MAAM,IAAI,yBAAyB,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC,CAAC;KAC3E;AACH,CAAC"}