*,*:before,*:after{box-sizing:border-box}*{margin:0;padding:0}html{font-size:16px;line-height:1.5;-webkit-text-size-adjust:100%;-moz-text-size-adjust:100%;text-size-adjust:100%}body{font-family:Noto Sans,Roboto,-apple-system,BlinkMacSystemFont,Segoe UI,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-color:#f4f6f8;color:#333;overflow-x:hidden}.hindi-text{font-family:Noto Sans Devanagari,Noto Sans,sans-serif}.tamil-text{font-family:Noto Sans Tamil,Noto Sans,sans-serif}.telugu-text{font-family:Noto Sans Telugu,Noto Sans,sans-serif}::-webkit-scrollbar{width:8px;height:8px}::-webkit-scrollbar-track{background:#f1f1f1;border-radius:4px}::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:4px}::-webkit-scrollbar-thumb:hover{background:#a8a8a8}*:focus{outline:2px solid #2E5BA8;outline-offset:2px}button:focus,.MuiButton-root:focus{outline:2px solid #2E5BA8;outline-offset:2px}a{color:#2e5ba8;text-decoration:none}a:hover{text-decoration:underline}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.mb-0{margin-bottom:0}.mb-1{margin-bottom:.25rem}.mb-2{margin-bottom:.5rem}.mb-3{margin-bottom:1rem}.mb-4{margin-bottom:1.5rem}.mb-5{margin-bottom:3rem}.mt-0{margin-top:0}.mt-1{margin-top:.25rem}.mt-2{margin-top:.5rem}.mt-3{margin-top:1rem}.mt-4{margin-top:1.5rem}.mt-5{margin-top:3rem}:root{--primary-color: #2E5BA8;--secondary-color: #FF9933;--success-color: #4CAF50;--warning-color: #FF9800;--error-color: #F44336;--info-color: #2196F3;--background-color: #f4f6f8;--surface-color: #ffffff;--text-primary: #333333;--text-secondary: #666666;--border-color: #e0e0e0;--shadow-light: 0 2px 4px rgba(0,0,0,.1);--shadow-medium: 0 4px 8px rgba(0,0,0,.15);--shadow-heavy: 0 8px 16px rgba(0,0,0,.2)}@media (prefers-color-scheme: dark){:root{--background-color: #121212;--surface-color: #1e1e1e;--text-primary: #ffffff;--text-secondary: #b3b3b3;--border-color: #333333}}@media print{*{background:transparent!important;color:#000!important;box-shadow:none!important;text-shadow:none!important}a,a:visited{text-decoration:underline}.no-print{display:none!important}}@media (prefers-contrast: high){:root{--primary-color: #000080;--secondary-color: #FF6600;--border-color: #000000}}@media (prefers-reduced-motion: reduce){*,*:before,*:after{animation-duration:.01ms!important;animation-iteration-count:1!important;transition-duration:.01ms!important;scroll-behavior:auto!important}}@keyframes fadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes slideInLeft{0%{opacity:0;transform:translate(-30px)}to{opacity:1;transform:translate(0)}}@keyframes slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}.fade-in{animation:fadeIn .3s ease-out}.slide-in-left{animation:slideInLeft .3s ease-out}.slide-in-right{animation:slideInRight .3s ease-out}.card{background:var(--surface-color);border-radius:8px;box-shadow:var(--shadow-light);padding:1.5rem;margin-bottom:1rem}.card-header{border-bottom:1px solid var(--border-color);padding-bottom:1rem;margin-bottom:1rem}.btn-primary{background-color:var(--primary-color);color:#fff;border:none;padding:.75rem 1.5rem;border-radius:4px;cursor:pointer;font-weight:500;transition:background-color .2s ease}.btn-primary:hover{background-color:#1e4a8c}.btn-secondary{background-color:var(--secondary-color);color:#fff;border:none;padding:.75rem 1.5rem;border-radius:4px;cursor:pointer;font-weight:500;transition:background-color .2s ease}.btn-secondary:hover{background-color:#e6851a}.loading{opacity:.6;pointer-events:none}.skeleton{background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:loading 1.5s infinite}@keyframes loading{0%{background-position:200% 0}to{background-position:-200% 0}}.mobile-only{display:none}.desktop-only{display:block}@media (max-width: 768px){.mobile-only{display:block}.desktop-only{display:none}.card{padding:1rem}}.error-text{color:var(--error-color);font-size:.875rem;margin-top:.25rem}.success-text{color:var(--success-color);font-size:.875rem;margin-top:.25rem}.warning-text{color:var(--warning-color);font-size:.875rem;margin-top:.25rem}
