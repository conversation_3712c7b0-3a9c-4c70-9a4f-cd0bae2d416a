{"version": 3, "file": "index.js", "names": ["Cell", "require", "Row", "Column", "utils", "colAccessor", "ws", "col", "cols", "module", "exports"], "sources": ["../../../source/lib/column/index.js"], "sourcesContent": ["const Cell = require('../cell/cell.js');\nconst Row = require('../row/row.js');\nconst Column = require('../column/column.js');\nconst utils = require('../utils.js');\n\n/**\n * Module repesenting a Column Accessor\n * @alias Worksheet.column\n * @namespace\n * @func Worksheet.column\n * @desc Access a column in order to manipulate values\n * @param {Number} col Column of top left cell\n * @returns {Column}\n */\nlet colAccessor = (ws, col) => {\n    if (!(ws.cols[col] instanceof Column)) {\n        ws.cols[col] = new Column(col, ws);\n    }\n    return ws.cols[col];\n};\n\nmodule.exports = colAccessor;"], "mappings": ";;AAAA,IAAMA,IAAI,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AACvC,IAAMC,GAAG,GAAGD,OAAO,CAAC,eAAe,CAAC;AACpC,IAAME,MAAM,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAC7C,IAAMG,KAAK,GAAGH,OAAO,CAAC,aAAa,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAII,WAAW,GAAG,SAAdA,WAAWA,CAAIC,EAAE,EAAEC,GAAG,EAAK;EAC3B,IAAI,EAAED,EAAE,CAACE,IAAI,CAACD,GAAG,CAAC,YAAYJ,MAAM,CAAC,EAAE;IACnCG,EAAE,CAACE,IAAI,CAACD,GAAG,CAAC,GAAG,IAAIJ,MAAM,CAACI,GAAG,EAAED,EAAE,CAAC;EACtC;EACA,OAAOA,EAAE,CAACE,IAAI,CAACD,GAAG,CAAC;AACvB,CAAC;AAEDE,MAAM,CAACC,OAAO,GAAGL,WAAW"}