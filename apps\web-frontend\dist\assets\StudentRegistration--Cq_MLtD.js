import{u as e,j as a,B as r,h as n,d as t,f as l,a0 as i,a1 as s,a2 as o,e as d,i as c,A as h,I as m,G as x,s as u,F as j,w as p,x as b,M as g,m as f,D as v,_ as y,l as C,a3 as N,a4 as W}from"./mui-core-BBO2DoRL.js";import{r as S}from"./vendor-CeOqOr8o.js";import{u as B}from"./routing-B6PnZiBG.js";import{u as P}from"./i18n-DWU17bW_.js";import{m as w,A as T}from"./animation-BJm6nf7i.js";import{d as k,b as A,a3 as I,f as O,s as E,a4 as D,k as F,a5 as q}from"./mui-icons-BXhTkfAe.js";import"./utils-misc-NFmYzBmQ.js";const G=[{value:"CBSE",label:"Central Board of Secondary Education (CBSE)",color:"#2E5BA8"},{value:"ICSE",label:"Indian Certificate of Secondary Education (ICSE)",color:"#FF9933"},{value:"STATE_AP",label:"Andhra Pradesh State Board",color:"#00C853"},{value:"STATE_TN",label:"Tamil Nadu State Board",color:"#9C27B0"},{value:"STATE_KA",label:"Karnataka State Board",color:"#FF5722"},{value:"STATE_TG",label:"Telangana State Board",color:"#607D8B"},{value:"IB",label:"International Baccalaureate (IB)",color:"#795548"}],R=[{value:1,label:"Class 1"},{value:2,label:"Class 2"},{value:3,label:"Class 3"},{value:4,label:"Class 4"},{value:5,label:"Class 5"},{value:6,label:"Class 6"},{value:7,label:"Class 7"},{value:8,label:"Class 8"},{value:9,label:"Class 9"},{value:10,label:"Class 10"},{value:11,label:"Class 11"},{value:12,label:"Class 12"}],L=["A","B","C","D","E","F"],M=[{value:"en",label:"English"},{value:"hi",label:"हिन्दी (Hindi)"},{value:"te",label:"తెలుగు (Telugu)"},{value:"ta",label:"தமிழ் (Tamil)"},{value:"kn",label:"ಕನ್ನಡ (Kannada)"},{value:"ml",label:"മലയാളം (Malayalam)"}],$=()=>{const h=e(),m=B(),{t:x}=P(["common","students"]),[u,j]=S.useState(0),[p,b]=S.useState(!1),[g,f]=S.useState({}),[v,y]=S.useState(null),[C,N]=S.useState({firstName:"",middleName:"",lastName:"",dateOfBirth:"",gender:"",bloodGroup:"",admissionNumber:"",grade:"",section:"",board:"",academicYear:"2024-2025",rollNumber:"",address:"",city:"",state:"",pincode:"",phone:"",email:"",fatherName:"",fatherOccupation:"",fatherPhone:"",motherName:"",motherOccupation:"",motherPhone:"",guardianName:"",guardianRelation:"",guardianPhone:"",emergencyContactName:"",emergencyContactPhone:"",emergencyContactRelation:"",preferredLanguage:"en",specialNeeds:"",medicalConditions:"",previousSchool:"",dataConsent:!1,communicationConsent:!1}),W=[{label:"Basic Information",icon:k,description:"Student personal details"},{label:"Academic Details",icon:A,description:"Educational information"},{label:"Contact Information",icon:I,description:"Address and contacts"},{label:"Review & Submit",icon:O,description:"Confirm details"}],q=e=>a=>{const r="checkbox"===a.target.type?a.target.checked:a.target.value;N((a=>({...a,[e]:r}))),g[e]&&f((a=>({...a,[e]:null})))},G=e=>{const a={};switch(e){case 0:C.firstName.trim()||(a.firstName="First name is required"),C.lastName.trim()||(a.lastName="Last name is required"),C.dateOfBirth||(a.dateOfBirth="Date of birth is required"),C.gender||(a.gender="Gender is required");break;case 1:C.admissionNumber.trim()||(a.admissionNumber="Admission number is required"),C.grade||(a.grade="Grade is required"),C.section||(a.section="Section is required"),C.board||(a.board="Educational board is required");break;case 2:C.address.trim()||(a.address="Address is required"),C.city.trim()||(a.city="City is required"),C.state.trim()||(a.state="State is required"),C.pincode.trim()||(a.pincode="Pincode is required"),C.fatherName.trim()||(a.fatherName="Father name is required"),C.motherName.trim()||(a.motherName="Mother name is required"),C.emergencyContactName.trim()||(a.emergencyContactName="Emergency contact is required"),C.emergencyContactPhone.trim()||(a.emergencyContactPhone="Emergency contact phone is required");break;case 3:C.dataConsent||(a.dataConsent="Data consent is required")}return f(a),0===Object.keys(a).length};return a.jsxs(r,{sx:{maxWidth:1200,mx:"auto",p:3},children:[a.jsx(w.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:a.jsxs(r,{sx:{mb:4},children:[a.jsx(n,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${h.palette.primary.main} 0%, ${h.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Student Registration"}),a.jsx(n,{variant:"body1",color:"text.secondary",children:"Register a new student with comprehensive information for SWOT analysis"})]})}),a.jsx(t,{sx:{mb:4,overflow:"visible"},children:a.jsx(l,{children:a.jsx(i,{activeStep:u,alternativeLabel:!0,children:W.map(((e,t)=>a.jsx(s,{children:a.jsxs(o,{StepIconComponent:({active:n,completed:t})=>a.jsx(r,{sx:{width:48,height:48,borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",background:t?`linear-gradient(135deg, ${h.palette.success.main} 0%, ${h.palette.success.dark} 100%)`:n?`linear-gradient(135deg, ${h.palette.primary.main} 0%, ${h.palette.secondary.main} 100%)`:d(h.palette.action.disabled,.12),color:t||n?"white":h.palette.action.disabled,transition:"all 0.3s ease"},children:a.jsx(e.icon,{sx:{fontSize:24}})}),children:[a.jsx(n,{variant:"subtitle2",sx:{fontWeight:500},children:e.label}),a.jsx(n,{variant:"caption",color:"text.secondary",children:e.description})]})},e.label)))})})}),a.jsx(t,{children:a.jsxs(l,{sx:{p:4},children:[a.jsx(T,{mode:"wait",children:a.jsxs(w.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:[0===u&&a.jsx(Y,{formData:C,errors:g,handleInputChange:q,profilePhoto:v,handlePhotoUpload:e=>{const a=e.target.files[0];if(a){const e=new FileReader;e.onload=e=>{y(e.target.result)},e.readAsDataURL(a)}}}),1===u&&a.jsx(_,{formData:C,errors:g,handleInputChange:q}),2===u&&a.jsx(z,{formData:C,errors:g,handleInputChange:q}),3===u&&a.jsx(K,{formData:C,errors:g,handleInputChange:q,profilePhoto:v})]},u)}),a.jsxs(r,{sx:{display:"flex",justifyContent:"space-between",mt:4},children:[a.jsx(c,{onClick:()=>{j((e=>e-1))},disabled:0===u,startIcon:a.jsx(E,{}),variant:"outlined",children:"Back"}),a.jsx(c,{onClick:u===W.length-1?async()=>{if(G(u)){b(!0);try{await new Promise((e=>setTimeout(e,2e3))),m("/dashboard/students")}catch(e){}finally{b(!1)}}}:()=>{G(u)&&j((e=>e+1))},endIcon:u===W.length-1?a.jsx(D,{}):a.jsx(F,{}),variant:"contained",loading:p,children:u===W.length-1?"Register Student":"Next"})]})]})})]})},Y=({formData:t,errors:l,handleInputChange:i,profilePhoto:s,handlePhotoUpload:o})=>{const c=e();return a.jsxs(r,{children:[a.jsx(n,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Basic Information"}),a.jsx(r,{sx:{display:"flex",justifyContent:"center",mb:4},children:a.jsxs(r,{sx:{position:"relative"},children:[a.jsx(h,{src:s,sx:{width:120,height:120,border:`4px solid ${d(c.palette.primary.main,.2)}`,background:`linear-gradient(135deg, ${c.palette.primary.main} 0%, ${c.palette.secondary.main} 100%)`},children:a.jsx(k,{sx:{fontSize:60}})}),a.jsxs(m,{component:"label",sx:{position:"absolute",bottom:0,right:0,background:c.palette.primary.main,color:"white","&:hover":{background:c.palette.primary.dark}},children:[a.jsx(q,{}),a.jsx("input",{type:"file",hidden:!0,accept:"image/*",onChange:o})]})]})}),a.jsxs(x,{container:!0,spacing:3,children:[a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"First Name *",value:t.firstName,onChange:i("firstName"),error:!!l.firstName,helperText:l.firstName,placeholder:"e.g., Sanju"})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"Middle Name",value:t.middleName,onChange:i("middleName"),placeholder:"e.g., Kumar"})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"Last Name *",value:t.lastName,onChange:i("lastName"),error:!!l.lastName,helperText:l.lastName,placeholder:"e.g., Reddy"})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Date of Birth *",type:"date",value:t.dateOfBirth,onChange:i("dateOfBirth"),error:!!l.dateOfBirth,helperText:l.dateOfBirth,InputLabelProps:{shrink:!0}})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsxs(j,{fullWidth:!0,error:!!l.gender,children:[a.jsx(p,{children:"Gender *"}),a.jsxs(b,{value:t.gender,onChange:i("gender"),label:"Gender *",children:[a.jsx(g,{value:"Male",children:"Male"}),a.jsx(g,{value:"Female",children:"Female"}),a.jsx(g,{value:"Other",children:"Other"})]})]})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsxs(j,{fullWidth:!0,children:[a.jsx(p,{children:"Blood Group"}),a.jsxs(b,{value:t.bloodGroup,onChange:i("bloodGroup"),label:"Blood Group",children:[a.jsx(g,{value:"A+",children:"A+"}),a.jsx(g,{value:"A-",children:"A-"}),a.jsx(g,{value:"B+",children:"B+"}),a.jsx(g,{value:"B-",children:"B-"}),a.jsx(g,{value:"AB+",children:"AB+"}),a.jsx(g,{value:"AB-",children:"AB-"}),a.jsx(g,{value:"O+",children:"O+"}),a.jsx(g,{value:"O-",children:"O-"})]})]})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsxs(j,{fullWidth:!0,children:[a.jsx(p,{children:"Preferred Language"}),a.jsx(b,{value:t.preferredLanguage,onChange:i("preferredLanguage"),label:"Preferred Language",children:M.map((e=>a.jsx(g,{value:e.value,children:e.label},e.value)))})]})})]})]})},_=({formData:t,errors:l,handleInputChange:i})=>(e(),a.jsxs(r,{children:[a.jsx(n,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Academic Information"}),a.jsxs(x,{container:!0,spacing:3,children:[a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Admission Number *",value:t.admissionNumber,onChange:i("admissionNumber"),error:!!l.admissionNumber,helperText:l.admissionNumber,placeholder:"e.g., VMS2024001"})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Roll Number",value:t.rollNumber,onChange:i("rollNumber"),placeholder:"e.g., 15"})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsxs(j,{fullWidth:!0,error:!!l.grade,children:[a.jsx(p,{children:"Grade/Class *"}),a.jsx(b,{value:t.grade,onChange:i("grade"),label:"Grade/Class *",children:R.map((e=>a.jsx(g,{value:e.value,children:e.label},e.value)))})]})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsxs(j,{fullWidth:!0,error:!!l.section,children:[a.jsx(p,{children:"Section *"}),a.jsx(b,{value:t.section,onChange:i("section"),label:"Section *",children:L.map((e=>a.jsxs(g,{value:e,children:["Section ",e]},e)))})]})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"Academic Year",value:t.academicYear,onChange:i("academicYear"),placeholder:"2024-2025"})}),a.jsx(x,{item:!0,xs:12,children:a.jsxs(j,{fullWidth:!0,error:!!l.board,children:[a.jsx(p,{children:"Educational Board *"}),a.jsx(b,{value:t.board,onChange:i("board"),label:"Educational Board *",children:G.map((e=>a.jsx(g,{value:e.value,children:a.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:1},children:[a.jsx(f,{size:"small",sx:{backgroundColor:e.color,color:"white",minWidth:60},label:e.value}),e.label]})},e.value)))})]})}),a.jsx(x,{item:!0,xs:12,children:a.jsx(u,{fullWidth:!0,label:"Previous School",value:t.previousSchool,onChange:i("previousSchool"),placeholder:"Name of previous school (if applicable)",multiline:!0,rows:2})})]})]})),z=({formData:e,errors:t,handleInputChange:l})=>a.jsxs(r,{children:[a.jsx(n,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Contact Information"}),a.jsx(n,{variant:"subtitle1",sx:{mb:2,fontWeight:500,color:"primary.main"},children:"Address Details"}),a.jsxs(x,{container:!0,spacing:3,sx:{mb:4},children:[a.jsx(x,{item:!0,xs:12,children:a.jsx(u,{fullWidth:!0,label:"Address *",value:e.address,onChange:l("address"),error:!!t.address,helperText:t.address,multiline:!0,rows:3,placeholder:"Complete address with house number, street, area"})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"City *",value:e.city,onChange:l("city"),error:!!t.city,helperText:t.city,placeholder:"e.g., Hyderabad"})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"State *",value:e.state,onChange:l("state"),error:!!t.state,helperText:t.state,placeholder:"e.g., Telangana"})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"Pincode *",value:e.pincode,onChange:l("pincode"),error:!!t.pincode,helperText:t.pincode,placeholder:"e.g., 500001"})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Phone Number",value:e.phone,onChange:l("phone"),placeholder:"+91 9876543210"})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Email Address",type:"email",value:e.email,onChange:l("email"),placeholder:"<EMAIL>"})})]}),a.jsx(v,{sx:{my:3}}),a.jsx(n,{variant:"subtitle1",sx:{mb:2,fontWeight:500,color:"primary.main"},children:"Parent/Guardian Information"}),a.jsxs(x,{container:!0,spacing:3,children:[a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Father's Name *",value:e.fatherName,onChange:l("fatherName"),error:!!t.fatherName,helperText:t.fatherName,placeholder:"Father's full name"})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Father's Occupation",value:e.fatherOccupation,onChange:l("fatherOccupation"),placeholder:"e.g., Software Engineer"})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Father's Phone",value:e.fatherPhone,onChange:l("fatherPhone"),placeholder:"+91 9876543210"})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Mother's Name *",value:e.motherName,onChange:l("motherName"),error:!!t.motherName,helperText:t.motherName,placeholder:"Mother's full name"})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Mother's Occupation",value:e.motherOccupation,onChange:l("motherOccupation"),placeholder:"e.g., Teacher"})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(u,{fullWidth:!0,label:"Mother's Phone",value:e.motherPhone,onChange:l("motherPhone"),placeholder:"+91 9876543210"})})]}),a.jsx(v,{sx:{my:3}}),a.jsx(n,{variant:"subtitle1",sx:{mb:2,fontWeight:500,color:"primary.main"},children:"Emergency Contact"}),a.jsxs(x,{container:!0,spacing:3,children:[a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"Emergency Contact Name *",value:e.emergencyContactName,onChange:l("emergencyContactName"),error:!!t.emergencyContactName,helperText:t.emergencyContactName,placeholder:"Contact person name"})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"Emergency Contact Phone *",value:e.emergencyContactPhone,onChange:l("emergencyContactPhone"),error:!!t.emergencyContactPhone,helperText:t.emergencyContactPhone,placeholder:"+91 9876543210"})}),a.jsx(x,{item:!0,xs:12,md:4,children:a.jsx(u,{fullWidth:!0,label:"Relation",value:e.emergencyContactRelation,onChange:l("emergencyContactRelation"),placeholder:"e.g., Uncle, Aunt"})})]})]}),K=({formData:i,errors:s,handleInputChange:o,profilePhoto:c})=>{var m;const u=e(),j=G.find((e=>e.value===i.board)),p=R.find((e=>e.value===i.grade));return a.jsxs(r,{children:[a.jsx(n,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Review & Submit"}),a.jsx(y,{severity:"info",sx:{mb:3},children:"Please review all information carefully before submitting. You can go back to make changes if needed."}),a.jsx(t,{sx:{mb:3,background:`linear-gradient(135deg, ${d(u.palette.primary.main,.05)} 0%, ${d(u.palette.secondary.main,.05)} 100%)`},children:a.jsxs(l,{children:[a.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:3,mb:3},children:[a.jsx(h,{src:c,sx:{width:80,height:80,border:`3px solid ${u.palette.primary.main}`},children:a.jsx(k,{sx:{fontSize:40}})}),a.jsxs(r,{children:[a.jsxs(n,{variant:"h5",sx:{fontWeight:600},children:[i.firstName," ",i.middleName," ",i.lastName]}),a.jsxs(n,{variant:"body1",color:"text.secondary",children:[null==p?void 0:p.label," - Section ",i.section]}),a.jsxs(n,{variant:"body2",color:"text.secondary",children:["Admission No: ",i.admissionNumber]})]})]}),j&&a.jsx(f,{label:j.label,sx:{backgroundColor:j.color,color:"white",fontWeight:500}})]})}),a.jsxs(x,{container:!0,spacing:3,sx:{mb:4},children:[a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(t,{children:a.jsxs(l,{children:[a.jsx(n,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Personal Information"}),a.jsxs(C,{spacing:1,children:[a.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[a.jsx(n,{variant:"body2",color:"text.secondary",children:"Date of Birth:"}),a.jsx(n,{variant:"body2",children:i.dateOfBirth})]}),a.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[a.jsx(n,{variant:"body2",color:"text.secondary",children:"Gender:"}),a.jsx(n,{variant:"body2",children:i.gender})]}),a.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[a.jsx(n,{variant:"body2",color:"text.secondary",children:"Blood Group:"}),a.jsx(n,{variant:"body2",children:i.bloodGroup||"Not specified"})]}),a.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[a.jsx(n,{variant:"body2",color:"text.secondary",children:"Preferred Language:"}),a.jsx(n,{variant:"body2",children:null==(m=M.find((e=>e.value===i.preferredLanguage)))?void 0:m.label})]})]})]})})}),a.jsx(x,{item:!0,xs:12,md:6,children:a.jsx(t,{children:a.jsxs(l,{children:[a.jsx(n,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Contact Information"}),a.jsxs(C,{spacing:1,children:[a.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[a.jsx(n,{variant:"body2",color:"text.secondary",children:"City:"}),a.jsx(n,{variant:"body2",children:i.city})]}),a.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[a.jsx(n,{variant:"body2",color:"text.secondary",children:"State:"}),a.jsx(n,{variant:"body2",children:i.state})]}),a.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[a.jsx(n,{variant:"body2",color:"text.secondary",children:"Father's Name:"}),a.jsx(n,{variant:"body2",children:i.fatherName})]}),a.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[a.jsx(n,{variant:"body2",color:"text.secondary",children:"Mother's Name:"}),a.jsx(n,{variant:"body2",children:i.motherName})]})]})]})})})]}),a.jsx(t,{children:a.jsxs(l,{children:[a.jsx(n,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Consent & Permissions"}),a.jsxs(C,{spacing:2,children:[a.jsx(N,{control:a.jsx(W,{checked:i.dataConsent,onChange:o("dataConsent"),color:"primary"}),label:a.jsx(n,{variant:"body2",children:"I consent to the collection and processing of student data for educational purposes and SWOT analysis. *"})}),s.dataConsent&&a.jsx(n,{variant:"caption",color:"error",children:s.dataConsent}),a.jsx(N,{control:a.jsx(W,{checked:i.communicationConsent,onChange:o("communicationConsent"),color:"primary"}),label:a.jsx(n,{variant:"body2",children:"I consent to receive communications about student progress, events, and important updates."})}),a.jsx(y,{severity:"warning",sx:{mt:2},children:a.jsx(n,{variant:"body2",children:"By submitting this form, you confirm that all information provided is accurate and complete. The student data will be used for educational assessment, SWOT analysis, and academic progress tracking."})})]})]})})]})};export{$ as default};
//# sourceMappingURL=StudentRegistration--Cq_MLtD.js.map
