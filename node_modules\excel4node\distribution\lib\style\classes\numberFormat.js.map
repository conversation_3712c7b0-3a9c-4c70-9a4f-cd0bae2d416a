{"version": 3, "file": "numberFormat.js", "names": ["NumberFormat", "fmt", "_classCallCheck", "formatCode", "id", "_createClass", "key", "get", "set", "value", "addToXMLele", "ele", "undefined", "att", "numFmtId", "module", "exports"], "sources": ["../../../../source/lib/style/classes/numberFormat.js"], "sourcesContent": ["class NumberFormat {\n    /**\n    * @class NumberFormat\n    * @param {String} fmt Format of the Number\n    * @returns {NumberFormat}\n    */\n    constructor(fmt) {\n        this.formatCode = fmt;\n        this.id;\n    }\n\n    get numFmtId() {\n        return this.id;\n    }\n    set numFmtId(id) {\n        this.id = id;\n    }\n\n    /**\n     * @alias NumberFormat.addToXMLele\n     * @desc When generating Workbook output, attaches style to the styles xml file\n     * @func NumberFormat.addToXMLele\n     * @param {xmlbuilder.Element} ele Element object of the xmlbuilder module\n     */\n    addToXMLele(ele) {\n        if (this.formatCode !== undefined) {\n            ele.ele('numFmt')\n            .att('formatCode', this.formatCode)\n            .att('numFmtId', this.numFmtId);\n        }\n    }\n}\n\nmodule.exports = NumberFormat;"], "mappings": ";;;;;IAAMA,YAAY;EACd;AACJ;AACA;AACA;AACA;EACI,SAAAA,aAAYC,GAAG,EAAE;IAAAC,eAAA,OAAAF,YAAA;IACb,IAAI,CAACG,UAAU,GAAGF,GAAG;IACrB,IAAI,CAACG,EAAE;EACX;EAACC,YAAA,CAAAL,YAAA;IAAAM,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAe;MACX,OAAO,IAAI,CAACH,EAAE;IAClB,CAAC;IAAAI,GAAA,EACD,SAAAA,IAAaJ,EAAE,EAAE;MACb,IAAI,CAACA,EAAE,GAAGA,EAAE;IAChB;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAE,GAAA;IAAAG,KAAA,EAMA,SAAAC,YAAYC,GAAG,EAAE;MACb,IAAI,IAAI,CAACR,UAAU,KAAKS,SAAS,EAAE;QAC/BD,GAAG,CAACA,GAAG,CAAC,QAAQ,CAAC,CAChBE,GAAG,CAAC,YAAY,EAAE,IAAI,CAACV,UAAU,CAAC,CAClCU,GAAG,CAAC,UAAU,EAAE,IAAI,CAACC,QAAQ,CAAC;MACnC;IACJ;EAAC;EAAA,OAAAd,YAAA;AAAA;AAGLe,MAAM,CAACC,OAAO,GAAGhB,YAAY"}