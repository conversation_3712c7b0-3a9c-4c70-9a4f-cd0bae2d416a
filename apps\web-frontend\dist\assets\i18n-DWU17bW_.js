import{r as e,d as t,e as n}from"./vendor-CeOqOr8o.js";function s(e,t){for(var n=0;n<t.length;n++){const s=t[n];if("string"!=typeof s&&!Array.isArray(s))for(const t in s)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(s,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>s[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}const o=e=>"string"==typeof e,i=()=>{let e,t;const n=new Promise(((n,s)=>{e=n,t=s}));return n.resolve=e,n.reject=t,n},r=e=>null==e?"":""+e,a=/###/g,l=e=>e&&e.indexOf("###")>-1?e.replace(a,"."):e,u=e=>!e||o(e),c=(e,t,n)=>{const s=o(t)?t.split("."):t;let i=0;for(;i<s.length-1;){if(u(e))return{};const t=l(s[i]);!e[t]&&n&&(e[t]=new n),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++i}return u(e)?{}:{obj:e,k:l(s[i])}},h=(e,t,n)=>{const{obj:s,k:o}=c(e,t,Object);if(void 0!==s||1===t.length)return void(s[o]=n);let i=t[t.length-1],r=t.slice(0,t.length-1),a=c(e,r,Object);for(;void 0===a.obj&&r.length;)i=`${r[r.length-1]}.${i}`,r=r.slice(0,r.length-1),a=c(e,r,Object),a&&a.obj&&void 0!==a.obj[`${a.k}.${i}`]&&(a.obj=void 0);a.obj[`${a.k}.${i}`]=n},p=(e,t)=>{const{obj:n,k:s}=c(e,t);if(n)return n[s]},d=(e,t,n)=>{for(const s in t)"__proto__"!==s&&"constructor"!==s&&(s in e?o(e[s])||e[s]instanceof String||o(t[s])||t[s]instanceof String?n&&(e[s]=t[s]):d(e[s],t[s],n):e[s]=t[s]);return e},f=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var g={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const y=e=>o(e)?e.replace(/[&<>"'\/]/g,(e=>g[e])):e;const m=[" ",",","?","!",";"],b=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(void 0!==t)return t;const n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}(20),v=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];const s=t.split(n);let o=e;for(let i=0;i<s.length;){if(!o||"object"!=typeof o)return;let e,t="";for(let r=i;r<s.length;++r)if(r!==i&&(t+=n),t+=s[r],e=o[t],void 0!==e){if(["string","number","boolean"].indexOf(typeof e)>-1&&r<s.length-1)continue;i+=r-i+1;break}o=e}return o},x=e=>e&&e.replace("_","-"),w={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]}};class O{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||w,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,s){return s&&!this.debug?null:(o(e[0])&&(e[0]=`${n}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new O(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new O(this.logger,e)}}var S=new O;class k{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach((e=>{this.observers[e]||(this.observers[e]=new Map);const n=this.observers[e].get(t)||0;this.observers[e].set(t,n+1)})),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];if(this.observers[e]){Array.from(this.observers[e].entries()).forEach((e=>{let[t,s]=e;for(let o=0;o<s;o++)t(...n)}))}if(this.observers["*"]){Array.from(this.observers["*"].entries()).forEach((t=>{let[s,o]=t;for(let i=0;i<o;i++)s.apply(s,[e,...n])}))}}}class L extends k{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const i=void 0!==s.keySeparator?s.keySeparator:this.options.keySeparator,r=void 0!==s.ignoreJSONStructure?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;e.indexOf(".")>-1?a=e.split("."):(a=[e,t],n&&(Array.isArray(n)?a.push(...n):o(n)&&i?a.push(...n.split(i)):a.push(n)));const l=p(this.data,a);return!l&&!t&&!n&&e.indexOf(".")>-1&&(e=a[0],t=a[1],n=a.slice(2).join(".")),!l&&r&&o(n)?v(this.data&&this.data[e]&&this.data[e][t],n,i):l}addResource(e,t,n,s){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1};const i=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator;let r=[e,t];n&&(r=r.concat(i?n.split(i):n)),e.indexOf(".")>-1&&(r=e.split("."),s=t,t=r[1]),this.addNamespaces(t),h(this.data,r,s),o.silent||this.emit("added",e,t,n,s)}addResources(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(const i in n)(o(n[i])||Array.isArray(n[i]))&&this.addResource(e,t,i,n[i],{silent:!0});s.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,s,o){let i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},r=[e,t];e.indexOf(".")>-1&&(r=e.split("."),s=n,n=t,t=r[1]),this.addNamespaces(t);let a=p(this.data,r)||{};i.skipCopy||(n=JSON.parse(JSON.stringify(n))),s?d(a,n,o):a={...a,...n},h(this.data,r,a),i.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find((e=>t[e]&&Object.keys(t[e]).length>0))}toJSON(){return this.data}}var P={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,s,o){return e.forEach((e=>{this.processors[e]&&(t=this.processors[e].process(t,n,s,o))})),t}};const R={};class j extends k{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var n,s;super(),n=e,s=this,["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"].forEach((e=>{n[e]&&(s[e]=n[e])})),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=S.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;const n=this.resolve(e,t);return n&&void 0!==n.res}extractFromKey(e,t){let n=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===n&&(n=":");const s=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator;let i=t.ns||this.options.defaultNS||[];const r=n&&e.indexOf(n)>-1,a=!(this.options.userDefinedKeySeparator||t.keySeparator||this.options.userDefinedNsSeparator||t.nsSeparator||((e,t,n)=>{t=t||"",n=n||"";const s=m.filter((e=>t.indexOf(e)<0&&n.indexOf(e)<0));if(0===s.length)return!0;const o=b.getRegExp(`(${s.map((e=>"?"===e?"\\?":e)).join("|")})`);let i=!o.test(e);if(!i){const t=e.indexOf(n);t>0&&!o.test(e.substring(0,t))&&(i=!0)}return i})(e,n,s));if(r&&!a){const t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:o(i)?[i]:i};const r=e.split(n);(n!==s||n===s&&this.options.ns.indexOf(r[0])>-1)&&(i=r.shift()),e=r.join(s)}return{key:e,namespaces:o(i)?[i]:i}}translate(e,t,n){if("object"!=typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof t&&(t={...t}),t||(t={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);const s=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,i=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:r,namespaces:a}=this.extractFromKey(e[e.length-1],t),l=a[a.length-1],u=t.lng||this.language,c=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u&&"cimode"===u.toLowerCase()){if(c){const e=t.nsSeparator||this.options.nsSeparator;return s?{res:`${l}${e}${r}`,usedKey:r,exactUsedKey:r,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:`${l}${e}${r}`}return s?{res:r,usedKey:r,exactUsedKey:r,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:r}const h=this.resolve(e,t);let p=h&&h.res;const d=h&&h.usedKey||r,f=h&&h.exactUsedKey||r,g=Object.prototype.toString.apply(p),y=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,m=!this.i18nFormat||this.i18nFormat.handleAsObject,b=!o(p)&&"boolean"!=typeof p&&"number"!=typeof p;if(!(m&&p&&b&&["[object Number]","[object Function]","[object RegExp]"].indexOf(g)<0)||o(y)&&Array.isArray(p))if(m&&o(y)&&Array.isArray(p))p=p.join(y),p&&(p=this.extendTranslation(p,e,t,n));else{let s=!1,a=!1;const c=void 0!==t.count&&!o(t.count),d=j.hasDefaultValue(t),f=c?this.pluralResolver.getSuffix(u,t.count,t):"",g=t.ordinal&&c?this.pluralResolver.getSuffix(u,t.count,{ordinal:!1}):"",y=c&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),m=y&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${f}`]||t[`defaultValue${g}`]||t.defaultValue;!this.isValidLookup(p)&&d&&(s=!0,p=m),this.isValidLookup(p)||(a=!0,p=r);const b=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&a?void 0:p,v=d&&m!==p&&this.options.updateMissing;if(a||s||v){if(this.logger.log(v?"updateKey":"missingKey",u,l,r,v?m:p),i){const e=this.resolve(r,{...t,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[];const n=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&n&&n[0])for(let t=0;t<n.length;t++)e.push(n[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);const s=(e,n,s)=>{const o=d&&s!==p?s:b;this.options.missingKeyHandler?this.options.missingKeyHandler(e,l,n,o,v,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,l,n,o,v,t),this.emit("missingKey",e,l,n,p)};this.options.saveMissing&&(this.options.saveMissingPlurals&&c?e.forEach((e=>{const n=this.pluralResolver.getSuffixes(e,t);y&&t[`defaultValue${this.options.pluralSeparator}zero`]&&n.indexOf(`${this.options.pluralSeparator}zero`)<0&&n.push(`${this.options.pluralSeparator}zero`),n.forEach((n=>{s([e],r+n,t[`defaultValue${n}`]||m)}))})):s(e,r,m))}p=this.extendTranslation(p,e,t,h,n),a&&p===r&&this.options.appendNamespaceToMissingKey&&(p=`${l}:${r}`),(a||s)&&this.options.parseMissingKeyHandler&&(p="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${r}`:r,s?p:void 0):this.options.parseMissingKeyHandler(p))}else{if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(d,p,{...t,ns:a}):`key '${r} (${this.language})' returned an object instead of string.`;return s?(h.res=e,h.usedParams=this.getUsedParamsDetails(t),h):e}if(i){const e=Array.isArray(p),n=e?[]:{},s=e?f:d;for(const o in p)if(Object.prototype.hasOwnProperty.call(p,o)){const e=`${s}${i}${o}`;n[o]=this.translate(e,{...t,joinArrays:!1,ns:a}),n[o]===e&&(n[o]=p[o])}p=n}}return s?(h.res=p,h.usedParams=this.getUsedParamsDetails(t),h):p}extendTranslation(e,t,n,s,i){var r=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init({...n,interpolation:{...this.options.interpolation,...n.interpolation}});const a=o(e)&&(n&&n.interpolation&&void 0!==n.interpolation.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let l;if(a){const t=e.match(this.interpolator.nestingRegexp);l=t&&t.length}let u=n.replace&&!o(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(u={...this.options.interpolation.defaultVariables,...u}),e=this.interpolator.interpolate(e,u,n.lng||this.language||s.usedLng,n),a){const t=e.match(this.interpolator.nestingRegexp);l<(t&&t.length)&&(n.nest=!1)}!n.lng&&"v1"!==this.options.compatibilityAPI&&s&&s.res&&(n.lng=this.language||s.usedLng),!1!==n.nest&&(e=this.interpolator.nest(e,(function(){for(var e=arguments.length,s=new Array(e),o=0;o<e;o++)s[o]=arguments[o];return i&&i[0]===s[0]&&!n.context?(r.logger.warn(`It seems you are nesting recursively key: ${s[0]} in key: ${t[0]}`),null):r.translate(...s,t)}),n)),n.interpolation&&this.interpolator.reset()}const a=n.postProcess||this.options.postProcess,l=o(a)?[a]:a;return null!=e&&l&&l.length&&!1!==n.applyPostProcessor&&(e=P.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(n)},...n}:n,this)),e}resolve(e){let t,n,s,i,r,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o(e)&&(e=[e]),e.forEach((e=>{if(this.isValidLookup(t))return;const l=this.extractFromKey(e,a),u=l.key;n=u;let c=l.namespaces;this.options.fallbackNS&&(c=c.concat(this.options.fallbackNS));const h=void 0!==a.count&&!o(a.count),p=h&&!a.ordinal&&0===a.count&&this.pluralResolver.shouldUseIntlApi(),d=void 0!==a.context&&(o(a.context)||"number"==typeof a.context)&&""!==a.context,f=a.lngs?a.lngs:this.languageUtils.toResolveHierarchy(a.lng||this.language,a.fallbackLng);c.forEach((e=>{this.isValidLookup(t)||(r=e,!R[`${f[0]}-${e}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(r)&&(R[`${f[0]}-${e}`]=!0,this.logger.warn(`key "${n}" for languages "${f.join(", ")}" won't get resolved as namespace "${r}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),f.forEach((n=>{if(this.isValidLookup(t))return;i=n;const o=[u];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(o,u,n,e,a);else{let e;h&&(e=this.pluralResolver.getSuffix(n,a.count,a));const t=`${this.options.pluralSeparator}zero`,s=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(h&&(o.push(u+e),a.ordinal&&0===e.indexOf(s)&&o.push(u+e.replace(s,this.options.pluralSeparator)),p&&o.push(u+t)),d){const n=`${u}${this.options.contextSeparator}${a.context}`;o.push(n),h&&(o.push(n+e),a.ordinal&&0===e.indexOf(s)&&o.push(n+e.replace(s,this.options.pluralSeparator)),p&&o.push(n+t))}}let r;for(;r=o.pop();)this.isValidLookup(t)||(s=r,t=this.getResource(n,e,r,a))})))}))})),{res:t,usedKey:n,exactUsedKey:s,usedLng:i,usedNS:r}}isValidLookup(e){return!(void 0===e||!this.options.returnNull&&null===e||!this.options.returnEmptyString&&""===e)}getResource(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,n,s):this.resourceStore.getResource(e,t,n,s)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],n=e.replace&&!o(e.replace);let s=n?e.replace:e;if(n&&void 0!==e.count&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!n){s={...s};for(const e of t)delete s[e]}return s}static hasDefaultValue(e){const t="defaultValue";for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,12)&&void 0!==e[n])return!0;return!1}}const N=e=>e.charAt(0).toUpperCase()+e.slice(1);class C{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=S.create("languageUtils")}getScriptPartFromCode(e){if(!(e=x(e))||e.indexOf("-")<0)return null;const t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}getLanguagePartFromCode(e){if(!(e=x(e))||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(o(e)&&e.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(t){}const n=["hans","hant","latn","cyrl","cans","mong","arab"];let s=e.split("-");return this.options.lowerCaseLng?s=s.map((e=>e.toLowerCase())):2===s.length?(s[0]=s[0].toLowerCase(),s[1]=s[1].toUpperCase(),n.indexOf(s[1].toLowerCase())>-1&&(s[1]=N(s[1].toLowerCase()))):3===s.length&&(s[0]=s[0].toLowerCase(),2===s[1].length&&(s[1]=s[1].toUpperCase()),"sgn"!==s[0]&&2===s[2].length&&(s[2]=s[2].toUpperCase()),n.indexOf(s[1].toLowerCase())>-1&&(s[1]=N(s[1].toLowerCase())),n.indexOf(s[2].toLowerCase())>-1&&(s[2]=N(s[2].toLowerCase()))),s.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach((e=>{if(t)return;const n=this.formatLanguageCode(e);this.options.supportedLngs&&!this.isSupportedCode(n)||(t=n)})),!t&&this.options.supportedLngs&&e.forEach((e=>{if(t)return;const n=this.getLanguagePartFromCode(e);if(this.isSupportedCode(n))return t=n;t=this.options.supportedLngs.find((e=>e===n?e:e.indexOf("-")<0&&n.indexOf("-")<0?void 0:e.indexOf("-")>0&&n.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===n||0===e.indexOf(n)&&n.length>1?e:void 0))})),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),o(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){const n=this.getFallbackCodes(t||this.options.fallbackLng||[],e),s=[],i=e=>{e&&(this.isSupportedCode(e)?s.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return o(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&i(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&i(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&i(this.getLanguagePartFromCode(e))):o(e)&&i(this.formatLanguageCode(e)),n.forEach((e=>{s.indexOf(e)<0&&i(this.formatLanguageCode(e))})),s}}let E=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],$={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)};const A=["v1","v2","v3"],T=["v4"],I={zero:0,one:1,two:2,few:3,many:4,other:5};class D{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=S.create("pluralResolver"),this.options.compatibilityJSON&&!T.includes(this.options.compatibilityJSON)||"undefined"!=typeof Intl&&Intl.PluralRules||(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=(()=>{const e={};return E.forEach((t=>{t.lngs.forEach((n=>{e[n]={numbers:t.nr,plurals:$[t.fc]}}))})),e})(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){const s=x("dev"===e?"en":e),o=t.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:s,type:o});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let r;try{r=new Intl.PluralRules(s,{type:o})}catch(n){if(!e.match(/-|_/))return;const s=this.languageUtils.getLanguagePartFromCode(e);r=this.getRule(s,t)}return this.pluralRulesCache[i]=r,r}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.getRule(e,t);return this.shouldUseIntlApi()?n&&n.resolvedOptions().pluralCategories.length>1:n&&n.numbers.length>1}getPluralFormsOfKey(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,n).map((e=>`${t}${e}`))}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.getRule(e,t);return n?this.shouldUseIntlApi()?n.resolvedOptions().pluralCategories.sort(((e,t)=>I[e]-I[t])).map((e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`)):n.numbers.map((n=>this.getSuffix(e,n,t))):[]}getSuffix(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=this.getRule(e,n);return s?this.shouldUseIntlApi()?`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${s.select(t)}`:this.getSuffixRetroCompatible(s,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){const n=e.noAbs?e.plurals(t):e.plurals(Math.abs(t));let s=e.numbers[n];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===s?s="plural":1===s&&(s=""));const o=()=>this.options.prepend&&s.toString()?this.options.prepend+s.toString():s.toString();return"v1"===this.options.compatibilityJSON?1===s?"":"number"==typeof s?`_plural_${s.toString()}`:o():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?o():this.options.prepend&&n.toString()?this.options.prepend+n.toString():n.toString()}shouldUseIntlApi(){return!A.includes(this.options.compatibilityJSON)}}const U=function(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],r=((e,t,n)=>{const s=p(e,n);return void 0!==s?s:p(t,n)})(e,t,n);return!r&&i&&o(n)&&(r=v(e,n,s),void 0===r&&(r=v(t,n,s))),r},F=e=>e.replace(/\$/g,"$$$$");class B{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=S.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:n,useRawValueToEscape:s,prefix:o,prefixEscaped:i,suffix:r,suffixEscaped:a,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:h,nestingPrefixEscaped:p,nestingSuffix:d,nestingSuffixEscaped:g,nestingOptionsSeparator:m,maxReplaces:b,alwaysFormat:v}=e.interpolation;this.escape=void 0!==t?t:y,this.escapeValue=void 0===n||n,this.useRawValueToEscape=void 0!==s&&s,this.prefix=o?f(o):i||"{{",this.suffix=r?f(r):a||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=h?f(h):p||f("$t("),this.nestingSuffix=d?f(d):g||f(")"),this.nestingOptionsSeparator=m||",",this.maxReplaces=b||1e3,this.alwaysFormat=void 0!==v&&v,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,n,s){let i,a,l;const u=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},c=e=>{if(e.indexOf(this.formatSeparator)<0){const o=U(t,u,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(o,void 0,n,{...s,...t,interpolationkey:e}):o}const o=e.split(this.formatSeparator),i=o.shift().trim(),r=o.join(this.formatSeparator).trim();return this.format(U(t,u,i,this.options.keySeparator,this.options.ignoreJSONStructure),r,n,{...s,...t,interpolationkey:i})};this.resetRegExp();const h=s&&s.missingInterpolationHandler||this.options.missingInterpolationHandler,p=s&&s.interpolation&&void 0!==s.interpolation.skipOnVariables?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>F(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?F(this.escape(e)):F(e)}].forEach((t=>{for(l=0;i=t.regex.exec(e);){const n=i[1].trim();if(a=c(n),void 0===a)if("function"==typeof h){const t=h(e,i,s);a=o(t)?t:""}else if(s&&Object.prototype.hasOwnProperty.call(s,n))a="";else{if(p){a=i[0];continue}this.logger.warn(`missed to pass in variable ${n} for interpolating ${e}`),a=""}else o(a)||this.useRawValueToEscape||(a=r(a));const u=t.safeValue(a);if(e=e.replace(i[0],u),p?(t.regex.lastIndex+=a.length,t.regex.lastIndex-=i[0].length):t.regex.lastIndex=0,l++,l>=this.maxReplaces)break}})),e}nest(e,t){let n,s,i,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const l=(e,t)=>{const n=this.nestingOptionsSeparator;if(e.indexOf(n)<0)return e;const s=e.split(new RegExp(`${n}[ ]*{`));let o=`{${s[1]}`;e=s[0],o=this.interpolate(o,i);const r=o.match(/'/g),a=o.match(/"/g);(r&&r.length%2==0&&!a||a.length%2!=0)&&(o=o.replace(/'/g,'"'));try{i=JSON.parse(o),t&&(i={...t,...i})}catch(l){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,l),`${e}${n}${o}`}return i.defaultValue&&i.defaultValue.indexOf(this.prefix)>-1&&delete i.defaultValue,e};for(;n=this.nestingRegexp.exec(e);){let u=[];i={...a},i=i.replace&&!o(i.replace)?i.replace:i,i.applyPostProcessor=!1,delete i.defaultValue;let c=!1;if(-1!==n[0].indexOf(this.formatSeparator)&&!/{.*}/.test(n[1])){const e=n[1].split(this.formatSeparator).map((e=>e.trim()));n[1]=e.shift(),u=e,c=!0}if(s=t(l.call(this,n[1].trim(),i),i),s&&n[0]===e&&!o(s))return s;o(s)||(s=r(s)),s||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${e}`),s=""),c&&(s=u.reduce(((e,t)=>this.format(e,t,a.lng,{...a,interpolationkey:n[1].trim()})),s.trim())),e=e.replace(n[0],s),this.regexp.lastIndex=0}return e}}const _=e=>{const t={};return(n,s,o)=>{let i=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(i={...i,[o.interpolationkey]:void 0});const r=s+JSON.stringify(i);let a=t[r];return a||(a=e(x(s),o),t[r]=a),a(n)}};class M{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=S.create("formatter"),this.options=e,this.formats={number:_(((e,t)=>{const n=new Intl.NumberFormat(e,{...t});return e=>n.format(e)})),currency:_(((e,t)=>{const n=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>n.format(e)})),datetime:_(((e,t)=>{const n=new Intl.DateTimeFormat(e,{...t});return e=>n.format(e)})),relativetime:_(((e,t)=>{const n=new Intl.RelativeTimeFormat(e,{...t});return e=>n.format(e,t.range||"day")})),list:_(((e,t)=>{const n=new Intl.ListFormat(e,{...t});return e=>n.format(e)}))},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=_(t)}format(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o=t.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find((e=>e.indexOf(")")>-1))){const e=o.findIndex((e=>e.indexOf(")")>-1));o[0]=[o[0],...o.splice(1,e)].join(this.formatSeparator)}return o.reduce(((e,t)=>{const{formatName:o,formatOptions:i}=(e=>{let t=e.toLowerCase().trim();const n={};if(e.indexOf("(")>-1){const s=e.split("(");t=s[0].toLowerCase().trim();const o=s[1].substring(0,s[1].length-1);"currency"===t&&o.indexOf(":")<0?n.currency||(n.currency=o.trim()):"relativetime"===t&&o.indexOf(":")<0?n.range||(n.range=o.trim()):o.split(";").forEach((e=>{if(e){const[t,...s]=e.split(":"),o=s.join(":").trim().replace(/^'+|'+$/g,""),i=t.trim();n[i]||(n[i]=o),"false"===o&&(n[i]=!1),"true"===o&&(n[i]=!0),isNaN(o)||(n[i]=parseInt(o,10))}}))}return{formatName:t,formatOptions:n}})(t);if(this.formats[o]){let t=e;try{const r=s&&s.formatParams&&s.formatParams[s.interpolationkey]||{},a=r.locale||r.lng||s.locale||s.lng||n;t=this.formats[o](e,a,{...i,...s,...r})}catch(r){this.logger.warn(r)}return t}return this.logger.warn(`there was no format function for ${o}`),e}),e)}}class H extends k{constructor(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=s,this.logger=S.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(n,s.backend,s)}queueLoad(e,t,n,s){const o={},i={},r={},a={};return e.forEach((e=>{let s=!0;t.forEach((t=>{const r=`${e}|${t}`;!n.reload&&this.store.hasResourceBundle(e,t)?this.state[r]=2:this.state[r]<0||(1===this.state[r]?void 0===i[r]&&(i[r]=!0):(this.state[r]=1,s=!1,void 0===i[r]&&(i[r]=!0),void 0===o[r]&&(o[r]=!0),void 0===a[t]&&(a[t]=!0)))})),s||(r[e]=!0)})),(Object.keys(o).length||Object.keys(i).length)&&this.queue.push({pending:i,pendingCount:Object.keys(i).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(o),pending:Object.keys(i),toLoadLanguages:Object.keys(r),toLoadNamespaces:Object.keys(a)}}loaded(e,t,n){const s=e.split("|"),o=s[0],i=s[1];t&&this.emit("failedLoading",o,i,t),!t&&n&&this.store.addResourceBundle(o,i,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&n&&(this.state[e]=0);const r={};this.queue.forEach((n=>{((e,t,n)=>{const{obj:s,k:o}=c(e,t,Object);s[o]=s[o]||[],s[o].push(n)})(n.loaded,[o],i),((e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)})(n,e),t&&n.errors.push(t),0!==n.pendingCount||n.done||(Object.keys(n.loaded).forEach((e=>{r[e]||(r[e]={});const t=n.loaded[e];t.length&&t.forEach((t=>{void 0===r[e][t]&&(r[e][t]=!0)}))})),n.done=!0,n.errors.length?n.callback(n.errors):n.callback())})),this.emit("loaded",r),this.queue=this.queue.filter((e=>!e.done))}read(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,i=arguments.length>5?arguments[5]:void 0;if(!e.length)return i(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:n,tried:s,wait:o,callback:i});this.readingCalls++;const r=(r,a)=>{if(this.readingCalls--,this.waitingReads.length>0){const e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}r&&a&&s<this.maxRetries?setTimeout((()=>{this.read.call(this,e,t,n,s+1,2*o,i)}),o):i(r,a)},a=this.backend[n].bind(this.backend);if(2!==a.length)return a(e,t,r);try{const n=a(e,t);n&&"function"==typeof n.then?n.then((e=>r(null,e))).catch(r):r(null,n)}catch(l){r(l)}}prepareLoading(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();o(e)&&(e=this.languageUtils.toResolveHierarchy(e)),o(t)&&(t=[t]);const i=this.queueLoad(e,t,n,s);if(!i.toLoad.length)return i.pending.length||s(),null;i.toLoad.forEach((e=>{this.loadOne(e)}))}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const n=e.split("|"),s=n[0],o=n[1];this.read(s,o,"read",void 0,void 0,((n,i)=>{n&&this.logger.warn(`${t}loading namespace ${o} for language ${s} failed`,n),!n&&i&&this.logger.log(`${t}loaded namespace ${o} for language ${s}`,i),this.loaded(e,n,i)}))}saveMissing(e,t,n,s,o){let i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t))this.logger.warn(`did not save key "${n}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");else if(null!=n&&""!==n){if(this.backend&&this.backend.create){const l={...i,isUpdate:o},u=this.backend.create.bind(this.backend);if(u.length<6)try{let o;o=5===u.length?u(e,t,n,s,l):u(e,t,n,s),o&&"function"==typeof o.then?o.then((e=>r(null,e))).catch(r):r(null,o)}catch(a){r(a)}else u(e,t,n,s,r,l)}e&&e[0]&&this.store.addResource(e[0],t,n,s)}}}const V=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),o(e[1])&&(t.defaultValue=e[1]),o(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){const n=e[3]||e[2];Object.keys(n).forEach((e=>{t[e]=n[e]}))}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),q=e=>(o(e.ns)&&(e.ns=[e.ns]),o(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),o(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&e.supportedLngs.indexOf("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),K=()=>{};class z extends k{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;var n;if(super(),this.options=q(e),this.services={},this.logger=S,this.modules={external:[]},n=this,Object.getOwnPropertyNames(Object.getPrototypeOf(n)).forEach((e=>{"function"==typeof n[e]&&(n[e]=n[e].bind(n))})),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout((()=>{this.init(e,t)}),0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(n=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(o(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const s=V();this.options={...s,...this.options,...q(t)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...s.interpolation,...this.options.interpolation}),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);const r=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?S.init(r(this.modules.logger),this.options):S.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!=typeof Intl&&(t=M);const n=new C(this.options);this.store=new L(this.options.resources,this.options);const o=this.services;o.logger=S,o.resourceStore=this.store,o.languageUtils=n,o.pluralResolver=new D(n,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!t||this.options.interpolation.format&&this.options.interpolation.format!==s.interpolation.format||(o.formatter=r(t),o.formatter.init(o,this.options),this.options.interpolation.format=o.formatter.format.bind(o.formatter)),o.interpolator=new B(this.options),o.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},o.backendConnector=new H(r(this.modules.backend),o.resourceStore,o,this.options),o.backendConnector.on("*",(function(t){for(var n=arguments.length,s=new Array(n>1?n-1:0),o=1;o<n;o++)s[o-1]=arguments[o];e.emit(t,...s)})),this.modules.languageDetector&&(o.languageDetector=r(this.modules.languageDetector),o.languageDetector.init&&o.languageDetector.init(o,this.options.detection,this.options)),this.modules.i18nFormat&&(o.i18nFormat=r(this.modules.i18nFormat),o.i18nFormat.init&&o.i18nFormat.init(this)),this.translator=new j(this.services,this.options),this.translator.on("*",(function(t){for(var n=arguments.length,s=new Array(n>1?n-1:0),o=1;o<n;o++)s[o-1]=arguments[o];e.emit(t,...s)})),this.modules.external.forEach((e=>{e.init&&e.init(this)}))}if(this.format=this.options.interpolation.format,n||(n=K),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach((t=>{this[t]=function(){return e.store[t](...arguments)}}));["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach((t=>{this[t]=function(){return e.store[t](...arguments),e}}));const a=i(),l=()=>{const e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(t),n(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?l():setTimeout(l,0),a}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:K;const n=o(e)?e:this.language;if("function"==typeof e&&(t=e),!this.options.resources||this.options.partialBundledLanguages){if(n&&"cimode"===n.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return t();const e=[],s=t=>{if(!t)return;if("cimode"===t)return;this.services.languageUtils.toResolveHierarchy(t).forEach((t=>{"cimode"!==t&&e.indexOf(t)<0&&e.push(t)}))};if(n)s(n);else{this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach((e=>s(e)))}this.options.preload&&this.options.preload.forEach((e=>s(e))),this.services.backendConnector.load(e,this.options.ns,(e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),t(e)}))}else t(null)}reloadResources(e,t,n){const s=i();return"function"==typeof e&&(n=e,e=void 0),"function"==typeof t&&(n=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),n||(n=K),this.services.backendConnector.reload(e,t,(e=>{s.resolve(),n(e)})),s}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&P.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let t=0;t<this.languages.length;t++){const e=this.languages[t];if(!(["cimode","dev"].indexOf(e)>-1)&&this.store.hasLanguageSomeTranslations(e)){this.resolvedLanguage=e;break}}}changeLanguage(e,t){var n=this;this.isLanguageChangingTo=e;const s=i();this.emit("languageChanging",e);const r=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},a=(e,o)=>{o?(r(o),this.translator.changeLanguage(o),this.isLanguageChangingTo=void 0,this.emit("languageChanged",o),this.logger.log("languageChanged",o)):this.isLanguageChangingTo=void 0,s.resolve((function(){return n.t(...arguments)})),t&&t(e,(function(){return n.t(...arguments)}))},l=t=>{e||t||!this.services.languageDetector||(t=[]);const n=o(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);n&&(this.language||r(n),this.translator.language||this.translator.changeLanguage(n),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(n)),this.loadResources(n,(e=>{a(e,n)}))};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e):l(this.services.languageDetector.detect()),s}getFixedT(e,t,n){var s=this;const i=function(e,t){let o;if("object"!=typeof t){for(var r=arguments.length,a=new Array(r>2?r-2:0),l=2;l<r;l++)a[l-2]=arguments[l];o=s.options.overloadTranslationOptionHandler([e,t].concat(a))}else o={...t};o.lng=o.lng||i.lng,o.lngs=o.lngs||i.lngs,o.ns=o.ns||i.ns,""!==o.keyPrefix&&(o.keyPrefix=o.keyPrefix||n||i.keyPrefix);const u=s.options.keySeparator||".";let c;return c=o.keyPrefix&&Array.isArray(e)?e.map((e=>`${o.keyPrefix}${u}${e}`)):o.keyPrefix?`${o.keyPrefix}${u}${e}`:e,s.t(c,o)};return o(e)?i.lng=e:i.lngs=e,i.ns=t,i.keyPrefix=n,i}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const n=t.lng||this.resolvedLanguage||this.languages[0],s=!!this.options&&this.options.fallbackLng,o=this.languages[this.languages.length-1];if("cimode"===n.toLowerCase())return!0;const i=(e,t)=>{const n=this.services.backendConnector.state[`${e}|${t}`];return-1===n||0===n||2===n};if(t.precheck){const e=t.precheck(this,i);if(void 0!==e)return e}return!!this.hasResourceBundle(n,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!i(n,e)||s&&!i(o,e)))}loadNamespaces(e,t){const n=i();return this.options.ns?(o(e)&&(e=[e]),e.forEach((e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)})),this.loadResources((e=>{n.resolve(),t&&t(e)})),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){const n=i();o(e)&&(e=[e]);const s=this.options.preload||[],r=e.filter((e=>s.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e)));return r.length?(this.options.preload=s.concat(r),this.loadResources((e=>{n.resolve(),t&&t(e)})),n):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";const t=this.services&&this.services.languageUtils||new C(V());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){return new z(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1?arguments[1]:void 0)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:K;const n=e.forkResourceStore;n&&delete e.forkResourceStore;const s={...this.options,...e,isClone:!0},o=new z(s);void 0===e.debug&&void 0===e.prefix||(o.logger=o.logger.clone(e));return["store","services","language"].forEach((e=>{o[e]=this[e]})),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},n&&(o.store=new L(this.store.data,s),o.services.resourceStore=o.store),o.translator=new j(o.services,s),o.translator.on("*",(function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];o.emit(e,...n)})),o.init(s,t),o.translator.options=s,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const J=z.createInstance();J.createInstance=z.createInstance,J.createInstance,J.dir,J.init,J.loadResources,J.reloadResources,J.use,J.changeLanguage,J.getFixedT,J.t,J.exists,J.setDefaultNamespace,J.hasLoadedNamespace,J.loadNamespaces,J.loadLanguages;const X={};function G(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];"string"==typeof t[0]&&X[t[0]]||("string"==typeof t[0]&&(X[t[0]]=new Date),function(){if(console&&console.warn){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];"string"==typeof t[0]&&(t[0]=`react-i18next:: ${t[0]}`)}}(...t))}const W=(e,t)=>()=>{if(e.isInitialized)t();else{const n=()=>{setTimeout((()=>{e.off("initialized",n)}),0),t()};e.on("initialized",n)}};function Y(e,t,n){e.loadNamespaces(t,W(e,n))}function Q(e,t,n,s){"string"==typeof n&&(n=[n]),n.forEach((t=>{e.options.ns.indexOf(t)<0&&e.options.ns.push(t)})),e.loadLanguages(t,W(e,s))}const Z=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,ee={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},te=e=>ee[e];let ne,se={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(Z,te)};const oe={type:"3rdParty",init(e){!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};se={...se,...e}}(e.options.react),function(e){ne=e}(e)}},ie=e.createContext();class re{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}function ae(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{i18n:s}=n,{i18n:o,defaultNS:i}=e.useContext(ie)||{},r=s||o||ne;if(r&&!r.reportNamespaces&&(r.reportNamespaces=new re),!r){G("You will need to pass in an i18next instance by using initReactI18next");const e=(e,t)=>"string"==typeof t?t:t&&"object"==typeof t&&"string"==typeof t.defaultValue?t.defaultValue:Array.isArray(e)?e[e.length-1]:e,t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}r.options.react&&void 0!==r.options.react.wait&&G("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...se,...r.options.react,...n},{useSuspense:l,keyPrefix:u}=a;let c=t||i||r.options&&r.options.defaultNS;c="string"==typeof c?[c]:c||["translation"],r.reportNamespaces.addUsedNamespaces&&r.reportNamespaces.addUsedNamespaces(c);const h=(r.isInitialized||r.initializedStoreOnce)&&c.every((e=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t.languages&&t.languages.length?void 0!==t.options.ignoreJSONStructure?t.hasLoadedNamespace(e,{lng:n.lng,precheck:(t,s)=>{if(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!s(t.isLanguageChangingTo,e))return!1}}):function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=t.languages[0],o=!!t.options&&t.options.fallbackLng,i=t.languages[t.languages.length-1];if("cimode"===s.toLowerCase())return!0;const r=(e,n)=>{const s=t.services.backendConnector.state[`${e}|${n}`];return-1===s||2===s};return!(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!r(t.isLanguageChangingTo,e)||!t.hasResourceBundle(s,e)&&t.services.backendConnector.backend&&(!t.options.resources||t.options.partialBundledLanguages)&&(!r(s,e)||o&&!r(i,e)))}(e,t,n):(G("i18n.languages were undefined or empty",t.languages),!0)}(e,r,a)));function p(){return r.getFixedT(n.lng||null,"fallback"===a.nsMode?c:c[0],u)}const[d,f]=e.useState(p);let g=c.join();n.lng&&(g=`${n.lng}${g}`);const y=((t,n)=>{const s=e.useRef();return e.useEffect((()=>{s.current=t}),[t,n]),s.current})(g),m=e.useRef(!0);e.useEffect((()=>{const{bindI18n:e,bindI18nStore:t}=a;function s(){m.current&&f(p)}return m.current=!0,h||l||(n.lng?Q(r,n.lng,c,(()=>{m.current&&f(p)})):Y(r,c,(()=>{m.current&&f(p)}))),h&&y&&y!==g&&m.current&&f(p),e&&r&&r.on(e,s),t&&r&&r.store.on(t,s),()=>{m.current=!1,e&&r&&e.split(" ").forEach((e=>r.off(e,s))),t&&r&&t.split(" ").forEach((e=>r.store.off(e,s)))}}),[r,g]);const b=e.useRef(!0);e.useEffect((()=>{m.current&&!b.current&&f(p),b.current=!1}),[r,u]);const v=[d,r,h];if(v.t=d,v.i18n=r,v.ready=h,h)return v;if(!h&&!l)return v;throw new Promise((e=>{n.lng?Q(r,n.lng,c,(()=>e())):Y(r,c,(()=>e()))}))}function le(e){return(le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ue(){return"function"==typeof XMLHttpRequest||"object"===("undefined"==typeof XMLHttpRequest?"undefined":le(XMLHttpRequest))}function ce(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var he,pe={exports:{}},de={exports:{}};function fe(){return he||(he=1,function(e,n){var s,o="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==t&&t,i=function(){function e(){this.fetch=!1,this.DOMException=o.DOMException}return e.prototype=o,new e}();s=i,function(e){var t=void 0!==s&&s||"undefined"!=typeof self&&self||void 0!==t&&t,n="URLSearchParams"in t,o="Symbol"in t&&"iterator"in Symbol,i="FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(e){return!1}}(),r="FormData"in t,a="ArrayBuffer"in t;if(a)var l=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],u=ArrayBuffer.isView||function(e){return e&&l.indexOf(Object.prototype.toString.call(e))>-1};function c(e){if("string"!=typeof e&&(e=String(e)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(e)||""===e)throw new TypeError('Invalid character in header field name: "'+e+'"');return e.toLowerCase()}function h(e){return"string"!=typeof e&&(e=String(e)),e}function p(e){var t={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return o&&(t[Symbol.iterator]=function(){return t}),t}function d(e){this.map={},e instanceof d?e.forEach((function(e,t){this.append(t,e)}),this):Array.isArray(e)?e.forEach((function(e){this.append(e[0],e[1])}),this):e&&Object.getOwnPropertyNames(e).forEach((function(t){this.append(t,e[t])}),this)}function f(e){if(e.bodyUsed)return Promise.reject(new TypeError("Already read"));e.bodyUsed=!0}function g(e){return new Promise((function(t,n){e.onload=function(){t(e.result)},e.onerror=function(){n(e.error)}}))}function y(e){var t=new FileReader,n=g(t);return t.readAsArrayBuffer(e),n}function m(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function b(){return this.bodyUsed=!1,this._initBody=function(e){var t;this.bodyUsed=this.bodyUsed,this._bodyInit=e,e?"string"==typeof e?this._bodyText=e:i&&Blob.prototype.isPrototypeOf(e)?this._bodyBlob=e:r&&FormData.prototype.isPrototypeOf(e)?this._bodyFormData=e:n&&URLSearchParams.prototype.isPrototypeOf(e)?this._bodyText=e.toString():a&&i&&(t=e)&&DataView.prototype.isPrototypeOf(t)?(this._bodyArrayBuffer=m(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):a&&(ArrayBuffer.prototype.isPrototypeOf(e)||u(e))?this._bodyArrayBuffer=m(e):this._bodyText=e=Object.prototype.toString.call(e):this._bodyText="",this.headers.get("content-type")||("string"==typeof e?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):n&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},i&&(this.blob=function(){var e=f(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){if(this._bodyArrayBuffer){var e=f(this);return e||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}return this.blob().then(y)}),this.text=function(){var e,t,n,s=f(this);if(s)return s;if(this._bodyBlob)return e=this._bodyBlob,t=new FileReader,n=g(t),t.readAsText(e),n;if(this._bodyArrayBuffer)return Promise.resolve(function(e){for(var t=new Uint8Array(e),n=new Array(t.length),s=0;s<t.length;s++)n[s]=String.fromCharCode(t[s]);return n.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},r&&(this.formData=function(){return this.text().then(w)}),this.json=function(){return this.text().then(JSON.parse)},this}d.prototype.append=function(e,t){e=c(e),t=h(t);var n=this.map[e];this.map[e]=n?n+", "+t:t},d.prototype.delete=function(e){delete this.map[c(e)]},d.prototype.get=function(e){return e=c(e),this.has(e)?this.map[e]:null},d.prototype.has=function(e){return this.map.hasOwnProperty(c(e))},d.prototype.set=function(e,t){this.map[c(e)]=h(t)},d.prototype.forEach=function(e,t){for(var n in this.map)this.map.hasOwnProperty(n)&&e.call(t,this.map[n],n,this)},d.prototype.keys=function(){var e=[];return this.forEach((function(t,n){e.push(n)})),p(e)},d.prototype.values=function(){var e=[];return this.forEach((function(t){e.push(t)})),p(e)},d.prototype.entries=function(){var e=[];return this.forEach((function(t,n){e.push([n,t])})),p(e)},o&&(d.prototype[Symbol.iterator]=d.prototype.entries);var v=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function x(e,t){if(!(this instanceof x))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var n,s,o=(t=t||{}).body;if(e instanceof x){if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new d(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,o||null==e._bodyInit||(o=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||"same-origin",!t.headers&&this.headers||(this.headers=new d(t.headers)),this.method=(n=t.method||this.method||"GET",s=n.toUpperCase(),v.indexOf(s)>-1?s:n),this.mode=t.mode||this.mode||null,this.signal=t.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&o)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(o),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==t.cache&&"no-cache"!==t.cache)){var i=/([?&])_=[^&]*/;i.test(this.url)?this.url=this.url.replace(i,"$1_="+(new Date).getTime()):this.url+=(/\?/.test(this.url)?"&":"?")+"_="+(new Date).getTime()}}function w(e){var t=new FormData;return e.trim().split("&").forEach((function(e){if(e){var n=e.split("="),s=n.shift().replace(/\+/g," "),o=n.join("=").replace(/\+/g," ");t.append(decodeURIComponent(s),decodeURIComponent(o))}})),t}function O(e,t){if(!(this instanceof O))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');t||(t={}),this.type="default",this.status=void 0===t.status?200:t.status,this.ok=this.status>=200&&this.status<300,this.statusText=void 0===t.statusText?"":""+t.statusText,this.headers=new d(t.headers),this.url=t.url||"",this._initBody(e)}x.prototype.clone=function(){return new x(this,{body:this._bodyInit})},b.call(x.prototype),b.call(O.prototype),O.prototype.clone=function(){return new O(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new d(this.headers),url:this.url})},O.error=function(){var e=new O(null,{status:0,statusText:""});return e.type="error",e};var S=[301,302,303,307,308];O.redirect=function(e,t){if(-1===S.indexOf(t))throw new RangeError("Invalid status code");return new O(null,{status:t,headers:{location:e}})},e.DOMException=t.DOMException;try{new e.DOMException}catch(L){e.DOMException=function(e,t){this.message=e,this.name=t;var n=Error(e);this.stack=n.stack},e.DOMException.prototype=Object.create(Error.prototype),e.DOMException.prototype.constructor=e.DOMException}function k(n,s){return new Promise((function(o,r){var l=new x(n,s);if(l.signal&&l.signal.aborted)return r(new e.DOMException("Aborted","AbortError"));var u=new XMLHttpRequest;function c(){u.abort()}u.onload=function(){var e,t,n={status:u.status,statusText:u.statusText,headers:(e=u.getAllResponseHeaders()||"",t=new d,e.replace(/\r?\n[\t ]+/g," ").split("\r").map((function(e){return 0===e.indexOf("\n")?e.substr(1,e.length):e})).forEach((function(e){var n=e.split(":"),s=n.shift().trim();if(s){var o=n.join(":").trim();t.append(s,o)}})),t)};n.url="responseURL"in u?u.responseURL:n.headers.get("X-Request-URL");var s="response"in u?u.response:u.responseText;setTimeout((function(){o(new O(s,n))}),0)},u.onerror=function(){setTimeout((function(){r(new TypeError("Network request failed"))}),0)},u.ontimeout=function(){setTimeout((function(){r(new TypeError("Network request failed"))}),0)},u.onabort=function(){setTimeout((function(){r(new e.DOMException("Aborted","AbortError"))}),0)},u.open(l.method,function(e){try{return""===e&&t.location.href?t.location.href:e}catch(n){return e}}(l.url),!0),"include"===l.credentials?u.withCredentials=!0:"omit"===l.credentials&&(u.withCredentials=!1),"responseType"in u&&(i?u.responseType="blob":a&&l.headers.get("Content-Type")&&-1!==l.headers.get("Content-Type").indexOf("application/octet-stream")&&(u.responseType="arraybuffer")),!s||"object"!=typeof s.headers||s.headers instanceof d?l.headers.forEach((function(e,t){u.setRequestHeader(t,e)})):Object.getOwnPropertyNames(s.headers).forEach((function(e){u.setRequestHeader(e,h(s.headers[e]))})),l.signal&&(l.signal.addEventListener("abort",c),u.onreadystatechange=function(){4===u.readyState&&l.signal.removeEventListener("abort",c)}),u.send(void 0===l._bodyInit?null:l._bodyInit)}))}k.polyfill=!0,t.fetch||(t.fetch=k,t.Headers=d,t.Request=x,t.Response=O),e.Headers=d,e.Request=x,e.Response=O,e.fetch=k}({}),i.fetch.ponyfill=!0,delete i.fetch.polyfill;var r=o.fetch?o:i;(n=r.fetch).default=r.fetch,n.fetch=r.fetch,n.Headers=r.Headers,n.Request=r.Request,n.Response=r.Response,e.exports=n}(de,de.exports)),de.exports}!function(e,n){var s="function"==typeof fetch?fetch:void 0;if(void 0!==t&&t.fetch?s=t.fetch:"undefined"!=typeof window&&window.fetch&&(s=window.fetch),void 0!==ce&&"undefined"==typeof window){var o=s||fe();o.default&&(o=o.default),n.default=o,e.exports=n.default}}(pe,pe.exports);var ge=pe.exports;const ye=n(ge),me=s({__proto__:null,default:ye},[ge]);function be(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function ve(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?be(Object(n),!0).forEach((function(t){xe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):be(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function xe(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=we(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t);if("object"!=we(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==we(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function we(e){return(we="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Oe,Se,ke="function"==typeof fetch?fetch:void 0;"undefined"!=typeof global&&global.fetch?ke=global.fetch:"undefined"!=typeof window&&window.fetch&&(ke=window.fetch),ue()&&("undefined"!=typeof global&&global.XMLHttpRequest?Oe=global.XMLHttpRequest:"undefined"!=typeof window&&window.XMLHttpRequest&&(Oe=window.XMLHttpRequest)),"function"==typeof ActiveXObject&&("undefined"!=typeof global&&global.ActiveXObject?Se=global.ActiveXObject:"undefined"!=typeof window&&window.ActiveXObject&&(Se=window.ActiveXObject)),ke||!me||Oe||Se||(ke=ye||me),"function"!=typeof ke&&(ke=void 0);var Le=function(e,t){if(t&&"object"===we(t)){var n="";for(var s in t)n+="&"+encodeURIComponent(s)+"="+encodeURIComponent(t[s]);if(!n)return e;e=e+(-1!==e.indexOf("?")?"&":"?")+n.slice(1)}return e},Pe=function(e,t,n,s){var o=function(e){if(!e.ok)return n(e.statusText||"Error",{status:e.status});e.text().then((function(t){n(null,{status:e.status,data:t})})).catch(n)};if(s){var i=s(e,t);if(i instanceof Promise)return void i.then(o).catch(n)}"function"==typeof fetch?fetch(e,t).then(o).catch(n):ke(e,t).then(o).catch(n)},Re=!1,je=function(e,t,n,s){return"function"==typeof n&&(s=n,n=void 0),s=s||function(){},ke&&0!==t.indexOf("file:")?function(e,t,n,s){e.queryStringParams&&(t=Le(t,e.queryStringParams));var o=ve({},"function"==typeof e.customHeaders?e.customHeaders():e.customHeaders);"undefined"==typeof window&&"undefined"!=typeof global&&void 0!==global.process&&global.process.versions&&global.process.versions.node&&(o["User-Agent"]="i18next-http-backend (node/".concat(global.process.version,"; ").concat(global.process.platform," ").concat(global.process.arch,")")),n&&(o["Content-Type"]="application/json");var i="function"==typeof e.requestOptions?e.requestOptions(n):e.requestOptions,r=ve({method:n?"POST":"GET",body:n?e.stringify(n):void 0,headers:o},Re?{}:i),a="function"==typeof e.alternateFetch&&e.alternateFetch.length>=1?e.alternateFetch:void 0;try{Pe(t,r,s,a)}catch(l){if(!i||0===Object.keys(i).length||!l.message||l.message.indexOf("not implemented")<0)return s(l);try{Object.keys(i).forEach((function(e){delete r[e]})),Pe(t,r,s,a),Re=!0}catch(u){s(u)}}}(e,t,n,s):ue()||"function"==typeof ActiveXObject?function(e,t,n,s){n&&"object"===we(n)&&(n=Le("",n).slice(1)),e.queryStringParams&&(t=Le(t,e.queryStringParams));try{var o;(o=Oe?new Oe:new Se("MSXML2.XMLHTTP.3.0")).open(n?"POST":"GET",t,1),e.crossDomain||o.setRequestHeader("X-Requested-With","XMLHttpRequest"),o.withCredentials=!!e.withCredentials,n&&o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.overrideMimeType&&o.overrideMimeType("application/json");var i=e.customHeaders;if(i="function"==typeof i?i():i)for(var r in i)o.setRequestHeader(r,i[r]);o.onreadystatechange=function(){o.readyState>3&&s(o.status>=400?o.statusText:null,{status:o.status,data:o.responseText})},o.send(n)}catch(a){console}}(e,t,n,s):void s(new Error("No fetch and no xhr implementation found!"))};function Ne(e){return(Ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ce(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ce(Object(n),!0).forEach((function(t){Ae(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ce(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function $e(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,Te(s.key),s)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ae(e,t,n){return(t=Te(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Te(e){var t=function(e,t){if("object"!=Ne(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t);if("object"!=Ne(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==Ne(t)?t:t+""}var Ie=$e((function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.services=t,this.options=n,this.allOptions=s,this.type="backend",this.init(t,n,s)}),[{key:"init",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.services=e,this.options=Ee(Ee(Ee({},{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(e){return JSON.parse(e)},stringify:JSON.stringify,parsePayload:function(e,t,n){return Ae({},t,n||"")},parseLoadPayload:function(e,t){},request:je,reloadInterval:"undefined"==typeof window&&36e5,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}),this.options||{}),n),this.allOptions=s,this.services&&this.options.reloadInterval){var o=setInterval((function(){return t.reload()}),this.options.reloadInterval);"object"===Ne(o)&&"function"==typeof o.unref&&o.unref()}}},{key:"readMulti",value:function(e,t,n){this._readAny(e,e,t,t,n)}},{key:"read",value:function(e,t,n){this._readAny([e],e,[t],t,n)}},{key:"_readAny",value:function(e,t,n,s,o){var i,r=this,a=this.options.loadPath;"function"==typeof this.options.loadPath&&(a=this.options.loadPath(e,n)),(a=function(e){return!!e&&"function"==typeof e.then}(i=a)?i:Promise.resolve(i)).then((function(i){if(!i)return o(null,{});var a=r.services.interpolator.interpolate(i,{lng:e.join("+"),ns:n.join("+")});r.loadUrl(a,o,t,s)}))}},{key:"loadUrl",value:function(e,t,n,s){var o=this,i="string"==typeof n?[n]:n,r="string"==typeof s?[s]:s,a=this.options.parseLoadPayload(i,r);this.options.request(this.options,e,a,(function(i,r){if(r&&(r.status>=500&&r.status<600||!r.status))return t("failed loading "+e+"; status code: "+r.status,!0);if(r&&r.status>=400&&r.status<500)return t("failed loading "+e+"; status code: "+r.status,!1);if(!r&&i&&i.message){var a=i.message.toLowerCase();if(["failed","fetch","network","load"].find((function(e){return a.indexOf(e)>-1})))return t("failed loading "+e+": "+i.message,!0)}if(i)return t(i,!1);var l,u;try{l="string"==typeof r.data?o.options.parse(r.data,n,s):r.data}catch(c){u="failed parsing "+e+" to json"}if(u)return t(u,!1);t(null,l)}))}},{key:"create",value:function(e,t,n,s,o){var i=this;if(this.options.addPath){"string"==typeof e&&(e=[e]);var r=this.options.parsePayload(t,n,s),a=0,l=[],u=[];e.forEach((function(n){var s=i.options.addPath;"function"==typeof i.options.addPath&&(s=i.options.addPath(n,t));var c=i.services.interpolator.interpolate(s,{lng:n,ns:t});i.options.request(i.options,c,r,(function(t,n){a+=1,l.push(t),u.push(n),a===e.length&&"function"==typeof o&&o(l,u)}))}))}}},{key:"reload",value:function(){var e=this,t=this.services,n=t.backendConnector,s=t.languageUtils,o=t.logger,i=n.language;if(!i||"cimode"!==i.toLowerCase()){var r=[],a=function(e){s.toResolveHierarchy(e).forEach((function(e){r.indexOf(e)<0&&r.push(e)}))};a(i),this.allOptions.preload&&this.allOptions.preload.forEach((function(e){return a(e)})),r.forEach((function(t){e.allOptions.ns.forEach((function(e){n.read(t,e,"read",null,null,(function(s,i){s&&o.warn("loading namespace ".concat(e," for language ").concat(t," failed"),s),!s&&i&&o.log("loaded namespace ".concat(e," for language ").concat(t),i),n.loaded("".concat(t,"|").concat(e),s,i)}))}))}))}}}]);Ie.type="backend";export{Ie as B,oe as a,J as i,ae as u};
//# sourceMappingURL=i18n-DWU17bW_.js.map
