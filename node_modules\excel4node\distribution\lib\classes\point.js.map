{"version": 3, "file": "point.js", "names": ["Point", "_createClass", "x", "y", "_classCallCheck", "module", "exports"], "sources": ["../../../source/lib/classes/point.js"], "sourcesContent": ["class Point {    \n    /** \n     * An XY coordinate point on the Worksheet with 0.0 being top left corner\n     * @class Point\n     * @property {Number} x X coordinate of Point\n     * @property {Number} y Y coordinate of Point\n     * @returns {Point} Excel Point\n     */\n    constructor(x, y) {    \n        this.x = x;\n        this.y = y;\n    }\n}\n\nmodule.exports = Point;"], "mappings": ";;;;;IAAMA,KAAK,gBAAAC,YAAA;AACP;AACJ;AACA;AACA;AACA;AACA;AACA;AACI,SAAAD,MAAYE,CAAC,EAAEC,CAAC,EAAE;EAAAC,eAAA,OAAAJ,KAAA;EACd,IAAI,CAACE,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,CAAC,GAAGA,CAAC;AACd,CAAC;AAGLE,MAAM,CAACC,OAAO,GAAGN,KAAK"}