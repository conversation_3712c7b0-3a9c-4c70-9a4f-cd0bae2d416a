{"version": 3, "file": "builder.js", "names": ["xmlbuilder", "require", "JSZip", "fs", "CTColor", "utils", "addRootContentTypesXML", "promiseObj", "Promise", "resolve", "reject", "xml", "create", "att", "contentTypesAdded", "extensionsAdded", "wb", "sheets", "for<PERSON>ach", "s", "i", "drawingCollection", "length", "drawings", "d", "indexOf", "extension", "typeRef", "contentType", "ele", "push", "Object", "keys", "comments", "concat", "sheetId", "xmlString", "doc", "end", "xmlOutVars", "xlsx", "file", "addRootRelsXML", "folder", "addWorkbookXML", "booksViewEle", "workbookViewEle", "opts", "workbookView", "viewOpts", "activeTab", "undefined", "firstVisibleTab", "sheet", "hidden", "autoFilterDateGrouping", "boolToInt", "firstSheet", "minimized", "showHorizontalScroll", "showSheetTabs", "showVerticalScroll", "tabRatio", "visibility", "windowWidth", "windowHeight", "xWindow", "yWindow", "showComments", "sheets<PERSON>le", "name", "printArea", "startCellRef", "getExcelAlpha", "startCol", "startRow", "endCellRef", "endCol", "endRow", "definedNameCollection", "addDefinedName", "localSheetId", "refForm<PERSON>", "isEmpty", "addToXMLele", "addWorkbookRelsXML", "addCorePropertiesXML", "text", "author", "dtStr", "Date", "toISOString", "addWorksheetsXML", "curSheet", "processNextSheet", "thisSheet", "generateXML", "then", "generateRelsXML", "generateCommentsXML", "generateCommentsVmlXML", "e", "logger", "error", "stack", "addSharedStringsXML", "sharedStrings", "txt", "Array", "thisSI", "theseRuns", "currProps", "curRun", "props", "_typeof", "k", "value", "run", "thisRun", "thisRunProps", "bold", "italics", "strike", "outline", "shadow", "condense", "extend", "color", "thisColor", "size", "underline", "vertAlign", "addStylesXML", "styleData", "numFmts", "nfXML", "nf", "fontXML", "fonts", "f", "fillXML", "fills", "fXML", "borderXML", "borders", "b", "cellStyleXfsXML", "cellXfsXML", "styles", "addXFtoXMLele", "cellStylesXML", "dxfCollection", "addDrawingsXML", "mediaCollection", "ws", "drawingRelXML", "drawingsXML", "kind", "target", "id", "image", "imagePath", "readFileSync", "rId", "type", "drawingsXMLStr", "drawingRelXMLStr", "writeToBuffer", "addWorksheet", "j<PERSON><PERSON>", "generateAsync", "buf", "workbookXML", "result", "files", "_data", "module", "exports"], "sources": ["../../../source/lib/workbook/builder.js"], "sourcesContent": ["const xmlbuilder = require('xmlbuilder');\nconst JSZip = require('jszip');\nconst fs = require('fs');\nconst CTColor = require('../style/classes/ctColor.js');\nconst utils = require('../utils');\n\nlet addRootContentTypesXML = (promiseObj) => {\n  // Required as stated in §12.2\n  return new Promise((resolve, reject) => {\n    let xml = xmlbuilder.create(\n        'Types', {\n          'version': '1.0',\n          'encoding': 'UTF-8',\n          'standalone': true,\n          'allowSurrogateChars': true\n        }\n      )\n      .att('xmlns', 'http://schemas.openxmlformats.org/package/2006/content-types');\n\n    let contentTypesAdded = [];\n    let extensionsAdded = [];\n    promiseObj.wb.sheets.forEach((s, i) => {\n      if (s.drawingCollection.length > 0) {\n        s.drawingCollection.drawings.forEach((d) => {\n          if (extensionsAdded.indexOf(d.extension) < 0) {\n            let typeRef = d.contentType + '.' + d.extension;\n            if (contentTypesAdded.indexOf(typeRef) < 0) {\n              xml.ele('Default').att('ContentType', d.contentType).att('Extension', d.extension);\n              contentTypesAdded.push(typeRef);\n            }\n            extensionsAdded.push(d.extension);\n          }\n        });\n      }\n      if (Object.keys(s.comments).length > 0) {\n        if (extensionsAdded.indexOf('vml') < 0) {\n          xml.ele('Default').att('Extension', 'vml').att('ContentType', 'application/vnd.openxmlformats-officedocument.vmlDrawing');\n          extensionsAdded.push('vml');\n        }\n      }\n    });\n    xml.ele('Default').att('ContentType', 'application/xml').att('Extension', 'xml');\n    xml.ele('Default').att('ContentType', 'application/vnd.openxmlformats-package.relationships+xml').att('Extension', 'rels');\n    xml.ele('Override').att('ContentType', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml').att('PartName', '/xl/workbook.xml');\n    promiseObj.wb.sheets.forEach((s, i) => {\n      xml.ele('Override')\n        .att('ContentType', 'application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml')\n        .att('PartName', `/xl/worksheets/sheet${i + 1}.xml`);\n\n      if (s.drawingCollection.length > 0) {\n        xml.ele('Override')\n          .att('ContentType', 'application/vnd.openxmlformats-officedocument.drawing+xml')\n          .att('PartName', '/xl/drawings/drawing' + s.sheetId + '.xml');\n      }\n      if (Object.keys(s.comments).length > 0) {\n        xml.ele('Override')\n          .att('ContentType', 'application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml')\n          .att('PartName', '/xl/comments' + s.sheetId + '.xml');\n      }\n    });\n    xml.ele('Override').att('ContentType', 'application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml').att('PartName', '/xl/styles.xml');\n    xml.ele('Override').att('ContentType', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml').att('PartName', '/xl/sharedStrings.xml');\n    xml.ele('Override').att('ContentType', 'application/vnd.openxmlformats-package.core-properties+xml').att('PartName', '/docProps/core.xml');\n\n    let xmlString = xml.doc().end(promiseObj.xmlOutVars);\n    promiseObj.xlsx.file('[Content_Types].xml', xmlString);\n    resolve(promiseObj);\n  });\n};\n\nlet addRootRelsXML = (promiseObj) => {\n  // Required as stated in §12.2\n  return new Promise((resolve, reject) => {\n    let xml = xmlbuilder.create(\n        'Relationships', {\n          'version': '1.0',\n          'encoding': 'UTF-8',\n          'standalone': true,\n          'allowSurrogateChars': true\n        }\n      )\n      .att('xmlns', 'http://schemas.openxmlformats.org/package/2006/relationships');\n\n    xml\n      .ele('Relationship')\n      .att('Id', 'rId1')\n      .att('Type', 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument')\n      .att('Target', 'xl/workbook.xml');\n\n    xml\n      .ele('Relationship')\n      .att('Id', 'rId2')\n      .att('Type', 'http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties')\n      .att('Target', 'docProps/core.xml');\n\n    let xmlString = xml.doc().end(promiseObj.xmlOutVars);\n    promiseObj.xlsx.folder('_rels').file('.rels', xmlString);\n    resolve(promiseObj);\n\n  });\n};\n\nlet addWorkbookXML = (promiseObj) => {\n  // Required as stated in §12.2\n  return new Promise((resolve, reject) => {\n\n    let xml = xmlbuilder.create(\n      'workbook', {\n        'version': '1.0',\n        'encoding': 'UTF-8',\n        'standalone': true,\n        'allowSurrogateChars': true\n      }\n    );\n    xml.att('mc:Ignorable', 'x15');\n    xml.att('xmlns', 'http://schemas.openxmlformats.org/spreadsheetml/2006/main');\n    xml.att('xmlns:mc', 'http://schemas.openxmlformats.org/markup-compatibility/2006');\n    xml.att('xmlns:r', 'http://schemas.openxmlformats.org/officeDocument/2006/relationships');\n    xml.att('xmlns:x15', 'http://schemas.microsoft.com/office/spreadsheetml/2010/11/main');\n\n    let booksViewEle = xml.ele('bookViews');\n    let workbookViewEle = booksViewEle.ele('workbookView');\n    // bookViews (§18.2.1)\n    if (promiseObj.wb.opts.workbookView) {\n      const viewOpts = promiseObj.wb.opts.workbookView;\n      if (viewOpts.activeTab !== null && viewOpts.activeTab !== undefined) {\n        workbookViewEle.att('activeTab', viewOpts.activeTab);\n      } else {\n        let firstVisibleTab = 0;\n        for (let i = 0; i < promiseObj.wb.sheets.length; i++) {\n          const sheet = promiseObj.wb.sheets[i];\n          if (!sheet.opts.hidden) {\n            firstVisibleTab = i;\n            break;\n          }\n        }\n        workbookViewEle.att('activeTab', firstVisibleTab);\n      }\n      if (viewOpts.autoFilterDateGrouping) {\n        workbookViewEle.att('autoFilterDateGrouping', utils.boolToInt(viewOpts.autoFilterDateGrouping));\n      }\n      if (viewOpts.firstSheet) {\n        workbookViewEle.att('firstSheet', viewOpts.firstSheet);\n      }\n      if (viewOpts.minimized) {\n        workbookViewEle.att('minimized', utils.boolToInt(viewOpts.minimized));\n      }\n      if (viewOpts.showHorizontalScroll) {\n        workbookViewEle.att('showHorizontalScroll', utils.boolToInt(viewOpts.showHorizontalScroll));\n      }\n      if (viewOpts.showSheetTabs) {\n        workbookViewEle.att('showSheetTabs', utils.boolToInt(viewOpts.showSheetTabs));\n      }\n      if (viewOpts.showVerticalScroll) {\n        workbookViewEle.att('showVerticalScroll', utils.boolToInt(viewOpts.showVerticalScroll));\n      }\n      if (viewOpts.tabRatio) {\n        workbookViewEle.att('tabRatio', viewOpts.tabRatio);\n      }\n      if (viewOpts.visibility) {\n        workbookViewEle.att('visibility', viewOpts.visibility);\n      }\n      if (viewOpts.windowWidth) {\n        workbookViewEle.att('windowWidth', viewOpts.windowWidth);\n      }\n      if (viewOpts.windowHeight) {\n        workbookViewEle.att('windowHeight', viewOpts.windowHeight);\n      }\n      if (viewOpts.xWindow) {\n        workbookViewEle.att('xWindow', viewOpts.xWindow);\n      }\n      if (viewOpts.yWindow) {\n        workbookViewEle.att('yWindow', viewOpts.yWindow);\n      }\n      if (viewOpts.showComments) {\n        workbookViewEle.att('showComments', viewOpts.showComments);\n      }\n    }\n\n    let sheetsEle = xml.ele('sheets');\n    promiseObj.wb.sheets.forEach((s, i) => {\n      const sheet = sheetsEle.ele('sheet')\n        .att('name', s.name)\n        .att('sheetId', i + 1)\n        .att('r:id', `rId${i + 1}`);\n\n      if (s.opts.hidden) {\n        sheet.att('state', 'hidden');\n      }\n\n      if (s.printArea) {\n        const name = s.name;\n        const startCellRef = `$${utils.getExcelAlpha(s.printArea.startCol)}$${s.printArea.startRow}`;\n        const endCellRef = `$${utils.getExcelAlpha(s.printArea.endCol)}$${s.printArea.endRow}`;\n        s.wb.definedNameCollection.addDefinedName({\n          name: '_xlnm.Print_Area',\n          localSheetId: s.localSheetId,\n          refFormula: `'${name}'!${startCellRef}:${endCellRef}`,\n        });\n      }\n    });\n\n    if (!promiseObj.wb.definedNameCollection.isEmpty) {\n      promiseObj.wb.definedNameCollection.addToXMLele(xml);\n    }\n\n    let xmlString = xml.doc().end(promiseObj.xmlOutVars);\n    promiseObj.xlsx.folder('xl').file('workbook.xml', xmlString);\n    resolve(promiseObj);\n\n  });\n};\n\nlet addWorkbookRelsXML = (promiseObj) => {\n  // Required as stated in §12.2\n  return new Promise((resolve, reject) => {\n\n    let xml = xmlbuilder.create(\n        'Relationships', {\n          'version': '1.0',\n          'encoding': 'UTF-8',\n          'standalone': true,\n          'allowSurrogateChars': true\n        }\n      )\n      .att('xmlns', 'http://schemas.openxmlformats.org/package/2006/relationships');\n\n    xml\n      .ele('Relationship')\n      .att('Id', `rId${promiseObj.wb.sheets.length + 1}`)\n      .att('Target', 'sharedStrings.xml')\n      .att('Type', 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings');\n\n    xml\n      .ele('Relationship')\n      .att('Id', `rId${promiseObj.wb.sheets.length + 2}`)\n      .att('Target', 'styles.xml')\n      .att('Type', 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles');\n\n    promiseObj.wb.sheets.forEach((s, i) => {\n      xml\n        .ele('Relationship')\n        .att('Id', `rId${i + 1}`)\n        .att('Target', `worksheets/sheet${i + 1}.xml`)\n        .att('Type', 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet');\n    });\n\n    let xmlString = xml.doc().end(promiseObj.xmlOutVars);\n    promiseObj.xlsx.folder('xl').folder('_rels').file('workbook.xml.rels', xmlString);\n    resolve(promiseObj);\n\n  });\n};\n\nlet addCorePropertiesXML = (promiseObj) => {\n  let xml = xmlbuilder.create(\n    'cp:coreProperties', {\n      'version': '1.0',\n      'encoding': 'UTF-8',\n      'standalone': true\n    }\n  )\n  .att('xmlns:cp', 'http://schemas.openxmlformats.org/package/2006/metadata/core-properties')\n  .att('xmlns:dc', 'http://purl.org/dc/elements/1.1/')\n  .att('xmlns:dcterms', 'http://purl.org/dc/terms/')\n  .att('xmlns:dcmitype', 'http://purl.org/dc/dcmitype/')\n  .att('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');\n\n  xml.ele('dc:creator').text(promiseObj.wb.author);\n  xml.ele('cp:lastModifiedBy').text(promiseObj.wb.author);\n  let dtStr = new Date().toISOString();\n  xml.ele('dcterms:created').att('xsi:type', 'dcterms:W3CDTF').text(dtStr);\n  xml.ele('dcterms:modified').att('xsi:type', 'dcterms:W3CDTF').text(dtStr);\n  let xmlString = xml.doc().end(promiseObj.xmlOutVars);\n  promiseObj.xlsx.folder('docProps').file('core.xml', xmlString);\n  return promiseObj;\n};\n\nlet addWorksheetsXML = (promiseObj) => {\n  // Required as stated in §12.2\n  return new Promise((resolve, reject) => {\n\n    let curSheet = 0;\n\n    let processNextSheet = () => {\n      let thisSheet = promiseObj.wb.sheets[curSheet];\n      if (thisSheet) {\n        curSheet++;\n        thisSheet.generateXML()\n          .then((xml) => {\n            return new Promise((resolve) => {\n              // Add worksheet to zip\n              promiseObj.xlsx.folder('xl').folder('worksheets').file(`sheet${curSheet}.xml`, xml);\n\n              resolve();\n            });\n          })\n          .then(() => {\n            return thisSheet.generateRelsXML();\n          })\n          .then((xml) => {\n            return new Promise((resolve) => {\n              if (xml) {\n                promiseObj.xlsx.folder('xl').folder('worksheets').folder('_rels').file(`sheet${curSheet}.xml.rels`, xml);\n              }\n              resolve();\n            });\n          })\n          .then(() => {\n            return thisSheet.generateCommentsXML();\n          })\n          .then((xml) => {\n            return new Promise((resolve) => {\n              if (xml) {\n                promiseObj.xlsx.folder('xl').file(`comments${curSheet}.xml`, xml);\n              }\n              resolve();\n            });\n          })\n          .then(() => {\n            return thisSheet.generateCommentsVmlXML();\n          })\n          .then((xml) => {\n            return new Promise((resolve) => {\n              if (xml) {\n                promiseObj.xlsx.folder('xl').folder('drawings').file(`commentsVml${curSheet}.vml`, xml);\n              }\n              resolve();\n            });\n          })\n          .then(processNextSheet)\n          .catch((e) => {\n            promiseObj.wb.logger.error(e.stack);\n          });\n      } else {\n        resolve(promiseObj);\n      }\n    };\n    processNextSheet();\n\n  });\n};\n\n/**\n * Generate XML for SharedStrings.xml file and add it to zip file. Called from _writeToBuffer()\n * @private\n * @memberof Workbook\n * @param {Object} promiseObj object containing jszip instance, workbook intance and xmlvars\n * @return {Promise} Resolves with promiseObj\n */\nlet addSharedStringsXML = (promiseObj) => {\n  // §12.3.15 Shared String Table Part\n  return new Promise((resolve, reject) => {\n\n    let xml = xmlbuilder.create(\n        'sst', {\n          'version': '1.0',\n          'encoding': 'UTF-8',\n          'standalone': true,\n          'allowSurrogateChars': true\n        }\n      )\n      .att('count', promiseObj.wb.sharedStrings.length)\n      .att('uniqueCount', promiseObj.wb.sharedStrings.length)\n      .att('xmlns', 'http://schemas.openxmlformats.org/spreadsheetml/2006/main');\n\n    promiseObj.wb.sharedStrings.forEach((s) => {\n      if (typeof s === 'string') {\n        xml.ele('si').ele('t').txt(s);\n      } else if (s instanceof Array) {\n\n        let thisSI = xml.ele('si');\n        let theseRuns = []; // §18.4.4 r (Rich Text Run)\n        let currProps = {};\n        let curRun;\n        let i = 0;\n        while (i < s.length) {\n          if (typeof s[i] === 'string') {\n            if (curRun === undefined) {\n              theseRuns.push({\n                props: {},\n                text: ''\n              });\n              curRun = theseRuns[theseRuns.length - 1];\n            }\n            curRun.text = curRun.text + s[i];\n          } else if (typeof s[i] === 'object') {\n            theseRuns.push({\n              props: {},\n              text: ''\n            });\n            curRun = theseRuns[theseRuns.length - 1];\n            Object.keys(s[i]).forEach((k) => {\n              currProps[k] = s[i][k];\n            });\n            Object.keys(currProps).forEach((k) => {\n              curRun.props[k] = currProps[k];\n            });\n            if (s[i].value !== undefined) {\n              curRun.text = s[i].value;\n            }\n          }\n          i++;\n        }\n\n        theseRuns.forEach((run) => {\n          if (Object.keys(run).length < 1) {\n            thisSI.ele('t', run.text).att('xml:space', 'preserve');\n          } else {\n            let thisRun = thisSI.ele('r');\n            let thisRunProps = thisRun.ele('rPr');\n            typeof run.props.name === 'string' ? thisRunProps.ele('rFont').att('val', run.props.name) : null;\n            run.props.bold === true ? thisRunProps.ele('b') : null;\n            run.props.italics === true ? thisRunProps.ele('i') : null;\n            run.props.strike === true ? thisRunProps.ele('strike') : null;\n            run.props.outline === true ? thisRunProps.ele('outline') : null;\n            run.props.shadow === true ? thisRunProps.ele('shadow') : null;\n            run.props.condense === true ? thisRunProps.ele('condense') : null;\n            run.props.extend === true ? thisRunProps.ele('extend') : null;\n            if (typeof run.props.color === 'string') {\n              let thisColor = new CTColor(run.props.color);\n              thisColor.addToXMLele(thisRunProps);\n            }\n            typeof run.props.size === 'number' ? thisRunProps.ele('sz').att('val', run.props.size) : null;\n            run.props.underline === true ? thisRunProps.ele('u') : null;\n            typeof run.props.vertAlign === 'string' ? thisRunProps.ele('vertAlign').att('val', run.props.vertAlign) : null;\n            thisRun.ele('t', run.text).att('xml:space', 'preserve');\n          }\n        });\n\n      }\n    });\n\n    let xmlString = xml.doc().end(promiseObj.xmlOutVars);\n    promiseObj.xlsx.folder('xl').file('sharedStrings.xml', xmlString);\n\n    resolve(promiseObj);\n\n  });\n};\n\nlet addStylesXML = (promiseObj) => {\n  // §12.3.20 Styles Part\n  return new Promise((resolve, reject) => {\n\n    let xml = xmlbuilder.create(\n        'styleSheet', {\n          'version': '1.0',\n          'encoding': 'UTF-8',\n          'standalone': true,\n          'allowSurrogateChars': true\n        }\n      )\n      .att('mc:Ignorable', 'x14ac')\n      .att('xmlns', 'http://schemas.openxmlformats.org/spreadsheetml/2006/main')\n      .att('xmlns:mc', 'http://schemas.openxmlformats.org/markup-compatibility/2006')\n      .att('xmlns:x14ac', 'http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac');\n\n    if (promiseObj.wb.styleData.numFmts.length > 0) {\n      let nfXML = xml\n        .ele('numFmts')\n        .att('count', promiseObj.wb.styleData.numFmts.length);\n      promiseObj.wb.styleData.numFmts.forEach((nf) => {\n        nf.addToXMLele(nfXML);\n      });\n    }\n\n    let fontXML = xml\n      .ele('fonts')\n      .att('count', promiseObj.wb.styleData.fonts.length);\n    promiseObj.wb.styleData.fonts.forEach((f) => {\n      f.addToXMLele(fontXML);\n    });\n\n    let fillXML = xml\n      .ele('fills')\n      .att('count', promiseObj.wb.styleData.fills.length);\n    promiseObj.wb.styleData.fills.forEach((f) => {\n      let fXML = fillXML.ele('fill');\n      f.addToXMLele(fXML);\n    });\n\n    let borderXML = xml\n      .ele('borders')\n      .att('count', promiseObj.wb.styleData.borders.length);\n    promiseObj.wb.styleData.borders.forEach((b) => {\n      b.addToXMLele(borderXML);\n    });\n\n    let cellStyleXfsXML = xml\n      .ele('cellStyleXfs')\n      .att('count', 1);\n    \n    cellStyleXfsXML.ele('xf')\n      .att('numFmtId', '0')\n      .att('fontId', '0')\n      .att('fillId','0')\n      .att('borderId', '0')\n      .att('applyFont', 'true')\n      .att('applyBorder', 'false')\n      .att('applyAlignment', 'false')\n      .att('applyProtection', 'false');\n\n    let cellXfsXML = xml\n      .ele('cellXfs')\n      .att('count', promiseObj.wb.styles.length);\n    promiseObj.wb.styles.forEach((s) => {\n      s.addXFtoXMLele(cellXfsXML);\n    });\n    \n\n    // insert default cell style\n    let cellStylesXML = xml\n      .ele('cellStyles')\n      .att('count', 1);\n    cellStylesXML.ele('cellStyle')\n      .att('name', 'Normal')\n      .att('xfId', '0')\n      .att('builtinId', '0');\n\n    if (promiseObj.wb.dxfCollection.length > 0) {\n      promiseObj.wb.dxfCollection.addToXMLele(xml);\n    } \n\n    let xmlString = xml.doc().end(promiseObj.xmlOutVars);\n    promiseObj.xlsx.folder('xl').file('styles.xml', xmlString);\n\n    resolve(promiseObj);\n  });\n};\n\nlet addDrawingsXML = (promiseObj) => {\n  return new Promise((resolve) => {\n    if (!promiseObj.wb.mediaCollection.isEmpty) {\n\n      promiseObj.wb.sheets.forEach((ws) => {\n        if (!ws.drawingCollection.isEmpty) {\n\n          let drawingRelXML = xmlbuilder.create('Relationships', {\n              'version': '1.0',\n              'encoding': 'UTF-8',\n              'standalone': true,\n              'allowSurrogateChars': true\n            })\n            .att('xmlns', 'http://schemas.openxmlformats.org/package/2006/relationships');\n\n          let drawingsXML = xmlbuilder.create(\n            'xdr:wsDr', {\n              'version': '1.0',\n              'encoding': 'UTF-8',\n              'standalone': true,\n              'allowSurrogateChars': true\n            }\n          );\n          drawingsXML\n            .att('xmlns:a', 'http://schemas.openxmlformats.org/drawingml/2006/main')\n            .att('xmlns:xdr', 'http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing');\n\n          ws.drawingCollection.drawings.forEach((d) => {\n\n            if (d.kind === 'image') {\n              let target = 'image' + d.id + '.' + d.extension;\n\n              let image = d.imagePath ? fs.readFileSync(d.imagePath) : d.image;\n              promiseObj.xlsx.folder('xl').folder('media').file(target, image);\n\n              drawingRelXML.ele('Relationship')\n                .att('Id', d.rId)\n                .att('Target', '../media/' + target)\n                .att('Type', d.type);\n\n            }\n\n\n\n            d.addToXMLele(drawingsXML);\n\n          });\n\n          let drawingsXMLStr = drawingsXML.doc().end(promiseObj.xmlOutVars);\n          let drawingRelXMLStr = drawingRelXML.doc().end(promiseObj.xmlOutVars);\n          promiseObj.xlsx.folder('xl').folder('drawings').file('drawing' + ws.sheetId + '.xml', drawingsXMLStr);\n          promiseObj.xlsx.folder('xl').folder('drawings').folder('_rels').file('drawing' + ws.sheetId + '.xml.rels', drawingRelXMLStr);\n        }\n      });\n\n    }\n    resolve(promiseObj);\n  });\n};\n\n/**\n * Use JSZip to generate file to a node buffer\n * @private\n * @memberof Workbook\n * @param {Workbook} wb Workbook instance\n * @return {Promise} resolves with Buffer\n */\nlet writeToBuffer = (wb) => {\n  return new Promise((resolve, reject) => {\n    let promiseObj = {\n      wb: wb,\n      xlsx: new JSZip(),\n      xmlOutVars: {}\n    };\n\n    if (promiseObj.wb.sheets.length === 0) {\n      promiseObj.wb.addWorksheet();\n    }\n\n    addRootContentTypesXML(promiseObj)\n      .then(addWorksheetsXML)\n      .then(addRootRelsXML)\n      .then(addWorkbookXML)\n      .then(addWorkbookRelsXML)\n      .then(addCorePropertiesXML)\n      .then(addSharedStringsXML)\n      .then(addStylesXML)\n      .then(addDrawingsXML)\n      .then(() => {\n        wb.opts.jszip.type = 'nodebuffer';\n        promiseObj.xlsx.generateAsync(wb.opts.jszip)\n          .then((buf) => {\n            resolve(buf);\n          })\n          .catch((e) => {\n            reject(e);\n          });\n      })\n      .catch((e) => {\n        reject(e);\n      });\n\n  });\n};\n\n/**\n * @desc Currently only used for testing the XML generated for a Workbook.\n * @param {*} wb Workbook instance\n * @return {Promise} resolves with Workbook XML\n */\nlet workbookXML = (wb) => {\n  let promiseObj = {\n    wb: wb,\n    xlsx: new JSZip(),\n    xmlOutVars: {}\n  };\n\n  return addWorkbookXML(promiseObj).then((result) => {\n    return result.xlsx.files['xl/workbook.xml']._data;\n  });\n}\n\nmodule.exports = {\n  writeToBuffer,\n  workbookXML\n};\n"], "mappings": ";;;AAAA,IAAMA,UAAU,GAAGC,OAAO,CAAC,YAAY,CAAC;AACxC,IAAMC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAC9B,IAAME,EAAE,GAAGF,OAAO,CAAC,IAAI,CAAC;AACxB,IAAMG,OAAO,GAAGH,OAAO,CAAC,6BAA6B,CAAC;AACtD,IAAMI,KAAK,GAAGJ,OAAO,CAAC,UAAU,CAAC;AAEjC,IAAIK,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIC,UAAU,EAAK;EAC3C;EACA,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC,IAAIC,GAAG,GAAGX,UAAU,CAACY,MAAM,CACvB,OAAO,EAAE;MACP,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,IAAI;MAClB,qBAAqB,EAAE;IACzB,CACF,CAAC,CACAC,GAAG,CAAC,OAAO,EAAE,8DAA8D,CAAC;IAE/E,IAAIC,iBAAiB,GAAG,EAAE;IAC1B,IAAIC,eAAe,GAAG,EAAE;IACxBR,UAAU,CAACS,EAAE,CAACC,MAAM,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;MACrC,IAAID,CAAC,CAACE,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAE;QAClCH,CAAC,CAACE,iBAAiB,CAACE,QAAQ,CAACL,OAAO,CAAC,UAACM,CAAC,EAAK;UAC1C,IAAIT,eAAe,CAACU,OAAO,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,CAAC,EAAE;YAC5C,IAAIC,OAAO,GAAGH,CAAC,CAACI,WAAW,GAAG,GAAG,GAAGJ,CAAC,CAACE,SAAS;YAC/C,IAAIZ,iBAAiB,CAACW,OAAO,CAACE,OAAO,CAAC,GAAG,CAAC,EAAE;cAC1ChB,GAAG,CAACkB,GAAG,CAAC,SAAS,CAAC,CAAChB,GAAG,CAAC,aAAa,EAAEW,CAAC,CAACI,WAAW,CAAC,CAACf,GAAG,CAAC,WAAW,EAAEW,CAAC,CAACE,SAAS,CAAC;cAClFZ,iBAAiB,CAACgB,IAAI,CAACH,OAAO,CAAC;YACjC;YACAZ,eAAe,CAACe,IAAI,CAACN,CAAC,CAACE,SAAS,CAAC;UACnC;QACF,CAAC,CAAC;MACJ;MACA,IAAIK,MAAM,CAACC,IAAI,CAACb,CAAC,CAACc,QAAQ,CAAC,CAACX,MAAM,GAAG,CAAC,EAAE;QACtC,IAAIP,eAAe,CAACU,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;UACtCd,GAAG,CAACkB,GAAG,CAAC,SAAS,CAAC,CAAChB,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAACA,GAAG,CAAC,aAAa,EAAE,0DAA0D,CAAC;UACzHE,eAAe,CAACe,IAAI,CAAC,KAAK,CAAC;QAC7B;MACF;IACF,CAAC,CAAC;IACFnB,GAAG,CAACkB,GAAG,CAAC,SAAS,CAAC,CAAChB,GAAG,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAACA,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC;IAChFF,GAAG,CAACkB,GAAG,CAAC,SAAS,CAAC,CAAChB,GAAG,CAAC,aAAa,EAAE,0DAA0D,CAAC,CAACA,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC;IAC1HF,GAAG,CAACkB,GAAG,CAAC,UAAU,CAAC,CAAChB,GAAG,CAAC,aAAa,EAAE,4EAA4E,CAAC,CAACA,GAAG,CAAC,UAAU,EAAE,kBAAkB,CAAC;IACxJN,UAAU,CAACS,EAAE,CAACC,MAAM,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;MACrCT,GAAG,CAACkB,GAAG,CAAC,UAAU,CAAC,CAChBhB,GAAG,CAAC,aAAa,EAAE,2EAA2E,CAAC,CAC/FA,GAAG,CAAC,UAAU,yBAAAqB,MAAA,CAAyBd,CAAC,GAAG,CAAC,SAAM,CAAC;MAEtD,IAAID,CAAC,CAACE,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAE;QAClCX,GAAG,CAACkB,GAAG,CAAC,UAAU,CAAC,CAChBhB,GAAG,CAAC,aAAa,EAAE,2DAA2D,CAAC,CAC/EA,GAAG,CAAC,UAAU,EAAE,sBAAsB,GAAGM,CAAC,CAACgB,OAAO,GAAG,MAAM,CAAC;MACjE;MACA,IAAIJ,MAAM,CAACC,IAAI,CAACb,CAAC,CAACc,QAAQ,CAAC,CAACX,MAAM,GAAG,CAAC,EAAE;QACtCX,GAAG,CAACkB,GAAG,CAAC,UAAU,CAAC,CAChBhB,GAAG,CAAC,aAAa,EAAE,0EAA0E,CAAC,CAC9FA,GAAG,CAAC,UAAU,EAAE,cAAc,GAAGM,CAAC,CAACgB,OAAO,GAAG,MAAM,CAAC;MACzD;IACF,CAAC,CAAC;IACFxB,GAAG,CAACkB,GAAG,CAAC,UAAU,CAAC,CAAChB,GAAG,CAAC,aAAa,EAAE,wEAAwE,CAAC,CAACA,GAAG,CAAC,UAAU,EAAE,gBAAgB,CAAC;IAClJF,GAAG,CAACkB,GAAG,CAAC,UAAU,CAAC,CAAChB,GAAG,CAAC,aAAa,EAAE,+EAA+E,CAAC,CAACA,GAAG,CAAC,UAAU,EAAE,uBAAuB,CAAC;IAChKF,GAAG,CAACkB,GAAG,CAAC,UAAU,CAAC,CAAChB,GAAG,CAAC,aAAa,EAAE,4DAA4D,CAAC,CAACA,GAAG,CAAC,UAAU,EAAE,oBAAoB,CAAC;IAE1I,IAAIuB,SAAS,GAAGzB,GAAG,CAAC0B,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC/B,UAAU,CAACgC,UAAU,CAAC;IACpDhC,UAAU,CAACiC,IAAI,CAACC,IAAI,CAAC,qBAAqB,EAAEL,SAAS,CAAC;IACtD3B,OAAO,CAACF,UAAU,CAAC;EACrB,CAAC,CAAC;AACJ,CAAC;AAED,IAAImC,cAAc,GAAG,SAAjBA,cAAcA,CAAInC,UAAU,EAAK;EACnC;EACA,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC,IAAIC,GAAG,GAAGX,UAAU,CAACY,MAAM,CACvB,eAAe,EAAE;MACf,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,IAAI;MAClB,qBAAqB,EAAE;IACzB,CACF,CAAC,CACAC,GAAG,CAAC,OAAO,EAAE,8DAA8D,CAAC;IAE/EF,GAAG,CACAkB,GAAG,CAAC,cAAc,CAAC,CACnBhB,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CACjBA,GAAG,CAAC,MAAM,EAAE,oFAAoF,CAAC,CACjGA,GAAG,CAAC,QAAQ,EAAE,iBAAiB,CAAC;IAEnCF,GAAG,CACAkB,GAAG,CAAC,cAAc,CAAC,CACnBhB,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CACjBA,GAAG,CAAC,MAAM,EAAE,uFAAuF,CAAC,CACpGA,GAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC;IAErC,IAAIuB,SAAS,GAAGzB,GAAG,CAAC0B,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC/B,UAAU,CAACgC,UAAU,CAAC;IACpDhC,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,OAAO,CAAC,CAACF,IAAI,CAAC,OAAO,EAAEL,SAAS,CAAC;IACxD3B,OAAO,CAACF,UAAU,CAAC;EAErB,CAAC,CAAC;AACJ,CAAC;AAED,IAAIqC,cAAc,GAAG,SAAjBA,cAAcA,CAAIrC,UAAU,EAAK;EACnC;EACA,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IAEtC,IAAIC,GAAG,GAAGX,UAAU,CAACY,MAAM,CACzB,UAAU,EAAE;MACV,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,IAAI;MAClB,qBAAqB,EAAE;IACzB,CACF,CAAC;IACDD,GAAG,CAACE,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC;IAC9BF,GAAG,CAACE,GAAG,CAAC,OAAO,EAAE,2DAA2D,CAAC;IAC7EF,GAAG,CAACE,GAAG,CAAC,UAAU,EAAE,6DAA6D,CAAC;IAClFF,GAAG,CAACE,GAAG,CAAC,SAAS,EAAE,qEAAqE,CAAC;IACzFF,GAAG,CAACE,GAAG,CAAC,WAAW,EAAE,gEAAgE,CAAC;IAEtF,IAAIgC,YAAY,GAAGlC,GAAG,CAACkB,GAAG,CAAC,WAAW,CAAC;IACvC,IAAIiB,eAAe,GAAGD,YAAY,CAAChB,GAAG,CAAC,cAAc,CAAC;IACtD;IACA,IAAItB,UAAU,CAACS,EAAE,CAAC+B,IAAI,CAACC,YAAY,EAAE;MACnC,IAAMC,QAAQ,GAAG1C,UAAU,CAACS,EAAE,CAAC+B,IAAI,CAACC,YAAY;MAChD,IAAIC,QAAQ,CAACC,SAAS,KAAK,IAAI,IAAID,QAAQ,CAACC,SAAS,KAAKC,SAAS,EAAE;QACnEL,eAAe,CAACjC,GAAG,CAAC,WAAW,EAAEoC,QAAQ,CAACC,SAAS,CAAC;MACtD,CAAC,MAAM;QACL,IAAIE,eAAe,GAAG,CAAC;QACvB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,UAAU,CAACS,EAAE,CAACC,MAAM,CAACK,MAAM,EAAEF,CAAC,EAAE,EAAE;UACpD,IAAMiC,KAAK,GAAG9C,UAAU,CAACS,EAAE,CAACC,MAAM,CAACG,CAAC,CAAC;UACrC,IAAI,CAACiC,KAAK,CAACN,IAAI,CAACO,MAAM,EAAE;YACtBF,eAAe,GAAGhC,CAAC;YACnB;UACF;QACF;QACA0B,eAAe,CAACjC,GAAG,CAAC,WAAW,EAAEuC,eAAe,CAAC;MACnD;MACA,IAAIH,QAAQ,CAACM,sBAAsB,EAAE;QACnCT,eAAe,CAACjC,GAAG,CAAC,wBAAwB,EAAER,KAAK,CAACmD,SAAS,CAACP,QAAQ,CAACM,sBAAsB,CAAC,CAAC;MACjG;MACA,IAAIN,QAAQ,CAACQ,UAAU,EAAE;QACvBX,eAAe,CAACjC,GAAG,CAAC,YAAY,EAAEoC,QAAQ,CAACQ,UAAU,CAAC;MACxD;MACA,IAAIR,QAAQ,CAACS,SAAS,EAAE;QACtBZ,eAAe,CAACjC,GAAG,CAAC,WAAW,EAAER,KAAK,CAACmD,SAAS,CAACP,QAAQ,CAACS,SAAS,CAAC,CAAC;MACvE;MACA,IAAIT,QAAQ,CAACU,oBAAoB,EAAE;QACjCb,eAAe,CAACjC,GAAG,CAAC,sBAAsB,EAAER,KAAK,CAACmD,SAAS,CAACP,QAAQ,CAACU,oBAAoB,CAAC,CAAC;MAC7F;MACA,IAAIV,QAAQ,CAACW,aAAa,EAAE;QAC1Bd,eAAe,CAACjC,GAAG,CAAC,eAAe,EAAER,KAAK,CAACmD,SAAS,CAACP,QAAQ,CAACW,aAAa,CAAC,CAAC;MAC/E;MACA,IAAIX,QAAQ,CAACY,kBAAkB,EAAE;QAC/Bf,eAAe,CAACjC,GAAG,CAAC,oBAAoB,EAAER,KAAK,CAACmD,SAAS,CAACP,QAAQ,CAACY,kBAAkB,CAAC,CAAC;MACzF;MACA,IAAIZ,QAAQ,CAACa,QAAQ,EAAE;QACrBhB,eAAe,CAACjC,GAAG,CAAC,UAAU,EAAEoC,QAAQ,CAACa,QAAQ,CAAC;MACpD;MACA,IAAIb,QAAQ,CAACc,UAAU,EAAE;QACvBjB,eAAe,CAACjC,GAAG,CAAC,YAAY,EAAEoC,QAAQ,CAACc,UAAU,CAAC;MACxD;MACA,IAAId,QAAQ,CAACe,WAAW,EAAE;QACxBlB,eAAe,CAACjC,GAAG,CAAC,aAAa,EAAEoC,QAAQ,CAACe,WAAW,CAAC;MAC1D;MACA,IAAIf,QAAQ,CAACgB,YAAY,EAAE;QACzBnB,eAAe,CAACjC,GAAG,CAAC,cAAc,EAAEoC,QAAQ,CAACgB,YAAY,CAAC;MAC5D;MACA,IAAIhB,QAAQ,CAACiB,OAAO,EAAE;QACpBpB,eAAe,CAACjC,GAAG,CAAC,SAAS,EAAEoC,QAAQ,CAACiB,OAAO,CAAC;MAClD;MACA,IAAIjB,QAAQ,CAACkB,OAAO,EAAE;QACpBrB,eAAe,CAACjC,GAAG,CAAC,SAAS,EAAEoC,QAAQ,CAACkB,OAAO,CAAC;MAClD;MACA,IAAIlB,QAAQ,CAACmB,YAAY,EAAE;QACzBtB,eAAe,CAACjC,GAAG,CAAC,cAAc,EAAEoC,QAAQ,CAACmB,YAAY,CAAC;MAC5D;IACF;IAEA,IAAIC,SAAS,GAAG1D,GAAG,CAACkB,GAAG,CAAC,QAAQ,CAAC;IACjCtB,UAAU,CAACS,EAAE,CAACC,MAAM,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;MACrC,IAAMiC,KAAK,GAAGgB,SAAS,CAACxC,GAAG,CAAC,OAAO,CAAC,CACjChB,GAAG,CAAC,MAAM,EAAEM,CAAC,CAACmD,IAAI,CAAC,CACnBzD,GAAG,CAAC,SAAS,EAAEO,CAAC,GAAG,CAAC,CAAC,CACrBP,GAAG,CAAC,MAAM,QAAAqB,MAAA,CAAQd,CAAC,GAAG,CAAC,CAAE,CAAC;MAE7B,IAAID,CAAC,CAAC4B,IAAI,CAACO,MAAM,EAAE;QACjBD,KAAK,CAACxC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;MAC9B;MAEA,IAAIM,CAAC,CAACoD,SAAS,EAAE;QACf,IAAMD,IAAI,GAAGnD,CAAC,CAACmD,IAAI;QACnB,IAAME,YAAY,OAAAtC,MAAA,CAAO7B,KAAK,CAACoE,aAAa,CAACtD,CAAC,CAACoD,SAAS,CAACG,QAAQ,CAAC,OAAAxC,MAAA,CAAIf,CAAC,CAACoD,SAAS,CAACI,QAAQ,CAAE;QAC5F,IAAMC,UAAU,OAAA1C,MAAA,CAAO7B,KAAK,CAACoE,aAAa,CAACtD,CAAC,CAACoD,SAAS,CAACM,MAAM,CAAC,OAAA3C,MAAA,CAAIf,CAAC,CAACoD,SAAS,CAACO,MAAM,CAAE;QACtF3D,CAAC,CAACH,EAAE,CAAC+D,qBAAqB,CAACC,cAAc,CAAC;UACxCV,IAAI,EAAE,kBAAkB;UACxBW,YAAY,EAAE9D,CAAC,CAAC8D,YAAY;UAC5BC,UAAU,MAAAhD,MAAA,CAAMoC,IAAI,QAAApC,MAAA,CAAKsC,YAAY,OAAAtC,MAAA,CAAI0C,UAAU;QACrD,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACrE,UAAU,CAACS,EAAE,CAAC+D,qBAAqB,CAACI,OAAO,EAAE;MAChD5E,UAAU,CAACS,EAAE,CAAC+D,qBAAqB,CAACK,WAAW,CAACzE,GAAG,CAAC;IACtD;IAEA,IAAIyB,SAAS,GAAGzB,GAAG,CAAC0B,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC/B,UAAU,CAACgC,UAAU,CAAC;IACpDhC,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACF,IAAI,CAAC,cAAc,EAAEL,SAAS,CAAC;IAC5D3B,OAAO,CAACF,UAAU,CAAC;EAErB,CAAC,CAAC;AACJ,CAAC;AAED,IAAI8E,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAI9E,UAAU,EAAK;EACvC;EACA,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IAEtC,IAAIC,GAAG,GAAGX,UAAU,CAACY,MAAM,CACvB,eAAe,EAAE;MACf,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,IAAI;MAClB,qBAAqB,EAAE;IACzB,CACF,CAAC,CACAC,GAAG,CAAC,OAAO,EAAE,8DAA8D,CAAC;IAE/EF,GAAG,CACAkB,GAAG,CAAC,cAAc,CAAC,CACnBhB,GAAG,CAAC,IAAI,QAAAqB,MAAA,CAAQ3B,UAAU,CAACS,EAAE,CAACC,MAAM,CAACK,MAAM,GAAG,CAAC,CAAE,CAAC,CAClDT,GAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAClCA,GAAG,CAAC,MAAM,EAAE,mFAAmF,CAAC;IAEnGF,GAAG,CACAkB,GAAG,CAAC,cAAc,CAAC,CACnBhB,GAAG,CAAC,IAAI,QAAAqB,MAAA,CAAQ3B,UAAU,CAACS,EAAE,CAACC,MAAM,CAACK,MAAM,GAAG,CAAC,CAAE,CAAC,CAClDT,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAC3BA,GAAG,CAAC,MAAM,EAAE,4EAA4E,CAAC;IAE5FN,UAAU,CAACS,EAAE,CAACC,MAAM,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;MACrCT,GAAG,CACAkB,GAAG,CAAC,cAAc,CAAC,CACnBhB,GAAG,CAAC,IAAI,QAAAqB,MAAA,CAAQd,CAAC,GAAG,CAAC,CAAE,CAAC,CACxBP,GAAG,CAAC,QAAQ,qBAAAqB,MAAA,CAAqBd,CAAC,GAAG,CAAC,SAAM,CAAC,CAC7CP,GAAG,CAAC,MAAM,EAAE,+EAA+E,CAAC;IACjG,CAAC,CAAC;IAEF,IAAIuB,SAAS,GAAGzB,GAAG,CAAC0B,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC/B,UAAU,CAACgC,UAAU,CAAC;IACpDhC,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACA,MAAM,CAAC,OAAO,CAAC,CAACF,IAAI,CAAC,mBAAmB,EAAEL,SAAS,CAAC;IACjF3B,OAAO,CAACF,UAAU,CAAC;EAErB,CAAC,CAAC;AACJ,CAAC;AAED,IAAI+E,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAI/E,UAAU,EAAK;EACzC,IAAII,GAAG,GAAGX,UAAU,CAACY,MAAM,CACzB,mBAAmB,EAAE;IACnB,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,OAAO;IACnB,YAAY,EAAE;EAChB,CACF,CAAC,CACAC,GAAG,CAAC,UAAU,EAAE,yEAAyE,CAAC,CAC1FA,GAAG,CAAC,UAAU,EAAE,kCAAkC,CAAC,CACnDA,GAAG,CAAC,eAAe,EAAE,2BAA2B,CAAC,CACjDA,GAAG,CAAC,gBAAgB,EAAE,8BAA8B,CAAC,CACrDA,GAAG,CAAC,WAAW,EAAE,2CAA2C,CAAC;EAE9DF,GAAG,CAACkB,GAAG,CAAC,YAAY,CAAC,CAAC0D,IAAI,CAAChF,UAAU,CAACS,EAAE,CAACwE,MAAM,CAAC;EAChD7E,GAAG,CAACkB,GAAG,CAAC,mBAAmB,CAAC,CAAC0D,IAAI,CAAChF,UAAU,CAACS,EAAE,CAACwE,MAAM,CAAC;EACvD,IAAIC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACpChF,GAAG,CAACkB,GAAG,CAAC,iBAAiB,CAAC,CAAChB,GAAG,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC0E,IAAI,CAACE,KAAK,CAAC;EACxE9E,GAAG,CAACkB,GAAG,CAAC,kBAAkB,CAAC,CAAChB,GAAG,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC0E,IAAI,CAACE,KAAK,CAAC;EACzE,IAAIrD,SAAS,GAAGzB,GAAG,CAAC0B,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC/B,UAAU,CAACgC,UAAU,CAAC;EACpDhC,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,UAAU,CAAC,CAACF,IAAI,CAAC,UAAU,EAAEL,SAAS,CAAC;EAC9D,OAAO7B,UAAU;AACnB,CAAC;AAED,IAAIqF,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIrF,UAAU,EAAK;EACrC;EACA,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IAEtC,IAAImF,QAAQ,GAAG,CAAC;IAEhB,IAAIC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC3B,IAAIC,SAAS,GAAGxF,UAAU,CAACS,EAAE,CAACC,MAAM,CAAC4E,QAAQ,CAAC;MAC9C,IAAIE,SAAS,EAAE;QACbF,QAAQ,EAAE;QACVE,SAAS,CAACC,WAAW,CAAC,CAAC,CACpBC,IAAI,CAAC,UAACtF,GAAG,EAAK;UACb,OAAO,IAAIH,OAAO,CAAC,UAACC,OAAO,EAAK;YAC9B;YACAF,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACA,MAAM,CAAC,YAAY,CAAC,CAACF,IAAI,SAAAP,MAAA,CAAS2D,QAAQ,WAAQlF,GAAG,CAAC;YAEnFF,OAAO,CAAC,CAAC;UACX,CAAC,CAAC;QACJ,CAAC,CAAC,CACDwF,IAAI,CAAC,YAAM;UACV,OAAOF,SAAS,CAACG,eAAe,CAAC,CAAC;QACpC,CAAC,CAAC,CACDD,IAAI,CAAC,UAACtF,GAAG,EAAK;UACb,OAAO,IAAIH,OAAO,CAAC,UAACC,OAAO,EAAK;YAC9B,IAAIE,GAAG,EAAE;cACPJ,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACA,MAAM,CAAC,YAAY,CAAC,CAACA,MAAM,CAAC,OAAO,CAAC,CAACF,IAAI,SAAAP,MAAA,CAAS2D,QAAQ,gBAAalF,GAAG,CAAC;YAC1G;YACAF,OAAO,CAAC,CAAC;UACX,CAAC,CAAC;QACJ,CAAC,CAAC,CACDwF,IAAI,CAAC,YAAM;UACV,OAAOF,SAAS,CAACI,mBAAmB,CAAC,CAAC;QACxC,CAAC,CAAC,CACDF,IAAI,CAAC,UAACtF,GAAG,EAAK;UACb,OAAO,IAAIH,OAAO,CAAC,UAACC,OAAO,EAAK;YAC9B,IAAIE,GAAG,EAAE;cACPJ,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACF,IAAI,YAAAP,MAAA,CAAY2D,QAAQ,WAAQlF,GAAG,CAAC;YACnE;YACAF,OAAO,CAAC,CAAC;UACX,CAAC,CAAC;QACJ,CAAC,CAAC,CACDwF,IAAI,CAAC,YAAM;UACV,OAAOF,SAAS,CAACK,sBAAsB,CAAC,CAAC;QAC3C,CAAC,CAAC,CACDH,IAAI,CAAC,UAACtF,GAAG,EAAK;UACb,OAAO,IAAIH,OAAO,CAAC,UAACC,OAAO,EAAK;YAC9B,IAAIE,GAAG,EAAE;cACPJ,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACA,MAAM,CAAC,UAAU,CAAC,CAACF,IAAI,eAAAP,MAAA,CAAe2D,QAAQ,WAAQlF,GAAG,CAAC;YACzF;YACAF,OAAO,CAAC,CAAC;UACX,CAAC,CAAC;QACJ,CAAC,CAAC,CACDwF,IAAI,CAACH,gBAAgB,CAAC,SACjB,CAAC,UAACO,CAAC,EAAK;UACZ9F,UAAU,CAACS,EAAE,CAACsF,MAAM,CAACC,KAAK,CAACF,CAAC,CAACG,KAAK,CAAC;QACrC,CAAC,CAAC;MACN,CAAC,MAAM;QACL/F,OAAO,CAACF,UAAU,CAAC;MACrB;IACF,CAAC;IACDuF,gBAAgB,CAAC,CAAC;EAEpB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIW,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIlG,UAAU,EAAK;EACxC;EACA,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IAEtC,IAAIC,GAAG,GAAGX,UAAU,CAACY,MAAM,CACvB,KAAK,EAAE;MACL,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,IAAI;MAClB,qBAAqB,EAAE;IACzB,CACF,CAAC,CACAC,GAAG,CAAC,OAAO,EAAEN,UAAU,CAACS,EAAE,CAAC0F,aAAa,CAACpF,MAAM,CAAC,CAChDT,GAAG,CAAC,aAAa,EAAEN,UAAU,CAACS,EAAE,CAAC0F,aAAa,CAACpF,MAAM,CAAC,CACtDT,GAAG,CAAC,OAAO,EAAE,2DAA2D,CAAC;IAE5EN,UAAU,CAACS,EAAE,CAAC0F,aAAa,CAACxF,OAAO,CAAC,UAACC,CAAC,EAAK;MACzC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACzBR,GAAG,CAACkB,GAAG,CAAC,IAAI,CAAC,CAACA,GAAG,CAAC,GAAG,CAAC,CAAC8E,GAAG,CAACxF,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAIA,CAAC,YAAYyF,KAAK,EAAE;QAE7B,IAAIC,MAAM,GAAGlG,GAAG,CAACkB,GAAG,CAAC,IAAI,CAAC;QAC1B,IAAIiF,SAAS,GAAG,EAAE,CAAC,CAAC;QACpB,IAAIC,SAAS,GAAG,CAAC,CAAC;QAClB,IAAIC,MAAM;QACV,IAAI5F,CAAC,GAAG,CAAC;QACT,OAAOA,CAAC,GAAGD,CAAC,CAACG,MAAM,EAAE;UACnB,IAAI,OAAOH,CAAC,CAACC,CAAC,CAAC,KAAK,QAAQ,EAAE;YAC5B,IAAI4F,MAAM,KAAK7D,SAAS,EAAE;cACxB2D,SAAS,CAAChF,IAAI,CAAC;gBACbmF,KAAK,EAAE,CAAC,CAAC;gBACT1B,IAAI,EAAE;cACR,CAAC,CAAC;cACFyB,MAAM,GAAGF,SAAS,CAACA,SAAS,CAACxF,MAAM,GAAG,CAAC,CAAC;YAC1C;YACA0F,MAAM,CAACzB,IAAI,GAAGyB,MAAM,CAACzB,IAAI,GAAGpE,CAAC,CAACC,CAAC,CAAC;UAClC,CAAC,MAAM,IAAI8F,OAAA,CAAO/F,CAAC,CAACC,CAAC,CAAC,MAAK,QAAQ,EAAE;YACnC0F,SAAS,CAAChF,IAAI,CAAC;cACbmF,KAAK,EAAE,CAAC,CAAC;cACT1B,IAAI,EAAE;YACR,CAAC,CAAC;YACFyB,MAAM,GAAGF,SAAS,CAACA,SAAS,CAACxF,MAAM,GAAG,CAAC,CAAC;YACxCS,MAAM,CAACC,IAAI,CAACb,CAAC,CAACC,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,UAACiG,CAAC,EAAK;cAC/BJ,SAAS,CAACI,CAAC,CAAC,GAAGhG,CAAC,CAACC,CAAC,CAAC,CAAC+F,CAAC,CAAC;YACxB,CAAC,CAAC;YACFpF,MAAM,CAACC,IAAI,CAAC+E,SAAS,CAAC,CAAC7F,OAAO,CAAC,UAACiG,CAAC,EAAK;cACpCH,MAAM,CAACC,KAAK,CAACE,CAAC,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC;YAChC,CAAC,CAAC;YACF,IAAIhG,CAAC,CAACC,CAAC,CAAC,CAACgG,KAAK,KAAKjE,SAAS,EAAE;cAC5B6D,MAAM,CAACzB,IAAI,GAAGpE,CAAC,CAACC,CAAC,CAAC,CAACgG,KAAK;YAC1B;UACF;UACAhG,CAAC,EAAE;QACL;QAEA0F,SAAS,CAAC5F,OAAO,CAAC,UAACmG,GAAG,EAAK;UACzB,IAAItF,MAAM,CAACC,IAAI,CAACqF,GAAG,CAAC,CAAC/F,MAAM,GAAG,CAAC,EAAE;YAC/BuF,MAAM,CAAChF,GAAG,CAAC,GAAG,EAAEwF,GAAG,CAAC9B,IAAI,CAAC,CAAC1E,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC;UACxD,CAAC,MAAM;YACL,IAAIyG,OAAO,GAAGT,MAAM,CAAChF,GAAG,CAAC,GAAG,CAAC;YAC7B,IAAI0F,YAAY,GAAGD,OAAO,CAACzF,GAAG,CAAC,KAAK,CAAC;YACrC,OAAOwF,GAAG,CAACJ,KAAK,CAAC3C,IAAI,KAAK,QAAQ,GAAGiD,YAAY,CAAC1F,GAAG,CAAC,OAAO,CAAC,CAAChB,GAAG,CAAC,KAAK,EAAEwG,GAAG,CAACJ,KAAK,CAAC3C,IAAI,CAAC,GAAG,IAAI;YAChG+C,GAAG,CAACJ,KAAK,CAACO,IAAI,KAAK,IAAI,GAAGD,YAAY,CAAC1F,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;YACtDwF,GAAG,CAACJ,KAAK,CAACQ,OAAO,KAAK,IAAI,GAAGF,YAAY,CAAC1F,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;YACzDwF,GAAG,CAACJ,KAAK,CAACS,MAAM,KAAK,IAAI,GAAGH,YAAY,CAAC1F,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI;YAC7DwF,GAAG,CAACJ,KAAK,CAACU,OAAO,KAAK,IAAI,GAAGJ,YAAY,CAAC1F,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI;YAC/DwF,GAAG,CAACJ,KAAK,CAACW,MAAM,KAAK,IAAI,GAAGL,YAAY,CAAC1F,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI;YAC7DwF,GAAG,CAACJ,KAAK,CAACY,QAAQ,KAAK,IAAI,GAAGN,YAAY,CAAC1F,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI;YACjEwF,GAAG,CAACJ,KAAK,CAACa,MAAM,KAAK,IAAI,GAAGP,YAAY,CAAC1F,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI;YAC7D,IAAI,OAAOwF,GAAG,CAACJ,KAAK,CAACc,KAAK,KAAK,QAAQ,EAAE;cACvC,IAAIC,SAAS,GAAG,IAAI5H,OAAO,CAACiH,GAAG,CAACJ,KAAK,CAACc,KAAK,CAAC;cAC5CC,SAAS,CAAC5C,WAAW,CAACmC,YAAY,CAAC;YACrC;YACA,OAAOF,GAAG,CAACJ,KAAK,CAACgB,IAAI,KAAK,QAAQ,GAAGV,YAAY,CAAC1F,GAAG,CAAC,IAAI,CAAC,CAAChB,GAAG,CAAC,KAAK,EAAEwG,GAAG,CAACJ,KAAK,CAACgB,IAAI,CAAC,GAAG,IAAI;YAC7FZ,GAAG,CAACJ,KAAK,CAACiB,SAAS,KAAK,IAAI,GAAGX,YAAY,CAAC1F,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;YAC3D,OAAOwF,GAAG,CAACJ,KAAK,CAACkB,SAAS,KAAK,QAAQ,GAAGZ,YAAY,CAAC1F,GAAG,CAAC,WAAW,CAAC,CAAChB,GAAG,CAAC,KAAK,EAAEwG,GAAG,CAACJ,KAAK,CAACkB,SAAS,CAAC,GAAG,IAAI;YAC9Gb,OAAO,CAACzF,GAAG,CAAC,GAAG,EAAEwF,GAAG,CAAC9B,IAAI,CAAC,CAAC1E,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC;UACzD;QACF,CAAC,CAAC;MAEJ;IACF,CAAC,CAAC;IAEF,IAAIuB,SAAS,GAAGzB,GAAG,CAAC0B,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC/B,UAAU,CAACgC,UAAU,CAAC;IACpDhC,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACF,IAAI,CAAC,mBAAmB,EAAEL,SAAS,CAAC;IAEjE3B,OAAO,CAACF,UAAU,CAAC;EAErB,CAAC,CAAC;AACJ,CAAC;AAED,IAAI6H,YAAY,GAAG,SAAfA,YAAYA,CAAI7H,UAAU,EAAK;EACjC;EACA,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IAEtC,IAAIC,GAAG,GAAGX,UAAU,CAACY,MAAM,CACvB,YAAY,EAAE;MACZ,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,IAAI;MAClB,qBAAqB,EAAE;IACzB,CACF,CAAC,CACAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAC5BA,GAAG,CAAC,OAAO,EAAE,2DAA2D,CAAC,CACzEA,GAAG,CAAC,UAAU,EAAE,6DAA6D,CAAC,CAC9EA,GAAG,CAAC,aAAa,EAAE,6DAA6D,CAAC;IAEpF,IAAIN,UAAU,CAACS,EAAE,CAACqH,SAAS,CAACC,OAAO,CAAChH,MAAM,GAAG,CAAC,EAAE;MAC9C,IAAIiH,KAAK,GAAG5H,GAAG,CACZkB,GAAG,CAAC,SAAS,CAAC,CACdhB,GAAG,CAAC,OAAO,EAAEN,UAAU,CAACS,EAAE,CAACqH,SAAS,CAACC,OAAO,CAAChH,MAAM,CAAC;MACvDf,UAAU,CAACS,EAAE,CAACqH,SAAS,CAACC,OAAO,CAACpH,OAAO,CAAC,UAACsH,EAAE,EAAK;QAC9CA,EAAE,CAACpD,WAAW,CAACmD,KAAK,CAAC;MACvB,CAAC,CAAC;IACJ;IAEA,IAAIE,OAAO,GAAG9H,GAAG,CACdkB,GAAG,CAAC,OAAO,CAAC,CACZhB,GAAG,CAAC,OAAO,EAAEN,UAAU,CAACS,EAAE,CAACqH,SAAS,CAACK,KAAK,CAACpH,MAAM,CAAC;IACrDf,UAAU,CAACS,EAAE,CAACqH,SAAS,CAACK,KAAK,CAACxH,OAAO,CAAC,UAACyH,CAAC,EAAK;MAC3CA,CAAC,CAACvD,WAAW,CAACqD,OAAO,CAAC;IACxB,CAAC,CAAC;IAEF,IAAIG,OAAO,GAAGjI,GAAG,CACdkB,GAAG,CAAC,OAAO,CAAC,CACZhB,GAAG,CAAC,OAAO,EAAEN,UAAU,CAACS,EAAE,CAACqH,SAAS,CAACQ,KAAK,CAACvH,MAAM,CAAC;IACrDf,UAAU,CAACS,EAAE,CAACqH,SAAS,CAACQ,KAAK,CAAC3H,OAAO,CAAC,UAACyH,CAAC,EAAK;MAC3C,IAAIG,IAAI,GAAGF,OAAO,CAAC/G,GAAG,CAAC,MAAM,CAAC;MAC9B8G,CAAC,CAACvD,WAAW,CAAC0D,IAAI,CAAC;IACrB,CAAC,CAAC;IAEF,IAAIC,SAAS,GAAGpI,GAAG,CAChBkB,GAAG,CAAC,SAAS,CAAC,CACdhB,GAAG,CAAC,OAAO,EAAEN,UAAU,CAACS,EAAE,CAACqH,SAAS,CAACW,OAAO,CAAC1H,MAAM,CAAC;IACvDf,UAAU,CAACS,EAAE,CAACqH,SAAS,CAACW,OAAO,CAAC9H,OAAO,CAAC,UAAC+H,CAAC,EAAK;MAC7CA,CAAC,CAAC7D,WAAW,CAAC2D,SAAS,CAAC;IAC1B,CAAC,CAAC;IAEF,IAAIG,eAAe,GAAGvI,GAAG,CACtBkB,GAAG,CAAC,cAAc,CAAC,CACnBhB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IAElBqI,eAAe,CAACrH,GAAG,CAAC,IAAI,CAAC,CACtBhB,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CACpBA,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAClBA,GAAG,CAAC,QAAQ,EAAC,GAAG,CAAC,CACjBA,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CACpBA,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CACxBA,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAC3BA,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAC9BA,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC;IAElC,IAAIsI,UAAU,GAAGxI,GAAG,CACjBkB,GAAG,CAAC,SAAS,CAAC,CACdhB,GAAG,CAAC,OAAO,EAAEN,UAAU,CAACS,EAAE,CAACoI,MAAM,CAAC9H,MAAM,CAAC;IAC5Cf,UAAU,CAACS,EAAE,CAACoI,MAAM,CAAClI,OAAO,CAAC,UAACC,CAAC,EAAK;MAClCA,CAAC,CAACkI,aAAa,CAACF,UAAU,CAAC;IAC7B,CAAC,CAAC;;IAGF;IACA,IAAIG,aAAa,GAAG3I,GAAG,CACpBkB,GAAG,CAAC,YAAY,CAAC,CACjBhB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IAClByI,aAAa,CAACzH,GAAG,CAAC,WAAW,CAAC,CAC3BhB,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CACrBA,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAChBA,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC;IAExB,IAAIN,UAAU,CAACS,EAAE,CAACuI,aAAa,CAACjI,MAAM,GAAG,CAAC,EAAE;MAC1Cf,UAAU,CAACS,EAAE,CAACuI,aAAa,CAACnE,WAAW,CAACzE,GAAG,CAAC;IAC9C;IAEA,IAAIyB,SAAS,GAAGzB,GAAG,CAAC0B,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC/B,UAAU,CAACgC,UAAU,CAAC;IACpDhC,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACF,IAAI,CAAC,YAAY,EAAEL,SAAS,CAAC;IAE1D3B,OAAO,CAACF,UAAU,CAAC;EACrB,CAAC,CAAC;AACJ,CAAC;AAED,IAAIiJ,cAAc,GAAG,SAAjBA,cAAcA,CAAIjJ,UAAU,EAAK;EACnC,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAK;IAC9B,IAAI,CAACF,UAAU,CAACS,EAAE,CAACyI,eAAe,CAACtE,OAAO,EAAE;MAE1C5E,UAAU,CAACS,EAAE,CAACC,MAAM,CAACC,OAAO,CAAC,UAACwI,EAAE,EAAK;QACnC,IAAI,CAACA,EAAE,CAACrI,iBAAiB,CAAC8D,OAAO,EAAE;UAEjC,IAAIwE,aAAa,GAAG3J,UAAU,CAACY,MAAM,CAAC,eAAe,EAAE;YACnD,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE,OAAO;YACnB,YAAY,EAAE,IAAI;YAClB,qBAAqB,EAAE;UACzB,CAAC,CAAC,CACDC,GAAG,CAAC,OAAO,EAAE,8DAA8D,CAAC;UAE/E,IAAI+I,WAAW,GAAG5J,UAAU,CAACY,MAAM,CACjC,UAAU,EAAE;YACV,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE,OAAO;YACnB,YAAY,EAAE,IAAI;YAClB,qBAAqB,EAAE;UACzB,CACF,CAAC;UACDgJ,WAAW,CACR/I,GAAG,CAAC,SAAS,EAAE,uDAAuD,CAAC,CACvEA,GAAG,CAAC,WAAW,EAAE,qEAAqE,CAAC;UAE1F6I,EAAE,CAACrI,iBAAiB,CAACE,QAAQ,CAACL,OAAO,CAAC,UAACM,CAAC,EAAK;YAE3C,IAAIA,CAAC,CAACqI,IAAI,KAAK,OAAO,EAAE;cACtB,IAAIC,MAAM,GAAG,OAAO,GAAGtI,CAAC,CAACuI,EAAE,GAAG,GAAG,GAAGvI,CAAC,CAACE,SAAS;cAE/C,IAAIsI,KAAK,GAAGxI,CAAC,CAACyI,SAAS,GAAG9J,EAAE,CAAC+J,YAAY,CAAC1I,CAAC,CAACyI,SAAS,CAAC,GAAGzI,CAAC,CAACwI,KAAK;cAChEzJ,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACA,MAAM,CAAC,OAAO,CAAC,CAACF,IAAI,CAACqH,MAAM,EAAEE,KAAK,CAAC;cAEhEL,aAAa,CAAC9H,GAAG,CAAC,cAAc,CAAC,CAC9BhB,GAAG,CAAC,IAAI,EAAEW,CAAC,CAAC2I,GAAG,CAAC,CAChBtJ,GAAG,CAAC,QAAQ,EAAE,WAAW,GAAGiJ,MAAM,CAAC,CACnCjJ,GAAG,CAAC,MAAM,EAAEW,CAAC,CAAC4I,IAAI,CAAC;YAExB;YAIA5I,CAAC,CAAC4D,WAAW,CAACwE,WAAW,CAAC;UAE5B,CAAC,CAAC;UAEF,IAAIS,cAAc,GAAGT,WAAW,CAACvH,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC/B,UAAU,CAACgC,UAAU,CAAC;UACjE,IAAI+H,gBAAgB,GAAGX,aAAa,CAACtH,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC/B,UAAU,CAACgC,UAAU,CAAC;UACrEhC,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACA,MAAM,CAAC,UAAU,CAAC,CAACF,IAAI,CAAC,SAAS,GAAGiH,EAAE,CAACvH,OAAO,GAAG,MAAM,EAAEkI,cAAc,CAAC;UACrG9J,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAACA,MAAM,CAAC,UAAU,CAAC,CAACA,MAAM,CAAC,OAAO,CAAC,CAACF,IAAI,CAAC,SAAS,GAAGiH,EAAE,CAACvH,OAAO,GAAG,WAAW,EAAEmI,gBAAgB,CAAC;QAC9H;MACF,CAAC,CAAC;IAEJ;IACA7J,OAAO,CAACF,UAAU,CAAC;EACrB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIgK,aAAa,GAAG,SAAhBA,aAAaA,CAAIvJ,EAAE,EAAK;EAC1B,OAAO,IAAIR,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC,IAAIH,UAAU,GAAG;MACfS,EAAE,EAAEA,EAAE;MACNwB,IAAI,EAAE,IAAItC,KAAK,CAAC,CAAC;MACjBqC,UAAU,EAAE,CAAC;IACf,CAAC;IAED,IAAIhC,UAAU,CAACS,EAAE,CAACC,MAAM,CAACK,MAAM,KAAK,CAAC,EAAE;MACrCf,UAAU,CAACS,EAAE,CAACwJ,YAAY,CAAC,CAAC;IAC9B;IAEAlK,sBAAsB,CAACC,UAAU,CAAC,CAC/B0F,IAAI,CAACL,gBAAgB,CAAC,CACtBK,IAAI,CAACvD,cAAc,CAAC,CACpBuD,IAAI,CAACrD,cAAc,CAAC,CACpBqD,IAAI,CAACZ,kBAAkB,CAAC,CACxBY,IAAI,CAACX,oBAAoB,CAAC,CAC1BW,IAAI,CAACQ,mBAAmB,CAAC,CACzBR,IAAI,CAACmC,YAAY,CAAC,CAClBnC,IAAI,CAACuD,cAAc,CAAC,CACpBvD,IAAI,CAAC,YAAM;MACVjF,EAAE,CAAC+B,IAAI,CAAC0H,KAAK,CAACL,IAAI,GAAG,YAAY;MACjC7J,UAAU,CAACiC,IAAI,CAACkI,aAAa,CAAC1J,EAAE,CAAC+B,IAAI,CAAC0H,KAAK,CAAC,CACzCxE,IAAI,CAAC,UAAC0E,GAAG,EAAK;QACblK,OAAO,CAACkK,GAAG,CAAC;MACd,CAAC,CAAC,SACI,CAAC,UAACtE,CAAC,EAAK;QACZ3F,MAAM,CAAC2F,CAAC,CAAC;MACX,CAAC,CAAC;IACN,CAAC,CAAC,SACI,CAAC,UAACA,CAAC,EAAK;MACZ3F,MAAM,CAAC2F,CAAC,CAAC;IACX,CAAC,CAAC;EAEN,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAIuE,WAAW,GAAG,SAAdA,WAAWA,CAAI5J,EAAE,EAAK;EACxB,IAAIT,UAAU,GAAG;IACfS,EAAE,EAAEA,EAAE;IACNwB,IAAI,EAAE,IAAItC,KAAK,CAAC,CAAC;IACjBqC,UAAU,EAAE,CAAC;EACf,CAAC;EAED,OAAOK,cAAc,CAACrC,UAAU,CAAC,CAAC0F,IAAI,CAAC,UAAC4E,MAAM,EAAK;IACjD,OAAOA,MAAM,CAACrI,IAAI,CAACsI,KAAK,CAAC,iBAAiB,CAAC,CAACC,KAAK;EACnD,CAAC,CAAC;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG;EACfV,aAAa,EAAbA,aAAa;EACbK,WAAW,EAAXA;AACF,CAAC"}