{"version": 3, "file": "pageOrder.js", "names": ["items", "_this", "opts", "for<PERSON>ach", "o", "i", "prototype", "validate", "val", "undefined", "name", "hasOwnProperty", "push", "TypeError", "join", "module", "exports"], "sources": ["../../../source/lib/types/pageOrder.js"], "sourcesContent": ["//§18.18.51 ST_PageOrder (Page Order)\n\nfunction items() {\n    let opts = ['downThenOver', 'overThenDown'];\n    opts.forEach((o, i) => {\n        this[o] = i + 1;\n    });\n}\n\n\nitems.prototype.validate = function (val) {\n    if (this[val] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError('Invalid value for pageSetup.pageOrder; Value must be one of ' + opts.join(', '));\n    } else {\n        return true;\n    }\n};\n\nmodule.exports = new items();"], "mappings": ";;AAAA;;AAEA,SAASA,KAAKA,CAAA,EAAG;EAAA,IAAAC,KAAA;EACb,IAAIC,IAAI,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;EAC3CA,IAAI,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACnBJ,KAAI,CAACG,CAAC,CAAC,GAAGC,CAAC,GAAG,CAAC;EACnB,CAAC,CAAC;AACN;AAGAL,KAAK,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACtC,IAAI,IAAI,CAACA,GAAG,CAAC,KAAKC,SAAS,EAAE;IACzB,IAAIP,IAAI,GAAG,EAAE;IACb,KAAK,IAAIQ,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BR,IAAI,CAACU,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIG,SAAS,CAAC,8DAA8D,GAAGX,IAAI,CAACY,IAAI,CAAC,IAAI,CAAC,CAAC;EACzG,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,IAAIhB,KAAK,CAAC,CAAC"}