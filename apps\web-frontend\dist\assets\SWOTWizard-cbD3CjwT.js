import{u as e,j as s,B as i,h as a,d as t,f as n,a0 as r,a1 as l,a2 as o,e as c,G as d,A as m,l as h,s as p,i as x,P as u,Q as j,R as g,I as b,X as y,_ as v}from"./mui-core-BBO2DoRL.js";import{r as f}from"./vendor-CeOqOr8o.js";import{u as S}from"./routing-B6PnZiBG.js";import{m as k,A as w}from"./animation-BJm6nf7i.js";import{P as C,T as W,W as A,aa as T,f as $,n as I,ab as O,ac as R,s as P,a4 as E,k as B}from"./mui-icons-BXhTkfAe.js";import"./utils-misc-NFmYzBmQ.js";const L={strengths:["Strong mathematical reasoning skills","Excellent memory and retention","Good leadership qualities in group activities","Consistent academic performance","Active participation in cultural events","Strong family support system","Multilingual communication abilities"],weaknesses:["Needs improvement in English speaking confidence","Time management during examinations","Hesitant to ask questions in class","Difficulty with practical applications","Limited exposure to technology","Peer pressure sensitivity"],opportunities:["Science Olympiad participation","Student council leadership roles","Inter-school cultural competitions","STEM career exploration programs","Scholarship opportunities for higher education","Skill development workshops","Community service projects"],threats:["Increased academic competition","Board examination pressure","Limited career guidance resources","Technology adaptation challenges","Economic constraints for higher education","Social media distractions"]},z=()=>{var z,M;const N=e(),G=S(),[q,F]=f.useState(0),[K,D]=f.useState(null),[H,Q]=f.useState({strengths:[],weaknesses:[],opportunities:[],threats:[]}),[U,X]=f.useState(""),[Y,_]=f.useState(!1),J=[{label:"Select Student",icon:C,description:"Choose student for SWOT analysis"},{label:"Strengths",icon:W,description:"Identify student strengths"},{label:"Weaknesses",icon:A,description:"Areas for improvement"},{label:"Opportunities",icon:T,description:"Growth opportunities"},{label:"Threats",icon:A,description:"Potential challenges"},{label:"Review & Save",icon:$,description:"Finalize SWOT analysis"}],V=()=>["","strengths","weaknesses","opportunities","threats"][q],Z=()=>{if(U.trim()&&q>=1&&q<=4){const e=V();Q((s=>({...s,[e]:[...s[e],U.trim()]}))),X("")}},ee=e=>{switch(e){case"strengths":return N.palette.success.main;case"weaknesses":return N.palette.error.main;case"opportunities":return N.palette.info.main;case"threats":return N.palette.warning.main;default:return N.palette.primary.main}},se=e=>{switch(e){case"strengths":return"💪";case"weaknesses":return"⚠️";case"opportunities":return"🚀";case"threats":return"⚡";default:return"📝"}};return s.jsxs(i,{sx:{maxWidth:1200,mx:"auto",p:3},children:[s.jsx(k.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(i,{sx:{mb:4},children:[s.jsx(a,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${N.palette.primary.main} 0%, ${N.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"SWOT Analysis Wizard"}),s.jsx(a,{variant:"body1",color:"text.secondary",children:"Create comprehensive SWOT analysis with AI-powered suggestions"})]})}),s.jsx(t,{sx:{mb:4,overflow:"visible"},children:s.jsx(n,{children:s.jsx(r,{activeStep:q,alternativeLabel:!0,children:J.map(((e,t)=>s.jsx(l,{children:s.jsxs(o,{StepIconComponent:({active:a,completed:t})=>s.jsx(i,{sx:{width:48,height:48,borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",background:t?`linear-gradient(135deg, ${N.palette.success.main} 0%, ${N.palette.success.dark} 100%)`:a?`linear-gradient(135deg, ${N.palette.primary.main} 0%, ${N.palette.secondary.main} 100%)`:c(N.palette.action.disabled,.12),color:t||a?"white":N.palette.action.disabled,transition:"all 0.3s ease"},children:s.jsx(e.icon,{sx:{fontSize:24}})}),children:[s.jsx(a,{variant:"subtitle2",sx:{fontWeight:500},children:e.label}),s.jsx(a,{variant:"caption",color:"text.secondary",children:e.description})]})},e.label)))})})}),s.jsx(t,{children:s.jsxs(n,{sx:{p:4},children:[s.jsx(w,{mode:"wait",children:s.jsxs(k.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:[0===q&&s.jsxs(i,{children:[s.jsx(a,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Select Student for SWOT Analysis"}),s.jsx(d,{container:!0,spacing:2,children:[{id:1,name:"Sanju Kumar Reddy",class:"10-A",avatar:"S"},{id:2,name:"Niraimathi Selvam",class:"10-A",avatar:"N"},{id:3,name:"Mahesh Reddy",class:"10-B",avatar:"M"},{id:4,name:"Ravi Teja Sharma",class:"9-A",avatar:"R"},{id:5,name:"Ankitha Patel",class:"10-A",avatar:"A"}].map((e=>s.jsx(d,{item:!0,xs:12,sm:6,md:4,children:s.jsx(t,{sx:{cursor:"pointer",border:(null==K?void 0:K.id)===e.id?`2px solid ${N.palette.primary.main}`:"1px solid",borderColor:(null==K?void 0:K.id)===e.id?"primary.main":"divider",transition:"all 0.2s ease","&:hover":{transform:"translateY(-2px)",boxShadow:N.shadows[4]}},onClick:()=>D(e),children:s.jsx(n,{children:s.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:2},children:[s.jsx(m,{sx:{bgcolor:"primary.main"},children:e.avatar}),s.jsxs(i,{children:[s.jsx(a,{variant:"subtitle1",sx:{fontWeight:600},children:e.name}),s.jsxs(a,{variant:"body2",color:"text.secondary",children:["Class ",e.class]})]})]})})})},e.id)))})]}),q>=1&&q<=4&&s.jsxs(i,{children:[s.jsxs(a,{variant:"h6",sx:{mb:3,fontWeight:600},children:[se(V())," ",J[q].label]}),s.jsx(i,{sx:{mb:4},children:s.jsxs(h,{direction:"row",spacing:2,alignItems:"center",children:[s.jsx(p,{fullWidth:!0,label:`Add ${J[q].label.toLowerCase()}`,value:U,onChange:e=>X(e.target.value),onKeyPress:e=>"Enter"===e.key&&Z(),placeholder:`Enter student ${J[q].label.toLowerCase()}...`}),s.jsx(x,{variant:"contained",startIcon:s.jsx(I,{}),onClick:Z,disabled:!U.trim(),children:"Add"})]})}),s.jsxs(d,{container:!0,spacing:3,children:[s.jsxs(d,{item:!0,xs:12,md:6,children:[s.jsxs(a,{variant:"subtitle1",sx:{mb:2,fontWeight:600},children:["Current ",J[q].label]}),s.jsx(u,{children:null==(z=H[V()])?void 0:z.map(((e,i)=>s.jsxs(j,{sx:{border:`1px solid ${c(ee(V()),.2)}`,borderRadius:1,mb:1,background:c(ee(V()),.05)},children:[s.jsx(g,{primary:e}),s.jsx(b,{size:"small",onClick:()=>((e,s)=>{Q((i=>({...i,[e]:i[e].filter(((e,i)=>i!==s))})))})(V(),i),color:"error",children:s.jsx(O,{})})]},i)))})]}),s.jsxs(d,{item:!0,xs:12,md:6,children:[s.jsxs(a,{variant:"subtitle1",sx:{mb:2,fontWeight:600},children:[s.jsx(R,{sx:{mr:1,verticalAlign:"middle"}}),"AI Suggestions"]}),s.jsx(u,{children:null==(M=L[V()])?void 0:M.map(((e,i)=>s.jsxs(j,{sx:{border:"1px solid",borderColor:"divider",borderRadius:1,mb:1,cursor:"pointer","&:hover":{background:c(N.palette.primary.main,.05)}},onClick:()=>((e,s)=>{H[e].includes(s)||Q((i=>({...i,[e]:[...i[e],s]})))})(V(),e),children:[s.jsx(y,{children:s.jsx(I,{color:"primary"})}),s.jsx(g,{primary:e})]},i)))})]})]})]}),5===q&&s.jsxs(i,{children:[s.jsx(a,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Review SWOT Analysis"}),K&&s.jsxs(v,{severity:"info",sx:{mb:3},children:["SWOT Analysis for ",s.jsx("strong",{children:K.name})," - Class ",K.class]}),s.jsx(d,{container:!0,spacing:3,children:Object.entries(H).map((([e,i])=>s.jsx(d,{item:!0,xs:12,md:6,children:s.jsx(t,{sx:{border:`2px solid ${c(ee(e),.2)}`,background:`linear-gradient(135deg, ${c(ee(e),.05)} 0%, ${c(ee(e),.02)} 100%)`},children:s.jsxs(n,{children:[s.jsxs(a,{variant:"h6",sx:{fontWeight:600,color:ee(e),mb:2},children:[se(e)," ",e.charAt(0).toUpperCase()+e.slice(1)]}),s.jsx(u,{dense:!0,children:i.map(((e,i)=>s.jsx(j,{children:s.jsx(g,{primary:`• ${e}`})},i)))})]})})},e)))})]})]},q)}),s.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",mt:4},children:[s.jsx(x,{onClick:()=>{q>0&&F((e=>e-1))},disabled:0===q,startIcon:s.jsx(P,{}),variant:"outlined",children:"Back"}),s.jsx(x,{onClick:q===J.length-1?async()=>{_(!0);try{await new Promise((e=>setTimeout(e,2e3))),alert("SWOT Analysis saved successfully!"),G("/dashboard/students")}catch(e){}finally{_(!1)}}:()=>{q<J.length-1&&F((e=>e+1))},endIcon:q===J.length-1?s.jsx(E,{}):s.jsx(B,{}),variant:"contained",disabled:0===q&&!K,loading:Y,sx:{background:`linear-gradient(135deg, ${N.palette.primary.main} 0%, ${N.palette.secondary.main} 100%)`},children:q===J.length-1?"Save SWOT Analysis":"Next"})]})]})})]})};export{z as default};
//# sourceMappingURL=SWOTWizard-cbD3CjwT.js.map
