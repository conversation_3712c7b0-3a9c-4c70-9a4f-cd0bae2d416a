{"version": 3, "file": "orientation.js", "names": ["items", "_this", "opts", "for<PERSON>ach", "o", "i", "prototype", "validate", "val", "toLowerCase", "undefined", "name", "hasOwnProperty", "push", "TypeError", "join", "module", "exports"], "sources": ["../../../source/lib/types/orientation.js"], "sourcesContent": ["//§18.18.50 ST_Orientation (Orientation)\n\nfunction items() {\n    let opts = ['default', 'portrait', 'landscape'];\n    opts.forEach((o, i) => {\n        this[o] = i + 1;\n    });\n}\n\n\nitems.prototype.validate = function (val) {\n    if (this[val.toLowerCase()] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError('Invalid value for pageSetup.orientation; Value must be one of ' + opts.join(', '));\n    } else {\n        return true;\n    }\n};\n\nmodule.exports = new items();"], "mappings": ";;AAAA;;AAEA,SAASA,KAAKA,CAAA,EAAG;EAAA,IAAAC,KAAA;EACb,IAAIC,IAAI,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC;EAC/CA,IAAI,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACnBJ,KAAI,CAACG,CAAC,CAAC,GAAGC,CAAC,GAAG,CAAC;EACnB,CAAC,CAAC;AACN;AAGAL,KAAK,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACtC,IAAI,IAAI,CAACA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;IACvC,IAAIR,IAAI,GAAG,EAAE;IACb,KAAK,IAAIS,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BT,IAAI,CAACW,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIG,SAAS,CAAC,gEAAgE,GAAGZ,IAAI,CAACa,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3G,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,IAAIjB,KAAK,CAAC,CAAC"}