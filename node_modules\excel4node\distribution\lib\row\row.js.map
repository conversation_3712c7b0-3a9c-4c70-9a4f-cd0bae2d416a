{"version": 3, "file": "row.js", "names": ["utils", "require", "Row", "row", "ws", "_classCallCheck", "cellRefs", "collapsed", "customFormat", "customHeight", "hidden", "ht", "outlineLevel", "r", "s", "thickBot", "thickTop", "_createClass", "key", "get", "set", "h", "TypeError", "value", "setHeight", "length", "startCol", "getExcelRowCol", "col", "endCol", "concat", "Array", "getExcelAlpha", "filter", "opts", "arguments", "undefined", "theseFilters", "filters", "o", "autoFilter", "startRow", "lastRow", "endRow", "firstColumn", "lastColumn", "hide", "group", "level", "parseInt", "freeze", "jumpTo", "sheetView", "pane", "state", "ySplit", "activePane", "xSplit", "topLeftCell", "getExcelCellRef", "module", "exports"], "sources": ["../../../source/lib/row/row.js"], "sourcesContent": ["const utils = require('../utils.js');\n\nclass Row {\n    /**\n     * Element representing an Excel Row\n     * @param {Number} row Row of cell\n     * @param {Worksheet} Worksheet that contains row\n     * @property {Worksheet} ws Worksheet that contains the specified Row\n     * @property {Array.String} cellRefs Array of excel cell references\n     * @property {Boolean} collapsed States whether row is collapsed when grouped\n     * @property {Boolean} customFormat States whether the row has a custom format\n     * @property {Boolean} customHeight States whether the row's height is different than default\n     * @property {Boolean} hidden States whether the row is hidden\n     * @property {Number} ht Height of the row (internal property)\n     * @property {Number} outlineLevel Grouping level of row\n     * @property {Number} r Row index\n     * @property {Number} s Style index\n     * @property {Boolean} thickBot States whether row has a thick bottom border\n     * @property {Boolean} thickTop States whether row has a thick top border\n     * @property {Number} height Height of row\n     * @property {String} spans String representation of excel cell range i.e. A1:A10\n     * @property {Number} firstColumn Index of the first column of the row containg data\n     * @property {String} firstColumnAlpha Alpha representation of the first column of the row containing data\n     * @property {Number} lastColumn Index of the last column of the row cotaining data\n     * @property {String} lastColumnAlpha Alpha representation of the last column of the row containing data\n     */\n    constructor(row, ws) {\n        this.ws = ws;\n        this.cellRefs = [];\n        this.collapsed = null;\n        this.customFormat = null;\n        this.customHeight = null;\n        this.hidden = null;\n        this.ht = null;\n        this.outlineLevel = null;\n        this.r = row;\n        this.s = null;\n        this.thickBot = null;\n        this.thickTop = null;\n    }\n\n    set height(h) {\n        if (typeof h === 'number') {\n            this.ht = h;\n            this.customHeight = true;\n        } else {\n            throw new TypeError('Row height must be a number');\n        }\n        return this.ht;\n    }\n    get height() {\n        return this.ht;\n    }\n\n    /**\n     * @alias Row.setHeight\n     * @desc Sets the height of a row\n     * @func Row.setHeight\n     * @param {Number} val New Height of row\n     * @returns {Row} Excel Row with attached methods\n     */\n    setHeight(h) {\n        if (typeof h === 'number') {\n            this.ht = h;\n            this.customHeight = true;\n        } else {\n            throw new TypeError('Row height must be a number');\n        }\n        return this;\n    }\n\n    get spans() {\n        if (this.cellRefs.length > 0) {\n            const startCol = utils.getExcelRowCol(this.cellRefs[0]).col;\n            const endCol = utils.getExcelRowCol(this.cellRefs[this.cellRefs.length - 1]).col;\n            return `${startCol}:${endCol}`;\n        } else {\n            return null;\n        }\n    }\n\n    get firstColumn() {\n        if (this.cellRefs instanceof Array && this.cellRefs.length > 0) {\n            return utils.getExcelRowCol(this.cellRefs[0]).col;\n        } else {\n            return 1;\n        }\n    }\n\n    get firstColumnAlpha() {\n        if (this.cellRefs instanceof Array && this.cellRefs.length > 0) {\n            return utils.getExcelAlpha(utils.getExcelRowCol(this.cellRefs[0]).col);\n        } else {\n            return 'A';\n        }\n    }\n\n    get lastColumn() {\n        if (this.cellRefs instanceof Array && this.cellRefs.length > 0) {\n            return utils.getExcelRowCol(this.cellRefs[this.cellRefs.length - 1]).col;\n        } else {\n            return 1;\n        }\n    }\n\n    get lastColumnAlpha() {\n        if (this.cellRefs instanceof Array && this.cellRefs.length > 0) {\n            return utils.getExcelAlpha(utils.getExcelRowCol(this.cellRefs[this.cellRefs.length - 1]).col);\n        } else {\n            return 'A';\n        }\n    }\n\n    /**\n     * @alias Row.filter\n     * @desc Add autofilter dropdowns to the items of the row\n     * @func Row.filter\n     * @param {Object} opts Object containing options for the fitler. \n     * @param {Number} opts.lastRow Last row in which the filter show effect filtered results (optional)\n     * @param {Number} opts.startCol First column that a filter dropdown should be added (optional)\n     * @param {Number} opts.lastCol Last column that a filter dropdown should be added (optional)\n     * @param {Array.DefinedName} opts.filters Array of filter paramaters\n     * @returns {Row} Excel Row with attached methods\n     */\n    filter(opts = {}) {\n\n        let theseFilters = opts.filters instanceof Array ? opts.filters : [];\n\n        let o = this.ws.opts.autoFilter;\n        o.startRow = this.r;\n        if (typeof opts.lastRow === 'number') {\n            o.endRow = opts.lastRow;\n        }\n\n        if (typeof opts.firstColumn === 'number' && typeof opts.lastColumn === 'number') {\n            o.startCol = opts.firstColumn;\n            o.endCol = opts.lastColumn;\n        }\n\n        // Programmer Note: DefinedName class is added to workbook during workbook write process for filters\n\n        this.ws.opts.autoFilter.filters = theseFilters;\n    }\n\n    /**\n     * @alias Row.hide\n     * @desc Hides the row\n     * @func Row.hide\n     * @returns {Row} Excel Row with attached methods\n     */\n    hide() {\n        this.hidden = true;\n        return this;\n    }\n\n    /**\n     * @alias Row.group\n     * @desc Hides the row\n     * @func Row.group\n     * @param {Number} level Group level of row\n     * @param {Boolean} collapsed States whether group should be collapsed or expanded by default\n     * @returns {Row} Excel Row with attached methods\n     */\n    group(level, collapsed) {\n        if (parseInt(level) === level) {\n            this.outlineLevel = level;\n        } else {\n            throw new TypeError('Row group level must be a positive integer');\n        }\n\n        if (collapsed === undefined) {\n            return this;\n        }\n\n        if (typeof collapsed === 'boolean') {\n            this.collapsed = collapsed;\n            this.hidden = collapsed;\n        } else {\n            throw new TypeError('Row group collapse flag must be a boolean');\n        }\n\n        return this;\n    }\n\n    /**\n     * @alias Row.freeze\n     * @desc Creates Worksheet panes and freezes the top pane\n     * @func Row.freeze\n     * @param {Number} jumpTo Row that the bottom pane should be scrolled to by default\n     * @returns {Row} Excel Row with attached methods\n     */\n    freeze(jumpTo) {\n        let o = this.ws.opts.sheetView.pane;\n        jumpTo = typeof jumpTo === 'number' && jumpTo > this.r ? jumpTo : this.r + 1;\n        o.state = 'frozen';\n        o.ySplit = this.r;\n        o.activePane = 'bottomRight';\n        o.xSplit === null ?\n            o.topLeftCell = utils.getExcelCellRef(jumpTo, 1) :\n            o.topLeftCell = utils.getExcelCellRef(jumpTo, utils.getExcelRowCol(o.topLeftCell).col);\n        return this;\n    }\n}\n\nmodule.exports = Row;"], "mappings": ";;;;;AAAA,IAAMA,KAAK,GAAGC,OAAO,CAAC,aAAa,CAAC;AAAC,IAE/BC,GAAG;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,IAAYC,GAAG,EAAEC,EAAE,EAAE;IAAAC,eAAA,OAAAH,GAAA;IACjB,IAAI,CAACE,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACE,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,CAAC,GAAGV,GAAG;IACZ,IAAI,CAACW,CAAC,GAAG,IAAI;IACb,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACxB;EAACC,YAAA,CAAAf,GAAA;IAAAgB,GAAA;IAAAC,GAAA,EAWD,SAAAA,IAAA,EAAa;MACT,OAAO,IAAI,CAACR,EAAE;IAClB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA,OANI;IAAAS,GAAA,EAbA,SAAAA,IAAWC,CAAC,EAAE;MACV,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACvB,IAAI,CAACV,EAAE,GAAGU,CAAC;QACX,IAAI,CAACZ,YAAY,GAAG,IAAI;MAC5B,CAAC,MAAM;QACH,MAAM,IAAIa,SAAS,CAAC,6BAA6B,CAAC;MACtD;MACA,OAAO,IAAI,CAACX,EAAE;IAClB;EAAC;IAAAO,GAAA;IAAAK,KAAA,EAYD,SAAAC,UAAUH,CAAC,EAAE;MACT,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACvB,IAAI,CAACV,EAAE,GAAGU,CAAC;QACX,IAAI,CAACZ,YAAY,GAAG,IAAI;MAC5B,CAAC,MAAM;QACH,MAAM,IAAIa,SAAS,CAAC,6BAA6B,CAAC;MACtD;MACA,OAAO,IAAI;IACf;EAAC;IAAAJ,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAY;MACR,IAAI,IAAI,CAACb,QAAQ,CAACmB,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAMC,QAAQ,GAAG1B,KAAK,CAAC2B,cAAc,CAAC,IAAI,CAACrB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACsB,GAAG;QAC3D,IAAMC,MAAM,GAAG7B,KAAK,CAAC2B,cAAc,CAAC,IAAI,CAACrB,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACmB,MAAM,GAAG,CAAC,CAAC,CAAC,CAACG,GAAG;QAChF,UAAAE,MAAA,CAAUJ,QAAQ,OAAAI,MAAA,CAAID,MAAM;MAChC,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ;EAAC;IAAAX,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAkB;MACd,IAAI,IAAI,CAACb,QAAQ,YAAYyB,KAAK,IAAI,IAAI,CAACzB,QAAQ,CAACmB,MAAM,GAAG,CAAC,EAAE;QAC5D,OAAOzB,KAAK,CAAC2B,cAAc,CAAC,IAAI,CAACrB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACsB,GAAG;MACrD,CAAC,MAAM;QACH,OAAO,CAAC;MACZ;IACJ;EAAC;IAAAV,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAuB;MACnB,IAAI,IAAI,CAACb,QAAQ,YAAYyB,KAAK,IAAI,IAAI,CAACzB,QAAQ,CAACmB,MAAM,GAAG,CAAC,EAAE;QAC5D,OAAOzB,KAAK,CAACgC,aAAa,CAAChC,KAAK,CAAC2B,cAAc,CAAC,IAAI,CAACrB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACsB,GAAG,CAAC;MAC1E,CAAC,MAAM;QACH,OAAO,GAAG;MACd;IACJ;EAAC;IAAAV,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAiB;MACb,IAAI,IAAI,CAACb,QAAQ,YAAYyB,KAAK,IAAI,IAAI,CAACzB,QAAQ,CAACmB,MAAM,GAAG,CAAC,EAAE;QAC5D,OAAOzB,KAAK,CAAC2B,cAAc,CAAC,IAAI,CAACrB,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACmB,MAAM,GAAG,CAAC,CAAC,CAAC,CAACG,GAAG;MAC5E,CAAC,MAAM;QACH,OAAO,CAAC;MACZ;IACJ;EAAC;IAAAV,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAsB;MAClB,IAAI,IAAI,CAACb,QAAQ,YAAYyB,KAAK,IAAI,IAAI,CAACzB,QAAQ,CAACmB,MAAM,GAAG,CAAC,EAAE;QAC5D,OAAOzB,KAAK,CAACgC,aAAa,CAAChC,KAAK,CAAC2B,cAAc,CAAC,IAAI,CAACrB,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACmB,MAAM,GAAG,CAAC,CAAC,CAAC,CAACG,GAAG,CAAC;MACjG,CAAC,MAAM;QACH,OAAO,GAAG;MACd;IACJ;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAVI;IAAAV,GAAA;IAAAK,KAAA,EAWA,SAAAU,OAAA,EAAkB;MAAA,IAAXC,IAAI,GAAAC,SAAA,CAAAV,MAAA,QAAAU,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MAEZ,IAAIE,YAAY,GAAGH,IAAI,CAACI,OAAO,YAAYP,KAAK,GAAGG,IAAI,CAACI,OAAO,GAAG,EAAE;MAEpE,IAAIC,CAAC,GAAG,IAAI,CAACnC,EAAE,CAAC8B,IAAI,CAACM,UAAU;MAC/BD,CAAC,CAACE,QAAQ,GAAG,IAAI,CAAC5B,CAAC;MACnB,IAAI,OAAOqB,IAAI,CAACQ,OAAO,KAAK,QAAQ,EAAE;QAClCH,CAAC,CAACI,MAAM,GAAGT,IAAI,CAACQ,OAAO;MAC3B;MAEA,IAAI,OAAOR,IAAI,CAACU,WAAW,KAAK,QAAQ,IAAI,OAAOV,IAAI,CAACW,UAAU,KAAK,QAAQ,EAAE;QAC7EN,CAAC,CAACb,QAAQ,GAAGQ,IAAI,CAACU,WAAW;QAC7BL,CAAC,CAACV,MAAM,GAAGK,IAAI,CAACW,UAAU;MAC9B;;MAEA;;MAEA,IAAI,CAACzC,EAAE,CAAC8B,IAAI,CAACM,UAAU,CAACF,OAAO,GAAGD,YAAY;IAClD;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAnB,GAAA;IAAAK,KAAA,EAMA,SAAAuB,KAAA,EAAO;MACH,IAAI,CAACpC,MAAM,GAAG,IAAI;MAClB,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAQ,GAAA;IAAAK,KAAA,EAQA,SAAAwB,MAAMC,KAAK,EAAEzC,SAAS,EAAE;MACpB,IAAI0C,QAAQ,CAACD,KAAK,CAAC,KAAKA,KAAK,EAAE;QAC3B,IAAI,CAACpC,YAAY,GAAGoC,KAAK;MAC7B,CAAC,MAAM;QACH,MAAM,IAAI1B,SAAS,CAAC,4CAA4C,CAAC;MACrE;MAEA,IAAIf,SAAS,KAAK6B,SAAS,EAAE;QACzB,OAAO,IAAI;MACf;MAEA,IAAI,OAAO7B,SAAS,KAAK,SAAS,EAAE;QAChC,IAAI,CAACA,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACG,MAAM,GAAGH,SAAS;MAC3B,CAAC,MAAM;QACH,MAAM,IAAIe,SAAS,CAAC,2CAA2C,CAAC;MACpE;MAEA,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAJ,GAAA;IAAAK,KAAA,EAOA,SAAA2B,OAAOC,MAAM,EAAE;MACX,IAAIZ,CAAC,GAAG,IAAI,CAACnC,EAAE,CAAC8B,IAAI,CAACkB,SAAS,CAACC,IAAI;MACnCF,MAAM,GAAG,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,GAAG,IAAI,CAACtC,CAAC,GAAGsC,MAAM,GAAG,IAAI,CAACtC,CAAC,GAAG,CAAC;MAC5E0B,CAAC,CAACe,KAAK,GAAG,QAAQ;MAClBf,CAAC,CAACgB,MAAM,GAAG,IAAI,CAAC1C,CAAC;MACjB0B,CAAC,CAACiB,UAAU,GAAG,aAAa;MAC5BjB,CAAC,CAACkB,MAAM,KAAK,IAAI,GACblB,CAAC,CAACmB,WAAW,GAAG1D,KAAK,CAAC2D,eAAe,CAACR,MAAM,EAAE,CAAC,CAAC,GAChDZ,CAAC,CAACmB,WAAW,GAAG1D,KAAK,CAAC2D,eAAe,CAACR,MAAM,EAAEnD,KAAK,CAAC2B,cAAc,CAACY,CAAC,CAACmB,WAAW,CAAC,CAAC9B,GAAG,CAAC;MAC1F,OAAO,IAAI;IACf;EAAC;EAAA,OAAA1B,GAAA;AAAA;AAGL0D,MAAM,CAACC,OAAO,GAAG3D,GAAG"}