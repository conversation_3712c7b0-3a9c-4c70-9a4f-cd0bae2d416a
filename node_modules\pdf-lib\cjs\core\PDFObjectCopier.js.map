{"version": 3, "file": "PDFObjectCopier.js", "sourceRoot": "", "sources": ["../../src/core/PDFObjectCopier.ts"], "names": [], "mappings": ";;;AAAA,wEAAiD;AACjD,sEAA+C;AAC/C,sEAA+C;AAE/C,oEAA6C;AAC7C,0EAAmD;AAEnD,iFAA0D;AAE1D;;;;;;;;;;;;;;;;;;;;GAoBG;AACH;IAQE,yBAAoB,GAAe,EAAE,IAAgB;QAArD,iBAGC;QALgB,qBAAgB,GAAG,IAAI,GAAG,EAAwB,CAAC;QAOpE,kBAAkB;QAClB,SAAI,GAAG,UAAsB,MAAS,IAAQ,OAAA,CAC1C,MAAM,YAAY,qBAAW,CAAC,CAAC,CAAC,KAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAC1D,CAAC,CAAC,MAAM,YAAY,iBAAO,CAAK,CAAC,CAAC,KAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC1D,CAAC,CAAC,MAAM,YAAY,kBAAQ,CAAI,CAAC,CAAC,KAAI,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC3D,CAAC,CAAC,MAAM,YAAY,mBAAS,CAAG,CAAC,CAAC,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC;wBAC5D,CAAC,CAAC,MAAM,YAAY,gBAAM,CAAM,CAAC,CAAC,KAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;4BACpE,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CACZ,EAPwC,CAOxC,CAAC;QAEC,gBAAW,GAAG,UAAC,YAAyB;YAC9C,IAAM,UAAU,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC;YAExC,uEAAuE;YACvE,qEAAqE;YACrE,YAAY;YACJ,IAAA,kBAAkB,GAAK,qBAAW,mBAAhB,CAAiB;YAC3C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,kBAAkB,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBACnE,IAAM,GAAG,GAAG,iBAAO,CAAC,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChD,IAAM,KAAK,GAAG,UAAU,CAAC,uBAAuB,CAAC,GAAG,CAAE,CAAC;gBACvD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK;oBAAE,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aAC/D;YAED,yEAAyE;YACzE,0DAA0D;YAC1D,UAAU,CAAC,MAAM,CAAC,iBAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;YAExC,OAAO,KAAI,CAAC,WAAW,CAAC,UAAU,CAAgB,CAAC;QACrD,CAAC,CAAC;QAEM,gBAAW,GAAG,UAAC,YAAqB;YAC1C,IAAI,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;gBAC3C,OAAO,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAY,CAAC;aAC3D;YAED,IAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC;YACjD,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAEpD,IAAM,OAAO,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC;YAEvC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBAClD,IAAA,KAAe,OAAO,CAAC,GAAG,CAAC,EAA1B,GAAG,QAAA,EAAE,KAAK,QAAgB,CAAC;gBAClC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aACvC;YAED,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC;QAEM,iBAAY,GAAG,UAAC,aAAuB;YAC7C,IAAI,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;gBAC5C,OAAO,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAa,CAAC;aAC7D;YAED,IAAM,WAAW,GAAG,aAAa,CAAC,KAAK,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC;YACnD,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAEtD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,aAAa,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBAC9D,IAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aACxC;YAED,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC;QAEM,kBAAa,GAAG,UAAC,cAAyB;YAChD,IAAI,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;gBAC7C,OAAO,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAc,CAAC;aAC/D;YAED,IAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC;YACrD,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAExD,IAAM,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAC9C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBAClD,IAAA,KAAe,OAAO,CAAC,GAAG,CAAC,EAA1B,GAAG,QAAA,EAAE,KAAK,QAAgB,CAAC;gBAClC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC9C;YAED,OAAO,YAAY,CAAC;QACtB,CAAC,CAAC;QAEM,0BAAqB,GAAG,UAAC,GAAW;YAC1C,IAAM,aAAa,GAAG,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAErD,IAAI,CAAC,aAAa,EAAE;gBAClB,IAAM,MAAM,GAAG,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnC,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBAEvC,IAAM,iBAAiB,GAAG,KAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC/C,IAAI,iBAAiB,EAAE;oBACrB,IAAM,MAAM,GAAG,KAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAC5C,KAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;iBAClC;aACF;YAED,OAAO,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAW,CAAC;QAClD,CAAC,CAAC;QApGA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAVM,mBAAG,GAAG,UAAC,GAAe,EAAE,IAAgB;QAC7C,OAAA,IAAI,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC;IAA9B,CAA8B,CAAC;IA4GnC,sBAAC;CAAA,AA9GD,IA8GC;AAED,kBAAe,eAAe,CAAC"}