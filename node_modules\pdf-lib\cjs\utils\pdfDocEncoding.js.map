{"version": 3, "file": "pdfDocEncoding.js", "sourceRoot": "", "sources": ["../../src/utils/pdfDocEncoding.ts"], "names": [], "mappings": ";;;AAAA,qCAA+C;AAE/C,oDAAoD;AACpD,IAAM,uBAAuB,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;AAErD,gDAAgD;AAChD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;IAClC,uBAAuB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;CACpC;AAED,mFAAmF;AACnF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB;AACxE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;AAC9D,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;AAC9D,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,oCAAoC;AAC1F,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY;AAClE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB;AAC5E,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;AAC/D,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa;AACnE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc;AACpE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,gDAAgD;AACtG,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;AAC/D,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;AAC/D,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB;AACtE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB;AAC5E,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU;AAChE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU;AAChE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,8BAA8B;AACpF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,2BAA2B;AACjF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,4CAA4C;AAClG,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,6CAA6C;AACnG,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa;AACnE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB;AACvE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,6CAA6C;AACnG,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,4CAA4C;AAClG,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,8CAA8C;AACpG,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,yCAAyC;AAC/F,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,2CAA2C;AACjG,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,+CAA+C;AACrG,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB;AACxE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,0BAA0B;AAChF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,0BAA0B;AAChF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,qCAAqC;AAC3F,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,4BAA4B;AAClF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,oCAAoC;AAC1F,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,wCAAwC;AAC9F,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,oCAAoC;AAC1F,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,+BAA+B;AACrF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,mCAAmC;AACzF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,0BAA0B;AAChF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,kCAAkC;AACxF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,kCAAkC;AACxF,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,gDAAgD;AACtG,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY;AAClE,uBAAuB,CAAC,IAAI,CAAC,GAAG,oBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,gDAAgD;AAEtG;;;;;GAKG;AACU,QAAA,oBAAoB,GAAG,UAAC,KAAiB;IACpD,IAAM,UAAU,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC3C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACtD,UAAU,CAAC,GAAG,CAAC,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KACvD;IACD,OAAO,MAAM,CAAC,aAAa,OAApB,MAAM,EAAkB,UAAU,EAAE;AAC7C,CAAC,CAAC"}