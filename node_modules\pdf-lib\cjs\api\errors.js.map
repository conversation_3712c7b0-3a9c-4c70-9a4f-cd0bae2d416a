{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/api/errors.ts"], "names": [], "mappings": ";AAAA,uCAAuC;;;;AAEvC,mDAAmD;AACnD;IAAuC,6CAAK;IAC1C;QAAA,iBAIC;QAHC,IAAM,GAAG,GACP,8JAA8J,CAAC;QACjK,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,wBAAC;AAAD,CAAC,AAND,CAAuC,KAAK,GAM3C;AANY,8CAAiB;AAQ9B,mDAAmD;AACnD;IAA+C,qDAAK;IAClD;QAAA,iBAIC;QAHC,IAAM,GAAG,GACP,wMAAwM,CAAC;QAC3M,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,gCAAC;AAAD,CAAC,AAND,CAA+C,KAAK,GAMnD;AANY,8DAAyB;AAQtC,mDAAmD;AACnD;IAAsC,4CAAK;IACzC;QAAA,iBAIC;QAHC,IAAM,GAAG,GACP,2QAA2Q,CAAC;QAC9Q,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,uBAAC;AAAD,CAAC,AAND,CAAsC,KAAK,GAM1C;AANY,4CAAgB;AAQ7B,mDAAmD;AACnD;IAAsD,4DAAK;IACzD;QAAA,iBAIC;QAHC,IAAM,GAAG,GACP,uEAAuE,CAAC;QAC1E,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,uCAAC;AAAD,CAAC,AAND,CAAsD,KAAK,GAM1D;AANY,4EAAgC;AAQ7C;IAAsC,4CAAK;IACzC,0BAAY,IAAY;QAAxB,iBAGC;QAFC,IAAM,GAAG,GAAG,mDAAgD,IAAI,OAAG,CAAC;QACpE,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,uBAAC;AAAD,CAAC,AALD,CAAsC,KAAK,GAK1C;AALY,4CAAgB;AAO7B;IAA8C,oDAAK;IACjD,kCAAY,IAAY,EAAE,QAAa,EAAE,MAAW;;QAApD,iBAOC;QANC,IAAM,YAAY,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC;QACpC,IAAM,UAAU,eAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,0CAAE,IAAI,mCAAI,MAAM,CAAC;QACvD,IAAM,GAAG,GACP,sBAAmB,IAAI,yBAAmB,YAAY,OAAI;aAC1D,gCAA8B,UAAY,CAAA,CAAC;QAC7C,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,+BAAC;AAAD,CAAC,AATD,CAA8C,KAAK,GASlD;AATY,4DAAwB;AAWrC;IAA8C,oDAAK;IACjD,kCAAY,OAAY;QAAxB,iBAGC;QAFC,IAAM,GAAG,GAAG,0DAAuD,OAAO,OAAG,CAAC;QAC9E,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,+BAAC;AAAD,CAAC,AALD,CAA8C,KAAK,GAKlD;AALY,4DAAwB;AAOrC;IAA6C,mDAAK;IAChD,iCAAY,IAAY;QAAxB,iBAGC;QAFC,IAAM,GAAG,GAAG,uDAAoD,IAAI,OAAG,CAAC;QACxE,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,8BAAC;AAAD,CAAC,AALD,CAA6C,KAAK,GAKjD;AALY,0DAAuB;AAOpC;IAA+C,qDAAK;IAClD,mCAAY,QAAgB;QAA5B,iBAGC;QAFC,IAAM,GAAG,GAAG,8CAA2C,QAAQ,OAAG,CAAC;QACnE,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,gCAAC;AAAD,CAAC,AALD,CAA+C,KAAK,GAKnD;AALY,8DAAyB;AAOtC;IAAmD,yDAAK;IACtD,uCAAY,IAAY;QAAxB,iBAGC;QAFC,IAAM,GAAG,GAAG,oEAAiE,IAAI,OAAG,CAAC;QACrF,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,oCAAC;AAAD,CAAC,AALD,CAAmD,KAAK,GAKvD;AALY,sEAA6B;AAO1C;IAA4C,kDAAK;IAC/C,gCAAY,SAAiB;QAA7B,iBAGC;QAFC,IAAM,GAAG,GAAG,mFAAiF,SAAW,CAAC;QACzG,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,6BAAC;AAAD,CAAC,AALD,CAA4C,KAAK,GAKhD;AALY,wDAAsB;AAOnC;IAA2C,iDAAK;IAC9C,+BAAY,UAAkB,EAAE,SAAiB;QAAjD,iBAGC;QAFC,IAAM,GAAG,GAAG,gDAA8C,UAAU,mCAA8B,SAAW,CAAC;QAC9G,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,4BAAC;AAAD,CAAC,AALD,CAA2C,KAAK,GAK/C;AALY,sDAAqB;AAOlC;IAA4C,kDAAK;IAC/C,gCAAY,UAAkB,EAAE,SAAiB,EAAE,IAAY;QAA/D,iBAGC;QAFC,IAAM,GAAG,GAAG,uCAAqC,UAAU,sCAAiC,SAAS,kBAAa,IAAM,CAAC;QACzH,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,6BAAC;AAAD,CAAC,AALD,CAA4C,KAAK,GAKhD;AALY,wDAAsB;AAOnC;IAA2C,iDAAK;IAC9C,+BAAY,UAAkB,EAAE,SAAiB,EAAE,IAAY;QAA/D,iBAGC;QAFC,IAAM,GAAG,GAAG,gCAA8B,SAAS,6BAAwB,UAAU,yDAAoD,IAAI,MAAG,CAAC;QACjJ,QAAA,kBAAM,GAAG,CAAC,SAAC;;IACb,CAAC;IACH,4BAAC;AAAD,CAAC,AALD,CAA2C,KAAK,GAK/C;AALY,sDAAqB"}