{"version": 3, "file": "border.js", "names": ["types", "require", "xmlbuilder", "CTColor", "BorderOrdinal", "opts", "_classCallCheck", "color", "undefined", "style", "borderStyle", "validate", "_createClass", "key", "value", "toObject", "obj", "Border", "_this", "left", "right", "top", "bottom", "diagonal", "outline", "diagonalDown", "diagonalUp", "Object", "keys", "for<PERSON>ach", "opt", "indexOf", "TypeError", "concat", "addToXMLele", "borderXML", "_this2", "bXML", "ele", "att", "ord", "thisOEle", "module", "exports"], "sources": ["../../../../source/lib/style/classes/border.js"], "sourcesContent": ["const types = require('../../types/index.js');\nconst xmlbuilder = require('xmlbuilder');\nconst CTColor = require('./ctColor.js');\n\nclass BorderOrdinal {\n    constructor(opts) {\n        opts = opts ? opts : {};\n        if (opts.color !== undefined) {\n            this.color = new CTColor(opts.color);\n        }\n        if (opts.style !== undefined) {\n            this.style = types.borderStyle.validate(opts.style) === true ? opts.style : null;\n        }\n    }\n\n    toObject() {\n        let obj = {};\n        if (this.color !== undefined) {\n            obj.color = this.color.toObject();\n        }\n        if (this.style !== undefined) {\n            obj.style = this.style;\n        }\n        return obj;\n    }\n}\n\nclass Border {\n    /** \n     * @class Border\n     * @desc Border object for Style\n     * @param {Object} opts Options for Border object\n     * @param {Object} opts.left Options for left side of Border\n     * @param {String} opts.left.color HEX represenation of color\n     * @param {String} opts.left.style Border style\n     * @param {Object} opts.right Options for right side of Border\n     * @param {String} opts.right.color HEX represenation of color\n     * @param {String} opts.right.style Border style\n     * @param {Object} opts.top Options for top side of Border\n     * @param {String} opts.top.color HEX represenation of color\n     * @param {String} opts.top.style Border style\n     * @param {Object} opts.bottom Options for bottom side of Border\n     * @param {String} opts.bottom.color HEX represenation of color\n     * @param {String} opts.bottom.style Border style\n     * @param {Object} opts.diagonal Options for diagonal side of Border\n     * @param {String} opts.diagonal.color HEX represenation of color\n     * @param {String} opts.diagonal.style Border style\n     * @param {Boolean} opts.outline States whether borders should be applied only to the outside borders of a cell range\n     * @param {Boolean} opts.diagonalDown States whether diagonal border should go from top left to bottom right\n     * @param {Boolean} opts.diagonalUp States whether diagonal border should go from bottom left to top right\n     * @returns {Border}\n     */\n    constructor(opts) {\n        opts = opts ? opts : {};\n        this.left;\n        this.right;\n        this.top;\n        this.bottom;\n        this.diagonal;\n        this.outline;\n        this.diagonalDown;\n        this.diagonalUp;\n\n        Object.keys(opts).forEach((opt) => {\n            if (['outline', 'diagonalDown', 'diagonalUp'].indexOf(opt) >= 0) {\n                if (typeof opts[opt] === 'boolean') {\n                    this[opt] = opts[opt];\n                } else {\n                    throw new TypeError('Border outline option must be of type Boolean');\n                }\n            } else if (['left', 'right', 'top', 'bottom', 'diagonal'].indexOf(opt) < 0) { //TODO: move logic to types folder\n                throw new TypeError(`Invalid key for border declaration ${opt}. Must be one of left, right, top, bottom, diagonal`);\n            } else {\n                this[opt] = new BorderOrdinal(opts[opt]);\n            }\n        });\n    }\n\n    /** \n     * @func Border.toObject\n     * @desc Converts the Border instance to a javascript object\n     * @returns {Object}\n     */\n    toObject() {\n        let obj = {};\n        obj.left;\n        obj.right;\n        obj.top;\n        obj.bottom;\n        obj.diagonal;\n\n        if (this.left !== undefined) {\n            obj.left = this.left.toObject();\n        }\n        if (this.right !== undefined) {\n            obj.right = this.right.toObject();\n        }\n        if (this.top !== undefined) {\n            obj.top = this.top.toObject();\n        }\n        if (this.bottom !== undefined) {\n            obj.bottom = this.bottom.toObject();\n        }\n        if (this.diagonal !== undefined) {\n            obj.diagonal = this.diagonal.toObject();\n        }\n        typeof this.outline === 'boolean' ? obj.outline = this.outline : null;\n        typeof this.diagonalDown === 'boolean' ? obj.diagonalDown = this.diagonalDown : null;\n        typeof this.diagonalUp === 'boolean' ? obj.diagonalUp = this.diagonalUp : null;\n\n        return obj;\n    }\n\n    /**\n     * @alias Border.addToXMLele\n     * @desc When generating Workbook output, attaches style to the styles xml file\n     * @func Border.addToXMLele\n     * @param {xmlbuilder.Element} ele Element object of the xmlbuilder module\n     */\n    addToXMLele(borderXML) {\n        let bXML = borderXML.ele('border');\n        if (this.outline === true) {\n            bXML.att('outline', '1');\n        }\n        if (this.diagonalUp === true) {\n            bXML.att('diagonalUp', '1');\n        }\n        if (this.diagonalDown === true) {\n            bXML.att('diagonalDown', '1');\n        }\n\n        ['left', 'right', 'top', 'bottom', 'diagonal'].forEach((ord) => {\n            let thisOEle = bXML.ele(ord);\n            if (this[ord] !== undefined) {\n                if (this[ord].style !== undefined) {\n                    thisOEle.att('style', this[ord].style);\n                }\n                if (this[ord].color instanceof CTColor) {\n                    this[ord].color.addToXMLele(thisOEle);\n                }\n            }\n        });\n    }\n}\n\nmodule.exports = Border;"], "mappings": ";;;;;AAAA,IAAMA,KAAK,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAMC,UAAU,GAAGD,OAAO,CAAC,YAAY,CAAC;AACxC,IAAME,OAAO,GAAGF,OAAO,CAAC,cAAc,CAAC;AAAC,IAElCG,aAAa;EACf,SAAAA,cAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,aAAA;IACdC,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC;IACvB,IAAIA,IAAI,CAACE,KAAK,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAACD,KAAK,GAAG,IAAIJ,OAAO,CAACE,IAAI,CAACE,KAAK,CAAC;IACxC;IACA,IAAIF,IAAI,CAACI,KAAK,KAAKD,SAAS,EAAE;MAC1B,IAAI,CAACC,KAAK,GAAGT,KAAK,CAACU,WAAW,CAACC,QAAQ,CAACN,IAAI,CAACI,KAAK,CAAC,KAAK,IAAI,GAAGJ,IAAI,CAACI,KAAK,GAAG,IAAI;IACpF;EACJ;EAACG,YAAA,CAAAR,aAAA;IAAAS,GAAA;IAAAC,KAAA,EAED,SAAAC,SAAA,EAAW;MACP,IAAIC,GAAG,GAAG,CAAC,CAAC;MACZ,IAAI,IAAI,CAACT,KAAK,KAAKC,SAAS,EAAE;QAC1BQ,GAAG,CAACT,KAAK,GAAG,IAAI,CAACA,KAAK,CAACQ,QAAQ,CAAC,CAAC;MACrC;MACA,IAAI,IAAI,CAACN,KAAK,KAAKD,SAAS,EAAE;QAC1BQ,GAAG,CAACP,KAAK,GAAG,IAAI,CAACA,KAAK;MAC1B;MACA,OAAOO,GAAG;IACd;EAAC;EAAA,OAAAZ,aAAA;AAAA;AAAA,IAGCa,MAAM;EACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,OAAYZ,IAAI,EAAE;IAAA,IAAAa,KAAA;IAAAZ,eAAA,OAAAW,MAAA;IACdZ,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC;IACvB,IAAI,CAACc,IAAI;IACT,IAAI,CAACC,KAAK;IACV,IAAI,CAACC,GAAG;IACR,IAAI,CAACC,MAAM;IACX,IAAI,CAACC,QAAQ;IACb,IAAI,CAACC,OAAO;IACZ,IAAI,CAACC,YAAY;IACjB,IAAI,CAACC,UAAU;IAEfC,MAAM,CAACC,IAAI,CAACvB,IAAI,CAAC,CAACwB,OAAO,CAAC,UAACC,GAAG,EAAK;MAC/B,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE,YAAY,CAAC,CAACC,OAAO,CAACD,GAAG,CAAC,IAAI,CAAC,EAAE;QAC7D,IAAI,OAAOzB,IAAI,CAACyB,GAAG,CAAC,KAAK,SAAS,EAAE;UAChCZ,KAAI,CAACY,GAAG,CAAC,GAAGzB,IAAI,CAACyB,GAAG,CAAC;QACzB,CAAC,MAAM;UACH,MAAM,IAAIE,SAAS,CAAC,+CAA+C,CAAC;QACxE;MACJ,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAACD,OAAO,CAACD,GAAG,CAAC,GAAG,CAAC,EAAE;QAAE;QAC1E,MAAM,IAAIE,SAAS,uCAAAC,MAAA,CAAuCH,GAAG,wDAAqD,CAAC;MACvH,CAAC,MAAM;QACHZ,KAAI,CAACY,GAAG,CAAC,GAAG,IAAI1B,aAAa,CAACC,IAAI,CAACyB,GAAG,CAAC,CAAC;MAC5C;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EAJIlB,YAAA,CAAAK,MAAA;IAAAJ,GAAA;IAAAC,KAAA,EAKA,SAAAC,SAAA,EAAW;MACP,IAAIC,GAAG,GAAG,CAAC,CAAC;MACZA,GAAG,CAACG,IAAI;MACRH,GAAG,CAACI,KAAK;MACTJ,GAAG,CAACK,GAAG;MACPL,GAAG,CAACM,MAAM;MACVN,GAAG,CAACO,QAAQ;MAEZ,IAAI,IAAI,CAACJ,IAAI,KAAKX,SAAS,EAAE;QACzBQ,GAAG,CAACG,IAAI,GAAG,IAAI,CAACA,IAAI,CAACJ,QAAQ,CAAC,CAAC;MACnC;MACA,IAAI,IAAI,CAACK,KAAK,KAAKZ,SAAS,EAAE;QAC1BQ,GAAG,CAACI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACL,QAAQ,CAAC,CAAC;MACrC;MACA,IAAI,IAAI,CAACM,GAAG,KAAKb,SAAS,EAAE;QACxBQ,GAAG,CAACK,GAAG,GAAG,IAAI,CAACA,GAAG,CAACN,QAAQ,CAAC,CAAC;MACjC;MACA,IAAI,IAAI,CAACO,MAAM,KAAKd,SAAS,EAAE;QAC3BQ,GAAG,CAACM,MAAM,GAAG,IAAI,CAACA,MAAM,CAACP,QAAQ,CAAC,CAAC;MACvC;MACA,IAAI,IAAI,CAACQ,QAAQ,KAAKf,SAAS,EAAE;QAC7BQ,GAAG,CAACO,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACR,QAAQ,CAAC,CAAC;MAC3C;MACA,OAAO,IAAI,CAACS,OAAO,KAAK,SAAS,GAAGR,GAAG,CAACQ,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI;MACrE,OAAO,IAAI,CAACC,YAAY,KAAK,SAAS,GAAGT,GAAG,CAACS,YAAY,GAAG,IAAI,CAACA,YAAY,GAAG,IAAI;MACpF,OAAO,IAAI,CAACC,UAAU,KAAK,SAAS,GAAGV,GAAG,CAACU,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,IAAI;MAE9E,OAAOV,GAAG;IACd;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAH,GAAA;IAAAC,KAAA,EAMA,SAAAoB,YAAYC,SAAS,EAAE;MAAA,IAAAC,MAAA;MACnB,IAAIC,IAAI,GAAGF,SAAS,CAACG,GAAG,CAAC,QAAQ,CAAC;MAClC,IAAI,IAAI,CAACd,OAAO,KAAK,IAAI,EAAE;QACvBa,IAAI,CAACE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC;MAC5B;MACA,IAAI,IAAI,CAACb,UAAU,KAAK,IAAI,EAAE;QAC1BW,IAAI,CAACE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC;MAC/B;MACA,IAAI,IAAI,CAACd,YAAY,KAAK,IAAI,EAAE;QAC5BY,IAAI,CAACE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC;MACjC;MAEA,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAACV,OAAO,CAAC,UAACW,GAAG,EAAK;QAC5D,IAAIC,QAAQ,GAAGJ,IAAI,CAACC,GAAG,CAACE,GAAG,CAAC;QAC5B,IAAIJ,MAAI,CAACI,GAAG,CAAC,KAAKhC,SAAS,EAAE;UACzB,IAAI4B,MAAI,CAACI,GAAG,CAAC,CAAC/B,KAAK,KAAKD,SAAS,EAAE;YAC/BiC,QAAQ,CAACF,GAAG,CAAC,OAAO,EAAEH,MAAI,CAACI,GAAG,CAAC,CAAC/B,KAAK,CAAC;UAC1C;UACA,IAAI2B,MAAI,CAACI,GAAG,CAAC,CAACjC,KAAK,YAAYJ,OAAO,EAAE;YACpCiC,MAAI,CAACI,GAAG,CAAC,CAACjC,KAAK,CAAC2B,WAAW,CAACO,QAAQ,CAAC;UACzC;QACJ;MACJ,CAAC,CAAC;IACN;EAAC;EAAA,OAAAxB,MAAA;AAAA;AAGLyB,MAAM,CAACC,OAAO,GAAG1B,MAAM"}