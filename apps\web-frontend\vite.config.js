import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@services': path.resolve(__dirname, './src/services'),
      '@store': path.resolve(__dirname, './src/store'),
      '@types': path.resolve(__dirname, './src/types'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@styles': path.resolve(__dirname, './src/styles'),
    },
  },
  server: {
    port: 5173,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        secure: false,
      },
      '/ai': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/ai/, ''),
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          // Core React libraries
          vendor: ['react', 'react-dom'],

          // Router and state management
          routing: ['react-router-dom', 'zustand'],

          // Material-UI core
          'mui-core': ['@mui/material', '@emotion/react', '@emotion/styled'],
          'mui-icons': ['@mui/icons-material'],
          'mui-x': ['@mui/x-charts', '@mui/x-data-grid', '@mui/x-date-pickers'],

          // Charts and visualization
          'charts-recharts': ['recharts'],
          'charts-chartjs': ['chart.js', 'react-chartjs-2'],

          // Utilities and helpers
          'utils-date': ['date-fns', 'dayjs'],
          'utils-lodash': ['lodash'],
          'utils-misc': ['clsx', 'html2canvas', 'jspdf'],

          // Forms and validation
          forms: ['react-hook-form', '@hookform/resolvers', 'yup'],

          // Internationalization
          i18n: ['i18next', 'react-i18next', 'i18next-http-backend', 'i18next-browser-languagedetector'],

          // Animation and motion
          animation: ['framer-motion'],

          // API and data fetching
          api: ['axios', 'react-query'],

          // File handling
          files: ['react-dropzone', 'react-pdf'],

          // UI enhancements
          ui: ['react-hot-toast'],
        },
      },
    },
    chunkSizeWarningLimit: 500,

    // Additional optimizations
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.js'],
    css: true,
  },
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@mui/material',
      '@mui/icons-material',
      'react-router-dom',
    ],
  },
})
