{"version": 3, "file": "index.js", "names": ["utils", "require", "types", "module", "exports", "Workbook", "getExcelRowCol", "getExcelAlpha", "getExcelTS", "getExcelCellRef", "PaperSize", "paperSize", "CellComment", "cellComments", "PrintError", "printError", "PageOrder", "pageOrder", "Orientation", "orientation", "Pane", "pane", "PaneState", "paneState", "HorizontalAlignment", "alignment", "horizontal", "VerticalAlignment", "vertical", "BorderStyle", "borderStyle", "PresetColorVal", "excelColor", "PatternType", "fillPattern", "PositiveUniversalMeasure", "positiveUniversalMeasure"], "sources": ["../source/index.js"], "sourcesContent": ["/* REFERENCES\n    http://www.ecma-international.org/news/TC45_current_work/OpenXML%20White%20Paper.pdf\n    http://www.ecma-international.org/publications/standards/Ecma-376.htm\n    http://www.openoffice.org/sc/excelfileformat.pdf \n    http://officeopenxml.com/anatomyofOOXML-xlsx.php\n*/\n\n/* \n    Code references specifications sections from ECMA-376 2nd edition doc\n    ECMA-376, Second Edition, Part 1 - Fundamentals And Markup Language Reference.pdf\n    found in ECMA-376 2nd edition Part 1 download at http://www.ecma-international.org/publications/standards/Ecma-376.htm\n    Sections are referenced in code comments with § \n*/\n\nconst utils = require('./lib/utils.js');\nconst types = require('./lib/types/index.js');\n\nmodule.exports = {\n    Workbook: require('./lib/workbook/index.js'),\n    getExcelRowCol: utils.getExcelRowCol,\n    getExcelAlpha: utils.getExcelAlpha,\n    getExcelTS: utils.getExcelTS,\n    getExcelCellRef: utils.getExcelCellRef,\n    PaperSize: types.paperSize,\n    CellComment: types.cellComments,\n    PrintError: types.printError,\n    PageOrder: types.pageOrder,\n    Orientation: types.orientation,\n    Pane: types.pane,\n    PaneState: types.paneState,\n    HorizontalAlignment: types.alignment.horizontal,\n    VerticalAlignment: types.alignment.vertical,\n    BorderStyle: types.borderStyle,\n    PresetColorVal: types.excelColor,\n    PatternType: types.fillPattern,\n    PositiveUniversalMeasure: types.positiveUniversalMeasure\n};\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAMA,KAAK,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACvC,IAAMC,KAAK,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAE7CE,MAAM,CAACC,OAAO,GAAG;EACbC,QAAQ,EAAEJ,OAAO,CAAC,yBAAyB,CAAC;EAC5CK,cAAc,EAAEN,KAAK,CAACM,cAAc;EACpCC,aAAa,EAAEP,KAAK,CAACO,aAAa;EAClCC,UAAU,EAAER,KAAK,CAACQ,UAAU;EAC5BC,eAAe,EAAET,KAAK,CAACS,eAAe;EACtCC,SAAS,EAAER,KAAK,CAACS,SAAS;EAC1BC,WAAW,EAAEV,KAAK,CAACW,YAAY;EAC/BC,UAAU,EAAEZ,KAAK,CAACa,UAAU;EAC5BC,SAAS,EAAEd,KAAK,CAACe,SAAS;EAC1BC,WAAW,EAAEhB,KAAK,CAACiB,WAAW;EAC9BC,IAAI,EAAElB,KAAK,CAACmB,IAAI;EAChBC,SAAS,EAAEpB,KAAK,CAACqB,SAAS;EAC1BC,mBAAmB,EAAEtB,KAAK,CAACuB,SAAS,CAACC,UAAU;EAC/CC,iBAAiB,EAAEzB,KAAK,CAACuB,SAAS,CAACG,QAAQ;EAC3CC,WAAW,EAAE3B,KAAK,CAAC4B,WAAW;EAC9BC,cAAc,EAAE7B,KAAK,CAAC8B,UAAU;EAChCC,WAAW,EAAE/B,KAAK,CAACgC,WAAW;EAC9BC,wBAAwB,EAAEjC,KAAK,CAACkC;AACpC,CAAC"}