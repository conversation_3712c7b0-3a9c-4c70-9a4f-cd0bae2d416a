/**
 * VidyaMitra Platform - Validation Dashboard
 * 
 * Debug component to validate platform features and data integrity
 * Provides comprehensive testing and reporting capabilities
 */

import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  LinearProgress,
  Grid,
  Paper,
  useTheme,
  alpha
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  PlayArrow as RunIcon,
  Assessment as ReportIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { runComprehensiveValidation, reportValidationResults } from '../../utils/validationUtils';

const ValidationDashboard = () => {
  const theme = useTheme();
  const [validation, setValidation] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [lastRun, setLastRun] = useState(null);

  // Run validation on component mount
  useEffect(() => {
    runValidation();
  }, []);

  const runValidation = async () => {
    setIsRunning(true);
    
    // Simulate async validation process
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    try {
      const results = runComprehensiveValidation();
      setValidation(results);
      setLastRun(new Date());
      
      // Also log to console for debugging
      reportValidationResults(results);
    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = (passed) => {
    return passed ? theme.palette.success.main : theme.palette.error.main;
  };

  const getStatusIcon = (passed) => {
    return passed ? <CheckIcon color="success" /> : <ErrorIcon color="error" />;
  };

  const getCategoryStats = (category) => {
    if (!validation) return { total: 0, passed: 0, failed: 0 };
    
    const categoryResults = validation.results.filter(r => r.category === category);
    return {
      total: categoryResults.length,
      passed: categoryResults.filter(r => r.passed).length,
      failed: categoryResults.filter(r => !r.passed).length
    };
  };

  if (!validation && !isRunning) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          VidyaMitra Validation Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<RunIcon />}
          onClick={runValidation}
          size="large"
        >
          Run Validation Tests
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
            🎓 VidyaMitra Validation Dashboard
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Comprehensive platform testing and validation results
          </Typography>
        </Box>

        {/* Loading State */}
        {isRunning && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Running Validation Tests...
              </Typography>
              <LinearProgress sx={{ mt: 2 }} />
            </CardContent>
          </Card>
        )}

        {/* Summary Cards */}
        {validation && (
          <>
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} md={3}>
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: alpha(theme.palette.primary.main, 0.1),
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
                  }}
                >
                  <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                    {validation.summary.total}
                  </Typography>
                  <Typography variant="subtitle2" color="text.secondary">
                    Total Tests
                  </Typography>
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: alpha(theme.palette.success.main, 0.1),
                    border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`
                  }}
                >
                  <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.success.main }}>
                    {validation.summary.passed}
                  </Typography>
                  <Typography variant="subtitle2" color="text.secondary">
                    Passed
                  </Typography>
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: alpha(theme.palette.error.main, 0.1),
                    border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`
                  }}
                >
                  <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.error.main }}>
                    {validation.summary.failed}
                  </Typography>
                  <Typography variant="subtitle2" color="text.secondary">
                    Failed
                  </Typography>
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: alpha(theme.palette.info.main, 0.1),
                    border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                  }}
                >
                  <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.info.main }}>
                    {Math.round((validation.summary.passed / validation.summary.total) * 100)}%
                  </Typography>
                  <Typography variant="subtitle2" color="text.secondary">
                    Success Rate
                  </Typography>
                </Paper>
              </Grid>
            </Grid>

            {/* Overall Status */}
            <Alert 
              severity={validation.success ? 'success' : 'warning'} 
              sx={{ mb: 3 }}
              icon={validation.success ? <CheckIcon /> : <ErrorIcon />}
            >
              <Typography variant="h6">
                {validation.success 
                  ? '🎉 All validation tests passed! VidyaMitra platform is ready.'
                  : `⚠️ ${validation.summary.failed} test(s) failed. Please review the issues below.`
                }
              </Typography>
            </Alert>

            {/* Action Buttons */}
            <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={runValidation}
                disabled={isRunning}
              >
                Re-run Tests
              </Button>
              <Button
                variant="outlined"
                startIcon={<ReportIcon />}
                onClick={() => reportValidationResults(validation)}
              >
                View Console Report
              </Button>
            </Box>

            {/* Detailed Results by Category */}
            <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }}>
              Detailed Results
            </Typography>

            {validation.summary.categories.map((category) => {
              const stats = getCategoryStats(category);
              const categoryResults = validation.results.filter(r => r.category === category);
              
              return (
                <Accordion key={category} sx={{ mb: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                      <Typography variant="h6" sx={{ flexGrow: 1 }}>
                        📂 {category}
                      </Typography>
                      <Chip
                        label={`${stats.passed}/${stats.total}`}
                        color={stats.failed === 0 ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List>
                      {categoryResults.map((result, index) => (
                        <ListItem key={index} sx={{ pl: 0 }}>
                          <ListItemIcon>
                            {getStatusIcon(result.passed)}
                          </ListItemIcon>
                          <ListItemText
                            primary={result.test}
                            secondary={result.message}
                            primaryTypographyProps={{
                              fontWeight: result.passed ? 400 : 600,
                              color: getStatusColor(result.passed)
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              );
            })}

            {/* Last Run Info */}
            {lastRun && (
              <Box sx={{ mt: 4, p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Last validation run: {lastRun.toLocaleString()}
                </Typography>
              </Box>
            )}
          </>
        )}
      </motion.div>
    </Box>
  );
};

export default ValidationDashboard;
