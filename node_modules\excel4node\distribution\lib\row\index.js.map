{"version": 3, "file": "index.js", "names": ["Row", "require", "rowAccessor", "ws", "row", "TypeError", "rows", "module", "exports"], "sources": ["../../../source/lib/row/index.js"], "sourcesContent": ["const Row = require('../row/row.js');\n\n/**\n * <PERSON><PERSON>le repesenting a Row Accessor\n * @alias Worksheet.row\n * @namespace\n * @func Worksheet.row\n * @desc Access a row in order to manipulate values\n * @param {Number} row Row of top left cell\n * @returns {Row}\n */\nlet rowAccessor = function (ws, row) {\n\n    if (typeof row !== 'number') {\n        throw new TypeError('Row sent to row accessor was not a number.');\n    }\n\n    if (!(ws.rows[row] instanceof Row)) {\n        ws.rows[row] = new Row(row, ws);\n    }\n\n    return ws.rows[row];\n};\n\n\n\nmodule.exports = rowAccessor;"], "mappings": ";;AAAA,IAAMA,GAAG,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,SAAdA,WAAWA,CAAaC,EAAE,EAAEC,GAAG,EAAE;EAEjC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzB,MAAM,IAAIC,SAAS,CAAC,4CAA4C,CAAC;EACrE;EAEA,IAAI,EAAEF,EAAE,CAACG,IAAI,CAACF,GAAG,CAAC,YAAYJ,GAAG,CAAC,EAAE;IAChCG,EAAE,CAACG,IAAI,CAACF,GAAG,CAAC,GAAG,IAAIJ,GAAG,CAACI,GAAG,EAAED,EAAE,CAAC;EACnC;EAEA,OAAOA,EAAE,CAACG,IAAI,CAACF,GAAG,CAAC;AACvB,CAAC;AAIDG,MAAM,CAACC,OAAO,GAAGN,WAAW"}