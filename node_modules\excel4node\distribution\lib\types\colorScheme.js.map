{"version": 3, "file": "colorScheme.js", "names": ["items", "_this", "opts", "for<PERSON>ach", "o", "i", "prototype", "validate", "val", "toLowerCase", "undefined", "name", "hasOwnProperty", "push", "TypeError", "join", "module", "exports"], "sources": ["../../../source/lib/types/colorScheme.js"], "sourcesContent": ["function items() {\n    this.opts = [//§20.1.6.2 clrScheme (Color Scheme)\n        'dark 1', \n        'light 1', \n        'dark 2', \n        'light 2', \n        'accent 1', \n        'accent 2', \n        'accent 3', \n        'accent 4', \n        'accent 5', \n        'accent 6', \n        'hyperlink', \n        'followed hyperlink'\n    ];\n    this.opts.forEach((o, i) => {\n        this[o] = i + 1;\n    });\n}\n\n\nitems.prototype.validate = function (val) {\n    if (this[val.toLowerCase()] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError('Invalid value for clrScheme; Value must be one of ' + this.opts.join(', '));\n    } else {\n        return true;\n    }\n};\n\nmodule.exports = new items();"], "mappings": ";;AAAA,SAASA,KAAKA,CAAA,EAAG;EAAA,IAAAC,KAAA;EACb,IAAI,CAACC,IAAI,GAAG;EAAC;EACT,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,WAAW,EACX,oBAAoB,CACvB;EACD,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACxBJ,KAAI,CAACG,CAAC,CAAC,GAAGC,CAAC,GAAG,CAAC;EACnB,CAAC,CAAC;AACN;AAGAL,KAAK,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACtC,IAAI,IAAI,CAACA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;IACvC,IAAIR,IAAI,GAAG,EAAE;IACb,KAAK,IAAIS,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BT,IAAI,CAACW,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIG,SAAS,CAAC,oDAAoD,GAAG,IAAI,CAACZ,IAAI,CAACa,IAAI,CAAC,IAAI,CAAC,CAAC;EACpG,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,IAAIjB,KAAK,CAAC,CAAC"}