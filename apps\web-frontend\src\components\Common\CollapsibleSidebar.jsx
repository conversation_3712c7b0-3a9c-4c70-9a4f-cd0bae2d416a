/**
 * VidyaMitra Platform - Collapsible Sidebar Navigation
 * 
 * Modern sidebar with smooth animations, responsive design, and accessibility features
 * Supports both expanded and collapsed states with glassmorphism design
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Typography,
  Tooltip,
  Divider,
  alpha,
  useTheme,
  useMediaQuery,
  Collapse,
  Badge
} from '@mui/material';
import {
  Settings as SettingsIcon,
  School as SchoolIcon,
  MenuOpen as MenuOpenIcon,
  Menu as MenuIcon,
  ExpandLess,
  ExpandMore,
  Logout as LogoutIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router-dom';
import { mainNavigation, settingsNavigation, getNavigationByRole } from '../../data/navigationData';

const DRAWER_WIDTH_EXPANDED = 280;
const DRAWER_WIDTH_COLLAPSED = 72;

const CollapsibleSidebar = ({ open, onToggle, userRole = 'teacher' }) => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [isExpanded, setIsExpanded] = useState(!isMobile);
  const [expandedItems, setExpandedItems] = useState({});

  // Auto-collapse on mobile
  useEffect(() => {
    if (isMobile) {
      setIsExpanded(false);
    }
  }, [isMobile]);

  // Get navigation items based on user role
  const menuItems = getNavigationByRole(mainNavigation, userRole);
  const bottomMenuItems = getNavigationByRole(settingsNavigation, userRole);

  // Handle submenu expansion
  const handleExpandClick = (itemId) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  const handleToggle = () => {
    if (isMobile) {
      onToggle();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  const handleNavigation = (path) => {
    navigate(path);
    if (isMobile) {
      onToggle(); // Close sidebar on mobile after navigation
    }
  };

  const isActive = (path) => {
    if (path === '/dashboard') {
      return location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(path);
  };

  const drawerWidth = isExpanded ? DRAWER_WIDTH_EXPANDED : DRAWER_WIDTH_COLLAPSED;

  const sidebarContent = (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        background: theme.palette.mode === 'dark'
          ? `linear-gradient(180deg, ${alpha('#0F172A', 0.95)} 0%, ${alpha('#1E293B', 0.95)} 100%)`
          : `linear-gradient(180deg, ${alpha('#F8FAFC', 0.95)} 0%, ${alpha('#E2E8F0', 0.95)} 100%)`,
        backdropFilter: 'blur(20px)',
        borderRight: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: isExpanded ? 'space-between' : 'center',
          minHeight: 64,
        }}
      >
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              style={{ display: 'flex', alignItems: 'center', gap: 12 }}
            >
              <SchoolIcon sx={{ color: theme.palette.primary.main, fontSize: 28 }} />
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                }}
              >
                VidyaMitra
              </Typography>
            </motion.div>
          )}
        </AnimatePresence>

        <IconButton
          onClick={handleToggle}
          sx={{
            color: theme.palette.text.secondary,
            '&:hover': {
              background: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main,
            },
          }}
        >
          {isExpanded ? <MenuOpenIcon /> : <MenuIcon />}
        </IconButton>
      </Box>

      <Divider sx={{ opacity: 0.3 }} />

      {/* Main Navigation */}
      <Box sx={{ flex: 1, py: 1 }}>
        <List sx={{ px: 1 }}>
          {menuItems.map((item) => {
            const active = isActive(item.path);
            const IconComponent = item.icon;
            const hasChildren = item.children && item.children.length > 0;
            const isExpanded = expandedItems[item.id];

            return (
              <React.Fragment key={item.id}>
                <ListItem disablePadding sx={{ mb: 0.5 }}>
                  <Tooltip
                    title={isExpanded ? '' : item.title}
                    placement="right"
                    arrow
                  >
                    <ListItemButton
                      onClick={() => {
                        if (hasChildren) {
                          handleExpandClick(item.id);
                        } else {
                          handleNavigation(item.path);
                        }
                      }}
                      sx={{
                        borderRadius: 2,
                        minHeight: 48,
                        px: isExpanded ? 2 : 1.5,
                        background: active
                          ? alpha(theme.palette.primary.main, 0.15)
                          : 'transparent',
                        color: active
                          ? theme.palette.primary.main
                          : theme.palette.text.secondary,
                        '&:hover': {
                          background: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          transform: 'translateX(4px)',
                        },
                        transition: 'all 0.3s ease',
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: isExpanded ? 40 : 'auto',
                          color: 'inherit',
                          justifyContent: 'center',
                        }}
                      >
                        <IconComponent sx={{ fontSize: 22 }} />
                      </ListItemIcon>
                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -10 }}
                            transition={{ duration: 0.2 }}
                            style={{ width: '100%', display: 'flex', alignItems: 'center' }}
                          >
                            <ListItemText
                              primary={item.title}
                              secondary={item.description}
                              primaryTypographyProps={{
                                fontSize: '0.875rem',
                                fontWeight: active ? 600 : 500,
                              }}
                              secondaryTypographyProps={{
                                fontSize: '0.75rem',
                                opacity: 0.7,
                              }}
                            />
                            {hasChildren && (
                              <Box sx={{ ml: 'auto' }}>
                                {isExpanded ? <ExpandLess /> : <ExpandMore />}
                              </Box>
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </ListItemButton>
                  </Tooltip>
                </ListItem>

                {/* Submenu items */}
                {hasChildren && (
                  <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding sx={{ pl: isExpanded ? 2 : 0 }}>
                      {item.children.map((child) => {
                        const childActive = isActive(child.path);
                        const ChildIconComponent = child.icon;

                        return (
                          <ListItem key={child.id} disablePadding sx={{ mb: 0.25 }}>
                            <Tooltip
                              title={isExpanded ? '' : child.title}
                              placement="right"
                              arrow
                            >
                              <ListItemButton
                                onClick={() => handleNavigation(child.path)}
                                sx={{
                                  borderRadius: 1.5,
                                  minHeight: 40,
                                  px: isExpanded ? 1.5 : 1,
                                  background: childActive
                                    ? alpha(theme.palette.primary.main, 0.1)
                                    : 'transparent',
                                  color: childActive
                                    ? theme.palette.primary.main
                                    : theme.palette.text.secondary,
                                  '&:hover': {
                                    background: alpha(theme.palette.primary.main, 0.08),
                                    color: theme.palette.primary.main,
                                  },
                                  transition: 'all 0.2s ease',
                                }}
                              >
                                <ListItemIcon
                                  sx={{
                                    minWidth: isExpanded ? 32 : 'auto',
                                    color: 'inherit',
                                    justifyContent: 'center',
                                  }}
                                >
                                  <ChildIconComponent sx={{ fontSize: 18 }} />
                                </ListItemIcon>
                                <AnimatePresence>
                                  {isExpanded && (
                                    <motion.div
                                      initial={{ opacity: 0, x: -10 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      exit={{ opacity: 0, x: -10 }}
                                      transition={{ duration: 0.15 }}
                                      style={{ width: '100%' }}
                                    >
                                      <ListItemText
                                        primary={child.title}
                                        primaryTypographyProps={{
                                          fontSize: '0.8rem',
                                          fontWeight: childActive ? 600 : 400,
                                        }}
                                      />
                                    </motion.div>
                                  )}
                                </AnimatePresence>
                              </ListItemButton>
                            </Tooltip>
                          </ListItem>
                        );
                      })}
                    </List>
                  </Collapse>
                )}
              </React.Fragment>
            );
          })}
        </List>
      </Box>

      <Divider sx={{ opacity: 0.3 }} />

      {/* Bottom Navigation */}
      <Box sx={{ py: 1 }}>
        <List sx={{ px: 1 }}>
          {bottomMenuItems.map((item) => {
            const active = isActive(item.path);
            const IconComponent = item.icon;

            return (
              <ListItem key={item.id} disablePadding>
                <Tooltip
                  title={isExpanded ? '' : item.title}
                  placement="right"
                  arrow
                >
                  <ListItemButton
                    onClick={() => handleNavigation(item.path)}
                    sx={{
                      borderRadius: 2,
                      minHeight: 48,
                      px: isExpanded ? 2 : 1.5,
                      background: active
                        ? alpha(theme.palette.primary.main, 0.15)
                        : 'transparent',
                      color: active
                        ? theme.palette.primary.main
                        : theme.palette.text.secondary,
                      '&:hover': {
                        background: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        transform: 'translateX(4px)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        minWidth: isExpanded ? 40 : 'auto',
                        color: 'inherit',
                        justifyContent: 'center',
                      }}
                    >
                      <IconComponent sx={{ fontSize: 22 }} />
                    </ListItemIcon>
                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          transition={{ duration: 0.2 }}
                          style={{ width: '100%' }}
                        >
                          <ListItemText
                            primary={item.title}
                            primaryTypographyProps={{
                              fontSize: '0.875rem',
                              fontWeight: active ? 600 : 500,
                            }}
                          />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </ListItemButton>
                </Tooltip>
              </ListItem>
            );
          })}

          {/* Logout Button */}
          <ListItem disablePadding sx={{ mt: 1 }}>
            <Tooltip
              title={isExpanded ? '' : 'Logout'}
              placement="right"
              arrow
            >
              <ListItemButton
                onClick={() => {
                  // Handle logout logic here
                  console.log('Logout clicked');
                }}
                sx={{
                  borderRadius: 2,
                  minHeight: 48,
                  px: isExpanded ? 2 : 1.5,
                  color: theme.palette.error.main,
                  '&:hover': {
                    background: alpha(theme.palette.error.main, 0.1),
                    transform: 'translateX(4px)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: isExpanded ? 40 : 'auto',
                    color: 'inherit',
                    justifyContent: 'center',
                  }}
                >
                  <LogoutIcon sx={{ fontSize: 22 }} />
                </ListItemIcon>
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.2 }}
                      style={{ width: '100%' }}
                    >
                      <ListItemText
                        primary="Logout"
                        primaryTypographyProps={{
                          fontSize: '0.875rem',
                          fontWeight: 500,
                        }}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </ListItemButton>
            </Tooltip>
          </ListItem>
        </List>
      </Box>
    </Box>
  );

  if (isMobile) {
    return (
      <Drawer
        variant="temporary"
        open={open}
        onClose={onToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: DRAWER_WIDTH_EXPANDED,
            boxSizing: 'border-box',
          },
        }}
      >
        {sidebarContent}
      </Drawer>
    );
  }

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
          overflowX: 'hidden',
        },
      }}
    >
      {sidebarContent}
    </Drawer>
  );
};

export default CollapsibleSidebar;
