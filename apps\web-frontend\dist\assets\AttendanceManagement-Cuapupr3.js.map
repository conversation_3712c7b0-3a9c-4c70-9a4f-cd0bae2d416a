{"version": 3, "file": "AttendanceManagement-Cuapupr3.js", "sources": ["../../src/components/Attendance/AttendanceManagement.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Attendance Management Component\n * \n * Daily attendance marking interface for teachers with Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  Button,\n  Avatar,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Stack,\n  Alert,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  CheckCircle,\n  Cancel,\n  Schedule,\n  CalendarToday,\n  Save,\n  Print,\n  Download,\n  Refresh,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\n\n// Sample attendance data with Indian student names\nconst attendanceData = [\n  { id: 1, name: '<PERSON><PERSON>', rollNumber: 1, status: 'present', lastAttendance: '95%' },\n  { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>', rollNumber: 2, status: 'present', lastAttendance: '92%' },\n  { id: 3, name: '<PERSON><PERSON><PERSON>', rollNumber: 3, status: 'absent', lastAttendance: '88%' },\n  { id: 4, name: '<PERSON>', rollNumber: 4, status: 'present', lastA<PERSON>dance: '94%' },\n  { id: 5, name: 'Ankitha Patel', rollNumber: 5, status: 'late', lastAttendance: '90%' },\n  { id: 6, name: 'Sirisha Nair', rollNumber: 6, status: 'present', lastAttendance: '96%' },\n  { id: 7, name: 'Priya Agarwal', rollNumber: 7, status: 'present', lastAttendance: '85%' },\n];\n\nconst AttendanceManagement = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const [selectedClass, setSelectedClass] = useState('10-A');\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n  const [attendance, setAttendance] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    // Initialize attendance state\n    const initialAttendance = {};\n    attendanceData.forEach(student => {\n      initialAttendance[student.id] = student.status;\n    });\n    setAttendance(initialAttendance);\n  }, []);\n\n  const handleAttendanceChange = (studentId, status) => {\n    setAttendance(prev => ({\n      ...prev,\n      [studentId]: status\n    }));\n  };\n\n  const handleSaveAttendance = async () => {\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      // Show success message\n      alert('Attendance saved successfully!');\n    } catch (error) {\n      console.error('Error saving attendance:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'present':\n        return 'success';\n      case 'absent':\n        return 'error';\n      case 'late':\n        return 'warning';\n      default:\n        return 'default';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'present':\n        return <CheckCircle />;\n      case 'absent':\n        return <Cancel />;\n      case 'late':\n        return <Schedule />;\n      default:\n        return null;\n    }\n  };\n\n  const attendanceSummary = {\n    present: Object.values(attendance).filter(status => status === 'present').length,\n    absent: Object.values(attendance).filter(status => status === 'absent').length,\n    late: Object.values(attendance).filter(status => status === 'late').length,\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Attendance Management\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Mark daily attendance for your students\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Controls */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Grid container spacing={3} alignItems=\"center\">\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Class</InputLabel>\n                <Select\n                  value={selectedClass}\n                  onChange={(e) => setSelectedClass(e.target.value)}\n                  label=\"Class\"\n                >\n                  <MenuItem value=\"9-A\">Class 9-A</MenuItem>\n                  <MenuItem value=\"9-B\">Class 9-B</MenuItem>\n                  <MenuItem value=\"10-A\">Class 10-A</MenuItem>\n                  <MenuItem value=\"10-B\">Class 10-B</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                  Date\n                </Typography>\n                <input\n                  type=\"date\"\n                  value={selectedDate}\n                  onChange={(e) => setSelectedDate(e.target.value)}\n                  style={{\n                    width: '100%',\n                    padding: '12px',\n                    border: `1px solid ${theme.palette.divider}`,\n                    borderRadius: '4px',\n                    fontSize: '16px',\n                  }}\n                />\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Stack direction=\"row\" spacing={2}>\n                <Button\n                  variant=\"contained\"\n                  startIcon={<Save />}\n                  onClick={handleSaveAttendance}\n                  loading={loading}\n                  sx={{\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  Save Attendance\n                </Button>\n                <Button variant=\"outlined\" startIcon={<Print />}>\n                  Print Report\n                </Button>\n                <Button variant=\"outlined\" startIcon={<Download />}>\n                  Export\n                </Button>\n              </Stack>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Attendance Summary */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {attendanceSummary.present}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Present\n                  </Typography>\n                </Box>\n                <CheckCircle sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.error.main} 0%, ${theme.palette.error.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {attendanceSummary.absent}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Absent\n                  </Typography>\n                </Box>\n                <Cancel sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {attendanceSummary.late}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Late\n                  </Typography>\n                </Box>\n                <Schedule sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {attendanceData.length}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Total Students\n                  </Typography>\n                </Box>\n                <CalendarToday sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Attendance Table */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Student Attendance - {selectedClass} ({selectedDate})\n          </Typography>\n\n          <TableContainer component={Paper} variant=\"outlined\">\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Roll No.</TableCell>\n                  <TableCell>Student Name</TableCell>\n                  <TableCell>Previous Attendance</TableCell>\n                  <TableCell>Status</TableCell>\n                  <TableCell>Actions</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {attendanceData.map((student) => (\n                  <TableRow key={student.id} hover>\n                    <TableCell>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                        {student.rollNumber}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                        <Avatar sx={{ width: 32, height: 32 }}>\n                          {student.name.charAt(0)}\n                        </Avatar>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {student.name}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\">{student.lastAttendance}</Typography>\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        icon={getStatusIcon(attendance[student.id])}\n                        label={attendance[student.id]?.charAt(0).toUpperCase() + attendance[student.id]?.slice(1)}\n                        color={getStatusColor(attendance[student.id])}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Stack direction=\"row\" spacing={1}>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleAttendanceChange(student.id, 'present')}\n                          color={attendance[student.id] === 'present' ? 'success' : 'default'}\n                        >\n                          <CheckCircle />\n                        </IconButton>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleAttendanceChange(student.id, 'absent')}\n                          color={attendance[student.id] === 'absent' ? 'error' : 'default'}\n                        >\n                          <Cancel />\n                        </IconButton>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleAttendanceChange(student.id, 'late')}\n                          color={attendance[student.id] === 'late' ? 'warning' : 'default'}\n                        >\n                          <Schedule />\n                        </IconButton>\n                      </Stack>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default AttendanceManagement;\n"], "names": ["attendanceData", "id", "name", "rollNumber", "status", "lastAttendance", "AttendanceManagement", "theme", "useTheme", "useNavigate", "selectedClass", "setSelectedClass", "useState", "selectedDate", "setSelectedDate", "Date", "toISOString", "split", "attendance", "setAttendance", "loading", "setLoading", "useEffect", "initialAttendance", "for<PERSON>ach", "student", "handleAttendanceChange", "studentId", "prev", "getStatusColor", "getStatusIcon", "CheckCircle", "Cancel", "Schedule", "attendanceSummary", "present", "Object", "values", "filter", "length", "absent", "late", "Box", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "jsxRuntimeExports", "jsx", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "mb", "Typography", "variant", "fontWeight", "background", "palette", "primary", "main", "secondary", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "color", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "container", "spacing", "alignItems", "item", "xs", "md", "jsxs", "FormControl", "fullWidth", "InputLabel", "Select", "value", "onChange", "e", "target", "label", "MenuItem", "type", "style", "width", "padding", "border", "divider", "borderRadius", "fontSize", "<PERSON><PERSON>", "direction", "<PERSON><PERSON>", "startIcon", "Save", "onClick", "async", "Promise", "resolve", "setTimeout", "alert", "error", "Print", "Download", "success", "dark", "display", "justifyContent", "warning", "info", "CalendarToday", "TableContainer", "component", "Paper", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "map", "hover", "gap", "Avatar", "height", "char<PERSON>t", "Chip", "icon", "_a", "toUpperCase", "_b", "slice", "size", "IconButton"], "mappings": "mcA+CA,MAAMA,EAAiB,CACrB,CAAEC,GAAI,EAAGC,KAAM,oBAAqBC,WAAY,EAAGC,OAAQ,UAAWC,eAAgB,OACtF,CAAEJ,GAAI,EAAGC,KAAM,oBAAqBC,WAAY,EAAGC,OAAQ,UAAWC,eAAgB,OACtF,CAAEJ,GAAI,EAAGC,KAAM,eAAgBC,WAAY,EAAGC,OAAQ,SAAUC,eAAgB,OAChF,CAAEJ,GAAI,EAAGC,KAAM,mBAAoBC,WAAY,EAAGC,OAAQ,UAAWC,eAAgB,OACrF,CAAEJ,GAAI,EAAGC,KAAM,gBAAiBC,WAAY,EAAGC,OAAQ,OAAQC,eAAgB,OAC/E,CAAEJ,GAAI,EAAGC,KAAM,eAAgBC,WAAY,EAAGC,OAAQ,UAAWC,eAAgB,OACjF,CAAEJ,GAAI,EAAGC,KAAM,gBAAiBC,WAAY,EAAGC,OAAQ,UAAWC,eAAgB,QAG9EC,EAAuB,KAC3B,MAAMC,EAAQC,IACeC,IAC7B,MAAOC,EAAeC,GAAoBC,EAAAA,SAAS,SAC5CC,EAAcC,GAAmBF,EAAAA,UAAa,IAAAG,MAAOC,cAAcC,MAAM,KAAK,KAC9EC,EAAYC,GAAiBP,EAAAA,SAAS,CAAA,IACtCQ,EAASC,GAAcT,EAAAA,UAAS,GAEvCU,EAAAA,WAAU,KAER,MAAMC,EAAoB,CAAC,EACZvB,EAAAwB,SAAmBC,IACdF,EAAAE,EAAQxB,IAAMwB,EAAQrB,MAAA,IAE1Ce,EAAcI,EAAiB,GAC9B,IAEG,MAAAG,EAAyB,CAACC,EAAWvB,KACzCe,GAAuBS,IAAA,IAClBA,EACHD,CAACA,GAAYvB,KACb,EAiBEyB,EAAkBzB,IACtB,OAAQA,GACN,IAAK,UACI,MAAA,UACT,IAAK,SACI,MAAA,QACT,IAAK,OACI,MAAA,UACT,QACS,MAAA,UAAA,EAIP0B,EAAiB1B,IACrB,OAAQA,GACN,IAAK,UACH,aAAQ2B,EAAY,IACtB,IAAK,SACH,aAAQC,EAAO,IACjB,IAAK,OACH,aAAQC,EAAS,IACnB,QACS,OAAA,KAAA,EAIPC,EAAoB,CACxBC,QAASC,OAAOC,OAAOnB,GAAYoB,QAAOlC,GAAqB,YAAXA,IAAsBmC,OAC1EC,OAAQJ,OAAOC,OAAOnB,GAAYoB,QAAOlC,GAAqB,WAAXA,IAAqBmC,OACxEE,KAAML,OAAOC,OAAOnB,GAAYoB,QAAOlC,GAAqB,SAAXA,IAAmBmC,QAIpE,cAACG,EAAI,CAAAC,GAAI,CAAEC,SAAU,KAAMC,GAAI,OAAQC,EAAG,GAExCC,SAAA,CAAAC,EAAAC,IAACC,EAAOC,IAAP,CACCC,QAAS,CAAEC,QAAS,EAAGC,GAAO,IAC9BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAExBV,gBAACL,EAAI,CAAAC,GAAI,CAAEe,GAAI,GACbX,SAAA,CAAAC,EAAAC,IAACU,EAAA,CACCC,QAAQ,KACRjB,GAAI,CACFkB,WAAY,IACZH,GAAI,EACJI,WAAY,2BAA2BvD,EAAMwD,QAAQC,QAAQC,YAAY1D,EAAMwD,QAAQG,UAAUD,aACjGE,qBAAsB,OACtBC,oBAAqB,cACrBC,eAAgB,QAEnBtB,SAAA,gCAGAY,EAAW,CAAAC,QAAQ,QAAQU,MAAM,iBAAiBvB,SAEnD,uDAKHwB,EAAK,CAAA5B,GAAI,CAAEe,GAAI,GACdX,SAAAC,EAAAC,IAACuB,EACC,CAAAzB,gBAAC0B,GAAKC,WAAS,EAACC,QAAS,EAAGC,WAAW,SACrC7B,SAAA,GAACE,IAAAwB,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAiC,EAAAA,KAACC,EAAY,CAAAC,WAAS,EACpBnC,SAAA,GAAAE,IAACkC,GAAWpC,SAAK,UACjBC,EAAAgC,KAACI,EAAA,CACCC,MAAO3E,EACP4E,SAAWC,GAAM5E,EAAiB4E,EAAEC,OAAOH,OAC3CI,MAAM,QAEN1C,SAAA,CAACE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,MAAMtC,SAAS,cAC9BE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,MAAMtC,SAAS,cAC9BE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,OAAOtC,SAAU,eAChCE,EAAAA,IAAAyC,EAAA,CAASL,MAAM,OAAOtC,SAAU,yBAIvCE,IAACwB,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAiC,EAAAA,KAACC,EAAY,CAAAC,WAAS,EACpBnC,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,QAAQU,MAAM,iBAAiB3B,GAAI,CAAEe,GAAI,GAAKX,SAElE,SACAC,EAAAC,IAAC,QAAA,CACC0C,KAAK,OACLN,MAAOxE,EACPyE,SAAWC,GAAMzE,EAAgByE,EAAEC,OAAOH,OAC1CO,MAAO,CACLC,MAAO,OACPC,QAAS,OACTC,OAAQ,aAAaxF,EAAMwD,QAAQiC,UACnCC,aAAc,MACdC,SAAU,iBAKjBjD,IAAAwB,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAACiC,EAAAA,KAAAmB,EAAA,CAAMC,UAAU,MAAMzB,QAAS,EAC9B5B,SAAA,CAAAC,EAAAC,IAACoD,EAAA,CACCzC,QAAQ,YACR0C,gBAAYC,EAAK,IACjBC,QArHaC,UAC3BpF,GAAW,GACP,UAEI,IAAIqF,SAAQC,GAAWC,WAAWD,EAAS,OAEjDE,MAAM,wCACCC,GACwC,CAC/C,QACAzF,GAAW,EAAK,GA4GJD,UACAuB,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMwD,QAAQC,QAAQC,YAAY1D,EAAMwD,QAAQG,UAAUD,cAEpGlB,SAAA,oBAGDE,EAAAA,IAACoD,GAAOzC,QAAQ,WAAW0C,gBAAYS,EAAA,CAAM,GAAIhE,SAEjD,iBACAE,EAAAA,IAACoD,GAAOzC,QAAQ,WAAW0C,UAAYtD,EAAAC,IAAA+D,EAAA,CAAA,GAAajE,SAEpD,uBAQViC,EAAAA,KAACP,EAAK,CAAAC,WAAS,EAACC,QAAS,EAAGhC,GAAI,CAAEe,GAAI,GACpCX,SAAA,CAAAE,MAACwB,GAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAC,EAAAC,IAACsB,EAAA,CACC5B,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMwD,QAAQkD,QAAQhD,YAAY1D,EAAMwD,QAAQkD,QAAQC,aAC/F5C,MAAO,SAGTvB,SAACC,EAAAC,IAAAuB,EAAA,CACCzB,WAACiC,KAAAtC,EAAA,CAAIC,GAAI,CAAEwE,QAAS,OAAQvC,WAAY,SAAUwC,eAAgB,iBAChErE,SAAA,QAACL,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAAb,EAAkBC,UAErBc,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,eAEFE,MAAClB,GAAYY,GAAI,CAAEuD,SAAU,GAAI7C,QAAS,qBAKjDoB,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAC,EAAAC,IAACsB,EAAA,CACC5B,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMwD,QAAQ+C,MAAM7C,YAAY1D,EAAMwD,QAAQ+C,MAAMI,aAC3F5C,MAAO,SAGTvB,SAACC,EAAAC,IAAAuB,EAAA,CACCzB,WAACiC,KAAAtC,EAAA,CAAIC,GAAI,CAAEwE,QAAS,OAAQvC,WAAY,SAAUwC,eAAgB,iBAChErE,SAAA,QAACL,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAAb,EAAkBM,SAErBS,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,cAEFE,MAACjB,GAAOW,GAAI,CAAEuD,SAAU,GAAI7C,QAAS,qBAK5CoB,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAC,EAAAC,IAACsB,EAAA,CACC5B,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMwD,QAAQsD,QAAQpD,YAAY1D,EAAMwD,QAAQsD,QAAQH,aAC/F5C,MAAO,SAGTvB,SAACC,EAAAC,IAAAuB,EAAA,CACCzB,WAACiC,KAAAtC,EAAA,CAAIC,GAAI,CAAEwE,QAAS,OAAQvC,WAAY,SAAUwC,eAAgB,iBAChErE,SAAA,QAACL,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAAb,EAAkBO,OAErBQ,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,YAEFE,MAAChB,GAASU,GAAI,CAAEuD,SAAU,GAAI7C,QAAS,qBAK9CoB,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrBhC,SAAAC,EAAAC,IAACsB,EAAA,CACC5B,GAAI,CACFmB,WAAY,2BAA2BvD,EAAMwD,QAAQuD,KAAKrD,YAAY1D,EAAMwD,QAAQuD,KAAKJ,aACzF5C,MAAO,SAGTvB,SAACC,EAAAC,IAAAuB,EAAA,CACCzB,WAACiC,KAAAtC,EAAA,CAAIC,GAAI,CAAEwE,QAAS,OAAQvC,WAAY,SAAUwC,eAAgB,iBAChErE,SAAA,QAACL,EACC,CAAAK,SAAA,CAACE,EAAAA,IAAAU,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEkB,WAAY,KACxCd,SAAA/C,EAAeuC,SAElBU,EAAAA,IAACU,GAAWC,QAAQ,QAAQjB,GAAI,CAAEU,QAAS,IAAON,SAElD,sBAEFE,MAACsE,GAAc5E,GAAI,CAAEuD,SAAU,GAAI7C,QAAS,kBAQtDJ,EAAAA,IAACsB,EACC,CAAAxB,SAAAiC,EAAAA,KAACR,EACC,CAAAzB,SAAA,CAACiC,EAAAA,KAAArB,EAAA,CAAWC,QAAQ,KAAKjB,GAAI,CAAEe,GAAI,EAAGG,WAAY,IAAKS,MAAO,gBAAkBvB,SAAA,CAAA,wBACxDrC,EAAc,KAAGG,EAAa,aAGrD2G,EAAe,CAAAC,UAAWC,EAAO9D,QAAQ,WACxCb,gBAAC4E,EACC,CAAA5E,SAAA,CAACE,EAAAA,IAAA2E,EAAA,CACC7E,gBAAC8E,EACC,CAAA9E,SAAA,GAAAE,IAAC6E,GAAU/E,SAAQ,eACnBE,IAAC6E,GAAU/E,SAAY,mBACvBE,IAAC6E,GAAU/E,SAAmB,0BAC9BE,IAAC6E,GAAU/E,SAAM,aACjBE,IAAC6E,GAAU/E,SAAO,iBAGtBE,EAAAA,IAAC8E,GACEhF,SAAe/C,EAAAgI,KAAKvG,YACnBuD,OAAAA,EAAAA,KAAC6C,EAA0B,CAAAI,OAAK,EAC9BlF,SAAA,CAAAE,EAAAA,IAAC6E,EACC,CAAA/E,SAAAC,EAAAC,IAACU,EAAW,CAAAC,QAAQ,QAAQjB,GAAI,CAAEkB,WAAY,KAC3Cd,SAAQtB,EAAAtB,iBAGZ8C,IAAA6E,EAAA,CACC/E,SAACiC,EAAAA,KAAAtC,EAAA,CAAIC,GAAI,CAAEwE,QAAS,OAAQvC,WAAY,SAAUsD,IAAK,GACrDnF,SAAA,GAAAE,IAACkF,EAAO,CAAAxF,GAAI,CAAEkD,MAAO,GAAIuC,OAAQ,IAC9BrF,SAAQtB,EAAAvB,KAAKmI,OAAO,KAEvBpF,EAAAA,IAACU,EAAW,CAAAC,QAAQ,QAAQjB,GAAI,CAAEkB,WAAY,KAC3Cd,SAAAtB,EAAQvB,cAIf+C,IAAC6E,GACC/E,WAACE,IAAAU,EAAA,CAAWC,QAAQ,QAASb,SAAAtB,EAAQpB,yBAEtCyH,EACC,CAAA/E,SAAAC,EAAAC,IAACqF,EAAA,CACCC,KAAMzG,EAAcZ,EAAWO,EAAQxB,KACvCwF,OAAO,OAAA+C,EAAAtH,EAAWO,EAAQxB,UAAK,EAAAuI,EAAAH,OAAO,GAAGI,gBAAgB,OAAAC,EAAWxH,EAAAO,EAAQxB,cAAK0I,MAAM,IACvFrE,MAAOzC,EAAeX,EAAWO,EAAQxB,KACzC2I,KAAK,kBAGRd,EACC,CAAA/E,SAAAiC,OAACmB,GAAMC,UAAU,MAAMzB,QAAS,EAC9B5B,SAAA,CAAAC,EAAAC,IAAC4F,EAAA,CACCD,KAAK,QACLpC,QAAS,IAAM9E,EAAuBD,EAAQxB,GAAI,WAClDqE,MAAkC,YAA3BpD,EAAWO,EAAQxB,IAAoB,UAAY,UAE1D8C,eAAChB,EAAY,CAAA,KAEfiB,EAAAC,IAAC4F,EAAA,CACCD,KAAK,QACLpC,QAAS,IAAM9E,EAAuBD,EAAQxB,GAAI,UAClDqE,MAAkC,WAA3BpD,EAAWO,EAAQxB,IAAmB,QAAU,UAEvD8C,eAACf,EAAO,CAAA,KAEVgB,EAAAC,IAAC4F,EAAA,CACCD,KAAK,QACLpC,QAAS,IAAM9E,EAAuBD,EAAQxB,GAAI,QAClDqE,MAAkC,SAA3BpD,EAAWO,EAAQxB,IAAiB,UAAY,UAEvD8C,eAACd,EAAS,CAAA,YAhDHR,EAAQxB,GAoDvB,kBAOd"}