/**
 * VidyaMitra Platform - End-to-End Test Suite
 * 
 * Comprehensive E2E testing for all user roles with Indian educational context
 * Tests complete user workflows from login to feature usage
 */

import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import App from '../App';
import { extendedStudentProfiles, schools, teachers, parents } from '../data/comprehensiveData';
import { mainNavigation, getNavigationByRole } from '../data/navigationData';

// Mock theme for testing
const mockTheme = createTheme();

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={mockTheme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock authentication context
const mockAuthContext = {
  isAuthenticated: true,
  user: { id: '1', name: 'Test User', role: 'teacher' },
  login: vi.fn(),
  logout: vi.fn()
};

// Helper function to simulate user login
const simulateLogin = async (role = 'teacher') => {
  const user = userEvent.setup();
  
  // Navigate to login page
  await user.click(screen.getByText(/login/i));
  
  // Fill login form
  await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
  await user.type(screen.getByLabelText(/password/i), 'password123');
  
  // Submit form
  await user.click(screen.getByRole('button', { name: /sign in/i }));
  
  // Wait for dashboard to load
  await waitFor(() => {
    expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
  });
};

describe('VidyaMitra E2E Test Suite', () => {
  beforeEach(() => {
    // Reset any global state
    localStorage.clear();
    vi.clearAllMocks();
  });

  describe('Principal User Workflow', () => {
    it('should complete full principal workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // 1. Login as Principal
      await simulateLogin('principal');

      // 2. Access Dashboard
      expect(screen.getByText(/analytics dashboard/i)).toBeInTheDocument();

      // 3. Navigate to Student Management
      await user.click(screen.getByText(/students/i));
      await waitFor(() => {
        expect(screen.getByText(/student management/i)).toBeInTheDocument();
      });

      // 4. View Student List with Indian Names
      expect(screen.getByText(/sanju kumar/i)).toBeInTheDocument();
      expect(screen.getByText(/mahesh reddy/i)).toBeInTheDocument();
      expect(screen.getByText(/ankitha patel/i)).toBeInTheDocument();

      // 5. Access Analytics
      await user.click(screen.getByText(/analytics/i));
      await waitFor(() => {
        expect(screen.getByText(/performance trends/i)).toBeInTheDocument();
      });

      // 6. Generate Reports
      await user.click(screen.getByText(/reports/i));
      await waitFor(() => {
        expect(screen.getByText(/report templates/i)).toBeInTheDocument();
      });

      // 7. Access Settings
      await user.click(screen.getByText(/settings/i));
      await waitFor(() => {
        expect(screen.getByText(/language preferences/i)).toBeInTheDocument();
      });
    });

    it('should validate Indian educational context for principal', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      await simulateLogin('principal');

      // Check for Indian educational boards
      await userEvent.setup().click(screen.getByText(/students/i));
      
      await waitFor(() => {
        expect(screen.getByText(/cbse/i)).toBeInTheDocument();
        expect(screen.getByText(/icse/i)).toBeInTheDocument();
        expect(screen.getByText(/state/i)).toBeInTheDocument();
      });

      // Check for Hyderabad schools
      expect(screen.getByText(/hyderabad/i)).toBeInTheDocument();
      expect(screen.getByText(/telangana/i)).toBeInTheDocument();
    });
  });

  describe('Teacher User Workflow', () => {
    it('should complete full teacher workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // 1. Login as Teacher
      await simulateLogin('teacher');

      // 2. Access Student Management
      await user.click(screen.getByText(/students/i));
      await waitFor(() => {
        expect(screen.getByText(/student management/i)).toBeInTheDocument();
      });

      // 3. View Individual Student Profile
      await user.click(screen.getByText(/sanju kumar/i));
      await waitFor(() => {
        expect(screen.getByText(/student profile/i)).toBeInTheDocument();
      });

      // 4. Conduct SWOT Analysis
      await user.click(screen.getByText(/swot analysis/i));
      await waitFor(() => {
        expect(screen.getByText(/strengths/i)).toBeInTheDocument();
        expect(screen.getByText(/weaknesses/i)).toBeInTheDocument();
        expect(screen.getByText(/opportunities/i)).toBeInTheDocument();
        expect(screen.getByText(/threats/i)).toBeInTheDocument();
      });

      // 5. Enter Grades
      await user.click(screen.getByText(/grades/i));
      await waitFor(() => {
        expect(screen.getByText(/grade entry/i)).toBeInTheDocument();
      });

      // 6. Mark Attendance
      await user.click(screen.getByText(/attendance/i));
      await waitFor(() => {
        expect(screen.getByText(/attendance management/i)).toBeInTheDocument();
      });
    });

    it('should validate SWOT analysis with Indian context', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      await simulateLogin('teacher');

      // Navigate to SWOT analysis
      await userEvent.setup().click(screen.getByText(/swot/i));
      
      await waitFor(() => {
        // Check for culturally relevant SWOT elements
        expect(screen.getByText(/guru-shishya tradition/i)).toBeInTheDocument();
        expect(screen.getByText(/family values/i)).toBeInTheDocument();
        expect(screen.getByText(/cultural awareness/i)).toBeInTheDocument();
      });
    });
  });

  describe('Student User Workflow', () => {
    it('should complete full student workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // 1. Login as Student
      await simulateLogin('student');

      // 2. View Personal Dashboard
      expect(screen.getByText(/student dashboard/i)).toBeInTheDocument();

      // 3. Check Academic Performance
      await user.click(screen.getByText(/performance/i));
      await waitFor(() => {
        expect(screen.getByText(/academic performance/i)).toBeInTheDocument();
      });

      // 4. View SWOT Analysis Results
      await user.click(screen.getByText(/swot/i));
      await waitFor(() => {
        expect(screen.getByText(/your strengths/i)).toBeInTheDocument();
      });

      // 5. Check Attendance Record
      await user.click(screen.getByText(/attendance/i));
      await waitFor(() => {
        expect(screen.getByText(/attendance record/i)).toBeInTheDocument();
      });
    });

    it('should display Indian academic calendar', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      await simulateLogin('student');

      // Check for Indian academic year structure (June to March)
      await waitFor(() => {
        expect(screen.getByText(/june/i)).toBeInTheDocument();
        expect(screen.getByText(/march/i)).toBeInTheDocument();
      });

      // Check for Indian festivals and holidays
      expect(screen.getByText(/diwali/i)).toBeInTheDocument();
      expect(screen.getByText(/independence day/i)).toBeInTheDocument();
    });
  });

  describe('Parent User Workflow', () => {
    it('should complete full parent workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // 1. Login as Parent
      await simulateLogin('parent');

      // 2. View Child's Dashboard
      expect(screen.getByText(/parent dashboard/i)).toBeInTheDocument();

      // 3. Check Child's Performance
      await user.click(screen.getByText(/performance/i));
      await waitFor(() => {
        expect(screen.getByText(/academic progress/i)).toBeInTheDocument();
      });

      // 4. View Reports
      await user.click(screen.getByText(/reports/i));
      await waitFor(() => {
        expect(screen.getByText(/progress reports/i)).toBeInTheDocument();
      });

      // 5. Communication with Teachers
      await user.click(screen.getByText(/communication/i));
      await waitFor(() => {
        expect(screen.getByText(/teacher communication/i)).toBeInTheDocument();
      });
    });

    it('should validate parent communication in Indian context', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      await simulateLogin('parent');

      // Check for Indian parent-teacher communication patterns
      await waitFor(() => {
        expect(screen.getByText(/parent-teacher meeting/i)).toBeInTheDocument();
        expect(screen.getByText(/progress discussion/i)).toBeInTheDocument();
      });
    });
  });

  describe('Cross-Role Feature Integration', () => {
    it('should validate navigation accessibility across roles', async () => {
      const roles = ['principal', 'teacher', 'student', 'parent'];
      
      for (const role of roles) {
        const navigation = getNavigationByRole(mainNavigation, role);
        expect(navigation.length).toBeGreaterThan(0);
        
        // Each role should have access to dashboard
        const dashboardAccess = navigation.find(item => item.id === 'dashboard');
        expect(dashboardAccess).toBeDefined();
      }
    });

    it('should validate Indian educational data consistency', async () => {
      // Check student data consistency
      extendedStudentProfiles.forEach(student => {
        expect(student.name).toBeDefined();
        expect(['cbse', 'icse', 'state', 'ib']).toContain(student.board);
        expect(student.languages).toContain('English');
        expect(student.region).toBeDefined();
      });

      // Check school data consistency
      schools.forEach(school => {
        expect(school.location).toContain('Hyderabad');
        expect(['cbse', 'icse', 'state', 'ib']).toContain(school.board);
      });
    });

    it('should validate responsive design across devices', async () => {
      // Test mobile viewport
      global.innerWidth = 375;
      global.innerHeight = 667;
      global.dispatchEvent(new Event('resize'));

      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      await simulateLogin('teacher');

      // Check if mobile navigation works
      const menuButton = screen.getByLabelText(/toggle sidebar/i);
      expect(menuButton).toBeInTheDocument();

      // Test tablet viewport
      global.innerWidth = 768;
      global.innerHeight = 1024;
      global.dispatchEvent(new Event('resize'));

      // Check if layout adapts
      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance and Accessibility', () => {
    it('should load components within performance thresholds', async () => {
      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      await simulateLogin('teacher');

      const endTime = performance.now();
      const loadTime = endTime - startTime;

      // Should load within 3 seconds
      expect(loadTime).toBeLessThan(3000);
    });

    it('should meet accessibility standards', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      await simulateLogin('teacher');

      // Check for proper ARIA labels
      expect(screen.getByRole('navigation')).toBeInTheDocument();
      expect(screen.getByRole('main')).toBeInTheDocument();

      // Check for keyboard navigation
      const firstButton = screen.getAllByRole('button')[0];
      firstButton.focus();
      expect(document.activeElement).toBe(firstButton);
    });
  });
});
