{"version": 3, "file": "fillPattern.js", "names": ["items", "_this", "opts", "for<PERSON>ach", "o", "i", "prototype", "validate", "val", "undefined", "name", "hasOwnProperty", "push", "TypeError", "join", "module", "exports"], "sources": ["../../../source/lib/types/fillPattern.js"], "sourcesContent": ["function items() {\n    this.opts = [//§18.18.55 ST_PatternType (Pattern Type)\n        'darkDown', \n        'darkGray', \n        'darkGrid', \n        'darkHorizontal', \n        'darkTrellis', \n        'darkUp', \n        'darkVerical', \n        'gray0625', \n        'gray125', \n        'lightDown', \n        'lightGray', \n        'lightGrid', \n        'lightHorizontal', \n        'lightTrellis', \n        'lightUp', \n        'lightVertical', \n        'mediumGray', \n        'none', \n        'solid'\n    ];\n    this.opts.forEach((o, i) => {\n        this[o] = i + 1;\n    });\n}\n\n\nitems.prototype.validate = function (val) {\n    if (this[val] === undefined) {\n        let opts = [];\n        for (let name in this) {\n            if (this.hasOwnProperty(name)) {\n                opts.push(name);\n            }\n        }\n        throw new TypeError('Invalid value for ST_PatternType; Value must be one of ' + this.opts.join(', '));\n    } else {\n        return true;\n    }\n};\n\nmodule.exports = new items();"], "mappings": ";;AAAA,SAASA,KAAKA,CAAA,EAAG;EAAA,IAAAC,KAAA;EACb,IAAI,CAACC,IAAI,GAAG;EAAC;EACT,UAAU,EACV,UAAU,EACV,UAAU,EACV,gBAAgB,EAChB,aAAa,EACb,QAAQ,EACR,aAAa,EACb,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,SAAS,EACT,eAAe,EACf,YAAY,EACZ,MAAM,EACN,OAAO,CACV;EACD,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACxBJ,KAAI,CAACG,CAAC,CAAC,GAAGC,CAAC,GAAG,CAAC;EACnB,CAAC,CAAC;AACN;AAGAL,KAAK,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EACtC,IAAI,IAAI,CAACA,GAAG,CAAC,KAAKC,SAAS,EAAE;IACzB,IAAIP,IAAI,GAAG,EAAE;IACb,KAAK,IAAIQ,IAAI,IAAI,IAAI,EAAE;MACnB,IAAI,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE;QAC3BR,IAAI,CAACU,IAAI,CAACF,IAAI,CAAC;MACnB;IACJ;IACA,MAAM,IAAIG,SAAS,CAAC,yDAAyD,GAAG,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,IAAI,CAAC,CAAC;EACzG,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,IAAIhB,KAAK,CAAC,CAAC"}