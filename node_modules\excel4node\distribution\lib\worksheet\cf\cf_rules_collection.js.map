{"version": 3, "file": "cf_rules_collection.js", "names": ["CfRule", "require", "CfRulesCollection", "_classCallCheck", "rulesBySqref", "_createClass", "key", "get", "Object", "keys", "length", "value", "add", "sqref", "ruleConfig", "rules", "newRule", "push", "addToXMLele", "ele", "_this", "for<PERSON>ach", "thisEle", "att", "rule", "up", "module", "exports"], "sources": ["../../../../source/lib/worksheet/cf/cf_rules_collection.js"], "sourcesContent": ["const CfRule = require('./cf_rule');\n\n// -----------------------------------------------------------------------------\n\nclass CfRulesCollection { // §18.3.1.18 conditionalFormatting (Conditional Formatting)\n    constructor() {\n        // rules are indexed by cell refs\n        this.rulesBySqref = {};\n    }\n\n    get count() {\n        return Object.keys(this.rulesBySqref).length;\n    }\n\n    add(sqref, ruleConfig) {\n        let rules = this.rulesBySqref[sqref] || [];\n        let newRule = new CfRule(ruleConfig);\n        rules.push(newRule);\n        this.rulesBySqref[sqref] = rules;\n        return this;\n    }\n\n    addToXMLele(ele) {\n        Object.keys(this.rulesBySqref).forEach((sqref) => {\n            let thisEle = ele.ele('conditionalFormatting').att('sqref', sqref);\n            this.rulesBySqref[sqref].forEach((rule) => {\n                rule.addToXMLele(thisEle);\n            });\n            thisEle.up();\n        });\n    }\n}\n\n\nmodule.exports = CfRulesCollection;"], "mappings": ";;;;;AAAA,IAAMA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;;AAEnC;AAAA,IAEMC,iBAAiB;EAAG;EACtB,SAAAA,kBAAA,EAAc;IAAAC,eAAA,OAAAD,iBAAA;IACV;IACA,IAAI,CAACE,YAAY,GAAG,CAAC,CAAC;EAC1B;EAACC,YAAA,CAAAH,iBAAA;IAAAI,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAY;MACR,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACL,YAAY,CAAC,CAACM,MAAM;IAChD;EAAC;IAAAJ,GAAA;IAAAK,KAAA,EAED,SAAAC,IAAIC,KAAK,EAAEC,UAAU,EAAE;MACnB,IAAIC,KAAK,GAAG,IAAI,CAACX,YAAY,CAACS,KAAK,CAAC,IAAI,EAAE;MAC1C,IAAIG,OAAO,GAAG,IAAIhB,MAAM,CAACc,UAAU,CAAC;MACpCC,KAAK,CAACE,IAAI,CAACD,OAAO,CAAC;MACnB,IAAI,CAACZ,YAAY,CAACS,KAAK,CAAC,GAAGE,KAAK;MAChC,OAAO,IAAI;IACf;EAAC;IAAAT,GAAA;IAAAK,KAAA,EAED,SAAAO,YAAYC,GAAG,EAAE;MAAA,IAAAC,KAAA;MACbZ,MAAM,CAACC,IAAI,CAAC,IAAI,CAACL,YAAY,CAAC,CAACiB,OAAO,CAAC,UAACR,KAAK,EAAK;QAC9C,IAAIS,OAAO,GAAGH,GAAG,CAACA,GAAG,CAAC,uBAAuB,CAAC,CAACI,GAAG,CAAC,OAAO,EAAEV,KAAK,CAAC;QAClEO,KAAI,CAAChB,YAAY,CAACS,KAAK,CAAC,CAACQ,OAAO,CAAC,UAACG,IAAI,EAAK;UACvCA,IAAI,CAACN,WAAW,CAACI,OAAO,CAAC;QAC7B,CAAC,CAAC;QACFA,OAAO,CAACG,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC;IACN;EAAC;EAAA,OAAAvB,iBAAA;AAAA;AAILwB,MAAM,CAACC,OAAO,GAAGzB,iBAAiB"}