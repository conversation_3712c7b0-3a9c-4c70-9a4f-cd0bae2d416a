{"version": 3, "file": "drawing.js", "names": ["CTMarker", "require", "Point", "EMU", "Drawing", "_classCallCheck", "_anchorType", "_anchorFrom", "_anchorTo", "_editAs", "_position", "_createClass", "key", "get", "set", "type", "types", "indexOf", "TypeError", "join", "val", "obj", "undefined", "Object", "col", "co<PERSON><PERSON><PERSON>", "row", "<PERSON><PERSON><PERSON>", "value", "anchor", "from", "to", "editAs", "anchorType", "anchorFrom", "anchorTo", "position", "cx", "cy", "thisCx", "thisCy", "module", "exports"], "sources": ["../../../source/lib/drawing/drawing.js"], "sourcesContent": ["const CTMarker = require('../classes/ctMarker.js');\nconst Point = require('../classes/point.js');\nconst EMU = require('../classes/emu.js');\n\nclass Drawing {\n    /**\n     * Element representing an Excel Drawing superclass\n     * @property {String} anchorType Proprty for type of anchor. One of 'absoluteAnchor', 'oneCellAnchor', 'twoCellAnchor'\n     * @property {CTMarker} anchorFrom Property for the top left corner position of drawing\n     * @property {CTMarker} anchorTo Property for the bottom left corner position of drawing\n     * @property {String} editAs Property that states how to interact with the Drawing in Excel. One of 'absolute', 'oneCell', 'twoCell'\n     * @property {Point} _position Internal property for position on Excel Worksheet when drawing type is absoluteAnchor\n     * @returns {Drawing} Excel Drawing \n     */\n    constructor() {\n        this._anchorType = null;\n        this._anchorFrom = null;\n        this._anchorTo = null;\n        this._editAs = null;\n        this._position = null;\n    }\n\n    get anchorType() {\n        return this._anchorType;\n    }\n    set anchorType(type) {\n        let types = ['absoluteAnchor', 'oneCellAnchor', 'twoCellAnchor'];\n        if (types.indexOf(type) < 0) {\n            throw new TypeError('Invalid option for anchor type. anchorType must be one of ' + types.join(', '));\n        }\n        this._anchorType = type;\n    }\n\n    get editAs() {\n        return this._editAs;\n    }\n    set editAs(val) {\n        let types = ['absolute', 'oneCell', 'twoCell'];\n        if (types.indexOf(val) < 0) {\n            throw new TypeError('Invalid option for editAs. editAs must be one of ' + types.join(', '));\n        }\n        this._editAs = val;\n    }\n\n    get anchorFrom() {\n        return this._anchorFrom;\n    }\n    set anchorFrom(obj) {\n        if (obj !== undefined && obj instanceof Object) {\n            this._anchorFrom = new CTMarker(obj.col - 1, obj.colOff, obj.row - 1, obj.rowOff);\n        }\n    }\n\n    get anchorTo() {\n        return this._anchorTo;\n    }\n    set anchorTo(obj) {\n        if (obj !== undefined && obj instanceof Object) {\n            this._anchorTo = new CTMarker(obj.col - 1, obj.colOff, obj.row - 1, obj.rowOff);\n        }\n    }\n\n    /**\n     * @alias Drawing.achor\n     * @desc Sets the postion and anchor properties of the Drawing\n     * @func Drawing.achor\n     * @param {String} type Anchor type of drawing\n     * @param {Object} from Properties for achorFrom property\n     * @param {Number} from.col Left edge of drawing will align with left edge of this column\n     * @param {String} from.colOff Offset. Drawing will be shifted to the right the specified amount. Float followed by measure [0-9]+(\\.[0-9]+)?(mm|cm|in|pt|pc|pi). i.e '10.5mm'\n     * @param {Number} from.row Top edge of drawing will align with top edge of this row\n     * @param {String} from.rowOff Offset. Drawing will be shifted down the specified amount. Float followed by measure [0-9]+(\\.[0-9]+)?(mm|cm|in|pt|pc|pi). i.e '10.5mm'\n     * @param {Object} to Properties for anchorTo property\n     * @param {Number} to.col Left edge of drawing will align with left edge of this column\n     * @param {String} to.colOff Offset. Drawing will be shifted to the right the specified amount. Float followed by measure [0-9]+(\\.[0-9]+)?(mm|cm|in|pt|pc|pi). i.e '10.5mm'\n     * @param {Number} to.row Top edge of drawing will align with top edge of this row\n     * @param {String} to.rowOff Offset. Drawing will be shifted down the specified amount. Float followed by measure [0-9]+(\\.[0-9]+)?(mm|cm|in|pt|pc|pi). i.e '10.5mm'\n     * @returns {Drawing} Excel Drawing with attached methods\n     */\n    anchor(type, from, to) {\n        if (type === 'twoCellAnchor') {\n            if (from === undefined || to === undefined) {\n                throw new TypeError('twoCellAnchor requires both from and two markers');\n            }\n            this.editAs = 'oneCell';\n        }\n        this.anchorType = type;\n        this.anchorFrom = from;\n        this.anchorTo = to;\n        return this;\n    }\n\n    /**\n     * @alias Drawing.position\n     * @desc The position of the top left corner of the image on the Worksheet\n     * @func Drawing.position\n     * @param {ST_PositiveUniversalMeasure} cx Postion from left of Worksheet edge\n     * @param {ST_PositiveUniversalMeasure} cy Postion from top of Worksheet edge\n     */\n    position(cx, cy) {\n        this.anchorType = 'absoluteAnchor';\n        let thisCx = new EMU(cx);\n        let thisCy = new EMU(cy);\n        this._position = new Point(thisCx.value, thisCy.value);\n    }\n}\n\nmodule.exports = Drawing;"], "mappings": ";;;;;AAAA,IAAMA,QAAQ,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAClD,IAAMC,KAAK,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAC5C,IAAME,GAAG,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AAAC,IAEnCG,OAAO;EACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,QAAA,EAAc;IAAAC,eAAA,OAAAD,OAAA;IACV,IAAI,CAACE,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;EACzB;EAACC,YAAA,CAAAP,OAAA;IAAAQ,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAiB;MACb,OAAO,IAAI,CAACP,WAAW;IAC3B,CAAC;IAAAQ,GAAA,EACD,SAAAA,IAAeC,IAAI,EAAE;MACjB,IAAIC,KAAK,GAAG,CAAC,gBAAgB,EAAE,eAAe,EAAE,eAAe,CAAC;MAChE,IAAIA,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAG,CAAC,EAAE;QACzB,MAAM,IAAIG,SAAS,CAAC,4DAA4D,GAAGF,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;MACxG;MACA,IAAI,CAACb,WAAW,GAAGS,IAAI;IAC3B;EAAC;IAAAH,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAa;MACT,OAAO,IAAI,CAACJ,OAAO;IACvB,CAAC;IAAAK,GAAA,EACD,SAAAA,IAAWM,GAAG,EAAE;MACZ,IAAIJ,KAAK,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC;MAC9C,IAAIA,KAAK,CAACC,OAAO,CAACG,GAAG,CAAC,GAAG,CAAC,EAAE;QACxB,MAAM,IAAIF,SAAS,CAAC,mDAAmD,GAAGF,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/F;MACA,IAAI,CAACV,OAAO,GAAGW,GAAG;IACtB;EAAC;IAAAR,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAiB;MACb,OAAO,IAAI,CAACN,WAAW;IAC3B,CAAC;IAAAO,GAAA,EACD,SAAAA,IAAeO,GAAG,EAAE;MAChB,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,YAAYE,MAAM,EAAE;QAC5C,IAAI,CAAChB,WAAW,GAAG,IAAIP,QAAQ,CAACqB,GAAG,CAACG,GAAG,GAAG,CAAC,EAAEH,GAAG,CAACI,MAAM,EAAEJ,GAAG,CAACK,GAAG,GAAG,CAAC,EAAEL,GAAG,CAACM,MAAM,CAAC;MACrF;IACJ;EAAC;IAAAf,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAe;MACX,OAAO,IAAI,CAACL,SAAS;IACzB,CAAC;IAAAM,GAAA,EACD,SAAAA,IAAaO,GAAG,EAAE;MACd,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,YAAYE,MAAM,EAAE;QAC5C,IAAI,CAACf,SAAS,GAAG,IAAIR,QAAQ,CAACqB,GAAG,CAACG,GAAG,GAAG,CAAC,EAAEH,GAAG,CAACI,MAAM,EAAEJ,GAAG,CAACK,GAAG,GAAG,CAAC,EAAEL,GAAG,CAACM,MAAM,CAAC;MACnF;IACJ;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAhBI;IAAAf,GAAA;IAAAgB,KAAA,EAiBA,SAAAC,OAAOd,IAAI,EAAEe,IAAI,EAAEC,EAAE,EAAE;MACnB,IAAIhB,IAAI,KAAK,eAAe,EAAE;QAC1B,IAAIe,IAAI,KAAKR,SAAS,IAAIS,EAAE,KAAKT,SAAS,EAAE;UACxC,MAAM,IAAIJ,SAAS,CAAC,kDAAkD,CAAC;QAC3E;QACA,IAAI,CAACc,MAAM,GAAG,SAAS;MAC3B;MACA,IAAI,CAACC,UAAU,GAAGlB,IAAI;MACtB,IAAI,CAACmB,UAAU,GAAGJ,IAAI;MACtB,IAAI,CAACK,QAAQ,GAAGJ,EAAE;MAClB,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAnB,GAAA;IAAAgB,KAAA,EAOA,SAAAQ,SAASC,EAAE,EAAEC,EAAE,EAAE;MACb,IAAI,CAACL,UAAU,GAAG,gBAAgB;MAClC,IAAIM,MAAM,GAAG,IAAIpC,GAAG,CAACkC,EAAE,CAAC;MACxB,IAAIG,MAAM,GAAG,IAAIrC,GAAG,CAACmC,EAAE,CAAC;MACxB,IAAI,CAAC5B,SAAS,GAAG,IAAIR,KAAK,CAACqC,MAAM,CAACX,KAAK,EAAEY,MAAM,CAACZ,KAAK,CAAC;IAC1D;EAAC;EAAA,OAAAxB,OAAA;AAAA;AAGLqC,MAAM,CAACC,OAAO,GAAGtC,OAAO"}