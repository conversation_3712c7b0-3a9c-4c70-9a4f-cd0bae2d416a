const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/StudentRegistration--Cq_MLtD.js","assets/mui-core-BBO2DoRL.js","assets/vendor-CeOqOr8o.js","assets/utils-misc-NFmYzBmQ.js","assets/routing-B6PnZiBG.js","assets/i18n-DWU17bW_.js","assets/animation-BJm6nf7i.js","assets/mui-icons-BXhTkfAe.js","assets/StudentProfile-BgI_raih.js","assets/charts-chartjs-Dl1vZNhv.js","assets/AttendanceManagement-Cuapupr3.js","assets/GradeEntry-C2wSA142.js","assets/TeacherDashboard-D8R1Yo_8.js","assets/SWOTWizard-cbD3CjwT.js","assets/ReportGeneration-WXxmrtL8.js"])))=>i.map(i=>d[i]);
import{a as e,j as t,T as a,C as i,u as n,b as r,d as s,e as o,f as l,B as d,A as c,g as x,h,I as p,L as m,i as u,k as g,S as j,G as b,l as f,m as y,n as v,o as S,p as C,q as w,r as F,D as k,s as A,t as W,v as I,F as M,w as E,x as T,M as B,y as P,z,E as D,H as R,J as O,K as L,N as $,O as H,P as N,Q as V,R as _,U as G,V as U,W as Y,Z as q,X as J,Y as K,_ as X,$ as Q}from"./mui-core-BBO2DoRL.js";import{c as Z,r as ee,a as te}from"./vendor-CeOqOr8o.js";import{_ as ae}from"./utils-misc-NFmYzBmQ.js";import{u as ie,a as ne,B as re,R as se,b as oe,N as le,O as de,L as ce}from"./routing-B6PnZiBG.js";import{u as xe,i as he,B as pe,a as me}from"./i18n-DWU17bW_.js";import{m as ue,A as ge}from"./animation-BJm6nf7i.js";import{M as je,A as be,a as fe,P as ye,B as ve,S as Se,b as Ce,c as we,F as Fe,N as ke,d as Ae,T as We,C as Ie,R as Me,e as Ee,L as Te,D as Be,f as Pe,G as ze,g as De,h as Re,E as Oe,V as Le,i as $e,j as He,k as Ne,l as Ve,m as _e,n as Ge,o as Ue,p as Ye,q as qe,W as Je,r as Ke,s as Xe,t as Qe,u as Ze,v as et,w as tt,x as at,y as it,z as nt,H as rt,I as st,J as ot,K as lt,O as dt,Q as ct,U as xt,X as ht,Y as pt,Z as mt,_ as ut,$ as gt,a0 as jt,a1 as bt,a2 as ft}from"./mui-icons-BXhTkfAe.js";import{C as yt,a as vt,L as St,P as Ct,b as wt,B as Ft,p as kt,c as At,d as Wt,A as It,e as Mt,f as Et,D as Tt,R as Bt,i as Pt,g as zt}from"./charts-chartjs-Dl1vZNhv.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const a of e)if("childList"===a.type)for(const e of a.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var Dt={},Rt=Z;Dt.createRoot=Rt.createRoot,Dt.hydrateRoot=Rt.hydrateRoot;const Ot=ee.createContext(),Lt=({children:n})=>{const[r,s]=ee.useState((()=>localStorage.getItem("vidyamitra-theme-mode")||"light")),o=((t="light")=>{const a="light"===t;return e({palette:{mode:t,primary:{50:a?"#E8F4FD":"#0F172A",100:a?"#C5E4FA":"#1E293B",200:a?"#9FD3F7":"#334155",300:a?"#79C2F4":"#475569",400:a?"#5CB5F1":"#64748B",500:a?"#2E5BA8":"#4A90E2",600:a?"#1E4A97":"#6BA3E8",700:a?"#1A4086":"#8BB8ED",800:a?"#163675":"#ABCDF2",900:a?"#0F2654":"#CBE2F7",main:a?"#2E5BA8":"#4A90E2",light:a?"#5CB5F1":"#6BA3E8",dark:a?"#1A4086":"#2E5BA8",contrastText:"#FFFFFF"},secondary:{50:a?"#FFF8E1":"#1A0F00",100:a?"#FFECB3":"#331F00",200:a?"#FFE082":"#4D2E00",300:a?"#FFD54F":"#663D00",400:a?"#FFCA28":"#804D00",500:a?"#FF9933":"#FFB366",600:a?"#FFB300":"#FFCC80",700:a?"#FF8F00":"#FFD699",800:a?"#FF6F00":"#FFE0B3",900:a?"#E65100":"#FFEBCC",main:a?"#FF9933":"#FFB366",light:a?"#FFCA28":"#FFCC80",dark:a?"#FF8F00":"#FF9933",contrastText:a?"#000000":"#FFFFFF"},success:{main:a?"#00C853":"#4CAF50",light:a?"#4CAF50":"#81C784",dark:a?"#00A046":"#388E3C",contrastText:"#FFFFFF"},warning:{main:a?"#FF9800":"#FFB74D",light:a?"#FFB74D":"#FFCC80",dark:a?"#F57C00":"#FF9800",contrastText:a?"#000000":"#FFFFFF"},error:{main:a?"#F44336":"#EF5350",light:a?"#EF5350":"#E57373",dark:a?"#D32F2F":"#C62828",contrastText:"#FFFFFF"},info:{main:a?"#2196F3":"#42A5F5",light:a?"#42A5F5":"#64B5F6",dark:a?"#1976D2":"#1565C0",contrastText:"#FFFFFF"},background:{default:a?"#F8FAFC":"#0F172A",paper:a?"#FFFFFF":"#1E293B",surface:a?"#F1F5F9":"#334155",elevated:a?"#FFFFFF":"#475569"},text:{primary:a?"#1E293B":"#F1F5F9",secondary:a?"#64748B":"#94A3B8",disabled:a?"#CBD5E1":"#475569"},divider:a?"#E2E8F0":"#334155",glass:{primary:a?"rgba(255, 255, 255, 0.25)":"rgba(255, 255, 255, 0.05)",secondary:a?"rgba(46, 91, 168, 0.1)":"rgba(74, 144, 226, 0.1)",backdrop:a?"rgba(255, 255, 255, 0.8)":"rgba(15, 23, 42, 0.8)",surface:a?"rgba(248, 250, 252, 0.8)":"rgba(30, 41, 59, 0.8)"},gradients:{primary:a?"linear-gradient(135deg, #2E5BA8 0%, #4A90E2 100%)":"linear-gradient(135deg, #4A90E2 0%, #6BA3E8 100%)",secondary:a?"linear-gradient(135deg, #FF9933 0%, #FFB366 100%)":"linear-gradient(135deg, #FFB366 0%, #FFCC80 100%)",success:a?"linear-gradient(135deg, #00C853 0%, #4CAF50 100%)":"linear-gradient(135deg, #4CAF50 0%, #81C784 100%)",surface:a?"linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%)":"linear-gradient(135deg, #1E293B 0%, #334155 100%)",glass:a?"linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)":"linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)"},board:{CBSE:a?"#2E5BA8":"#4A90E2",ICSE:a?"#FF9933":"#FFB366",STATE:a?"#00C853":"#4CAF50",IB:a?"#9C27B0":"#BA68C8"}},typography:{fontFamily:["Inter","Roboto","Noto Sans","-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Arial","sans-serif"].join(","),h1:{fontSize:"clamp(2.5rem, 2rem + 2vw, 4rem)",fontWeight:600,lineHeight:1.1,letterSpacing:"-0.025em",color:a?"#1E293B":"#F1F5F9"},h2:{fontSize:"clamp(2rem, 1.7rem + 1.5vw, 3rem)",fontWeight:500,lineHeight:1.2,letterSpacing:"-0.025em",color:a?"#1E293B":"#F1F5F9"},h3:{fontSize:"clamp(1.75rem, 1.5rem + 1.25vw, 2.5rem)",fontWeight:500,lineHeight:1.25,color:a?"#1E293B":"#F1F5F9"},h4:{fontSize:"clamp(1.5rem, 1.3rem + 1vw, 2rem)",fontWeight:500,lineHeight:1.3,color:a?"#1E293B":"#F1F5F9"},h5:{fontSize:"clamp(1.25rem, 1.1rem + 0.75vw, 1.75rem)",fontWeight:500,lineHeight:1.35,color:a?"#1E293B":"#F1F5F9"},h6:{fontSize:"clamp(1.125rem, 1rem + 0.625vw, 1.5rem)",fontWeight:500,lineHeight:1.4,color:a?"#1E293B":"#F1F5F9"},body1:{fontSize:"1rem",lineHeight:1.6,fontWeight:400,color:a?"#334155":"#CBD5E1"},body2:{fontSize:"0.875rem",lineHeight:1.5,fontWeight:400,color:a?"#475569":"#94A3B8"},button:{fontSize:"0.875rem",fontWeight:500,textTransform:"none",letterSpacing:"0.025em"}},spacing:4,shape:{borderRadius:16,modernRadius:20,glassRadius:24},shadows:["none",a?"0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.08)":"0px 1px 3px rgba(0, 0, 0, 0.24), 0px 1px 2px rgba(0, 0, 0, 0.16)",a?"0px 3px 6px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.08)":"0px 3px 6px rgba(0, 0, 0, 0.32), 0px 2px 4px rgba(0, 0, 0, 0.16)",a?"0px 6px 12px rgba(0, 0, 0, 0.16), 0px 4px 8px rgba(0, 0, 0, 0.08)":"0px 6px 12px rgba(0, 0, 0, 0.32), 0px 4px 8px rgba(0, 0, 0, 0.16)",a?"0px 8px 16px rgba(0, 0, 0, 0.16), 0px 6px 12px rgba(0, 0, 0, 0.08)":"0px 8px 16px rgba(0, 0, 0, 0.32), 0px 6px 12px rgba(0, 0, 0, 0.16)",a?"0px 12px 24px rgba(0, 0, 0, 0.16), 0px 8px 16px rgba(0, 0, 0, 0.08)":"0px 12px 24px rgba(0, 0, 0, 0.32), 0px 8px 16px rgba(0, 0, 0, 0.16)",a?"0px 8px 32px rgba(46, 91, 168, 0.15), 0px 1px 0px rgba(255, 255, 255, 0.05) inset":"0px 8px 32px rgba(0, 0, 0, 0.4), 0px 1px 0px rgba(255, 255, 255, 0.1) inset"],transitions:{easing:{easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)",modern:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",bounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)"},duration:{shortest:150,shorter:200,short:250,standard:300,complex:375,modern:400,enteringScreen:225,leavingScreen:195}},components:{MuiCard:{styleOverrides:{root:{borderRadius:20,border:a?"1px solid rgba(226, 232, 240, 0.8)":"1px solid rgba(51, 65, 85, 0.8)",backdropFilter:"blur(20px)",background:a?"rgba(255, 255, 255, 0.8)":"rgba(30, 41, 59, 0.8)",transition:"all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)","&:hover":{transform:"translateY(-4px)",boxShadow:a?"0px 20px 40px rgba(46, 91, 168, 0.15)":"0px 20px 40px rgba(0, 0, 0, 0.4)"}}}},MuiButton:{styleOverrides:{root:{borderRadius:12,textTransform:"none",fontWeight:600,padding:"12px 24px",transition:"all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)","&:hover":{transform:"translateY(-2px)"}},contained:{boxShadow:"0px 4px 12px rgba(46, 91, 168, 0.3)","&:hover":{boxShadow:"0px 8px 24px rgba(46, 91, 168, 0.4)"}}}},MuiChip:{styleOverrides:{root:{borderRadius:12,fontWeight:500,backdropFilter:"blur(10px)"}}}}})})(r),l=e=>{"light"!==e&&"dark"!==e||(s(e),localStorage.setItem("vidyamitra-theme-mode",e))};ee.useEffect((()=>{const e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{localStorage.getItem("vidyamitra-theme-mode")||s(e.matches?"dark":"light")};return e.addEventListener("change",t),()=>{e.removeEventListener("change",t)}}),[]);const d={mode:r,theme:o,toggleMode:()=>{const e="light"===r?"dark":"light";s(e),localStorage.setItem("vidyamitra-theme-mode",e)},setThemeMode:l,setSystemMode:()=>{const e=window.matchMedia("(prefers-color-scheme: dark)").matches;l(e?"dark":"light")},isDark:"dark"===r,isLight:"light"===r};return t.jsx(Ot.Provider,{value:d,children:t.jsxs(a,{theme:o,children:[t.jsx(i,{}),n]})})},$t=({value:e,duration:a=2e3,suffix:i="",prefix:n=""})=>{const[r,s]=ee.useState(0);return ee.useEffect((()=>{let t,i;const n=r=>{t||(t=r);const o=Math.min((r-t)/a,1),l=1-Math.pow(1-o,4),d=Math.floor(l*e);s(d),o<1&&(i=requestAnimationFrame(n))};return i=requestAnimationFrame(n),()=>{i&&cancelAnimationFrame(i)}}),[e,a]),t.jsxs("span",{children:[n,r.toLocaleString(),i]})},Ht=({value:e,color:a="primary",delay:i=0})=>{const[n,r]=ee.useState(0);return ee.useEffect((()=>{const t=setTimeout((()=>{r(e)}),i);return()=>clearTimeout(t)}),[e,i]),t.jsx(m,{variant:"determinate",value:n,color:a,sx:{height:8,borderRadius:4,backgroundColor:o("#000",.1),"& .MuiLinearProgress-bar":{borderRadius:4,transition:"transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)"}}})},Nt=({trend:e,value:a,label:i})=>{const r=n(),s="up"===e;return t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:.5,px:1,py:.5,borderRadius:1,backgroundColor:o(s?r.palette.success.main:r.palette.error.main,.1)},children:[s?t.jsx(be,{sx:{fontSize:16,color:r.palette.success.main}}):t.jsx(fe,{sx:{fontSize:16,color:r.palette.error.main}}),t.jsxs(h,{variant:"caption",sx:{fontWeight:600,color:s?r.palette.success.main:r.palette.error.main},children:[a,"%"]}),t.jsx(h,{variant:"caption",color:"text.secondary",children:i})]})},Vt=({title:e,value:a,subtitle:i,icon:m,trend:u,trendValue:g,trendLabel:j="vs last month",progress:b,progressLabel:f,color:y="primary",variant:v="default",onClick:S,loading:C=!1,actionIcon:w,onActionClick:F,tooltip:k,gradient:A=!1,glassmorphism:W=!0})=>{var I;const M=n(),[E,T]=ee.useState(!1),B=()=>A?M.palette.gradients[y]||M.palette.gradients.primary:W?o(M.palette.background.paper,.8):M.palette.background.paper,P=()=>A?"#FFFFFF":M.palette.text.primary;return t.jsx(r,{title:k,arrow:!0,placement:"top",children:t.jsx(ue.div,{variants:{initial:{scale:1,y:0,boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)"},hover:{scale:1.02,y:-8,boxShadow:"0px 20px 40px rgba(46, 91, 168, 0.15)",transition:{type:"spring",stiffness:300,damping:20}}},initial:"initial",whileHover:"hover",onHoverStart:()=>T(!0),onHoverEnd:()=>T(!1),children:t.jsx(s,{onClick:S,sx:{height:"100%",cursor:S?"pointer":"default",borderRadius:3,background:B(),backdropFilter:W?"blur(20px)":"none",border:W?`1px solid ${o(M.palette.primary.main,.1)}`:"none",overflow:"hidden",position:"relative","&::before":A?{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:B(),zIndex:-1}:{}},children:t.jsxs(l,{sx:{p:3,height:"100%"},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"flex-start",justifyContent:"space-between",mb:2},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2},children:[t.jsx(ue.div,{variants:{initial:{rotate:0,scale:1},hover:{rotate:5,scale:1.1,transition:{type:"spring",stiffness:400,damping:10}}},children:t.jsx(c,{sx:{bgcolor:A?o("#FFFFFF",.2):o((null==(I=M.palette[y])?void 0:I.main)||M.palette.primary.main,.1),color:A?"#FFFFFF":(null==(z=M.palette[y])?void 0:z.main)||M.palette.primary.main,width:56,height:56},children:C?t.jsx(x,{size:24,color:"inherit"}):t.jsx(m,{sx:{fontSize:28}})})}),t.jsxs(d,{children:[t.jsx(h,{variant:"body2",sx:{color:A?o("#FFFFFF",.8):M.palette.text.secondary,fontWeight:500,mb:.5},children:e}),i&&t.jsx(h,{variant:"caption",sx:{color:A?o("#FFFFFF",.6):M.palette.text.disabled},children:i})]})]}),(w||F)&&t.jsx(p,{size:"small",onClick:F,sx:{color:A?o("#FFFFFF",.8):M.palette.text.secondary},children:w||t.jsx(je,{})})]}),t.jsxs(d,{sx:{mb:2},children:[t.jsx(h,{variant:"h3",sx:{fontWeight:800,color:P(),lineHeight:1,mb:1},children:C?t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[t.jsx(x,{size:20,color:"inherit"}),t.jsx("span",{children:"--"})]}):"number"==typeof a?t.jsx($t,{value:a}):a}),u&&g&&t.jsx(Nt,{trend:u,value:g,label:j})]}),void 0!==b&&t.jsxs(d,{sx:{mb:1},children:[t.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[t.jsx(h,{variant:"caption",sx:{color:A?o("#FFFFFF",.8):M.palette.text.secondary,fontWeight:500},children:f||"Progress"}),t.jsxs(h,{variant:"caption",sx:{color:P(),fontWeight:600},children:[b,"%"]})]}),t.jsx(Ht,{value:b,color:y,delay:500})]}),t.jsx(ue.div,{initial:{opacity:0,height:0},animate:{opacity:E?1:0,height:E?"auto":0},transition:{duration:.3},children:t.jsx(d,{sx:{mt:2,pt:2,borderTop:`1px solid ${o(P(),.1)}`},children:t.jsx(h,{variant:"caption",sx:{color:A?o("#FFFFFF",.6):M.palette.text.disabled},children:"Click for detailed view"})})})]})})})});var z},_t={cbse:["Mathematics","Science","English","Hindi","Social Studies","Computer Science"],icse:["Mathematics","Physics","Chemistry","Biology","English","Hindi","History","Geography"],state:["Mathematics","Science","English","Telugu/Tamil/Regional","Social Studies","Environmental Science"],ib:["Mathematics","Sciences","English","Hindi","Individuals & Societies","Arts"]},Gt=(e,t)=>{const a=_t[e]||_t.cbse,i=t.academicLevel;return a.map((e=>({subject:e,currentScore:Math.max(35,Math.min(100,i+20*(Math.random()-.5))),previousScore:Math.max(30,Math.min(95,i+25*(Math.random()-.5))),trend:Math.random()>.6?"improving":Math.random()>.3?"stable":"declining",assignments:Math.floor(10*Math.random())+15,assignmentsCompleted:Math.floor(5*Math.random())+12})))},Ut=()=>{const e=["Late to class","Incomplete homework","Disruptive behavior","Excellent participation","Helped classmates","Leadership in group work","Creative thinking","Respectful behavior"],t=Math.floor(6*Math.random())+2;return Array.from({length:t},(()=>({date:new Date(2024,Math.floor(12*Math.random()),Math.floor(28*Math.random())+1),type:e[Math.floor(Math.random()*e.length)],severity:Math.random()>.7?"high":Math.random()>.4?"medium":"low",description:"Detailed incident description would be here"})))},Yt=()=>{const e=["Cricket","Football","Basketball","Badminton","Chess","Debate Club","Science Club","Art Club","Music","Dance","Drama","Robotics","Environmental Club","Literary Society","Mathematics Olympiad"],t=Math.floor(4*Math.random())+1;return Array.from({length:t},(()=>({activity:e[Math.floor(Math.random()*e.length)],level:Math.random()>.6?"advanced":Math.random()>.3?"intermediate":"beginner",achievements:Math.random()>.7?["District Level Winner"]:Math.random()>.4?["School Level Participant"]:[]})))},qt=[{id:"STU001",name:"Sanju Kumar",grade:"10th",section:"A",board:"cbse",rollNumber:"CBSE2024001",academicLevel:85,region:"Telangana",parentContact:"+91 9876543210",email:"<EMAIL>",dateOfBirth:"2009-03-15",address:"Hyderabad, Telangana"},{id:"STU002",name:"Niraimathi Selvam",grade:"9th",section:"B",board:"state",rollNumber:"TN2024002",academicLevel:78,region:"Tamil Nadu",parentContact:"+91 9876543211",email:"<EMAIL>",dateOfBirth:"2010-07-22",address:"Chennai, Tamil Nadu"},{id:"STU003",name:"Mahesh Reddy",grade:"11th",section:"A",board:"cbse",rollNumber:"CBSE2024003",academicLevel:92,region:"Andhra Pradesh",parentContact:"+91 9876543212",email:"<EMAIL>",dateOfBirth:"2008-11-08",address:"Vijayawada, Andhra Pradesh"},{id:"STU004",name:"Ravi Teja Sharma",grade:"10th",section:"C",board:"icse",rollNumber:"ICSE2024004",academicLevel:88,region:"Karnataka",parentContact:"+91 9876543213",email:"<EMAIL>",dateOfBirth:"2009-01-30",address:"Bangalore, Karnataka"},{id:"STU005",name:"Ankitha Patel",grade:"12th",section:"A",board:"cbse",rollNumber:"CBSE2024005",academicLevel:95,region:"Gujarat",parentContact:"+91 9876543214",email:"<EMAIL>",dateOfBirth:"2007-05-12",address:"Ahmedabad, Gujarat"},{id:"STU006",name:"Sirisha Nair",grade:"9th",section:"B",board:"state",rollNumber:"KL2024006",academicLevel:82,region:"Kerala",parentContact:"+91 9876543215",email:"<EMAIL>",dateOfBirth:"2010-09-18",address:"Kochi, Kerala"},{id:"STU007",name:"Priya Agarwal",grade:"11th",section:"B",board:"cbse",rollNumber:"CBSE2024007",academicLevel:89,region:"Rajasthan",parentContact:"+91 9876543216",email:"<EMAIL>",dateOfBirth:"2008-12-03",address:"Jaipur, Rajasthan"},{id:"STU008",name:"Arjun Singh",grade:"10th",section:"A",board:"cbse",rollNumber:"CBSE2024008",academicLevel:76,region:"Punjab",parentContact:"+91 9876543217",email:"<EMAIL>",dateOfBirth:"2009-04-25",address:"Chandigarh, Punjab"},{id:"STU009",name:"Kavya Menon",grade:"12th",section:"C",board:"icse",rollNumber:"ICSE2024009",academicLevel:91,region:"Kerala",parentContact:"+91 9876543218",email:"<EMAIL>",dateOfBirth:"2007-08-14",address:"Thiruvananthapuram, Kerala"},{id:"STU010",name:"Rohit Gupta",grade:"9th",section:"A",board:"state",rollNumber:"UP2024010",academicLevel:84,region:"Uttar Pradesh",parentContact:"+91 9876543219",email:"<EMAIL>",dateOfBirth:"2010-02-28",address:"Lucknow, Uttar Pradesh"}],Jt=()=>qt.map((e=>({...e,performance:Gt(e.board,e),attendance:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((e=>({month:e,present:Math.floor(8*Math.random())+18,total:Math.floor(3*Math.random())+22,percentage:Math.floor(20*Math.random())+75}))),behavioral:Ut(),extracurricular:Yt(),swotAnalysis:{strengths:["Strong analytical thinking","Excellent communication skills","Creative problem solving","Leadership qualities","Team collaboration","Mathematical aptitude","Scientific reasoning","Artistic abilities","Sports excellence","Language proficiency","Technical skills"].sort((()=>.5-Math.random())).slice(0,3),weaknesses:["Time management","Public speaking anxiety","Difficulty with abstract concepts","Procrastination tendency","Perfectionism","Attention to detail","Organization skills","Stress management","Peer interaction","Technology adaptation"].sort((()=>.5-Math.random())).slice(0,2),opportunities:["Advanced placement courses","Science fair participation","Leadership roles","Internship programs","Skill development workshops","Mentorship programs","Competition participation","Community service","Online learning platforms"].sort((()=>.5-Math.random())).slice(0,3),threats:["Academic pressure","Peer competition","Technology distractions","Time constraints","Resource limitations","Career uncertainty","Social media influence","Health concerns","Family expectations"].sort((()=>.5-Math.random())).slice(0,2),lastUpdated:new Date,confidence:Math.floor(20*Math.random())+75},overallGrade:e.academicLevel>=90?"A+":e.academicLevel>=80?"A":e.academicLevel>=70?"B":e.academicLevel>=60?"C":"D",rank:Math.floor(50*Math.random())+1,lastUpdated:new Date}))),Kt=e=>{const t=e.length,a=e.reduce(((e,t)=>e+t.academicLevel),0)/t,i=e.reduce(((e,t)=>e+t.attendance.reduce(((e,t)=>e+t.present),0)/t.attendance.reduce(((e,t)=>e+t.total),0)*100),0)/t;return{totalStudents:t,averagePerformance:Math.round(a),averageAttendance:Math.round(i),topPerformers:e.filter((e=>e.academicLevel>=90)).length,needsAttention:e.filter((e=>e.academicLevel<70)).length,boardDistribution:{cbse:e.filter((e=>"cbse"===e.board)).length,icse:e.filter((e=>"icse"===e.board)).length,state:e.filter((e=>"state"===e.board)).length,ib:e.filter((e=>"ib"===e.board)).length},gradeDistribution:{"9th":e.filter((e=>"9th"===e.grade)).length,"10th":e.filter((e=>"10th"===e.grade)).length,"11th":e.filter((e=>"11th"===e.grade)).length,"12th":e.filter((e=>"12th"===e.grade)).length},performanceTrends:Xt(),subjectAnalysis:Qt(e),attendancePatterns:Zt(e),swotDistribution:ea(e)}},Xt=e=>["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((e=>({month:e,average:Math.floor(15*Math.random())+75,cbse:Math.floor(15*Math.random())+78,icse:Math.floor(15*Math.random())+80,state:Math.floor(15*Math.random())+73}))),Qt=e=>["Mathematics","Science","English","Hindi","Social Studies","Computer Science"].map((t=>({subject:t,averageScore:Math.floor(20*Math.random())+70,topScore:Math.floor(10*Math.random())+90,lowestScore:Math.floor(20*Math.random())+45,studentsAbove80:Math.floor(Math.random()*e.length*.6),studentsBelow60:Math.floor(Math.random()*e.length*.2)}))),Zt=e=>["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((t=>({month:t,averageAttendance:Math.floor(15*Math.random())+80,totalStudents:e.length,presentStudents:Math.floor(e.length*(.8+.15*Math.random()))}))),ea=e=>({strengthsDistribution:{"Analytical Thinking":Math.floor(.3*e.length),"Communication Skills":Math.floor(.25*e.length),Leadership:Math.floor(.2*e.length),Creativity:Math.floor(.35*e.length),"Technical Skills":Math.floor(.4*e.length)},weaknessesDistribution:{"Time Management":Math.floor(.4*e.length),"Public Speaking":Math.floor(.3*e.length),Organization:Math.floor(.25*e.length),"Stress Management":Math.floor(.35*e.length)},opportunitiesDistribution:{"Advanced Courses":Math.floor(.6*e.length),"Leadership Roles":Math.floor(.3*e.length),Competitions:Math.floor(.5*e.length),"Skill Development":Math.floor(.7*e.length)},threatsDistribution:{"Academic Pressure":Math.floor(.5*e.length),"Peer Competition":Math.floor(.4*e.length),"Technology Distractions":Math.floor(.6*e.length),"Time Constraints":Math.floor(.45*e.length)}}),ta=({children:e,loading:a=!1,disabled:i=!1,variant:s="contained",color:l="primary",size:c="medium",startIcon:h,endIcon:p,onClick:m,tooltip:g,fullWidth:j=!1,sx:b={},loadingText:f="Loading...",successFeedback:y=!1,errorFeedback:v=!1,...S})=>{const C=n(),[w,F]=ee.useState(!1),[k,A]=ee.useState(!1),[W,I]=ee.useState(!1),M=()=>k?"success":W?"error":l,E={borderRadius:3,textTransform:"none",fontWeight:600,minHeight:"small"===c?36:"large"===c?52:44,position:"relative",overflow:"hidden",transition:"all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)","&:hover":{transform:"translateY(-2px)",boxShadow:"contained"===s?`0px 8px 24px ${o(C.palette[M()].main,.4)}`:`0px 4px 12px ${o(C.palette[M()].main,.2)}`},"&:active":{transform:"translateY(0px)"},"&:focus-visible":{outline:`2px solid ${C.palette[M()].main}`,outlineOffset:"2px"},"&:disabled":{opacity:.6,transform:"none",boxShadow:"none"},...a&&{pointerEvents:"none"},...b},T=t.jsx(ue.div,{variants:{initial:{scale:1},hover:{scale:1.02,transition:{type:"spring",stiffness:400,damping:25}},tap:{scale:.98,transition:{type:"spring",stiffness:400,damping:25}}},initial:"initial",whileHover:a||i?"initial":"hover",whileTap:a||i?"initial":"tap",style:{display:j?"block":"inline-block",width:j?"100%":"auto"},children:t.jsx(u,{variant:s,color:M(),size:c,disabled:i||a,onClick:async e=>{if(!a&&!i){F(!0);try{if(m){const t=await m(e);y&&!1!==t&&(A(!0),setTimeout((()=>A(!1)),2e3))}}catch(t){v&&(I(!0),setTimeout((()=>I(!1)),2e3))}finally{setTimeout((()=>F(!1)),150)}}},fullWidth:j,sx:E,...S,children:a?t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[t.jsx(x,{size:16,color:"inherit",sx:{color:"contained"===s?"white":C.palette[l].main}}),f]}):k?t.jsx(d,{sx:{display:"flex",alignItems:"center",gap:1},children:"✓ Success"}):W?t.jsx(d,{sx:{display:"flex",alignItems:"center",gap:1},children:"✗ Error"}):t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[h,e,p]})})});return g?t.jsx(r,{title:g,arrow:!0,placement:"top",children:t.jsx("span",{style:{display:j?"block":"inline-block"},children:T})}):T},aa=({children:e,onClick:a,elevation:i=2,variant:r="default",loading:c=!1,disabled:x=!1,hoverable:h=!0,clickable:p=!1,sx:m={},contentSx:u={},actions:b,header:f,footer:y,color:v="primary",gradient:S=!1,glassmorphism:C=!1,borderRadius:w=3,...F})=>{const k=n(),[A,W]=ee.useState(!1),I={initial:{scale:1,y:0,boxShadow:k.shadows[i]},hover:h?{scale:1.02,y:-8,boxShadow:k.shadows[Math.min(i+4,24)],transition:{type:"spring",stiffness:300,damping:20}}:{},tap:p?{scale:.98,transition:{type:"spring",stiffness:400,damping:25}}:{}},M={borderRadius:w,background:C?"dark"===k.palette.mode?`linear-gradient(135deg, ${o("#1E293B",.8)} 0%, ${o("#334155",.6)} 100%)`:`linear-gradient(135deg, ${o("#FFFFFF",.9)} 0%, ${o("#F8FAFC",.8)} 100%)`:S?`linear-gradient(135deg, ${k.palette[v].main} 0%, ${k.palette[v].dark} 100%)`:k.palette.background.paper,backdropFilter:C?"blur(20px)":"none",border:C?`1px solid ${o(k.palette.divider,.1)}`:"none",cursor:p?"pointer":"default",transition:"all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)",position:"relative",overflow:"hidden","&:hover":h?{"& .card-content":{transform:"translateY(-2px)"},"& .card-actions":{opacity:1,transform:"translateY(0)"}}:{},...p&&{"&:focus-visible":{outline:`2px solid ${k.palette[v].main}`,outlineOffset:"2px"}},...x&&{opacity:.6,pointerEvents:"none"},...c&&{pointerEvents:"none"},...m},E={transition:"transform 0.3s ease",...u},T=()=>t.jsxs(l,{children:[t.jsx(j,{variant:"text",width:"60%",height:32,sx:{mb:2}}),t.jsx(j,{variant:"text",width:"100%",height:20,sx:{mb:1}}),t.jsx(j,{variant:"text",width:"80%",height:20,sx:{mb:2}}),t.jsx(j,{variant:"rectangular",width:"100%",height:120,sx:{borderRadius:2}})]});return t.jsx(ue.div,{variants:I,initial:"initial",whileHover:x||c?"initial":"hover",whileTap:x||c||!p?"initial":"tap",onHoverStart:()=>W(!0),onHoverEnd:()=>W(!1),children:t.jsxs(s,{elevation:0,onClick:e=>{x||c||!a||a(e)},sx:M,tabIndex:p?0:-1,role:p?"button":void 0,...F,children:[C&&t.jsx(d,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,background:`linear-gradient(135deg, ${o("#FFFFFF",.1)} 0%, ${o("#FFFFFF",.05)} 100%)`,pointerEvents:"none",zIndex:0}}),f&&t.jsx(d,{sx:{position:"relative",zIndex:1},children:f}),t.jsx(l,{className:"card-content",sx:E,style:{position:"relative",zIndex:1},children:c?t.jsx(T,{}):e}),b&&t.jsx(g,{className:"card-actions",sx:{position:"relative",zIndex:1,opacity:h?.8:1,transform:h?"translateY(4px)":"none",transition:"all 0.3s ease"},children:b}),y&&t.jsx(d,{sx:{position:"relative",zIndex:1},children:y}),p&&A&&t.jsx(d,{sx:{position:"absolute",top:"50%",left:"50%",width:0,height:0,borderRadius:"50%",background:o(k.palette[v].main,.1),transform:"translate(-50%, -50%)",animation:"ripple 0.6s linear","@keyframes ripple":{to:{width:"200%",height:"200%",opacity:0}}}})]})})},ia=({darkMode:e,onToggle:a})=>{const i=n();return t.jsx(r,{title:e?"Switch to Light Mode":"Switch to Dark Mode",children:t.jsx(p,{onClick:a,sx:{background:o(i.palette.background.paper,.8),backdropFilter:"blur(10px)",border:`1px solid ${o(i.palette.divider,.2)}`,"&:hover":{background:o(i.palette.background.paper,.9),transform:"scale(1.05)"},transition:"all 0.3s ease"},children:t.jsx(ue.div,{initial:!1,animate:{rotate:e?180:0},transition:{duration:.5,ease:"easeInOut"},children:e?t.jsx(Te,{}):t.jsx(Be,{})})})})},na=({darkMode:e,onThemeToggle:a,user:i})=>{var r;const s=n();return t.jsx(v,{position:"sticky",elevation:0,sx:{background:o(s.palette.background.paper,.8),backdropFilter:"blur(20px)",borderBottom:`1px solid ${o(s.palette.divider,.1)}`,color:s.palette.text.primary},children:t.jsxs(S,{sx:{justifyContent:"space-between",py:1},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2},children:[t.jsx(ue.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:t.jsx(c,{sx:{bgcolor:s.palette.primary.main,width:48,height:48},children:t.jsx(Ce,{})})}),t.jsxs(d,{children:[t.jsx(h,{variant:"h5",sx:{fontWeight:800,background:`linear-gradient(135deg, ${s.palette.primary.main} 0%, ${s.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"VidyaMitra"}),t.jsx(h,{variant:"caption",color:"text.secondary",children:"Modern Educational Dashboard"})]})]}),t.jsxs(f,{direction:"row",spacing:1,alignItems:"center",children:[t.jsx(p,{children:t.jsx(we,{})}),t.jsx(p,{children:t.jsx(Fe,{})}),t.jsx(C,{badgeContent:3,color:"error",children:t.jsx(p,{children:t.jsx(ke,{})})}),t.jsx(ia,{darkMode:e,onToggle:a}),t.jsx(c,{sx:{width:40,height:40,cursor:"pointer",border:`2px solid ${s.palette.primary.main}`},children:(null==(r=null==i?void 0:i.name)?void 0:r.charAt(0))||"U"})]})]})})},ra=({loading:e,analyticsData:a})=>{const i=ie(),n=a?[{title:"Total Students",value:a.totalStudents,subtitle:"Active learners",icon:Ae,trend:"up",trendValue:12,progress:85,progressLabel:"Enrollment",color:"primary",onClick:()=>i("/dashboard/students")},{title:"SWOT Analyses",value:a.totalStudents,subtitle:"Generated this month",icon:ye,trend:"up",trendValue:8,progress:72,progressLabel:"Completion",color:"secondary",gradient:!0,onClick:()=>i("/dashboard/swot")},{title:"Average Performance",value:`${a.averagePerformance}%`,subtitle:"Class average",icon:We,trend:"up",trendValue:5,progress:a.averagePerformance,progressLabel:"Target: 85%",color:"success",onClick:()=>i("/dashboard/analytics")},{title:"Attendance Rate",value:`${a.averageAttendance}%`,subtitle:"Overall attendance",icon:Ie,trend:a.averageAttendance>=90?"up":"down",trendValue:2,progress:a.averageAttendance,progressLabel:"Target: 95%",color:"info",onClick:()=>i("/dashboard/reports")}]:[];return t.jsx(b,{container:!0,spacing:3,children:n.map(((a,i)=>t.jsx(b,{item:!0,xs:12,sm:6,lg:3,children:t.jsx(ue.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*i,duration:.6},children:t.jsx(Vt,{...a,loading:e,onClick:()=>{},tooltip:`View detailed ${a.title.toLowerCase()} analytics`})})},a.title)))})},sa=({loading:e,studentsData:a})=>{const i=ie(),n=(()=>{var e,t,n;if(!a||0===a.length)return[];const r=[];a.slice(0,3).forEach(((e,t)=>{r.push({id:`swot_${e.id}`,type:"swot_generated",title:"SWOT Analysis Generated",description:`For student ${e.name} (${e.grade} ${e.section})`,time:5*(t+1)+" minutes ago",icon:ye,color:"primary",onClick:()=>i(`/dashboard/students/${e.id}/swot`)})}));const s=a.filter((e=>e.academicLevel<70));return s.length>0&&r.push({id:"performance_alert",type:"performance_alert",title:"Performance Alert",description:`${s.length} students need attention`,time:"15 minutes ago",icon:We,color:"warning",onClick:()=>i("/dashboard/analytics")}),r.push({id:"new_student",type:"new_student",title:"New Student Enrolled",description:`${null==(e=a[a.length-1])?void 0:e.name} joined ${null==(t=a[a.length-1])?void 0:t.grade} ${null==(n=a[a.length-1])?void 0:n.section}`,time:"1 hour ago",icon:Ae,color:"success",onClick:()=>i("/dashboard/students")}),r.slice(0,4)})();return t.jsxs(aa,{loading:e,glassmorphism:!0,hoverable:!0,sx:{height:"100%"},children:[t.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600},children:"Recent Activity"}),t.jsx(ta,{size:"small",variant:"outlined",startIcon:t.jsx(Me,{}),tooltip:"Refresh activities",onClick:()=>window.location.reload(),children:"Refresh"})]}),t.jsx(f,{spacing:2,children:n.map(((e,a)=>t.jsx(ue.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*a},children:t.jsxs(d,{sx:{display:"flex",gap:2,p:2,borderRadius:2,cursor:e.onClick?"pointer":"default",transition:"all 0.3s ease","&:hover":{background:o("#000",.02),transform:"translateX(4px)"}},onClick:e.onClick,children:[t.jsx(c,{sx:{bgcolor:`${e.color}.main`,width:40,height:40},children:t.jsx(e.icon,{sx:{fontSize:20}})}),t.jsxs(d,{sx:{flex:1},children:[t.jsx(h,{variant:"body2",sx:{fontWeight:600,mb:.5},children:e.title}),t.jsx(h,{variant:"caption",color:"text.secondary",sx:{display:"block",mb:.5},children:e.description}),t.jsx(h,{variant:"caption",color:"text.disabled",children:e.time})]})]})},e.id)))})]})},oa=()=>{n();const e=ie(),a=[{label:"Generate SWOT",icon:ye,color:"primary",onClick:()=>e("/dashboard/swot")},{label:"Add Student",icon:Ae,color:"secondary",onClick:()=>e("/dashboard/students")},{label:"View Reports",icon:ve,color:"success",onClick:()=>e("/dashboard/reports")},{label:"Analytics",icon:Ee,color:"info",onClick:()=>e("/dashboard/analytics")}];return t.jsx(d,{sx:{position:"fixed",bottom:24,right:24,zIndex:1e3},children:t.jsx(f,{spacing:2,children:a.map(((e,a)=>t.jsx(ue.div,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:.1*a,type:"spring"},children:t.jsx(ta,{variant:"contained",color:e.color,onClick:e.onClick,tooltip:e.label,startIcon:t.jsx(e.icon,{}),sx:{borderRadius:"50%",minWidth:56,width:56,height:56,backdropFilter:"blur(10px)",boxShadow:"0px 8px 24px rgba(0, 0, 0, 0.15)"}})},e.label)))})})},la=()=>{const{t:e}=xe(["dashboard","common"]),a=n(),[i,r]=ee.useState(!1),[c,x]=ee.useState(!0),[p,m]=ee.useState([]),[u,g]=ee.useState(null),[v]=ee.useState({name:"Dr. Priya Sharma",role:"Principal",school:"Delhi Public School"});ee.useEffect((()=>{(async()=>{x(!0);try{await new Promise((e=>setTimeout(e,1500)));const e=Jt(),t=Kt(e);m(e),g(t)}catch(e){}finally{x(!1)}})()}),[]);return t.jsxs(d,{sx:{minHeight:"100vh",background:`linear-gradient(135deg, ${a.palette.background.default} 0%, ${a.palette.background.surface||a.palette.background.paper} 100%)`},children:[t.jsx(na,{darkMode:i,onThemeToggle:()=>{r(!i)},user:v}),t.jsxs(d,{sx:{p:3},children:[t.jsx(ue.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:t.jsxs(d,{sx:{mb:4},children:[t.jsxs(h,{variant:"h4",sx:{fontWeight:700,mb:1,background:`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:["Welcome back, ",v.name,"! 👋"]}),t.jsxs(h,{variant:"body1",color:"text.secondary",sx:{fontSize:"1.1rem"},children:["Here's what's happening at ",v.school," today"]})]})}),t.jsx(d,{sx:{mb:4},children:t.jsx(ra,{loading:c,analyticsData:u})}),t.jsxs(b,{container:!0,spacing:3,children:[t.jsx(b,{item:!0,xs:12,lg:6,children:t.jsx(ue.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3,duration:.6},children:t.jsx(sa,{loading:c,studentsData:p})})}),t.jsx(b,{item:!0,xs:12,lg:6,children:t.jsx(ue.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.4,duration:.6},children:t.jsx(s,{sx:{height:"100%"},children:t.jsxs(l,{children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:3},children:"Performance Overview"}),c?t.jsxs(f,{spacing:2,children:[t.jsx(j,{variant:"rectangular",height:200}),t.jsx(j,{variant:"text"}),t.jsx(j,{variant:"text",width:"60%"})]}):t.jsx(d,{children:t.jsxs(d,{sx:{mb:3},children:[t.jsx(h,{variant:"body2",sx:{mb:2,fontWeight:500},children:"Board Performance"}),t.jsx(f,{spacing:2,children:[{board:"CBSE",score:92,students:450},{board:"ICSE",score:89,students:320},{board:"State Board",score:85,students:477}].map(((e,i)=>{var n,r,s;return t.jsx(ue.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.5+.1*i},children:t.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",p:2,borderRadius:2,background:o((null==(n=a.palette.board)?void 0:n[e.board])||a.palette.primary.main,.1),border:`1px solid ${o((null==(r=a.palette.board)?void 0:r[e.board])||a.palette.primary.main,.2)}`},children:[t.jsxs(d,{children:[t.jsx(h,{variant:"body2",sx:{fontWeight:600},children:e.board}),t.jsxs(h,{variant:"caption",color:"text.secondary",children:[e.students," students"]})]}),t.jsx(y,{label:`${e.score}%`,size:"small",sx:{bgcolor:(null==(s=a.palette.board)?void 0:s[e.board])||a.palette.primary.main,color:"white",fontWeight:600}})]})},e.board)}))})]})})]})})})})]}),t.jsx(d,{sx:{mt:4},children:t.jsxs(b,{container:!0,spacing:3,children:[t.jsx(b,{item:!0,xs:12,md:4,children:t.jsx(ue.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6,duration:.6},children:t.jsxs(s,{sx:{p:3,textAlign:"center",background:`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.primary.dark} 100%)`,color:"white",cursor:"pointer",transition:"all 0.3s ease","&:hover":{transform:"translateY(-4px)",boxShadow:"0px 20px 40px rgba(46, 91, 168, 0.3)"}},children:[t.jsx(ye,{sx:{fontSize:48,mb:2}}),t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:1},children:"Generate SWOT Analysis"}),t.jsx(h,{variant:"body2",sx:{opacity:.9},children:"Create comprehensive student assessments"})]})})}),t.jsx(b,{item:!0,xs:12,md:4,children:t.jsx(ue.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7,duration:.6},children:t.jsxs(s,{sx:{p:3,textAlign:"center",background:`linear-gradient(135deg, ${a.palette.secondary.main} 0%, ${a.palette.secondary.dark} 100%)`,color:"white",cursor:"pointer",transition:"all 0.3s ease","&:hover":{transform:"translateY(-4px)",boxShadow:"0px 20px 40px rgba(255, 153, 51, 0.3)"}},children:[t.jsx(ve,{sx:{fontSize:48,mb:2}}),t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:1},children:"View Analytics"}),t.jsx(h,{variant:"body2",sx:{opacity:.9},children:"Detailed performance insights"})]})})}),t.jsx(b,{item:!0,xs:12,md:4,children:t.jsx(ue.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8,duration:.6},children:t.jsxs(s,{sx:{p:3,textAlign:"center",background:`linear-gradient(135deg, ${a.palette.success.main} 0%, ${a.palette.success.dark} 100%)`,color:"white",cursor:"pointer",transition:"all 0.3s ease","&:hover":{transform:"translateY(-4px)",boxShadow:"0px 20px 40px rgba(0, 200, 83, 0.3)"}},children:[t.jsx(Se,{sx:{fontSize:48,mb:2}}),t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:1},children:"Manage Students"}),t.jsx(h,{variant:"body2",sx:{opacity:.9},children:"Student profiles and records"})]})})})]})})]}),t.jsx(oa,{})]})},da=()=>{const e=n();return t.jsx(d,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,background:`linear-gradient(135deg, ${e.palette.primary.main}15 0%, ${e.palette.secondary.main}10 100%)`,overflow:"hidden",zIndex:-1},children:[...Array(6)].map(((a,i)=>t.jsx(ue.div,{initial:{opacity:0,scale:0},animate:{opacity:[.1,.3,.1],scale:[1,1.2,1],x:[0,100,0],y:[0,-50,0]},transition:{duration:8+2*i,repeat:1/0,delay:1.5*i},style:{position:"absolute",top:20+15*i+"%",left:10+15*i+"%",width:60+20*i,height:60+20*i,borderRadius:"50%",background:i%2==0?`linear-gradient(135deg, ${e.palette.primary.main}20, ${e.palette.primary.light}10)`:`linear-gradient(135deg, ${e.palette.secondary.main}20, ${e.palette.secondary.light}10)`,backdropFilter:"blur(10px)"}},i)))})},ca=({icon:e,title:a,description:i,delay:r=0})=>{const s=n();return t.jsx(ue.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:r,duration:.6},children:t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2,p:2,borderRadius:2,background:o(s.palette.background.paper,.1),backdropFilter:"blur(10px)",border:`1px solid ${o(s.palette.primary.main,.1)}`,transition:"all 0.3s ease","&:hover":{transform:"translateX(8px)",background:o(s.palette.background.paper,.2)}},children:[t.jsx(c,{sx:{bgcolor:s.palette.primary.main,width:48,height:48},children:t.jsx(e,{})}),t.jsxs(d,{children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:.5},children:a}),t.jsx(h,{variant:"body2",color:"text.secondary",children:i})]})]})})},xa=({onSubmit:e,loading:a})=>{const[i,n]=ee.useState(!1),[r,s]=ee.useState({email:"",password:""}),l=e=>t=>{s((a=>({...a,[e]:t.target.value})))};return t.jsx(d,{component:"form",onSubmit:t=>{t.preventDefault(),e(r)},sx:{width:"100%"},children:t.jsxs(f,{spacing:3,children:[t.jsx(A,{fullWidth:!0,label:"Email Address",type:"email",value:r.email,onChange:l("email"),InputProps:{startAdornment:t.jsx(W,{position:"start",children:t.jsx(Oe,{color:"primary"})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:o("#fff",.1)}}}),t.jsx(A,{fullWidth:!0,label:"Password",type:i?"text":"password",value:r.password,onChange:l("password"),InputProps:{startAdornment:t.jsx(W,{position:"start",children:t.jsx(He,{color:"primary"})}),endAdornment:t.jsx(W,{position:"end",children:t.jsx(p,{onClick:()=>n(!i),edge:"end",children:i?t.jsx(Le,{}):t.jsx($e,{})})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:o("#fff",.1)}}}),t.jsx(u,{type:"submit",fullWidth:!0,variant:"contained",size:"large",disabled:a,endIcon:t.jsx(Ne,{}),sx:{py:1.5,borderRadius:2,background:"linear-gradient(135deg, #2E5BA8 0%, #4A90E2 100%)",boxShadow:"0px 8px 24px rgba(46, 91, 168, 0.3)","&:hover":{background:"linear-gradient(135deg, #1E4A97 0%, #2E5BA8 100%)",boxShadow:"0px 12px 32px rgba(46, 91, 168, 0.4)",transform:"translateY(-2px)"}},children:a?"Signing In...":"Sign In"})]})})},ha=({onSubmit:e,loading:a})=>{const[i,n]=ee.useState(!1),[r,s]=ee.useState({name:"",email:"",password:"",role:"teacher",board:"CBSE"}),l=e=>t=>{s((a=>({...a,[e]:t.target.value})))};return t.jsx(d,{component:"form",onSubmit:t=>{t.preventDefault(),e(r)},sx:{width:"100%"},children:t.jsxs(f,{spacing:3,children:[t.jsx(A,{fullWidth:!0,label:"Full Name",value:r.name,onChange:l("name"),InputProps:{startAdornment:t.jsx(W,{position:"start",children:t.jsx(Ae,{color:"primary"})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:o("#fff",.1)}}}),t.jsx(A,{fullWidth:!0,label:"Email Address",type:"email",value:r.email,onChange:l("email"),InputProps:{startAdornment:t.jsx(W,{position:"start",children:t.jsx(Oe,{color:"primary"})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:o("#fff",.1)}}}),t.jsx(A,{fullWidth:!0,label:"Password",type:i?"text":"password",value:r.password,onChange:l("password"),InputProps:{startAdornment:t.jsx(W,{position:"start",children:t.jsx(He,{color:"primary"})}),endAdornment:t.jsx(W,{position:"end",children:t.jsx(p,{onClick:()=>n(!i),edge:"end",children:i?t.jsx(Le,{}):t.jsx($e,{})})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:o("#fff",.1)}}}),t.jsxs(d,{children:[t.jsx(h,{variant:"body2",sx:{mb:1,fontWeight:500},children:"Education Board"}),t.jsx(f,{direction:"row",spacing:1,flexWrap:"wrap",children:["CBSE","ICSE","State Board","IB"].map((e=>t.jsx(y,{label:e,clickable:!0,color:r.board===e?"primary":"default",onClick:()=>s((t=>({...t,board:e}))),sx:{borderRadius:2,fontWeight:500}},e)))})]}),t.jsx(u,{type:"submit",fullWidth:!0,variant:"contained",size:"large",disabled:a,endIcon:t.jsx(Ne,{}),sx:{py:1.5,borderRadius:2,background:"linear-gradient(135deg, #FF9933 0%, #FFB366 100%)",boxShadow:"0px 8px 24px rgba(255, 153, 51, 0.3)","&:hover":{background:"linear-gradient(135deg, #FF8F00 0%, #FF9933 100%)",boxShadow:"0px 12px 32px rgba(255, 153, 51, 0.4)",transform:"translateY(-2px)"}},children:a?"Creating Account...":"Create Account"})]})})},pa=({onLogin:e,onSignup:a})=>{const{t:i}=xe(["auth","common"]),r=n(),[x,m]=ee.useState(0),[u,g]=ee.useState(!1);return t.jsxs(d,{sx:{minHeight:"100vh",display:"flex",position:"relative",background:`linear-gradient(135deg, ${r.palette.background.default} 0%, ${r.palette.background.surface} 100%)`},children:[t.jsx(da,{}),t.jsx(d,{sx:{flex:1,display:{xs:"none",md:"flex"},flexDirection:"column",justifyContent:"center",p:6,position:"relative"},children:t.jsxs(ue.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},children:[t.jsxs(d,{sx:{mb:6},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2,mb:3},children:[t.jsx(c,{sx:{bgcolor:r.palette.primary.main,width:64,height:64},children:t.jsx(Ce,{sx:{fontSize:32}})}),t.jsx(h,{variant:"h3",sx:{fontWeight:600,color:"dark"===r.palette.mode?"#FFFFFF":"#1E293B",textShadow:"dark"===r.palette.mode?"0 2px 8px rgba(0,0,0,0.5)":"0 2px 4px rgba(0,0,0,0.1)"},children:"VidyaMitra"})]}),t.jsx(h,{variant:"h5",sx:{fontWeight:600,color:r.palette.text.primary,mb:2},children:"Empowering Indian Education with AI-Driven SWOT Analysis"}),t.jsx(h,{variant:"body1",sx:{color:r.palette.text.secondary,fontSize:"1.1rem",lineHeight:1.6},children:"Transform student assessment and development with our comprehensive platform designed specifically for Indian educational institutions."})]}),t.jsxs(f,{spacing:3,children:[t.jsx(ca,{icon:We,title:"AI-Powered Analysis",description:"Advanced SWOT analysis using machine learning algorithms",delay:.2}),t.jsx(ca,{icon:Ce,title:"Board-Specific Features",description:"Tailored for CBSE, ICSE, and State Board curricula",delay:.4}),t.jsx(ca,{icon:Pe,title:"Comprehensive Tracking",description:"Academic, behavioral, and extracurricular monitoring",delay:.6})]})]})}),t.jsx(d,{sx:{flex:{xs:1,md:.6},display:"flex",alignItems:"center",justifyContent:"center",p:3},children:t.jsx(ue.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},style:{width:"100%",maxWidth:480},children:t.jsx(s,{sx:{p:4,borderRadius:4,background:o(r.palette.background.paper,.9),backdropFilter:"blur(20px)",border:`1px solid ${o(r.palette.primary.main,.1)}`,boxShadow:"0px 20px 40px rgba(46, 91, 168, 0.15)"},children:t.jsxs(l,{sx:{p:0},children:[t.jsxs(d,{sx:{textAlign:"center",mb:4},children:[t.jsx(h,{variant:"h4",sx:{fontWeight:500,mb:1,color:r.palette.text.primary},children:"Welcome Back"}),t.jsx(h,{variant:"body1",color:"text.secondary",children:"Access your educational dashboard"})]}),t.jsxs(w,{value:x,onChange:(e,t)=>{m(t)},centered:!0,sx:{mb:4,"& .MuiTab-root":{textTransform:"none",fontWeight:600,fontSize:"1rem",minWidth:120},"& .MuiTabs-indicator":{borderRadius:2,height:3}},children:[t.jsx(F,{label:"Sign In"}),t.jsx(F,{label:"Sign Up"})]}),t.jsx(ge,{mode:"wait",children:0===x?t.jsx(ue.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:t.jsx(xa,{onSubmit:async t=>{g(!0);try{e?await e(t):await new Promise((e=>setTimeout(e,2e3)))}catch(a){}finally{g(!1)}},loading:u})},"login"):t.jsx(ue.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:t.jsx(ha,{onSubmit:async e=>{g(!0);try{a?await a(e):await new Promise((e=>setTimeout(e,2e3)))}catch(t){}finally{g(!1)}},loading:u})},"signup")}),t.jsxs(d,{sx:{mt:4},children:[t.jsx(k,{sx:{mb:3},children:t.jsx(h,{variant:"body2",color:"text.secondary",children:"Or continue with"})}),t.jsxs(f,{direction:"row",spacing:2,justifyContent:"center",children:[t.jsx(p,{sx:{border:`1px solid ${o(r.palette.divider,.5)}`,borderRadius:2,p:1.5,"&:hover":{background:o(r.palette.primary.main,.1),transform:"translateY(-2px)"}},children:t.jsx(ze,{})}),t.jsx(p,{sx:{border:`1px solid ${o(r.palette.divider,.5)}`,borderRadius:2,p:1.5,"&:hover":{background:o(r.palette.primary.main,.1),transform:"translateY(-2px)"}},children:t.jsx(De,{})}),t.jsx(p,{sx:{border:`1px solid ${o(r.palette.divider,.5)}`,borderRadius:2,p:1.5,"&:hover":{background:o(r.palette.primary.main,.1),transform:"translateY(-2px)"}},children:t.jsx(Re,{})})]})]})]})})})})]})};yt.register(vt,St,Ct,wt,Ft,kt,At,Wt,It);const ma=()=>{const e=n(),[a,i]=ee.useState(0),[r,c]=ee.useState("year"),[x,p]=ee.useState("all"),[m,u]=ee.useState([]),[g,j]=ee.useState(null);ee.useEffect((()=>{const e=Jt();u(e),j(Kt(e))}),[]);if(!g)return t.jsx(d,{children:"Loading..."});const f={data:{labels:g.performanceTrends.map((e=>e.month)),datasets:[{label:"Overall Average",data:g.performanceTrends.map((e=>e.average)),borderColor:e.palette.primary.main,backgroundColor:o(e.palette.primary.main,.1),tension:.4},{label:"CBSE",data:g.performanceTrends.map((e=>e.cbse)),borderColor:e.palette.secondary.main,backgroundColor:o(e.palette.secondary.main,.1),tension:.4},{label:"ICSE",data:g.performanceTrends.map((e=>e.icse)),borderColor:e.palette.success.main,backgroundColor:o(e.palette.success.main,.1),tension:.4},{label:"State Board",data:g.performanceTrends.map((e=>e.state)),borderColor:e.palette.warning.main,backgroundColor:o(e.palette.warning.main,.1),tension:.4}]},options:{responsive:!0,plugins:{legend:{position:"top"},title:{display:!0,text:"Academic Performance Trends"}},scales:{y:{beginAtZero:!1,min:60,max:100}}}},y={data:{labels:g.subjectAnalysis.map((e=>e.subject)),datasets:[{label:"Average Score",data:g.subjectAnalysis.map((e=>e.averageScore)),backgroundColor:[o(e.palette.primary.main,.8),o(e.palette.secondary.main,.8),o(e.palette.success.main,.8),o(e.palette.warning.main,.8),o(e.palette.error.main,.8),o(e.palette.info.main,.8)],borderColor:[e.palette.primary.main,e.palette.secondary.main,e.palette.success.main,e.palette.warning.main,e.palette.error.main,e.palette.info.main],borderWidth:2}]},options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Subject-wise Performance Analysis"}},scales:{y:{beginAtZero:!1,min:50,max:100}}}},v={data:{labels:["CBSE","ICSE","State Board","IB"],datasets:[{data:[g.boardDistribution.cbse,g.boardDistribution.icse,g.boardDistribution.state,g.boardDistribution.ib],backgroundColor:[o(e.palette.primary.main,.8),o(e.palette.secondary.main,.8),o(e.palette.success.main,.8),o(e.palette.warning.main,.8)],borderColor:[e.palette.primary.main,e.palette.secondary.main,e.palette.success.main,e.palette.warning.main],borderWidth:2}]},options:{responsive:!0,plugins:{legend:{position:"bottom"},title:{display:!0,text:"Student Distribution by Board"}}}},S={data:{labels:g.attendancePatterns.map((e=>e.month)),datasets:[{label:"Attendance Percentage",data:g.attendancePatterns.map((e=>e.averageAttendance)),backgroundColor:o(e.palette.success.main,.6),borderColor:e.palette.success.main,borderWidth:2}]},options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Monthly Attendance Patterns"}},scales:{y:{beginAtZero:!1,min:70,max:100}}}},C=[{title:"Total Students",value:g.totalStudents,icon:Ve,color:"primary",trend:"+5.2%",description:"Active students enrolled"},{title:"Average Performance",value:`${g.averagePerformance}%`,icon:We,color:"success",trend:"+2.1%",description:"Overall academic performance"},{title:"Average Attendance",value:`${g.averageAttendance}%`,icon:Ce,color:"info",trend:"+1.8%",description:"Monthly attendance rate"},{title:"Top Performers",value:g.topPerformers,icon:_e,color:"warning",trend:"+3.5%",description:"Students scoring 90%+"}];return t.jsx(I,{maxWidth:"xl",sx:{py:3},children:t.jsxs(ue.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[t.jsxs(d,{sx:{mb:4},children:[t.jsx(h,{variant:"h4",sx:{fontWeight:600,mb:1,color:e.palette.text.primary},children:"Analytics Dashboard"}),t.jsx(h,{variant:"body1",color:"text.secondary",children:"Comprehensive insights into student performance and institutional metrics"})]}),t.jsxs(d,{sx:{mb:4,display:"flex",gap:2,flexWrap:"wrap"},children:[t.jsxs(M,{size:"small",sx:{minWidth:120},children:[t.jsx(E,{children:"Time Range"}),t.jsxs(T,{value:r,label:"Time Range",onChange:e=>c(e.target.value),children:[t.jsx(B,{value:"month",children:"This Month"}),t.jsx(B,{value:"quarter",children:"This Quarter"}),t.jsx(B,{value:"year",children:"This Year"}),t.jsx(B,{value:"all",children:"All Time"})]})]}),t.jsxs(M,{size:"small",sx:{minWidth:120},children:[t.jsx(E,{children:"Board"}),t.jsxs(T,{value:x,label:"Board",onChange:e=>p(e.target.value),children:[t.jsx(B,{value:"all",children:"All Boards"}),t.jsx(B,{value:"cbse",children:"CBSE"}),t.jsx(B,{value:"icse",children:"ICSE"}),t.jsx(B,{value:"state",children:"State Board"}),t.jsx(B,{value:"ib",children:"IB"})]})]})]}),t.jsx(b,{container:!0,spacing:3,sx:{mb:4},children:C.map(((e,a)=>t.jsx(b,{item:!0,xs:12,sm:6,md:3,children:t.jsx(ue.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*a},children:t.jsx(Vt,{...e})})},e.title)))}),t.jsxs(s,{sx:{background:o(e.palette.background.paper,.9),backdropFilter:"blur(20px)",border:`1px solid ${o(e.palette.divider,.1)}`},children:[t.jsxs(w,{value:a,onChange:(e,t)=>{i(t)},sx:{borderBottom:`1px solid ${o(e.palette.divider,.1)}`,px:2},children:[t.jsx(F,{label:"Performance Trends",icon:t.jsx(Ee,{})}),t.jsx(F,{label:"Subject Analysis",icon:t.jsx(ve,{})}),t.jsx(F,{label:"Board Distribution",icon:t.jsx(Ce,{})}),t.jsx(F,{label:"Attendance Patterns",icon:t.jsx(We,{})})]}),t.jsxs(l,{sx:{p:3},children:[0===a&&t.jsx(d,{sx:{height:400},children:t.jsx(Mt,{...f})}),1===a&&t.jsx(d,{sx:{height:400},children:t.jsx(Et,{...y})}),2===a&&t.jsx(d,{sx:{height:400,display:"flex",justifyContent:"center"},children:t.jsx(d,{sx:{width:400},children:t.jsx(Tt,{...v})})}),3===a&&t.jsx(d,{sx:{height:400},children:t.jsx(Et,{...S})})]})]})]})})},ua=({student:e,onView:a,onEdit:i,onGenerateSWOT:r})=>{const x=n(),[m,g]=ee.useState(null),[j,b]=ee.useState(!1),f=()=>{g(null)},v=e=>e>=80?x.palette.success.main:e>=60?x.palette.warning.main:x.palette.error.main,S=e=>e>0?{icon:We,color:x.palette.success.main}:e<0?{icon:qe,color:x.palette.error.main}:{icon:null,color:x.palette.text.secondary},C=S(e.performanceTrend).icon;return t.jsx(D,{in:!0,timeout:300,children:t.jsxs(s,{sx:{height:"100%",cursor:"pointer",transition:"all 0.3s ease-in-out",transform:j?"translateY(-4px)":"translateY(0)",boxShadow:j?x.shadows[8]:x.shadows[1],border:`1px solid ${o(x.palette.primary.main,.1)}`,"&:hover":{borderColor:x.palette.primary.main}},onMouseEnter:()=>b(!0),onMouseLeave:()=>b(!1),onClick:()=>a(e),children:[t.jsxs(l,{sx:{p:3},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"flex-start",justifyContent:"space-between",mb:2},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2,flex:1},children:[t.jsx(c,{sx:{width:56,height:56,bgcolor:x.palette.primary.main,fontSize:"1.25rem",fontWeight:600},children:e.name.split(" ").map((e=>e[0])).join("").toUpperCase()}),t.jsxs(d,{sx:{flex:1,minWidth:0},children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:.5},children:e.name}),t.jsxs(h,{variant:"body2",color:"text.secondary",children:[e.class," • Roll No: ",e.rollNumber]}),t.jsxs(h,{variant:"caption",color:"text.secondary",children:["ID: ",e.admissionNumber]})]})]}),t.jsx(p,{size:"small",onClick:e=>{e.stopPropagation(),g(e.currentTarget)},sx:{opacity:j?1:.7,transition:"opacity 0.2s ease-in-out"},children:t.jsx(je,{})})]}),t.jsxs(d,{sx:{mb:2},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1},children:[t.jsx(h,{variant:"body2",color:"text.secondary",children:"Overall Performance"}),t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:.5},children:[C&&t.jsx(C,{sx:{fontSize:16,color:S(e.performanceTrend).color}}),t.jsxs(h,{variant:"body2",sx:{fontWeight:600,color:v(e.overallScore)},children:[e.overallScore,"%"]})]})]}),t.jsx(d,{sx:{height:6,borderRadius:3,bgcolor:o(v(e.overallScore),.2),overflow:"hidden"},children:t.jsx(d,{sx:{height:"100%",width:`${e.overallScore}%`,bgcolor:v(e.overallScore),borderRadius:3,transition:"width 0.5s ease-in-out"}})})]}),t.jsxs(d,{sx:{display:"flex",gap:1,flexWrap:"wrap",mb:2},children:[t.jsx(y,{label:`${e.attendance}% Attendance`,size:"small",color:e.attendance>=90?"success":e.attendance>=75?"warning":"error",variant:"outlined"}),e.hasActiveSWOT&&t.jsx(y,{label:"SWOT Active",size:"small",color:"primary",variant:"filled"})]}),t.jsxs(d,{sx:{display:"flex",gap:1},children:[t.jsx(u,{size:"small",variant:"outlined",startIcon:t.jsx($e,{}),onClick:t=>{t.stopPropagation(),a(e)},sx:{flex:1},children:"View"}),t.jsx(u,{size:"small",variant:"contained",startIcon:t.jsx(_e,{}),onClick:t=>{t.stopPropagation(),r(e)},sx:{flex:1},children:"SWOT"})]})]}),t.jsxs(R,{anchorEl:m,open:Boolean(m),onClose:f,onClick:e=>e.stopPropagation(),children:[t.jsxs(B,{onClick:()=>{a(e),f()},children:[t.jsx($e,{sx:{mr:1}}),"View Details"]}),t.jsxs(B,{onClick:()=>{i(e),f()},children:[t.jsx(Ue,{sx:{mr:1}}),"Edit Student"]}),t.jsxs(B,{onClick:()=>{r(e),f()},children:[t.jsx(_e,{sx:{mr:1}}),"Generate SWOT"]})]})]})})},ga=({open:e,onClose:a,filters:i,onFiltersChange:n})=>{const{t:r}=xe(["common"]);return t.jsxs(O,{open:e,onClose:a,maxWidth:"sm",fullWidth:!0,children:[t.jsx(L,{children:t.jsxs(d,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:["Filter Students",t.jsx(p,{onClick:a,size:"small",children:t.jsx(Ye,{})})]})}),t.jsx($,{children:t.jsxs(b,{container:!0,spacing:2,sx:{mt:1},children:[t.jsx(b,{item:!0,xs:12,sm:6,children:t.jsxs(A,{fullWidth:!0,label:"Class",select:!0,value:i.class||"",onChange:e=>n({...i,class:e.target.value}),children:[t.jsx(B,{value:"",children:"All Classes"}),t.jsx(B,{value:"10-A",children:"Class 10-A"}),t.jsx(B,{value:"10-B",children:"Class 10-B"}),t.jsx(B,{value:"11-Science",children:"Class 11-Science"}),t.jsx(B,{value:"12-Commerce",children:"Class 12-Commerce"})]})}),t.jsx(b,{item:!0,xs:12,sm:6,children:t.jsxs(A,{fullWidth:!0,label:"Performance",select:!0,value:i.performance||"",onChange:e=>n({...i,performance:e.target.value}),children:[t.jsx(B,{value:"",children:"All Performance"}),t.jsx(B,{value:"excellent",children:"Excellent (80%+)"}),t.jsx(B,{value:"good",children:"Good (60-79%)"}),t.jsx(B,{value:"needs-improvement",children:"Needs Improvement (<60%)"})]})}),t.jsx(b,{item:!0,xs:12,sm:6,children:t.jsxs(A,{fullWidth:!0,label:"Attendance",select:!0,value:i.attendance||"",onChange:e=>n({...i,attendance:e.target.value}),children:[t.jsx(B,{value:"",children:"All Attendance"}),t.jsx(B,{value:"excellent",children:"Excellent (90%+)"}),t.jsx(B,{value:"good",children:"Good (75-89%)"}),t.jsx(B,{value:"poor",children:"Poor (<75%)"})]})}),t.jsx(b,{item:!0,xs:12,sm:6,children:t.jsxs(A,{fullWidth:!0,label:"SWOT Status",select:!0,value:i.swotStatus||"",onChange:e=>n({...i,swotStatus:e.target.value}),children:[t.jsx(B,{value:"",children:"All Students"}),t.jsx(B,{value:"active",children:"Has Active SWOT"}),t.jsx(B,{value:"pending",children:"SWOT Pending"})]})})]})}),t.jsxs(H,{sx:{p:3},children:[t.jsx(u,{onClick:()=>n({}),children:"Clear All"}),t.jsx(u,{variant:"contained",onClick:a,children:"Apply Filters"})]})]})},ja=()=>{const{t:e}=xe(["common"]),a=n(),i=P(a.breakpoints.down("md")),[r,s]=ee.useState([]),[o,l]=ee.useState(!0),[c,x]=ee.useState(""),[p,m]=ee.useState({}),[g,j]=ee.useState(!1);ee.useEffect((()=>{const e=[{id:1,name:"Aarav Sharma",class:"10-A",rollNumber:"001",admissionNumber:"VM2024001",overallScore:85,attendance:92,performanceTrend:5,hasActiveSWOT:!0},{id:2,name:"Priya Patel",class:"10-A",rollNumber:"002",admissionNumber:"VM2024002",overallScore:78,attendance:88,performanceTrend:-2,hasActiveSWOT:!1},{id:3,name:"Arjun Kumar",class:"10-B",rollNumber:"003",admissionNumber:"VM2024003",overallScore:92,attendance:95,performanceTrend:8,hasActiveSWOT:!0},{id:4,name:"Ananya Singh",class:"11-Science",rollNumber:"004",admissionNumber:"VM2024004",overallScore:67,attendance:82,performanceTrend:3,hasActiveSWOT:!1}];setTimeout((()=>{s(e),l(!1)}),1e3)}),[]);const f=ee.useMemo((()=>r.filter((e=>{const t=e.name.toLowerCase().includes(c.toLowerCase())||e.admissionNumber.toLowerCase().includes(c.toLowerCase())||e.class.toLowerCase().includes(c.toLowerCase()),a=!p.class||e.class===p.class,i=!p.performance||"excellent"===p.performance&&e.overallScore>=80||"good"===p.performance&&e.overallScore>=60&&e.overallScore<80||"needs-improvement"===p.performance&&e.overallScore<60,n=!p.attendance||"excellent"===p.attendance&&e.attendance>=90||"good"===p.attendance&&e.attendance>=75&&e.attendance<90||"poor"===p.attendance&&e.attendance<75,r=!p.swotStatus||"active"===p.swotStatus&&e.hasActiveSWOT||"pending"===p.swotStatus&&!e.hasActiveSWOT;return t&&a&&i&&n&&r}))),[r,c,p]),y=e=>{},v=e=>{},S=e=>{},C=()=>{};return t.jsxs(d,{sx:{p:{xs:2,md:3}},children:[t.jsxs(d,{sx:{mb:4},children:[t.jsx(h,{variant:"h4",component:"h1",sx:{fontWeight:700,mb:1},children:"Student Management"}),t.jsx(h,{variant:"body1",color:"text.secondary",children:"Manage student profiles, track performance, and generate SWOT analyses"})]}),t.jsx(d,{sx:{mb:3},children:t.jsxs(b,{container:!0,spacing:2,alignItems:"center",children:[t.jsx(b,{item:!0,xs:12,md:8,children:t.jsx(A,{fullWidth:!0,placeholder:"Search students by name, ID, or class...",value:c,onChange:e=>x(e.target.value),InputProps:{startAdornment:t.jsx(W,{position:"start",children:t.jsx(we,{color:"action"})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}})}),t.jsx(b,{item:!0,xs:12,md:4,children:t.jsxs(d,{sx:{display:"flex",gap:1,justifyContent:"flex-end"},children:[t.jsx(u,{variant:"outlined",startIcon:t.jsx(Fe,{}),onClick:()=>j(!0),sx:{borderRadius:2},children:"Filter"}),t.jsx(u,{variant:"contained",startIcon:t.jsx(Ge,{}),onClick:C,sx:{borderRadius:2},children:"Add Student"})]})})]})}),t.jsx(d,{sx:{mb:3},children:t.jsxs(h,{variant:"body2",color:"text.secondary",children:["Showing ",f.length," of ",r.length," students"]})}),t.jsx(b,{container:!0,spacing:3,children:f.map((e=>t.jsx(b,{item:!0,xs:12,sm:6,lg:4,children:t.jsx(ua,{student:e,onView:y,onEdit:v,onGenerateSWOT:S})},e.id)))}),0===f.length&&!o&&t.jsxs(d,{sx:{textAlign:"center",py:8},children:[t.jsx(Ae,{sx:{fontSize:64,color:"text.disabled",mb:2}}),t.jsx(h,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"No students found"}),t.jsx(h,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Try adjusting your search criteria or add a new student"}),t.jsx(u,{variant:"contained",startIcon:t.jsx(Ge,{}),onClick:C,children:"Add First Student"})]}),i&&t.jsx(z,{color:"primary","aria-label":"add student",onClick:C,sx:{position:"fixed",bottom:24,right:24,zIndex:a.zIndex.fab},children:t.jsx(Ge,{})}),t.jsx(ga,{open:g,onClose:()=>j(!1),filters:p,onFiltersChange:m})]})},ba=({type:e,title:a,items:i,color:r,icon:o,loading:x=!1,culturalPattern:p="lotus"})=>{const m=n();return x?t.jsx(s,{sx:{height:"100%",minHeight:300},children:t.jsxs(l,{children:[t.jsx(j,{variant:"circular",width:48,height:48}),t.jsx(j,{variant:"text",width:"60%",sx:{mt:2}}),[...Array(4)].map(((e,a)=>t.jsx(j,{variant:"text",width:"90%",sx:{mt:1}},a)))]})}):t.jsx(D,{in:!0,timeout:300,children:t.jsx(s,{sx:{height:"100%",minHeight:300,background:(e=>{switch(e){case"lotus":return`radial-gradient(circle at 20% 80%, ${r}15 0%, transparent 50%), \n                radial-gradient(circle at 80% 20%, ${r}10 0%, transparent 50%)`;case"mandala":return`conic-gradient(from 0deg at 50% 50%, ${r}05, ${r}15, ${r}05)`;case"paisley":return`linear-gradient(45deg, ${r}08 25%, transparent 25%), \n                linear-gradient(-45deg, ${r}08 25%, transparent 25%)`;default:return`linear-gradient(135deg, ${r}10 0%, ${r}05 100%)`}})(p),border:`2px solid ${r}20`,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:m.shadows[8],border:`2px solid ${r}40`}},children:t.jsxs(l,{sx:{p:3},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",mb:3},children:[t.jsx(c,{sx:{bgcolor:r,width:48,height:48,mr:2},children:t.jsx(o,{})}),t.jsxs(d,{children:[t.jsx(h,{variant:"h6",sx:{fontWeight:700,color:r},children:a}),t.jsxs(h,{variant:"body2",color:"text.secondary",children:[i.length," identified areas"]})]})]}),t.jsx(d,{children:i.map(((e,a)=>t.jsx(D,{in:!0,timeout:300+100*a,children:t.jsxs(d,{sx:{p:2,mb:2,borderRadius:2,bgcolor:"background.paper",border:`1px solid ${r}20`,transition:"all 0.2s ease-in-out","&:hover":{bgcolor:`${r}05`,border:`1px solid ${r}40`}},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",mb:1},children:[t.jsx(d,{sx:{width:8,height:8,borderRadius:"50%",bgcolor:r,mr:1}}),t.jsx(h,{variant:"body2",sx:{fontWeight:600},children:e.title}),e.priority&&t.jsx(y,{label:e.priority,size:"small",sx:{ml:"auto",fontSize:"0.75rem"},color:"High"===e.priority?"error":"Medium"===e.priority?"warning":"default"})]}),t.jsx(h,{variant:"caption",color:"text.secondary",children:e.description}),e.culturalContext&&t.jsxs(h,{variant:"caption",sx:{display:"block",mt:.5,fontStyle:"italic",color:r},children:["Cultural Context: ",e.culturalContext]})]})},a)))})]})})})},fa=({studentId:e,boardType:a="CBSE"})=>{const i=n();P(i.breakpoints.down("md"));const[r,s]=ee.useState(!0),[o,l]=ee.useState({}),[x,p]=ee.useState(null);ee.useEffect((()=>{(async()=>{s(!0),await new Promise((e=>setTimeout(e,1500)));l({strengths:[{title:"Strong Mathematical Foundation",description:"Excellent performance in algebra and geometry",priority:"High",culturalContext:"Vedic mathematics influence"},{title:"Hindi Language Proficiency",description:"Native speaker with excellent writing skills",priority:"High",culturalContext:"Mother tongue advantage"},{title:"Cultural Values Integration",description:"Strong moral and ethical foundation",priority:"Medium",culturalContext:"Family and community values"},{title:"Collaborative Learning",description:"Works well in group projects and team activities",priority:"Medium",culturalContext:"Community-oriented upbringing"}],weaknesses:[{title:"English Communication",description:"Needs improvement in spoken English confidence",priority:"High",culturalContext:"Regional language dominance"},{title:"Technology Adaptation",description:"Slower adoption of digital learning tools",priority:"Medium",culturalContext:"Traditional learning methods preference"},{title:"Time Management",description:"Struggles with deadline management",priority:"Medium",culturalContext:"Flexible time concept in culture"}],opportunities:[{title:"Competitive Exam Preparation",description:"Strong foundation for JEE/NEET preparation",priority:"High",culturalContext:"Engineering/Medical career aspirations"},{title:"Multilingual Advantage",description:"Can leverage multiple language skills",priority:"High",culturalContext:"Diverse linguistic environment"},{title:"Cultural Leadership",description:"Can lead cultural and festival activities",priority:"Medium",culturalContext:"Rich cultural heritage knowledge"}],threats:[{title:"Urban Competition",description:"Intense competition from metro city students",priority:"High",culturalContext:"Resource disparity between regions"},{title:"Digital Divide",description:"Limited access to advanced technology",priority:"Medium",culturalContext:"Infrastructure limitations"},{title:"Career Pressure",description:"Family expectations for traditional careers",priority:"Medium",culturalContext:"Societal career preferences"}]}),p({name:"Arjun Sharma",class:"Class X-A",board:a,rollNumber:"CB2024001"}),s(!1)})()}),[e,a]);const m=[{type:"strengths",title:"Strengths (शक्तियाँ)",color:i.palette.success.main,icon:ye,pattern:"lotus",items:o.strengths||[]},{type:"weaknesses",title:"Weaknesses (कमजोरियाँ)",color:i.palette.error.main,icon:Je,pattern:"mandala",items:o.weaknesses||[]},{type:"opportunities",title:"Opportunities (अवसर)",color:i.palette.primary.main,icon:We,pattern:"paisley",items:o.opportunities||[]},{type:"threats",title:"Threats (चुनौतियाँ)",color:i.palette.warning.main,icon:Ke,pattern:"lotus",items:o.threats||[]}];return t.jsxs(d,{sx:{p:{xs:2,md:3}},children:[t.jsxs(d,{sx:{mb:4},children:[t.jsx(h,{variant:"h4",component:"h1",sx:{fontWeight:700,mb:1},children:"Cultural SWOT Analysis"}),x&&t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[t.jsx(c,{sx:{bgcolor:"primary.main"},children:x.name.charAt(0)}),t.jsxs(d,{children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600},children:x.name}),t.jsxs(h,{variant:"body2",color:"text.secondary",children:[x.class," • ",x.board," Board • Roll: ",x.rollNumber]})]})]}),t.jsx(h,{variant:"body1",color:"text.secondary",children:"Comprehensive analysis with Indian educational and cultural context"})]}),t.jsx(b,{container:!0,spacing:3,children:m.map((e=>t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(ba,{type:e.type,title:e.title,items:e.items,color:e.color,icon:e.icon,culturalPattern:e.pattern,loading:r})},e.type)))})]})};yt.register(Bt,Ct,wt,Pt,At,Wt);const ya=({performanceData:e,loading:a})=>{if(a)return t.jsx(d,{sx:{height:300,display:"flex",alignItems:"center",justifyContent:"center"},children:t.jsx(m,{sx:{width:"80%"}})});return t.jsx(d,{sx:{height:300},children:t.jsx(zt,{data:{labels:["Math","English","Science","Geography","History","Art","PE"],datasets:[{label:"Student Performance",data:[87,92,95,75,78,96,72],backgroundColor:"rgba(54, 162, 235, 0.2)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:2,pointBackgroundColor:"rgba(54, 162, 235, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(54, 162, 235, 1)"},{label:"Class Average",data:[75,78,80,72,74,82,76],backgroundColor:"rgba(255, 99, 132, 0.2)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:2,pointBackgroundColor:"rgba(255, 99, 132, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 99, 132, 1)"}]},options:{responsive:!0,maintainAspectRatio:!1,scales:{r:{angleLines:{display:!0},suggestedMin:0,suggestedMax:100,ticks:{stepSize:20}}},plugins:{legend:{position:"bottom"}}}})})},va=({title:e,items:a,color:i,icon:r})=>(n(),t.jsx(s,{sx:{height:"100%",border:`2px solid ${i}`},children:t.jsxs(l,{children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[t.jsx(r,{sx:{color:i}}),t.jsx(h,{variant:"h6",sx:{fontWeight:600,color:i},children:e})]}),t.jsx(N,{dense:!0,children:a.map(((e,a)=>t.jsx(V,{sx:{px:0},children:t.jsx(_,{primary:t.jsxs(h,{variant:"body2",sx:{fontWeight:500},children:["• ",e]})})},a)))})]})})),Sa=({attendanceData:e,loading:a})=>{if(a)return t.jsx(d,{sx:{height:200,display:"flex",alignItems:"center",justifyContent:"center"},children:t.jsx(m,{sx:{width:"80%"}})});const i=Array.from({length:30},((e,t)=>({day:t+1,status:Math.random()>.05?"present":Math.random()>.5?"absent":"late"}))),n=e=>{switch(e){case"present":return"#4CAF50";case"absent":return"#F44336";case"late":return"#FF9800";default:return"#E0E0E0"}};return t.jsxs(d,{children:[t.jsx(h,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Attendance"}),t.jsx(d,{sx:{display:"grid",gridTemplateColumns:"repeat(7, 1fr)",gap:1,mb:2},children:i.map((e=>t.jsx(r,{title:`Day ${e.day}: ${e.status}`,children:t.jsx(d,{sx:{width:32,height:32,borderRadius:1,backgroundColor:n(e.status),display:"flex",alignItems:"center",justifyContent:"center",fontSize:"0.75rem",color:"white",fontWeight:500,cursor:"pointer"},children:e.day})},e.day)))}),t.jsxs(d,{sx:{display:"flex",flexDirection:"column",gap:1},children:[t.jsxs(h,{variant:"body2",children:[t.jsx("strong",{children:"Present:"})," 43 days (95.6%)"]}),t.jsxs(h,{variant:"body2",children:[t.jsx("strong",{children:"Absent:"})," 2 days"]}),t.jsxs(h,{variant:"body2",children:[t.jsx("strong",{children:"Tardy:"})," 1 day"]})]})]})},Ca=({behaviorData:e,loading:a})=>{if(a)return t.jsx(d,{sx:{height:200,display:"flex",alignItems:"center",justifyContent:"center"},children:t.jsx(m,{sx:{width:"80%"}})});return t.jsxs(d,{children:[t.jsx(h,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Behavior"}),t.jsxs(d,{sx:{position:"relative",height:60,mb:2},children:[t.jsx(d,{sx:{position:"absolute",top:"50%",left:0,right:0,height:2,backgroundColor:"#E0E0E0",transform:"translateY(-50%)"}}),[{date:5,type:"positive",description:"Helped new student"},{date:10,type:"positive",description:"Excellent project presentation"},{date:15,type:"negative",description:"Late to class"},{date:20,type:"positive",description:"Volunteered for cleanup"}].map(((e,a)=>t.jsx(r,{title:e.description,children:t.jsx(d,{sx:{position:"absolute",left:e.date/30*100+"%",top:"50%",transform:"translate(-50%, -50%)",width:16,height:16,borderRadius:"50%",backgroundColor:"positive"===e.type?"#4CAF50":"#F44336",cursor:"pointer","&:hover":{transform:"translate(-50%, -50%) scale(1.2)"},transition:"transform 0.2s ease-in-out"}})},a))),t.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",mt:3},children:[t.jsx(h,{variant:"caption",children:"1"}),t.jsx(h,{variant:"caption",children:"10"}),t.jsx(h,{variant:"caption",children:"20"}),t.jsx(h,{variant:"caption",children:"30"})]})]}),t.jsxs(d,{sx:{display:"flex",flexDirection:"column",gap:1},children:[t.jsxs(h,{variant:"body2",children:[t.jsx("strong",{children:"Positive Incidents:"})," 3"]}),t.jsxs(h,{variant:"body2",children:[t.jsx("strong",{children:"Negative Incidents:"})," 1"]}),t.jsxs(h,{variant:"body2",children:[t.jsx("strong",{children:"Trend:"})," ",t.jsx("span",{style:{color:"#4CAF50"},children:"Improving"})]})]})]})},wa=({activitiesData:e,loading:a})=>{if(a)return t.jsx(d,{sx:{height:150,display:"flex",alignItems:"center",justifyContent:"center"},children:t.jsx(m,{sx:{width:"80%"}})});return t.jsxs(d,{children:[t.jsx(h,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Extracurricular Activities"}),t.jsx(f,{spacing:2,children:[{name:"Chess Club",hours:2,attendance:100},{name:"School Newspaper",hours:3,attendance:92}].map(((e,a)=>t.jsx(s,{variant:"outlined",children:t.jsxs(l,{sx:{py:2},children:[t.jsx(h,{variant:"body1",sx:{fontWeight:500},children:e.name}),t.jsxs(h,{variant:"body2",color:"text.secondary",children:[e.hours," hrs/week, ",e.attendance,"% attendance"]})]})},a)))})]})},Fa=({recommendations:e,loading:a})=>{if(a)return t.jsx(d,{sx:{height:150,display:"flex",alignItems:"center",justifyContent:"center"},children:t.jsx(m,{sx:{width:"80%"}})});return t.jsx(s,{children:t.jsxs(l,{children:[t.jsx(h,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Recommendations"}),t.jsx(N,{children:["Consider Math Olympiad to strengthen skills","Monitor History performance - offer additional resources","Encourage consistent PE participation"].map(((e,a)=>t.jsx(V,{sx:{px:0},children:t.jsx(_,{primary:t.jsxs(h,{variant:"body2",children:["• ",e]})})},a)))})]})})},ka=()=>{const{t:e}=xe(["swot","common"]);n();const a=ie(),{studentId:i}=ne(),[r,o]=ee.useState(!0),[c,x]=ee.useState({name:"Jane Doe",grade:9,id:"STU12345",quarter:"Q1 2024-2025",gpa:3.7,classRank:8,totalStudents:28,subjects:[{name:"Math",grade:"B+",score:87},{name:"English",grade:"A-",score:92},{name:"Science",grade:"A",score:95},{name:"Geography",grade:"C",score:75},{name:"History",grade:"C+",score:78},{name:"Art",grade:"A",score:96},{name:"PE",grade:"C-",score:72}],swotData:{strengths:["Math","Science","Art"],weaknesses:["Geography","History","PE"],opportunities:["Join Science Club","Math tutoring"],threats:["Attendance pattern in History class","Declining PE scores"]}});ee.useEffect((()=>{(async()=>{o(!0),await new Promise((e=>setTimeout(e,1500))),o(!1)})()}),[i]);return t.jsxs(d,{sx:{p:3},children:[t.jsxs(d,{sx:{mb:3},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2},children:[t.jsx(p,{onClick:()=>{a("/dashboard/students")},children:t.jsx(Xe,{})}),t.jsxs(h,{variant:"h4",sx:{fontWeight:700},children:["Student: ",c.name]})]}),t.jsx(d,{sx:{display:"flex",gap:1,alignItems:"center"},children:t.jsx(p,{children:t.jsx(we,{})})})]}),t.jsxs(d,{sx:{display:"flex",gap:2,alignItems:"center",mb:2},children:[t.jsx(y,{label:`Grade: ${c.grade}`,color:"primary"}),t.jsx(y,{label:`ID: ${c.id}`,variant:"outlined"}),t.jsx(y,{label:c.quarter,variant:"outlined"})]})]}),t.jsxs(b,{container:!0,spacing:3,children:[t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(s,{sx:{mb:3},children:t.jsxs(l,{children:[t.jsx(h,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Academic Performance"}),t.jsx(ya,{loading:r}),t.jsxs(d,{sx:{mt:3},children:[c.subjects.map(((e,a)=>t.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[t.jsxs(h,{variant:"body2",children:[e.name,":"]}),t.jsxs(h,{variant:"body2",sx:{fontWeight:500},children:[e.grade," (",e.score,"%)"]})]},a))),t.jsx(k,{sx:{my:2}}),t.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[t.jsx(h,{variant:"body1",sx:{fontWeight:600},children:"GPA:"}),t.jsx(h,{variant:"body1",sx:{fontWeight:600},children:c.gpa})]}),t.jsxs(d,{sx:{display:"flex",justifyContent:"space-between"},children:[t.jsx(h,{variant:"body1",sx:{fontWeight:600},children:"Class Rank:"}),t.jsxs(h,{variant:"body1",sx:{fontWeight:600},children:[c.classRank,"/",c.totalStudents]})]})]})]})})}),t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(s,{sx:{mb:3},children:t.jsxs(l,{children:[t.jsx(h,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"SWOT Analysis"}),t.jsxs(b,{container:!0,spacing:2,children:[t.jsx(b,{item:!0,xs:6,children:t.jsx(va,{title:"Strengths",items:c.swotData.strengths,color:"#4CAF50",icon:Pe})}),t.jsx(b,{item:!0,xs:6,children:t.jsx(va,{title:"Weaknesses",items:c.swotData.weaknesses,color:"#FF5722",icon:Je})}),t.jsx(b,{item:!0,xs:6,children:t.jsx(va,{title:"Opportunities",items:c.swotData.opportunities,color:"#2196F3",icon:We})}),t.jsx(b,{item:!0,xs:6,children:t.jsx(va,{title:"Threats",items:c.swotData.threats,color:"#FF9800",icon:qe})})]})]})})}),t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(s,{sx:{height:"100%"},children:t.jsx(l,{children:t.jsx(Sa,{loading:r})})})}),t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(s,{sx:{height:"100%"},children:t.jsx(l,{children:t.jsx(Ca,{loading:r})})})}),t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(s,{children:t.jsx(l,{children:t.jsx(wa,{loading:r})})})}),t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(Fa,{loading:r})})]}),t.jsxs(d,{sx:{mt:3,display:"flex",gap:2,justifyContent:"flex-end"},children:[t.jsx(u,{variant:"outlined",startIcon:t.jsx(Qe,{}),sx:{textTransform:"none"},children:"Download Report"}),t.jsx(u,{variant:"outlined",startIcon:t.jsx(Ze,{}),sx:{textTransform:"none"},children:"Share"}),t.jsx(u,{variant:"outlined",startIcon:t.jsx(et,{}),sx:{textTransform:"none"},children:"Print"}),t.jsx(u,{variant:"contained",startIcon:t.jsx(ye,{}),sx:{textTransform:"none"},children:"Generate New Analysis"})]})]})},Aa=({threshold:e=.1,root:t=null,rootMargin:a="0px",once:i=!0,enabled:n=!0})=>{const[r,s]=ee.useState(!1),[o,l]=ee.useState(!1),d=ee.useRef(null),c=ee.useRef(null);return ee.useEffect((()=>{if(!n)return;const r=d.current;if(!r)return;if(!window.IntersectionObserver)return s(!0),void l(!0);return c.current=new IntersectionObserver((e=>{const[t]=e,a=t.isIntersecting;s(a),a&&!o&&(l(!0),i&&c.current&&c.current.disconnect())}),{threshold:Array.isArray(e)?e:[e],root:t,rootMargin:a}),c.current.observe(r),()=>{c.current&&c.current.disconnect()}}),[e,t,a,i,n,o]),[d,i?o:r,o]},Wa=()=>{const e=ie(),a=n(),i=P(a.breakpoints.down("md")),[r,s]=ee.useState(!1),o=()=>{s(!r)},l=[{label:"Home",path:"/"},{label:"About",path:"/about"},{label:"Features",path:"/features"},{label:"Contact",path:"/contact"}],c=t.jsxs(d,{onClick:o,sx:{textAlign:"center"},children:[t.jsx(h,{variant:"h6",sx:{my:2,fontWeight:700},children:"VidyaMitra"}),t.jsxs(N,{children:[l.map((a=>t.jsx(V,{disablePadding:!0,children:t.jsx(u,{fullWidth:!0,onClick:()=>e(a.path),sx:{textAlign:"center",py:1},children:t.jsx(_,{primary:a.label})})},a.label))),t.jsx(V,{disablePadding:!0,children:t.jsx(u,{fullWidth:!0,variant:"contained",onClick:()=>e("/login"),sx:{m:2},children:"Login"})})]})]});return t.jsxs(t.Fragment,{children:[t.jsx(v,{position:"fixed",sx:{bgcolor:"rgba(255, 255, 255, 0.95)",backdropFilter:"blur(10px)",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",color:"text.primary"},children:t.jsxs(S,{children:[t.jsx(h,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:700,color:a.palette.primary.main,cursor:"pointer"},onClick:()=>e("/"),children:"VidyaMitra"}),i?t.jsx(p,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:o,children:t.jsx(tt,{})}):t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2},children:[l.map((a=>t.jsx(u,{onClick:()=>e(a.path),sx:{color:"text.primary",fontWeight:500,"&:hover":{bgcolor:"rgba(46, 91, 168, 0.1)"}},children:a.label},a.label))),t.jsx(u,{variant:"contained",onClick:()=>e("/login"),sx:{ml:2,borderRadius:2,px:3},children:"Login"})]})]})}),t.jsx(G,{variant:"temporary",open:r,onClose:o,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",md:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:240}},children:c})]})},Ia=()=>{const e=ie(),a=n(),[i,r]=Aa({threshold:.1});return t.jsx(d,{ref:i,sx:{minHeight:"100vh",background:`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.secondary.main} 100%)`,display:"flex",alignItems:"center",position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")'}},children:t.jsx(I,{maxWidth:"lg",sx:{position:"relative",zIndex:1},children:t.jsxs(b,{container:!0,spacing:4,alignItems:"center",children:[t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(D,{in:r,timeout:1e3,children:t.jsxs(d,{children:[t.jsx(h,{variant:"h1",sx:{color:"white",fontWeight:800,mb:3,textShadow:"0 2px 4px rgba(0,0,0,0.3)",fontSize:{xs:"2.5rem",md:"3.5rem",lg:"4rem"}},children:"VidyaMitra"}),t.jsx(h,{variant:"h4",sx:{color:"rgba(255,255,255,0.9)",mb:3,fontWeight:400,fontSize:{xs:"1.2rem",md:"1.5rem"}},children:"Student SWOT Analysis Platform"}),t.jsx(h,{variant:"h6",sx:{color:"rgba(255,255,255,0.8)",mb:4,lineHeight:1.6,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Empowering Indian education through intelligent student analysis. Supporting CBSE, ICSE, and State boards with AI-powered insights."}),t.jsxs(d,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[t.jsx(u,{variant:"contained",size:"large",onClick:()=>e("/login"),sx:{bgcolor:"white",color:a.palette.primary.main,px:4,py:1.5,fontSize:"1.1rem",fontWeight:600,borderRadius:3,boxShadow:"0 4px 12px rgba(0,0,0,0.2)","&:hover":{bgcolor:"rgba(255,255,255,0.9)",transform:"translateY(-2px)",boxShadow:"0 6px 16px rgba(0,0,0,0.3)"}},endIcon:t.jsx(Ne,{}),children:"Get Started"}),t.jsx(u,{variant:"outlined",size:"large",onClick:()=>e("/features"),sx:{color:"white",borderColor:"white",px:4,py:1.5,fontSize:"1.1rem",fontWeight:600,borderRadius:3,"&:hover":{bgcolor:"rgba(255,255,255,0.1)",borderColor:"white",transform:"translateY(-2px)"}},children:"Learn More"})]})]})})}),t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(U,{direction:"left",in:r,timeout:1200,children:t.jsx(d,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:{xs:300,md:400}},children:t.jsx(Y,{elevation:8,sx:{p:4,borderRadius:4,bgcolor:"rgba(255,255,255,0.95)",backdropFilter:"blur(10px)",maxWidth:400,width:"100%"},children:t.jsxs(d,{sx:{textAlign:"center"},children:[t.jsx(c,{sx:{width:80,height:80,bgcolor:a.palette.secondary.main,mx:"auto",mb:2},children:t.jsx(Ce,{sx:{fontSize:40}})}),t.jsx(h,{variant:"h6",sx:{mb:2,fontWeight:600},children:"Comprehensive Student Analysis"}),t.jsx(h,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"AI-powered SWOT analysis tailored for Indian educational boards"}),t.jsxs(d,{sx:{display:"flex",justifyContent:"space-around"},children:[t.jsxs(d,{sx:{textAlign:"center"},children:[t.jsx(h,{variant:"h4",color:"primary",sx:{fontWeight:700},children:"1000+"}),t.jsx(h,{variant:"caption",children:"Students"})]}),t.jsxs(d,{sx:{textAlign:"center"},children:[t.jsx(h,{variant:"h4",color:"secondary",sx:{fontWeight:700},children:"50+"}),t.jsx(h,{variant:"caption",children:"Schools"})]}),t.jsxs(d,{sx:{textAlign:"center"},children:[t.jsx(h,{variant:"h4",color:"success.main",sx:{fontWeight:700},children:"95%"}),t.jsx(h,{variant:"caption",children:"Satisfaction"})]})]})]})})})})})]})})})},Ma=()=>{const e=n(),[a,i]=Aa({threshold:.1}),r=[{icon:_e,title:"AI-Powered SWOT Analysis",description:"Comprehensive analysis of student strengths, weaknesses, opportunities, and threats using advanced AI algorithms.",color:e.palette.primary.main},{icon:Ce,title:"Multi-Board Support",description:"Full support for CBSE, ICSE, and all major State boards with curriculum-specific insights.",color:e.palette.secondary.main},{icon:We,title:"Performance Tracking",description:"Real-time monitoring of student progress with detailed analytics and trend analysis.",color:e.palette.success.main},{icon:Ve,title:"Multi-Stakeholder Access",description:"Dedicated interfaces for teachers, parents, administrators, and students with role-based permissions.",color:e.palette.warning.main}];return t.jsx(d,{ref:a,sx:{py:8,bgcolor:"background.default"},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(D,{in:i,timeout:800,children:t.jsxs(d,{sx:{textAlign:"center",mb:6},children:[t.jsx(h,{variant:"h3",sx:{fontWeight:700,mb:2,color:"text.primary"},children:"Powerful Features for Indian Education"}),t.jsx(h,{variant:"h6",color:"text.secondary",sx:{maxWidth:600,mx:"auto"},children:"Designed specifically for the Indian education system with cultural sensitivity and local requirements in mind."})]})}),t.jsx(b,{container:!0,spacing:4,children:r.map(((a,n)=>t.jsx(b,{item:!0,xs:12,sm:6,md:3,children:t.jsx(q,{in:i,timeout:800+200*n,children:t.jsxs(s,{sx:{height:"100%",textAlign:"center",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:e.shadows[8]}},children:[t.jsx(c,{sx:{width:64,height:64,bgcolor:a.color,mx:"auto",mb:2},children:t.jsx(a.icon,{sx:{fontSize:32}})}),t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:a.title}),t.jsx(h,{variant:"body2",color:"text.secondary",children:a.description})]})})},n)))})]})})},Ea=()=>{const[e,a]=Aa({threshold:.1});return t.jsx(d,{ref:e,sx:{py:8,bgcolor:"primary.main",color:"white"},children:t.jsx(I,{maxWidth:"lg",children:t.jsxs(b,{container:!0,spacing:6,alignItems:"center",children:[t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(D,{in:a,timeout:800,children:t.jsxs(d,{children:[t.jsx(h,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Our Vision"}),t.jsx(h,{variant:"h6",sx:{mb:4,lineHeight:1.6,opacity:.9},children:"To revolutionize Indian education by providing intelligent, data-driven insights that help every student reach their full potential while respecting cultural values and educational traditions."}),t.jsxs(d,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[t.jsx(y,{label:"AI-Powered",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),t.jsx(y,{label:"Culturally Sensitive",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),t.jsx(y,{label:"Student-Centric",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}})]})]})})}),t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(U,{direction:"left",in:a,timeout:1e3,children:t.jsxs(d,{children:[t.jsx(h,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Our Mission"}),t.jsx(h,{variant:"h6",sx:{mb:4,lineHeight:1.6,opacity:.9},children:"Empowering teachers, parents, and administrators with comprehensive SWOT analysis tools that provide actionable insights for student development across all Indian educational boards."}),t.jsxs(d,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[t.jsx(y,{label:"Multi-Board Support",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),t.jsx(y,{label:"Real-time Analytics",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),t.jsx(y,{label:"Collaborative Platform",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}})]})]})})})]})})})},Ta=()=>{const e=n(),[a,i]=Aa({threshold:.1}),r=[{title:"For Teachers",description:"Generate comprehensive SWOT reports, track student progress, and identify areas for improvement with AI-powered insights.",features:["Automated SWOT Generation","Progress Tracking","Parent Communication","Class Analytics"],color:e.palette.primary.main,icon:"👩‍🏫"},{title:"For Parents",description:"Stay informed about your child's academic journey with detailed reports and actionable recommendations.",features:["Student Reports","Performance Trends","Improvement Suggestions","Meeting Scheduling"],color:e.palette.secondary.main,icon:"👨‍👩‍👧‍👦"},{title:"For Administrators",description:"Manage school-wide analytics, monitor teacher effectiveness, and make data-driven decisions.",features:["School Analytics","Teacher Performance","Resource Planning","Compliance Reports"],color:e.palette.success.main,icon:"🏫"}];return t.jsx(d,{ref:a,sx:{py:8,bgcolor:"background.paper"},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(D,{in:i,timeout:800,children:t.jsxs(d,{sx:{textAlign:"center",mb:6},children:[t.jsx(h,{variant:"h3",sx:{fontWeight:700,mb:2,color:"text.primary"},children:"Designed for Every Stakeholder"}),t.jsx(h,{variant:"h6",color:"text.secondary",sx:{maxWidth:600,mx:"auto"},children:"VidyaMitra serves the entire educational ecosystem with tailored solutions for each user type."})]})}),t.jsx(b,{container:!0,spacing:4,children:r.map(((a,n)=>t.jsx(b,{item:!0,xs:12,md:4,children:t.jsx(q,{in:i,timeout:800+200*n,children:t.jsxs(s,{sx:{height:"100%",p:4,transition:"all 0.3s ease-in-out",border:"2px solid transparent","&:hover":{transform:"translateY(-8px)",boxShadow:e.shadows[12],borderColor:a.color}},children:[t.jsxs(d,{sx:{textAlign:"center",mb:3},children:[t.jsx(h,{variant:"h2",sx:{mb:1},children:a.icon}),t.jsx(h,{variant:"h5",sx:{fontWeight:600,color:a.color},children:a.title})]}),t.jsx(h,{variant:"body1",sx:{mb:3,lineHeight:1.6},children:a.description}),t.jsx(d,{children:a.features.map(((e,i)=>t.jsxs(d,{sx:{display:"flex",alignItems:"center",mb:1},children:[t.jsx(at,{sx:{fontSize:16,color:a.color,mr:1}}),t.jsx(h,{variant:"body2",children:e})]},i)))})]})})},n)))})]})})},Ba=()=>{const e=ie();return t.jsx(d,{sx:{bgcolor:"grey.900",color:"white",py:6},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsxs(b,{container:!0,spacing:4,children:[t.jsxs(b,{item:!0,xs:12,md:4,children:[t.jsx(h,{variant:"h6",sx:{fontWeight:700,mb:2},children:"VidyaMitra"}),t.jsx(h,{variant:"body2",sx:{mb:2,opacity:.8},children:"Empowering Indian education through intelligent student analysis. Supporting CBSE, ICSE, and State boards with AI-powered insights."}),t.jsxs(d,{sx:{display:"flex",gap:1},children:[t.jsx(y,{label:"AI-Powered",size:"small",sx:{bgcolor:"rgba(255,255,255,0.1)",color:"white"}}),t.jsx(y,{label:"Multi-Board",size:"small",sx:{bgcolor:"rgba(255,255,255,0.1)",color:"white"}})]})]}),t.jsxs(b,{item:!0,xs:12,sm:6,md:2,children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Platform"}),t.jsxs(d,{sx:{display:"flex",flexDirection:"column",gap:1},children:[t.jsx(u,{color:"inherit",sx:{justifyContent:"flex-start",p:0},onClick:()=>e("/features"),children:"Features"}),t.jsx(u,{color:"inherit",sx:{justifyContent:"flex-start",p:0},onClick:()=>e("/about"),children:"About"}),t.jsx(u,{color:"inherit",sx:{justifyContent:"flex-start",p:0},onClick:()=>e("/login"),children:"Login"})]})]}),t.jsxs(b,{item:!0,xs:12,sm:6,md:3,children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Support"}),t.jsxs(d,{sx:{display:"flex",flexDirection:"column",gap:1},children:[t.jsx(u,{color:"inherit",sx:{justifyContent:"flex-start",p:0},onClick:()=>e("/contact"),children:"Contact Us"}),t.jsx(u,{color:"inherit",sx:{justifyContent:"flex-start",p:0},children:"Help Center"}),t.jsx(u,{color:"inherit",sx:{justifyContent:"flex-start",p:0},children:"Documentation"})]})]}),t.jsxs(b,{item:!0,xs:12,md:3,children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Contact Info"}),t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[t.jsx(Oe,{sx:{fontSize:16}}),t.jsx(h,{variant:"body2",children:"<EMAIL>"})]}),t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[t.jsx(it,{sx:{fontSize:16}}),t.jsx(h,{variant:"body2",children:"+91 98765 43210"})]}),t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[t.jsx(nt,{sx:{fontSize:16}}),t.jsx(h,{variant:"body2",children:"Mumbai, India"})]})]})]}),t.jsx(d,{sx:{borderTop:"1px solid rgba(255,255,255,0.1)",mt:4,pt:3,textAlign:"center"},children:t.jsx(h,{variant:"body2",sx:{opacity:.7},children:"© 2024 VidyaMitra. All rights reserved. | Privacy Policy | Terms of Service"})})]})})};function Pa(){return t.jsxs(d,{children:[t.jsx(Wa,{}),t.jsxs(d,{sx:{pt:8},children:[" ",t.jsx(Ia,{}),t.jsx(Ma,{}),t.jsx(Ea,{}),t.jsx(Ta,{})]}),t.jsx(Ba,{})]})}const za=()=>{const{t:e}=xe("common"),a=ie(),i=n(),r=[{icon:Ce,title:"Educational Excellence",description:"Committed to enhancing the quality of education through data-driven insights and personalized learning approaches.",color:i.palette.primary.main},{icon:ye,title:"AI-Powered Innovation",description:"Leveraging cutting-edge artificial intelligence to provide meaningful and actionable student analysis.",color:i.palette.secondary.main},{icon:rt,title:"Collaborative Approach",description:"Bringing together teachers, parents, and administrators in a unified platform for student success.",color:i.palette.success.main},{icon:We,title:"Continuous Improvement",description:"Constantly evolving our platform based on user feedback and educational best practices.",color:i.palette.warning.main}];return t.jsxs(d,{children:[t.jsx(d,{sx:{bgcolor:"primary.main",color:"white",py:8},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(u,{startIcon:t.jsx(Xe,{}),onClick:()=>a("/"),sx:{color:"white",mb:4},children:"Back to Home"}),t.jsx(h,{variant:"h2",sx:{fontWeight:700,mb:3},children:"About VidyaMitra"}),t.jsx(h,{variant:"h5",sx:{opacity:.9,maxWidth:800},children:"Revolutionizing Indian education through intelligent student analysis and comprehensive SWOT insights tailored for CBSE, ICSE, and State boards."})]})}),t.jsx(d,{sx:{py:8,bgcolor:"background.default"},children:t.jsx(I,{maxWidth:"lg",children:t.jsxs(b,{container:!0,spacing:6,children:[t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(D,{in:!0,timeout:800,children:t.jsxs(Y,{elevation:2,sx:{p:4,height:"100%"},children:[t.jsx(h,{variant:"h4",sx:{fontWeight:600,mb:3,color:"primary.main"},children:"Our Mission"}),t.jsx(h,{variant:"body1",sx:{lineHeight:1.8,mb:3},children:"To empower educators, parents, and administrators with comprehensive SWOT analysis tools that provide actionable insights for student development across all Indian educational boards."}),t.jsx(h,{variant:"body1",sx:{lineHeight:1.8},children:"We believe every student has unique strengths and potential. Our platform helps identify these strengths while addressing weaknesses through personalized recommendations and data-driven strategies."})]})})}),t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(D,{in:!0,timeout:1e3,children:t.jsxs(Y,{elevation:2,sx:{p:4,height:"100%"},children:[t.jsx(h,{variant:"h4",sx:{fontWeight:600,mb:3,color:"secondary.main"},children:"Our Vision"}),t.jsx(h,{variant:"body1",sx:{lineHeight:1.8,mb:3},children:"To become the leading platform for student analysis in India, helping create a generation of well-rounded individuals who are prepared for the challenges of the 21st century."}),t.jsx(h,{variant:"body1",sx:{lineHeight:1.8},children:"We envision a future where every student receives personalized attention and guidance, leading to improved academic outcomes and holistic development."})]})})})]})})}),t.jsx(d,{sx:{py:8,bgcolor:"background.paper"},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(h,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Our Core Values"}),t.jsx(b,{container:!0,spacing:4,children:r.map(((e,a)=>t.jsx(b,{item:!0,xs:12,sm:6,md:3,children:t.jsx(D,{in:!0,timeout:800+200*a,children:t.jsxs(s,{sx:{height:"100%",textAlign:"center",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:i.shadows[8]}},children:[t.jsx(c,{sx:{width:64,height:64,bgcolor:e.color,mx:"auto",mb:2},children:t.jsx(e.icon,{sx:{fontSize:32}})}),t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:e.title}),t.jsx(h,{variant:"body2",color:"text.secondary",children:e.description})]})})},a)))})]})}),t.jsx(d,{sx:{py:8,bgcolor:"primary.main",color:"white"},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(h,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Our Impact"}),t.jsx(b,{container:!0,spacing:4,children:[{number:"1000+",label:"Students Analyzed"},{number:"50+",label:"Partner Schools"},{number:"95%",label:"User Satisfaction"},{number:"3",label:"Educational Boards"}].map(((e,a)=>t.jsx(b,{item:!0,xs:6,md:3,children:t.jsx(D,{in:!0,timeout:800+200*a,children:t.jsxs(d,{sx:{textAlign:"center"},children:[t.jsx(h,{variant:"h2",sx:{fontWeight:800,mb:1},children:e.number}),t.jsx(h,{variant:"h6",sx:{opacity:.9},children:e.label})]})})},a)))})]})}),t.jsx(d,{sx:{py:8,bgcolor:"background.default",textAlign:"center"},children:t.jsxs(I,{maxWidth:"md",children:[t.jsx(h,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Ready to Transform Education?"}),t.jsx(h,{variant:"h6",color:"text.secondary",sx:{mb:4},children:"Join thousands of educators who are already using VidyaMitra to enhance student outcomes."}),t.jsxs(d,{sx:{display:"flex",gap:2,justifyContent:"center",flexWrap:"wrap"},children:[t.jsx(u,{variant:"contained",size:"large",onClick:()=>a("/login"),sx:{px:4,py:1.5},children:"Get Started Today"}),t.jsx(u,{variant:"outlined",size:"large",onClick:()=>a("/contact"),sx:{px:4,py:1.5},children:"Contact Us"})]})]})})]})},Da=()=>{const{t:e}=xe("common"),a=ie(),i=n(),r=[{icon:_e,title:"AI-Powered SWOT Analysis",description:"Advanced artificial intelligence algorithms analyze student data to generate comprehensive SWOT reports.",features:["Automated strength identification","Weakness pattern recognition","Opportunity mapping","Threat assessment","Personalized recommendations"],color:i.palette.primary.main},{icon:Ce,title:"Multi-Board Support",description:"Complete support for CBSE, ICSE, and all major State educational boards across India.",features:["CBSE curriculum alignment","ICSE standards compliance","State board customization","Regional language support","Board-specific analytics"],color:i.palette.secondary.main},{icon:We,title:"Performance Tracking",description:"Real-time monitoring and analysis of student academic and behavioral performance.",features:["Grade tracking","Attendance monitoring","Behavioral analysis","Progress visualization","Trend identification"],color:i.palette.success.main},{icon:Ve,title:"Multi-Stakeholder Platform",description:"Dedicated interfaces for teachers, parents, administrators, and students.",features:["Teacher dashboard","Parent portal","Admin analytics","Student interface","Role-based permissions"],color:i.palette.warning.main}],o=[{icon:st,title:"Multi-Language Support",description:"Interface available in English, Hindi, and major regional languages."},{icon:Ke,title:"Data Security",description:"Enterprise-grade security with FERPA compliance and data encryption."},{icon:ot,title:"Cloud Integration",description:"Seamless cloud synchronization with offline capabilities."},{icon:lt,title:"Advanced Analytics",description:"Comprehensive reporting with exportable insights and visualizations."},{icon:ye,title:"Behavioral Insights",description:"AI-driven analysis of student behavior patterns and social interactions."},{icon:dt,title:"Intuitive Dashboards",description:"User-friendly interfaces designed specifically for Indian educational context."}];return t.jsxs(d,{children:[t.jsx(d,{sx:{bgcolor:"primary.main",color:"white",py:8},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(u,{startIcon:t.jsx(Xe,{}),onClick:()=>a("/"),sx:{color:"white",mb:4},children:"Back to Home"}),t.jsx(h,{variant:"h2",sx:{fontWeight:700,mb:3},children:"Platform Features"}),t.jsx(h,{variant:"h5",sx:{opacity:.9,maxWidth:800},children:"Discover the comprehensive suite of features designed specifically for Indian educational institutions and stakeholders."})]})}),t.jsx(d,{sx:{py:8,bgcolor:"background.default"},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(h,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Core Features"}),t.jsx(b,{container:!0,spacing:4,children:r.map(((e,a)=>t.jsx(b,{item:!0,xs:12,md:6,children:t.jsx(D,{in:!0,timeout:800+200*a,children:t.jsxs(s,{sx:{height:"100%",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:i.shadows[8]}},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"flex-start",mb:3},children:[t.jsx(c,{sx:{width:56,height:56,bgcolor:e.color,mr:2},children:t.jsx(e.icon,{sx:{fontSize:28}})}),t.jsxs(d,{children:[t.jsx(h,{variant:"h5",sx:{fontWeight:600,mb:1},children:e.title}),t.jsx(h,{variant:"body2",color:"text.secondary",children:e.description})]})]}),t.jsx(N,{dense:!0,children:e.features.map(((a,i)=>t.jsxs(V,{sx:{px:0},children:[t.jsx(J,{sx:{minWidth:32},children:t.jsx(Pe,{sx:{fontSize:20,color:e.color}})}),t.jsx(_,{primary:a,primaryTypographyProps:{variant:"body2"}})]},i)))})]})})},a)))})]})}),t.jsx(d,{sx:{py:8,bgcolor:"background.paper"},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(h,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Additional Capabilities"}),t.jsx(b,{container:!0,spacing:4,children:o.map(((e,a)=>t.jsx(b,{item:!0,xs:12,sm:6,md:4,children:t.jsx(D,{in:!0,timeout:800+150*a,children:t.jsxs(s,{sx:{height:"100%",textAlign:"center",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:i.shadows[6]}},children:[t.jsx(c,{sx:{width:64,height:64,bgcolor:"primary.main",mx:"auto",mb:2},children:t.jsx(e.icon,{sx:{fontSize:32}})}),t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:e.title}),t.jsx(h,{variant:"body2",color:"text.secondary",children:e.description})]})})},a)))})]})}),t.jsx(d,{sx:{py:8,bgcolor:"secondary.main",color:"white"},children:t.jsx(I,{maxWidth:"lg",children:t.jsxs(b,{container:!0,spacing:6,alignItems:"center",children:[t.jsxs(b,{item:!0,xs:12,md:6,children:[t.jsx(h,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Built for Indian Education"}),t.jsx(h,{variant:"h6",sx:{mb:4,opacity:.9},children:"Every feature is designed with the Indian educational context in mind, ensuring cultural sensitivity and local relevance."}),t.jsxs(d,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[t.jsx(y,{label:"CBSE Aligned",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),t.jsx(y,{label:"ICSE Compatible",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),t.jsx(y,{label:"State Board Ready",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),t.jsx(y,{label:"Multi-Language",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}})]})]}),t.jsx(b,{item:!0,xs:12,md:6,children:t.jsxs(Y,{elevation:4,sx:{p:4},children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Supported Educational Boards"}),t.jsxs(N,{children:[t.jsxs(V,{children:[t.jsx(J,{children:t.jsx(Pe,{color:"primary"})}),t.jsx(_,{primary:"Central Board of Secondary Education (CBSE)"})]}),t.jsxs(V,{children:[t.jsx(J,{children:t.jsx(Pe,{color:"primary"})}),t.jsx(_,{primary:"Indian Certificate of Secondary Education (ICSE)"})]}),t.jsxs(V,{children:[t.jsx(J,{children:t.jsx(Pe,{color:"primary"})}),t.jsx(_,{primary:"State Education Boards (All Major States)"})]}),t.jsxs(V,{children:[t.jsx(J,{children:t.jsx(Pe,{color:"primary"})}),t.jsx(_,{primary:"International Baccalaureate (IB)"})]})]})]})})]})})}),t.jsx(d,{sx:{py:8,bgcolor:"background.default",textAlign:"center"},children:t.jsxs(I,{maxWidth:"md",children:[t.jsx(h,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Experience the Power of VidyaMitra"}),t.jsx(h,{variant:"h6",color:"text.secondary",sx:{mb:4},children:"Start your journey towards data-driven education today."}),t.jsxs(d,{sx:{display:"flex",gap:2,justifyContent:"center",flexWrap:"wrap"},children:[t.jsx(u,{variant:"contained",size:"large",onClick:()=>a("/login"),sx:{px:4,py:1.5},children:"Try VidyaMitra"}),t.jsx(u,{variant:"outlined",size:"large",onClick:()=>a("/contact"),sx:{px:4,py:1.5},children:"Request Demo"})]})]})})]})},Ra=()=>{const{t:e}=xe("common"),a=ie(),i=n(),[r,o]=ee.useState({name:"",email:"",organization:"",subject:"",message:""}),[l,x]=ee.useState(!1),p=e=>{o({...r,[e.target.name]:e.target.value})},m=[{icon:Oe,title:"Email Us",primary:"<EMAIL>",secondary:"<EMAIL>",color:i.palette.primary.main},{icon:it,title:"Call Us",primary:"+91 98765 43210",secondary:"+91 98765 43211",color:i.palette.secondary.main},{icon:nt,title:"Visit Us",primary:"Mumbai, Maharashtra",secondary:"India - 400001",color:i.palette.success.main},{icon:ct,title:"Business Hours",primary:"Mon - Fri: 9:00 AM - 6:00 PM",secondary:"Sat: 10:00 AM - 4:00 PM",color:i.palette.warning.main}],g=[{icon:ht,title:"Technical Support",description:"Get help with platform usage, troubleshooting, and technical issues.",action:"Get Support"},{icon:pt,title:"Sales Inquiry",description:"Learn about pricing, features, and how VidyaMitra can benefit your institution.",action:"Contact Sales"},{icon:Ce,title:"Educational Partnership",description:"Explore partnership opportunities and institutional collaborations.",action:"Partner With Us"}];return t.jsxs(d,{children:[t.jsx(d,{sx:{bgcolor:"primary.main",color:"white",py:8},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(u,{startIcon:t.jsx(Xe,{}),onClick:()=>a("/"),sx:{color:"white",mb:4},children:"Back to Home"}),t.jsx(h,{variant:"h2",sx:{fontWeight:700,mb:3},children:"Contact Us"}),t.jsx(h,{variant:"h5",sx:{opacity:.9,maxWidth:800},children:"We're here to help you transform education. Get in touch with our team for support, sales inquiries, or partnership opportunities."})]})}),t.jsx(d,{sx:{py:8,bgcolor:"background.default"},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsx(h,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Get In Touch"}),t.jsx(b,{container:!0,spacing:4,children:m.map(((e,a)=>t.jsx(b,{item:!0,xs:12,sm:6,md:3,children:t.jsx(D,{in:!0,timeout:800+200*a,children:t.jsxs(s,{sx:{height:"100%",textAlign:"center",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:i.shadows[8]}},children:[t.jsx(c,{sx:{width:64,height:64,bgcolor:e.color,mx:"auto",mb:2},children:t.jsx(e.icon,{sx:{fontSize:32}})}),t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:e.title}),t.jsx(h,{variant:"body2",sx:{mb:1},children:e.primary}),t.jsx(h,{variant:"body2",color:"text.secondary",children:e.secondary})]})})},a)))})]})}),t.jsx(d,{sx:{py:8,bgcolor:"background.paper"},children:t.jsx(I,{maxWidth:"lg",children:t.jsxs(b,{container:!0,spacing:6,children:[t.jsx(b,{item:!0,xs:12,md:8,children:t.jsxs(Y,{elevation:2,sx:{p:4},children:[t.jsx(h,{variant:"h4",sx:{fontWeight:600,mb:3},children:"Send us a Message"}),t.jsx(d,{component:"form",onSubmit:e=>{e.preventDefault(),x(!0),o({name:"",email:"",organization:"",subject:"",message:""})},children:t.jsxs(b,{container:!0,spacing:3,children:[t.jsx(b,{item:!0,xs:12,sm:6,children:t.jsx(A,{fullWidth:!0,label:"Full Name",name:"name",value:r.name,onChange:p,required:!0})}),t.jsx(b,{item:!0,xs:12,sm:6,children:t.jsx(A,{fullWidth:!0,label:"Email Address",name:"email",type:"email",value:r.email,onChange:p,required:!0})}),t.jsx(b,{item:!0,xs:12,children:t.jsx(A,{fullWidth:!0,label:"Organization/School",name:"organization",value:r.organization,onChange:p})}),t.jsx(b,{item:!0,xs:12,children:t.jsx(A,{fullWidth:!0,label:"Subject",name:"subject",value:r.subject,onChange:p,required:!0})}),t.jsx(b,{item:!0,xs:12,children:t.jsx(A,{fullWidth:!0,label:"Message",name:"message",multiline:!0,rows:4,value:r.message,onChange:p,required:!0})}),t.jsx(b,{item:!0,xs:12,children:t.jsx(u,{type:"submit",variant:"contained",size:"large",endIcon:t.jsx(xt,{}),sx:{px:4,py:1.5},children:"Send Message"})})]})})]})}),t.jsxs(b,{item:!0,xs:12,md:4,children:[t.jsx(h,{variant:"h5",sx:{fontWeight:600,mb:3},children:"How Can We Help?"}),g.map(((e,a)=>t.jsx(D,{in:!0,timeout:800+200*a,children:t.jsx(s,{sx:{mb:3,p:3},children:t.jsxs(d,{sx:{display:"flex",alignItems:"flex-start",mb:2},children:[t.jsx(c,{sx:{width:48,height:48,bgcolor:"primary.main",mr:2},children:t.jsx(e.icon,{sx:{fontSize:24}})}),t.jsxs(d,{children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:1},children:e.title}),t.jsx(h,{variant:"body2",color:"text.secondary",sx:{mb:2},children:e.description}),t.jsx(u,{variant:"outlined",size:"small",sx:{textTransform:"none"},children:e.action})]})]})})},a)))]})]})})}),t.jsx(d,{sx:{py:8,bgcolor:"background.default"},children:t.jsxs(I,{maxWidth:"md",children:[t.jsx(h,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Frequently Asked Questions"}),t.jsxs(b,{container:!0,spacing:3,children:[t.jsx(b,{item:!0,xs:12,children:t.jsxs(Y,{elevation:1,sx:{p:3},children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:"How does VidyaMitra ensure data privacy?"}),t.jsx(h,{variant:"body2",color:"text.secondary",children:"We implement enterprise-grade security measures including data encryption, FERPA compliance, and strict access controls to protect student information."})]})}),t.jsx(b,{item:!0,xs:12,children:t.jsxs(Y,{elevation:1,sx:{p:3},children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Which educational boards are supported?"}),t.jsx(h,{variant:"body2",color:"text.secondary",children:"VidyaMitra supports CBSE, ICSE, and all major State educational boards across India, with customizable features for each curriculum."})]})}),t.jsx(b,{item:!0,xs:12,children:t.jsxs(Y,{elevation:1,sx:{p:3},children:[t.jsx(h,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Is training provided for teachers and staff?"}),t.jsx(h,{variant:"body2",color:"text.secondary",children:"Yes, we provide comprehensive training sessions, documentation, and ongoing support to ensure successful platform adoption."})]})})]})]})}),t.jsx(K,{open:l,autoHideDuration:6e3,onClose:()=>x(!1),anchorOrigin:{vertical:"bottom",horizontal:"center"},children:t.jsx(X,{onClose:()=>x(!1),severity:"success",sx:{width:"100%"},children:"Thank you for your message! We'll get back to you within 24 hours."})})]})},Oa=()=>{const e=n(),a=(new Date).getFullYear(),i=[{icon:Re,href:"#",label:"Facebook"},{icon:mt,href:"#",label:"Twitter"},{icon:ut,href:"#",label:"LinkedIn"},{icon:gt,href:"#",label:"Instagram"},{icon:jt,href:"#",label:"YouTube"}];return t.jsx(d,{component:"footer",sx:{background:"dark"===e.palette.mode?`linear-gradient(135deg, ${o("#1E293B",.98)} 0%, ${o("#334155",.95)} 100%)`:`linear-gradient(135deg, ${o("#2D3748",.98)} 0%, ${o("#4A5568",.95)} 100%)`,backdropFilter:"blur(20px)",borderTop:`1px solid ${o(e.palette.divider,.3)}`,mt:"auto",py:6,position:"relative","&::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:"dark"===e.palette.mode?"rgba(30, 41, 59, 0.2)":"rgba(45, 55, 72, 0.2)",zIndex:-1}},children:t.jsxs(I,{maxWidth:"lg",children:[t.jsxs(b,{container:!0,spacing:4,children:[t.jsx(b,{item:!0,xs:12,md:4,children:t.jsx(ue.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:t.jsxs(d,{sx:{mb:3},children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[t.jsx(Ce,{sx:{fontSize:32,color:e.palette.primary.main}}),t.jsx(h,{variant:"h5",sx:{fontWeight:600,color:e.palette.text.primary},children:"VidyaMitra"})]}),t.jsx(h,{variant:"body2",sx:{color:e.palette.text.secondary,lineHeight:1.6,mb:3},children:"Empowering Indian education through intelligent student analysis. Supporting CBSE, ICSE, and State boards with AI-powered insights."}),t.jsxs(f,{spacing:1.5,children:[t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1.5},children:[t.jsx(nt,{sx:{fontSize:18,color:e.palette.primary.main,opacity:.8}}),t.jsx(h,{variant:"body2",sx:{color:"dark"===e.palette.mode?"#FFFFFF":"#2D3748",fontWeight:500,opacity:1},children:"Hyderabad, India"})]}),t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1.5},children:[t.jsx(it,{sx:{fontSize:18,color:e.palette.primary.main,opacity:.8}}),t.jsx(h,{variant:"body2",sx:{color:"dark"===e.palette.mode?"#FFFFFF":"#2D3748",fontWeight:500,opacity:1},children:"+91 9392233989"})]}),t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1.5},children:[t.jsx(Oe,{sx:{fontSize:18,color:e.palette.primary.main,opacity:.8}}),t.jsx(h,{variant:"body2",sx:{color:"dark"===e.palette.mode?"#FFFFFF":"#2D3748",fontWeight:500,opacity:1},children:"<EMAIL>"})]})]})]})})}),[{title:"Platform",links:[{label:"Dashboard",href:"/dashboard"},{label:"Student Management",href:"/dashboard/students"},{label:"SWOT Analysis",href:"/dashboard/swot"},{label:"Reports",href:"/dashboard/reports"}]},{title:"Educational Boards",links:[{label:"CBSE Integration",href:"/features#cbse"},{label:"ICSE Support",href:"/features#icse"},{label:"State Boards",href:"/features#state"},{label:"International Boards",href:"/features#international"}]},{title:"Resources",links:[{label:"Documentation",href:"/docs"},{label:"API Reference",href:"/api"},{label:"Support Center",href:"/support"},{label:"Training Materials",href:"/training"}]},{title:"Company",links:[{label:"About Us",href:"/about"},{label:"Privacy Policy",href:"/privacy"},{label:"Terms of Service",href:"/terms"},{label:"Contact",href:"/contact"}]}].map(((a,i)=>t.jsx(b,{item:!0,xs:6,md:2,children:t.jsxs(ue.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*i},viewport:{once:!0},children:[t.jsx(h,{variant:"h6",sx:{fontWeight:500,mb:2,color:e.palette.text.primary},children:a.title}),t.jsx(f,{spacing:1,children:a.links.map((a=>t.jsx(Q,{href:a.href,sx:{color:e.palette.text.secondary,textDecoration:"none",fontSize:"0.875rem",transition:"all 0.3s ease","&:hover":{color:e.palette.primary.main,transform:"translateX(4px)"}},children:a.label},a.label)))})]})},a.title)))]}),t.jsx(k,{sx:{my:4,opacity:.3}}),t.jsxs(d,{sx:{display:"flex",flexDirection:{xs:"column",md:"row"},justifyContent:"space-between",alignItems:"center",gap:2},children:[t.jsxs(h,{variant:"body2",sx:{color:"dark"===e.palette.mode?"#FFFFFF":"#2D3748",textAlign:{xs:"center",md:"left"},fontWeight:500,opacity:1},children:["© ",a," VidyaMitra. All rights reserved. Empowering Indian education with AI-driven insights."]}),t.jsx(f,{direction:"row",spacing:1,children:i.map((a=>t.jsx(p,{href:a.href,"aria-label":a.label,sx:{color:e.palette.text.secondary,border:`1px solid ${o(e.palette.divider,.3)}`,borderRadius:2,p:1,transition:"all 0.3s ease","&:hover":{color:e.palette.primary.main,borderColor:e.palette.primary.main,transform:"translateY(-2px)",background:o(e.palette.primary.main,.1)}},children:t.jsx(a.icon,{sx:{fontSize:18}})},a.label)))})]})]})})},La=ee.createContext(),$a=({children:e})=>{const[a,i]=ee.useState("true"===localStorage.getItem("isAuthenticated")),[n,r]=ee.useState(JSON.parse(localStorage.getItem("user")));return t.jsx(La.Provider,{value:{isAuthenticated:a,user:n,login:e=>new Promise((t=>{setTimeout((()=>{const a={username:e.username,role:"teacher",school_id:"school123",name:"Demo User"};localStorage.setItem("isAuthenticated","true"),localStorage.setItem("user",JSON.stringify(a)),i(!0),r(a),t(a)}),500)})),logout:()=>{localStorage.removeItem("isAuthenticated"),localStorage.removeItem("user"),i(!1),r(null)}},children:e})},Ha=()=>ee.useContext(La),Na=ee.createContext(),Va=({children:e})=>{const[a,i]=ee.useState(localStorage.getItem("appLanguage")||"en");ee.useEffect((()=>{he.changeLanguage(a),localStorage.setItem("appLanguage",a)}),[a]);return t.jsx(Na.Provider,{value:{language:a,changeLanguage:e=>{i(e)}},children:e})},_a=()=>ee.useContext(Na),Ga=ee.createContext(),Ua=({children:e})=>{const[a,i]=ee.useState(!0);return t.jsx(Ga.Provider,{value:{sidebarOpen:a,setSidebarOpen:i,toggleSidebar:()=>{i(!a)}},children:e})},Ya=()=>ee.useContext(Ga);he.use(pe).use(me).init({supportedLngs:["en","hi"],fallbackLng:"en",debug:!1,interpolation:{escapeValue:!1},backend:{loadPath:"/locales/{{lng}}/{{ns}}.json"},ns:["common","dashboard","login"],defaultNS:"common"});const qa=te.lazy((()=>ae((()=>import("./StudentRegistration--Cq_MLtD.js")),__vite__mapDeps([0,1,2,3,4,5,6,7])))),Ja=te.lazy((()=>ae((()=>import("./StudentProfile-BgI_raih.js")),__vite__mapDeps([8,1,2,3,9,4,6,7])))),Ka=te.lazy((()=>ae((()=>import("./AttendanceManagement-Cuapupr3.js")),__vite__mapDeps([10,1,2,3,4,6,7])))),Xa=te.lazy((()=>ae((()=>import("./GradeEntry-C2wSA142.js")),__vite__mapDeps([11,1,2,3,4,6,7])))),Qa=te.lazy((()=>ae((()=>import("./TeacherDashboard-D8R1Yo_8.js")),__vite__mapDeps([12,1,2,3,9,4,5,6,7])))),Za=te.lazy((()=>ae((()=>import("./SWOTWizard-cbD3CjwT.js")),__vite__mapDeps([13,1,2,3,4,6,7])))),ei=te.lazy((()=>ae((()=>import("./ReportGeneration-WXxmrtL8.js")),__vite__mapDeps([14,1,2,3,4,7,6])))),ti=()=>{const{t:e}=xe("common"),{logout:a,user:i}=Ha(),{language:n,changeLanguage:r}=_a(),{toggleSidebar:s}=Ya(),o=ie();return t.jsx(v,{position:"fixed",sx:{zIndex:e=>e.zIndex.drawer+1},children:t.jsxs(S,{sx:{px:{xs:2,md:3}},children:[t.jsx(p,{color:"inherit","aria-label":"toggle sidebar",onClick:s,edge:"start",sx:{mr:2},children:t.jsx(tt,{})}),t.jsx(h,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1,fontWeight:600},children:e("platformTitle")}),t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2},children:[t.jsxs(M,{size:"small",sx:{minWidth:120},children:[t.jsx(E,{id:"language-select-label",children:e("language")}),t.jsxs(T,{labelId:"language-select-label",id:"language-select",value:n,label:e("language"),onChange:e=>{r(e.target.value)},sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:"rgba(255, 255, 255, 0.3)"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"rgba(255, 255, 255, 0.5)"}},children:[t.jsx(B,{value:"en",children:"🇬🇧 English"}),t.jsx(B,{value:"hi",children:"🇮🇳 हिन्दी"})]})]}),i&&t.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[t.jsx(c,{sx:{width:32,height:32,bgcolor:"secondary.main",fontSize:"0.875rem",fontWeight:600},children:(i.name||i.username).charAt(0).toUpperCase()}),t.jsx(h,{sx:{display:{xs:"none",sm:"block"}},children:i.name||i.username})]}),t.jsx(u,{color:"inherit",onClick:()=>{a(),o("/login")},startIcon:t.jsx(bt,{}),sx:{borderRadius:2,"&:hover":{bgcolor:"rgba(255, 255, 255, 0.1)"}},children:e("logout")})]})]})})},ai=()=>{const{t:e}=xe("common"),{sidebarOpen:a}=Ya(),i=[{text:e("dashboard"),icon:t.jsx(dt,{}),path:"/dashboard"},{text:e("students"),icon:t.jsx(Ve,{}),path:"/dashboard/students"},{text:e("reports"),icon:t.jsx(_e,{}),path:"/dashboard/reports"},{text:e("settings"),icon:t.jsx(ft,{}),path:"/dashboard/settings"}];return t.jsxs(G,{variant:"permanent",sx:{width:a?240:64,flexShrink:0,transition:"width 0.3s ease","& .MuiDrawer-paper":{width:a?240:64,boxSizing:"border-box",backgroundColor:"#2E5BA8",color:"white",transition:"width 0.3s ease",overflowX:"hidden"}},children:[t.jsx(S,{})," ",t.jsx(d,{sx:{overflow:"auto"},children:t.jsx(N,{children:i.map((e=>t.jsx(r,{title:a?"":e.text,placement:"right",arrow:!0,children:t.jsxs(V,{component:ce,to:e.path,sx:{"&:hover":{backgroundColor:"rgba(255, 255, 255, 0.1)"},"& .MuiListItemIcon-root":{color:"white",minWidth:a?56:"auto",justifyContent:"center"},justifyContent:a?"initial":"center",px:a?2:1},children:[t.jsx(J,{children:e.icon}),a&&t.jsx(_,{primary:e.text})]})},e.text)))})})]})},ii=()=>{const{sidebarOpen:e}=Ya();return t.jsxs(d,{sx:{display:"flex",flexDirection:"column",minHeight:"100vh"},children:[t.jsxs(d,{sx:{display:"flex",flex:1},children:[t.jsx(i,{}),t.jsx(ti,{}),t.jsx(ai,{}),t.jsxs(d,{component:"main",sx:{flexGrow:1,bgcolor:"background.default",p:3,width:`calc(100% - ${e?240:64}px)`,display:"flex",flexDirection:"column",transition:"width 0.3s ease, margin 0.3s ease",marginLeft:0},children:[t.jsx(S,{})," ",t.jsx(d,{sx:{flex:1},children:t.jsxs(ee.Suspense,{fallback:t.jsx(yi,{}),children:[t.jsx(de,{})," "]})})]})]}),t.jsx(Oa,{})]})},ni=()=>{const{login:e}=Ha(),a=ie();return t.jsx(pa,{onLogin:async t=>{try{await e({username:t.email,password:t.password}),a("/dashboard")}catch(i){}},onSignup:async t=>{try{await e({username:t.email,password:t.password}),a("/dashboard")}catch(i){}}})},ri=()=>t.jsx(la,{}),si=()=>t.jsx(ja,{}),oi=()=>t.jsx(ee.Suspense,{fallback:t.jsx(vi,{}),children:t.jsx(qa,{})}),li=()=>t.jsx(ee.Suspense,{fallback:t.jsx(vi,{}),children:t.jsx(Ja,{})}),di=()=>t.jsx(ee.Suspense,{fallback:t.jsx(vi,{}),children:t.jsx(Qa,{})}),ci=()=>t.jsx(ee.Suspense,{fallback:t.jsx(vi,{}),children:t.jsx(Ka,{})}),xi=()=>t.jsx(ee.Suspense,{fallback:t.jsx(vi,{}),children:t.jsx(Xa,{})}),hi=()=>t.jsx(ee.Suspense,{fallback:t.jsx(vi,{}),children:t.jsx(Za,{})}),pi=()=>t.jsx(ee.Suspense,{fallback:t.jsx(vi,{}),children:t.jsx(ei,{})}),mi=()=>t.jsx(fa,{}),ui=()=>t.jsx(ka,{}),gi=()=>t.jsx(ma,{}),ji=()=>t.jsx(ee.Suspense,{fallback:t.jsx(vi,{}),children:t.jsx(ei,{})}),bi=()=>{const{t:e}=xe("common"),{language:a,changeLanguage:i}=_a();return t.jsxs(d,{sx:{p:3},children:[t.jsx(h,{variant:"h4",sx:{fontWeight:700,mb:4},children:e("settings")}),t.jsxs(d,{sx:{maxWidth:600},children:[t.jsx(h,{variant:"h6",sx:{mb:2},children:"Language Preferences"}),t.jsxs(M,{fullWidth:!0,sx:{mb:3},children:[t.jsx(E,{children:"Language"}),t.jsxs(T,{value:a,label:"Language",onChange:e=>i(e.target.value),children:[t.jsx(B,{value:"en",children:"🇬🇧 English"}),t.jsx(B,{value:"hi",children:"🇮🇳 हिन्दी"})]})]}),t.jsx(h,{variant:"h6",sx:{mb:2},children:"Platform Information"}),t.jsxs(d,{sx:{p:2,bgcolor:"background.paper",borderRadius:1,border:1,borderColor:"divider"},children:[t.jsx(h,{variant:"body2",color:"text.secondary",children:"VidyaMitra Platform v1.0.0"}),t.jsx(h,{variant:"body2",color:"text.secondary",children:"Empowering Indian education through intelligent student analysis"})]})]})]})},fi=()=>t.jsxs(d,{sx:{textAlign:"center",mt:8,p:4,display:"flex",flexDirection:"column",alignItems:"center",minHeight:"60vh",justifyContent:"center"},children:[t.jsx(h,{variant:"h2",sx:{fontWeight:700,mb:2,color:"primary.main"},children:"Page Not Found"}),t.jsx(h,{variant:"h6",sx:{mb:4,color:"text.secondary",maxWidth:600},children:"The page you are looking for does not exist."}),t.jsxs(d,{sx:{display:"flex",gap:2,flexWrap:"wrap",justifyContent:"center"},children:[t.jsx(u,{component:ce,to:"/dashboard",variant:"contained",size:"large",sx:{px:4,py:1.5,borderRadius:2,fontWeight:600},children:"Go to Dashboard"}),t.jsx(u,{component:ce,to:"/dashboard/students",variant:"outlined",size:"large",sx:{px:4,py:1.5,borderRadius:2,fontWeight:600},children:"Student Management"})]})]}),yi=()=>t.jsx(d,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"80vh"},children:t.jsx(x,{})}),vi=()=>t.jsxs(d,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh"},children:[t.jsx(x,{size:40}),t.jsx(h,{sx:{ml:2},children:"Loading component..."})]}),Si=({children:e})=>{const{isAuthenticated:a}=Ha();return a?e:t.jsx(le,{to:"/login",replace:!0})};function Ci(){return t.jsx(Lt,{children:t.jsx($a,{children:t.jsx(Va,{children:t.jsx(Ua,{children:t.jsx(ee.Suspense,{fallback:t.jsx(yi,{}),children:t.jsx(re,{children:t.jsxs(se,{children:[t.jsx(oe,{path:"/",element:t.jsx(Pa,{})}),t.jsx(oe,{path:"/about",element:t.jsx(za,{})}),t.jsx(oe,{path:"/features",element:t.jsx(Da,{})}),t.jsx(oe,{path:"/contact",element:t.jsx(Ra,{})}),t.jsx(oe,{path:"/login",element:t.jsx(ni,{})}),t.jsx(oe,{path:"/students",element:t.jsx(le,{to:"/dashboard/students",replace:!0})}),t.jsx(oe,{path:"/reports",element:t.jsx(le,{to:"/dashboard/reports",replace:!0})}),t.jsx(oe,{path:"/settings",element:t.jsx(le,{to:"/dashboard/settings",replace:!0})}),t.jsx(oe,{path:"/teacher",element:t.jsx(le,{to:"/dashboard/teacher",replace:!0})}),t.jsx(oe,{path:"/attendance",element:t.jsx(le,{to:"/dashboard/attendance",replace:!0})}),t.jsx(oe,{path:"/grades",element:t.jsx(le,{to:"/dashboard/grades",replace:!0})}),t.jsx(oe,{path:"/swot",element:t.jsx(le,{to:"/dashboard/swot",replace:!0})}),t.jsx(oe,{path:"/analytics",element:t.jsx(le,{to:"/dashboard/analytics",replace:!0})}),t.jsxs(oe,{path:"/dashboard",element:t.jsx(Si,{children:t.jsx(ii,{})}),children:[t.jsx(oe,{index:!0,element:t.jsx(ri,{})}),t.jsx(oe,{path:"students",element:t.jsx(si,{})}),t.jsx(oe,{path:"students/register",element:t.jsx(oi,{})}),t.jsx(oe,{path:"students/:studentId",element:t.jsx(li,{})}),t.jsx(oe,{path:"students/:studentId/swot",element:t.jsx(ui,{})}),t.jsx(oe,{path:"teacher",element:t.jsx(di,{})}),t.jsx(oe,{path:"attendance",element:t.jsx(ci,{})}),t.jsx(oe,{path:"grades",element:t.jsx(xi,{})}),t.jsx(oe,{path:"swot/wizard",element:t.jsx(hi,{})}),t.jsx(oe,{path:"analytics",element:t.jsx(gi,{})}),t.jsx(oe,{path:"swot",element:t.jsx(mi,{})}),t.jsx(oe,{path:"reports",element:t.jsx(ji,{})}),t.jsx(oe,{path:"reports/generate",element:t.jsx(pi,{})}),t.jsx(oe,{path:"settings",element:t.jsx(bi,{})})]}),t.jsx(oe,{path:"*",element:t.jsx(fi,{})})]})})})})})})})}class wi extends te.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){}render(){return this.state.hasError?t.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",fontFamily:"Arial, sans-serif"},children:[t.jsx("h1",{children:"Something went wrong."}),t.jsx("p",{children:"Please refresh the page or contact support if the problem persists."}),t.jsx("button",{onClick:()=>window.location.reload(),style:{padding:"10px 20px",backgroundColor:"#2E5BA8",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Refresh Page"})]}):this.props.children}}Dt.createRoot(document.getElementById("root")).render(t.jsx(te.StrictMode,{children:t.jsx(wi,{children:t.jsx(Ci,{})})}));
//# sourceMappingURL=index-CdYvGdFC.js.map
