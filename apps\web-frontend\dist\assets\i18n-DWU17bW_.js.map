{"version": 3, "file": "i18n-DWU17bW_.js", "sources": ["../../../../node_modules/i18next/dist/esm/i18next.js", "../../../../node_modules/react-i18next/dist/es/utils.js", "../../../../node_modules/react-i18next/dist/es/unescape.js", "../../../../node_modules/react-i18next/dist/es/defaults.js", "../../../../node_modules/react-i18next/dist/es/i18nInstance.js", "../../../../node_modules/react-i18next/dist/es/initReactI18next.js", "../../../../node_modules/react-i18next/dist/es/context.js", "../../../../node_modules/react-i18next/dist/es/useTranslation.js", "../../../../node_modules/i18next-http-backend/esm/utils.js", "../../../../node_modules/cross-fetch/dist/browser-ponyfill.js", "../../../../node_modules/i18next-http-backend/esm/getFetch.cjs", "../../../../node_modules/i18next-http-backend/esm/request.js", "../../../../node_modules/i18next-http-backend/esm/index.js"], "sourcesContent": ["const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last && last.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = function (obj, path) {\n  let keySeparator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '.';\n  if (!obj) return undefined;\n  if (obj[path]) return obj[path];\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code && code.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    if (console && console[type]) console[type].apply(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return this.forward(args, 'log', '', true);\n  }\n  warn() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return this.forward(args, 'warn', '', true);\n  }\n  error() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    return this.forward(args, 'error', '');\n  }\n  deprecate() {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(_ref => {\n        let [observer, numTimesAdded] = _ref;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(_ref2 => {\n        let [observer, numTimesAdded] = _ref2;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      ns: ['translation'],\n      defaultNS: 'translation'\n    };\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data && this.data[lng] && this.data[lng][ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value) {\n    let options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      silent: false\n    };\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n      silent: false\n    };\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {\n      silent: false,\n      skipCopy: false\n    };\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    if (this.options.compatibilityAPI === 'v1') return {\n      ...{},\n      ...this.getResource(lng, ns)\n    };\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      if (this.processors[processor]) value = this.processors[processor].process(value, key, options, translator);\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nclass Translator extends EventEmitter {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    if (key === undefined || key === null) {\n      return false;\n    }\n    const resolved = this.resolve(key, options);\n    return resolved && resolved.res !== undefined;\n  }\n  extractFromKey(key, options) {\n    let nsSeparator = options.nsSeparator !== undefined ? options.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let namespaces = options.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !options.keySeparator && !this.options.userDefinedNsSeparator && !options.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, options, lastKey) {\n    if (typeof options !== 'object' && this.options.overloadTranslationOptionHandler) {\n      options = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') options = {\n      ...options\n    };\n    if (!options) options = {};\n    if (keys === undefined || keys === null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = options.returnDetails !== undefined ? options.returnDetails : this.options.returnDetails;\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], options);\n    const namespace = namespaces[namespaces.length - 1];\n    const lng = options.lng || this.language;\n    const appendNamespaceToCIMode = options.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng && lng.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        const nsSeparator = options.nsSeparator || this.options.nsSeparator;\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(options)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(options)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, options);\n    let res = resolved && resolved.res;\n    const resUsedKey = resolved && resolved.usedKey || key;\n    const resExactUsedKey = resolved && resolved.exactUsedKey || key;\n    const resType = Object.prototype.toString.apply(res);\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = options.joinArrays !== undefined ? options.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const handleAsObject = !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\n    if (handleAsObjectInI18nFormat && res && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(res))) {\n      if (!options.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, res, {\n          ...options,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(options);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(res);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in res) {\n          if (Object.prototype.hasOwnProperty.call(res, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            copy[m] = this.translate(deepKey, {\n              ...options,\n              ...{\n                joinArrays: false,\n                ns: namespaces\n              }\n            });\n            if (copy[m] === deepKey) copy[m] = res[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, options, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      const needsPluralHandling = options.count !== undefined && !isString(options.count);\n      const hasDefaultValue = Translator.hasDefaultValue(options);\n      const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, options) : '';\n      const defaultValueSuffixOrdinalFallback = options.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, {\n        ordinal: false\n      }) : '';\n      const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n      const defaultValue = needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] || options[`defaultValue${defaultValueSuffix}`] || options[`defaultValue${defaultValueSuffixOrdinalFallback}`] || options.defaultValue;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = options.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...options,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, options.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(options.lng || this.language);\n        } else {\n          lngs.push(options.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, options);\n          } else if (this.backendConnector && this.backendConnector.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, options);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, options);\n              if (needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, options[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, options, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) res = `${namespace}:${key}`;\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        if (this.options.compatibilityAPI !== 'v1') {\n          res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}:${key}` : key, usedDefault ? res : undefined);\n        } else {\n          res = this.options.parseMissingKeyHandler(res);\n        }\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(options);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, options, resolved, lastKey) {\n    var _this = this;\n    if (this.i18nFormat && this.i18nFormat.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...options\n      }, options.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!options.skipInterpolation) {\n      if (options.interpolation) this.interpolator.init({\n        ...options,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...options.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = options.replace && !isString(options.replace) ? options.replace : options;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, options.lng || this.language || resolved.usedLng, options);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) options.nest = false;\n      }\n      if (!options.lng && this.options.compatibilityAPI !== 'v1' && resolved && resolved.res) options.lng = this.language || resolved.usedLng;\n      if (options.nest !== false) res = this.interpolator.nest(res, function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (lastKey && lastKey[0] === args[0] && !options.context) {\n          _this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return _this.translate(...args, key);\n      }, options);\n      if (options.interpolation) this.interpolator.reset();\n    }\n    const postProcess = options.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res !== undefined && res !== null && postProcessorNames && postProcessorNames.length && options.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(options)\n        },\n        ...options\n      } : options, this);\n    }\n    return res;\n  }\n  resolve(keys) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, options);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = options.count !== undefined && !isString(options.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n      const needsContextHandling = options.context !== undefined && (isString(options.context) || typeof options.context === 'number') && options.context !== '';\n      const codes = options.lngs ? options.lngs : this.languageUtils.toResolveHierarchy(options.lng || this.language, options.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils && this.utils.hasLoadedNamespace && !this.utils.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat && this.i18nFormat.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, options);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, options.count, options);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${options.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, options);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    if (this.i18nFormat && this.i18nFormat.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nconst capitalize = string => string.charAt(0).toUpperCase() + string.slice(1);\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      if (typeof Intl !== 'undefined' && typeof Intl.getCanonicalLocales !== 'undefined') {\n        try {\n          let formattedCode = Intl.getCanonicalLocales(code)[0];\n          if (formattedCode && this.options.lowerCaseLng) {\n            formattedCode = formattedCode.toLowerCase();\n          }\n          if (formattedCode) return formattedCode;\n        } catch (e) {}\n      }\n      const specialCases = ['hans', 'hant', 'latn', 'cyrl', 'cans', 'mong', 'arab'];\n      let p = code.split('-');\n      if (this.options.lowerCaseLng) {\n        p = p.map(part => part.toLowerCase());\n      } else if (p.length === 2) {\n        p[0] = p[0].toLowerCase();\n        p[1] = p[1].toUpperCase();\n        if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n      } else if (p.length === 3) {\n        p[0] = p[0].toLowerCase();\n        if (p[1].length === 2) p[1] = p[1].toUpperCase();\n        if (p[0] !== 'sgn' && p[2].length === 2) p[2] = p[2].toUpperCase();\n        if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n        if (specialCases.indexOf(p[2].toLowerCase()) > -1) p[2] = capitalize(p[2].toLowerCase());\n      }\n      return p.join('-');\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes(fallbackCode || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nlet sets = [{\n  lngs: ['ach', 'ak', 'am', 'arn', 'br', 'fil', 'gun', 'ln', 'mfe', 'mg', 'mi', 'oc', 'pt', 'pt-BR', 'tg', 'tl', 'ti', 'tr', 'uz', 'wa'],\n  nr: [1, 2],\n  fc: 1\n}, {\n  lngs: ['af', 'an', 'ast', 'az', 'bg', 'bn', 'ca', 'da', 'de', 'dev', 'el', 'en', 'eo', 'es', 'et', 'eu', 'fi', 'fo', 'fur', 'fy', 'gl', 'gu', 'ha', 'hi', 'hu', 'hy', 'ia', 'it', 'kk', 'kn', 'ku', 'lb', 'mai', 'ml', 'mn', 'mr', 'nah', 'nap', 'nb', 'ne', 'nl', 'nn', 'no', 'nso', 'pa', 'pap', 'pms', 'ps', 'pt-PT', 'rm', 'sco', 'se', 'si', 'so', 'son', 'sq', 'sv', 'sw', 'ta', 'te', 'tk', 'ur', 'yo'],\n  nr: [1, 2],\n  fc: 2\n}, {\n  lngs: ['ay', 'bo', 'cgg', 'fa', 'ht', 'id', 'ja', 'jbo', 'ka', 'km', 'ko', 'ky', 'lo', 'ms', 'sah', 'su', 'th', 'tt', 'ug', 'vi', 'wo', 'zh'],\n  nr: [1],\n  fc: 3\n}, {\n  lngs: ['be', 'bs', 'cnr', 'dz', 'hr', 'ru', 'sr', 'uk'],\n  nr: [1, 2, 5],\n  fc: 4\n}, {\n  lngs: ['ar'],\n  nr: [0, 1, 2, 3, 11, 100],\n  fc: 5\n}, {\n  lngs: ['cs', 'sk'],\n  nr: [1, 2, 5],\n  fc: 6\n}, {\n  lngs: ['csb', 'pl'],\n  nr: [1, 2, 5],\n  fc: 7\n}, {\n  lngs: ['cy'],\n  nr: [1, 2, 3, 8],\n  fc: 8\n}, {\n  lngs: ['fr'],\n  nr: [1, 2],\n  fc: 9\n}, {\n  lngs: ['ga'],\n  nr: [1, 2, 3, 7, 11],\n  fc: 10\n}, {\n  lngs: ['gd'],\n  nr: [1, 2, 3, 20],\n  fc: 11\n}, {\n  lngs: ['is'],\n  nr: [1, 2],\n  fc: 12\n}, {\n  lngs: ['jv'],\n  nr: [0, 1],\n  fc: 13\n}, {\n  lngs: ['kw'],\n  nr: [1, 2, 3, 4],\n  fc: 14\n}, {\n  lngs: ['lt'],\n  nr: [1, 2, 10],\n  fc: 15\n}, {\n  lngs: ['lv'],\n  nr: [1, 2, 0],\n  fc: 16\n}, {\n  lngs: ['mk'],\n  nr: [1, 2],\n  fc: 17\n}, {\n  lngs: ['mnk'],\n  nr: [0, 1, 2],\n  fc: 18\n}, {\n  lngs: ['mt'],\n  nr: [1, 2, 11, 20],\n  fc: 19\n}, {\n  lngs: ['or'],\n  nr: [2, 1],\n  fc: 2\n}, {\n  lngs: ['ro'],\n  nr: [1, 2, 20],\n  fc: 20\n}, {\n  lngs: ['sl'],\n  nr: [5, 1, 2, 3],\n  fc: 21\n}, {\n  lngs: ['he', 'iw'],\n  nr: [1, 2, 20, 21],\n  fc: 22\n}];\nlet _rulesPluralsTypes = {\n  1: n => Number(n > 1),\n  2: n => Number(n != 1),\n  3: n => 0,\n  4: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  5: n => Number(n == 0 ? 0 : n == 1 ? 1 : n == 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5),\n  6: n => Number(n == 1 ? 0 : n >= 2 && n <= 4 ? 1 : 2),\n  7: n => Number(n == 1 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  8: n => Number(n == 1 ? 0 : n == 2 ? 1 : n != 8 && n != 11 ? 2 : 3),\n  9: n => Number(n >= 2),\n  10: n => Number(n == 1 ? 0 : n == 2 ? 1 : n < 7 ? 2 : n < 11 ? 3 : 4),\n  11: n => Number(n == 1 || n == 11 ? 0 : n == 2 || n == 12 ? 1 : n > 2 && n < 20 ? 2 : 3),\n  12: n => Number(n % 10 != 1 || n % 100 == 11),\n  13: n => Number(n !== 0),\n  14: n => Number(n == 1 ? 0 : n == 2 ? 1 : n == 3 ? 2 : 3),\n  15: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  16: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n !== 0 ? 1 : 2),\n  17: n => Number(n == 1 || n % 10 == 1 && n % 100 != 11 ? 0 : 1),\n  18: n => Number(n == 0 ? 0 : n == 1 ? 1 : 2),\n  19: n => Number(n == 1 ? 0 : n == 0 || n % 100 > 1 && n % 100 < 11 ? 1 : n % 100 > 10 && n % 100 < 20 ? 2 : 3),\n  20: n => Number(n == 1 ? 0 : n == 0 || n % 100 > 0 && n % 100 < 20 ? 1 : 2),\n  21: n => Number(n % 100 == 1 ? 1 : n % 100 == 2 ? 2 : n % 100 == 3 || n % 100 == 4 ? 3 : 0),\n  22: n => Number(n == 1 ? 0 : n == 2 ? 1 : (n < 0 || n > 10) && n % 10 == 0 ? 2 : 3)\n};\nconst nonIntlVersions = ['v1', 'v2', 'v3'];\nconst intlVersions = ['v4'];\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst createRules = () => {\n  const rules = {};\n  sets.forEach(set => {\n    set.lngs.forEach(l => {\n      rules[l] = {\n        numbers: set.nr,\n        plurals: _rulesPluralsTypes[set.fc]\n      };\n    });\n  });\n  return rules;\n};\nclass PluralResolver {\n  constructor(languageUtils) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    if ((!this.options.compatibilityJSON || intlVersions.includes(this.options.compatibilityJSON)) && (typeof Intl === 'undefined' || !Intl.PluralRules)) {\n      this.options.compatibilityJSON = 'v3';\n      this.logger.error('Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.');\n    }\n    this.rules = createRules();\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (this.shouldUseIntlApi()) {\n      const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n      const type = options.ordinal ? 'ordinal' : 'cardinal';\n      const cacheKey = JSON.stringify({\n        cleanedCode,\n        type\n      });\n      if (cacheKey in this.pluralRulesCache) {\n        return this.pluralRulesCache[cacheKey];\n      }\n      let rule;\n      try {\n        rule = new Intl.PluralRules(cleanedCode, {\n          type\n        });\n      } catch (err) {\n        if (!code.match(/-|_/)) return;\n        const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n        rule = this.getRule(lngPart, options);\n      }\n      this.pluralRulesCache[cacheKey] = rule;\n      return rule;\n    }\n    return this.rules[code] || this.rules[this.languageUtils.getLanguagePartFromCode(code)];\n  }\n  needsPlural(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const rule = this.getRule(code, options);\n    if (this.shouldUseIntlApi()) {\n      return rule && rule.resolvedOptions().pluralCategories.length > 1;\n    }\n    return rule && rule.numbers.length > 1;\n  }\n  getPluralFormsOfKey(code, key) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const rule = this.getRule(code, options);\n    if (!rule) {\n      return [];\n    }\n    if (this.shouldUseIntlApi()) {\n      return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n    }\n    return rule.numbers.map(number => this.getSuffix(code, number, options));\n  }\n  getSuffix(code, count) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const rule = this.getRule(code, options);\n    if (rule) {\n      if (this.shouldUseIntlApi()) {\n        return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n      }\n      return this.getSuffixRetroCompatible(rule, count);\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return '';\n  }\n  getSuffixRetroCompatible(rule, count) {\n    const idx = rule.noAbs ? rule.plurals(count) : rule.plurals(Math.abs(count));\n    let suffix = rule.numbers[idx];\n    if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n      if (suffix === 2) {\n        suffix = 'plural';\n      } else if (suffix === 1) {\n        suffix = '';\n      }\n    }\n    const returnSuffix = () => this.options.prepend && suffix.toString() ? this.options.prepend + suffix.toString() : suffix.toString();\n    if (this.options.compatibilityJSON === 'v1') {\n      if (suffix === 1) return '';\n      if (typeof suffix === 'number') return `_plural_${suffix.toString()}`;\n      return returnSuffix();\n    } else if (this.options.compatibilityJSON === 'v2') {\n      return returnSuffix();\n    } else if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n      return returnSuffix();\n    }\n    return this.options.prepend && idx.toString() ? this.options.prepend + idx.toString() : idx.toString();\n  }\n  shouldUseIntlApi() {\n    return !nonIntlVersions.includes(this.options.compatibilityJSON);\n  }\n}\n\nconst deepFindWithDefaults = function (data, defaultData, key) {\n  let keySeparator = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '.';\n  let ignoreJSONStructure = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options.interpolation && options.interpolation.format || (value => value);\n    this.init(options);\n  }\n  init() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp && existingRegExp.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options && options.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if (matchedSingleQuotes && matchedSingleQuotes.length % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      let doReduce = false;\n      if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n        const r = match[1].split(this.formatSeparator).map(elem => elem.trim());\n        match[1] = r.shift();\n        formatters = r;\n        doReduce = true;\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (doReduce) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (val, lng, options) => {\n    let optForCache = options;\n    if (options && options.interpolationkey && options.formatParams && options.formatParams[options.interpolationkey] && options[options.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [options.interpolationkey]: undefined\n      };\n    }\n    const key = lng + JSON.stringify(optForCache);\n    let formatter = cache[key];\n    if (!formatter) {\n      formatter = fn(getCleanedCode(lng), options);\n      cache[key] = formatter;\n    }\n    return formatter(val);\n  };\n};\nclass Formatter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.formats = {\n      number: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n    this.init(options);\n  }\n  init(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options && options.formatParams && options.formatParams[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    if (this.backend && this.backend.init) {\n      this.backend.init(services, options.backend, options);\n    }\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName) {\n    let tried = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n    let wait = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : this.retryTimeout;\n    let callback = arguments.length > 5 ? arguments[5] : undefined;\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let callback = arguments.length > 3 ? arguments[3] : undefined;\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name) {\n    let prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n    let clb = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : () => {};\n    if (this.services.utils && this.services.utils.hasLoadedNamespace && !this.services.utils.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend && this.backend.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initImmediate: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  }\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs && options.supportedLngs.indexOf('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initImmediate) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init() {\n    var _this = this;\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!options.defaultNS && options.defaultNS !== false && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    if (this.options.compatibilityAPI !== 'v1') {\n      this.options.interpolation = {\n        ...defOpts.interpolation,\n        ...this.options.interpolation\n      };\n    }\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else if (typeof Intl !== 'undefined') {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        compatibilityJSON: this.options.compatibilityJSON,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', function (event) {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        _this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', function (event) {\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        _this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = function () {\n        return _this.store[fcName](...arguments);\n      };\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = function () {\n        _this.store[fcName](...arguments);\n        return _this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && this.options.compatibilityAPI !== 'v1' && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initImmediate) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language) {\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng && usedLng.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      if (this.options.preload) {\n        this.options.preload.forEach(l => append(l));\n      }\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n  }\n  changeLanguage(lng, callback) {\n    var _this2 = this;\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        setLngProps(l);\n        this.translator.changeLanguage(l);\n        this.isLanguageChangingTo = undefined;\n        this.emit('languageChanged', l);\n        this.logger.log('languageChanged', l);\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve(function () {\n        return _this2.t(...arguments);\n      });\n      if (callback) callback(err, function () {\n        return _this2.t(...arguments);\n      });\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const l = isString(lngs) ? lngs : this.services.languageUtils.getBestMatchFromCodes(lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        if (this.services.languageDetector && this.services.languageDetector.cacheUserLanguage) this.services.languageDetector.cacheUserLanguage(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    var _this3 = this;\n    const fixedT = function (key, opts) {\n      let options;\n      if (typeof opts !== 'object') {\n        for (var _len3 = arguments.length, rest = new Array(_len3 > 2 ? _len3 - 2 : 0), _key3 = 2; _key3 < _len3; _key3++) {\n          rest[_key3 - 2] = arguments[_key3];\n        }\n        options = _this3.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        options = {\n          ...opts\n        };\n      }\n      options.lng = options.lng || fixedT.lng;\n      options.lngs = options.lngs || fixedT.lngs;\n      options.ns = options.ns || fixedT.ns;\n      if (options.keyPrefix !== '') options.keyPrefix = options.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = _this3.options.keySeparator || '.';\n      let resultKey;\n      if (options.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${options.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = options.keyPrefix ? `${options.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return _this3.t(resultKey, options);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t() {\n    return this.translator && this.translator.translate(...arguments);\n  }\n  exists() {\n    return this.translator && this.translator.exists(...arguments);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages && this.languages.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services && this.services.languageUtils || new LanguageUtil(get());\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    return new I18n(options, callback);\n  }\n  cloneInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      clone.store = new ResourceStore(this.store.data, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', function (event) {\n      for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n        args[_key4 - 1] = arguments[_key4];\n      }\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n", "export function warn() {\n  if (console && console.warn) {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (typeof args[0] === 'string') args[0] = `react-i18next:: ${args[0]}`;\n    console.warn(...args);\n  }\n}\nconst alreadyWarned = {};\nexport function warnOnce() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  if (typeof args[0] === 'string' && alreadyWarned[args[0]]) return;\n  if (typeof args[0] === 'string') alreadyWarned[args[0]] = new Date();\n  warn(...args);\n}\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport function loadNamespaces(i18n, ns, cb) {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n}\nexport function loadLanguages(i18n, lng, ns, cb) {\n  if (typeof ns === 'string') ns = [ns];\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n}\nfunction oldI18nextHasLoadedNamespace(ns, i18n) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const lng = i18n.languages[0];\n  const fallbackLng = i18n.options ? i18n.options.fallbackLng : false;\n  const lastLng = i18n.languages[i18n.languages.length - 1];\n  if (lng.toLowerCase() === 'cimode') return true;\n  const loadNotPending = (l, n) => {\n    const loadState = i18n.services.backendConnector.state[`${l}|${n}`];\n    return loadState === -1 || loadState === 2;\n  };\n  if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18n.services.backendConnector.backend && i18n.isLanguageChangingTo && !loadNotPending(i18n.isLanguageChangingTo, ns)) return false;\n  if (i18n.hasResourceBundle(lng, ns)) return true;\n  if (!i18n.services.backendConnector.backend || i18n.options.resources && !i18n.options.partialBundledLanguages) return true;\n  if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n  return false;\n}\nexport function hasLoadedNamespace(ns, i18n) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce('i18n.languages were undefined or empty', i18n.languages);\n    return true;\n  }\n  const isNewerI18next = i18n.options.ignoreJSONStructure !== undefined;\n  if (!isNewerI18next) {\n    return oldI18nextHasLoadedNamespace(ns, i18n, options);\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n}\nexport function getDisplayName(Component) {\n  return Component.displayName || Component.name || (typeof Component === 'string' && Component.length > 0 ? Component : 'Unknown');\n}", "const matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nexport const unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);", "import { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport function setDefaults() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n}\nexport function getDefaults() {\n  return defaultOptions;\n}", "let i18nInstance;\nexport function setI18n(instance) {\n  i18nInstance = instance;\n}\nexport function getI18n() {\n  return i18nInstance;\n}", "import { setDefaults } from './defaults.js';\nimport { setI18n } from './i18nInstance.js';\nexport const initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};", "import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport function composeInitialProps(ForComponent) {\n  return ctx => new Promise(resolve => {\n    const i18nInitialProps = getInitialProps();\n    if (ForComponent.getInitialProps) {\n      ForComponent.getInitialProps(ctx).then(componentsInitialProps => {\n        resolve({\n          ...componentsInitialProps,\n          ...i18nInitialProps\n        });\n      });\n    } else {\n      resolve(i18nInitialProps);\n    }\n  });\n}\nexport function getInitialProps() {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces ? i18n.reportNamespaces.getUsedNamespaces() : [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n}", "import { useState, useEffect, useContext, useRef } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nexport function useTranslation(ns) {\n  let props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (typeof optsOrDefaultValue === 'string') return optsOrDefaultValue;\n      if (optsOrDefaultValue && typeof optsOrDefaultValue === 'object' && typeof optsOrDefaultValue.defaultValue === 'string') return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react && i18n.options.react.wait !== undefined) warnOnce('It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...getDefaults(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  if (i18n.reportNamespaces.addUsedNamespaces) i18n.reportNamespaces.addUsedNamespaces(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  function getT() {\n    return i18n.getFixedT(props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  }\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getT);\n    }\n    function boundReset() {\n      if (isMounted.current) setT(getT);\n    }\n    if (bindI18n && i18n) i18n.on(bindI18n, boundReset);\n    if (bindI18nStore && i18n) i18n.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (bindI18n && i18n) bindI18n.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  const isInitial = useRef(true);\n  useEffect(() => {\n    if (isMounted.current && !isInitial.current) {\n      setT(getT);\n    }\n    isInitial.current = false;\n  }, [i18n, keyPrefix]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n}", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nexport function defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nexport function hasXMLHttpRequest() {\n  return typeof XMLHttpRequest === 'function' || (typeof XMLHttpRequest === \"undefined\" ? \"undefined\" : _typeof(XMLHttpRequest)) === 'object';\n}\nfunction isPromise(maybePromise) {\n  return !!maybePromise && typeof maybePromise.then === 'function';\n}\nexport function makePromise(maybePromise) {\n  if (isPromise(maybePromise)) {\n    return maybePromise;\n  }\n  return Promise.resolve(maybePromise);\n}", "// Save global object in a variable\nvar __global__ =\n(typeof globalThis !== 'undefined' && globalThis) ||\n(typeof self !== 'undefined' && self) ||\n(typeof global !== 'undefined' && global);\n// Create an object that extends from __global__ without the fetch function\nvar __globalThis__ = (function () {\nfunction F() {\nthis.fetch = false;\nthis.DOMException = __global__.DOMException\n}\nF.prototype = __global__; // Needed for feature detection on whatwg-fetch's code\nreturn new F();\n})();\n// Wraps whatwg-fetch with a function scope to hijack the global object\n// \"globalThis\" that's going to be patched\n(function(globalThis) {\n\nvar irrelevant = (function (exports) {\n\n  var global =\n    (typeof globalThis !== 'undefined' && globalThis) ||\n    (typeof self !== 'undefined' && self) ||\n    (typeof global !== 'undefined' && global);\n\n  var support = {\n    searchParams: 'URLSearchParams' in global,\n    iterable: 'Symbol' in global && 'iterator' in Symbol,\n    blob:\n      'FileReader' in global &&\n      'Blob' in global &&\n      (function() {\n        try {\n          new Blob();\n          return true\n        } catch (e) {\n          return false\n        }\n      })(),\n    formData: 'FormData' in global,\n    arrayBuffer: 'ArrayBuffer' in global\n  };\n\n  function isDataView(obj) {\n    return obj && DataView.prototype.isPrototypeOf(obj)\n  }\n\n  if (support.arrayBuffer) {\n    var viewClasses = [\n      '[object Int8Array]',\n      '[object Uint8Array]',\n      '[object Uint8ClampedArray]',\n      '[object Int16Array]',\n      '[object Uint16Array]',\n      '[object Int32Array]',\n      '[object Uint32Array]',\n      '[object Float32Array]',\n      '[object Float64Array]'\n    ];\n\n    var isArrayBufferView =\n      ArrayBuffer.isView ||\n      function(obj) {\n        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n      };\n  }\n\n  function normalizeName(name) {\n    if (typeof name !== 'string') {\n      name = String(name);\n    }\n    if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n      throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n    }\n    return name.toLowerCase()\n  }\n\n  function normalizeValue(value) {\n    if (typeof value !== 'string') {\n      value = String(value);\n    }\n    return value\n  }\n\n  // Build a destructive iterator for the value list\n  function iteratorFor(items) {\n    var iterator = {\n      next: function() {\n        var value = items.shift();\n        return {done: value === undefined, value: value}\n      }\n    };\n\n    if (support.iterable) {\n      iterator[Symbol.iterator] = function() {\n        return iterator\n      };\n    }\n\n    return iterator\n  }\n\n  function Headers(headers) {\n    this.map = {};\n\n    if (headers instanceof Headers) {\n      headers.forEach(function(value, name) {\n        this.append(name, value);\n      }, this);\n    } else if (Array.isArray(headers)) {\n      headers.forEach(function(header) {\n        this.append(header[0], header[1]);\n      }, this);\n    } else if (headers) {\n      Object.getOwnPropertyNames(headers).forEach(function(name) {\n        this.append(name, headers[name]);\n      }, this);\n    }\n  }\n\n  Headers.prototype.append = function(name, value) {\n    name = normalizeName(name);\n    value = normalizeValue(value);\n    var oldValue = this.map[name];\n    this.map[name] = oldValue ? oldValue + ', ' + value : value;\n  };\n\n  Headers.prototype['delete'] = function(name) {\n    delete this.map[normalizeName(name)];\n  };\n\n  Headers.prototype.get = function(name) {\n    name = normalizeName(name);\n    return this.has(name) ? this.map[name] : null\n  };\n\n  Headers.prototype.has = function(name) {\n    return this.map.hasOwnProperty(normalizeName(name))\n  };\n\n  Headers.prototype.set = function(name, value) {\n    this.map[normalizeName(name)] = normalizeValue(value);\n  };\n\n  Headers.prototype.forEach = function(callback, thisArg) {\n    for (var name in this.map) {\n      if (this.map.hasOwnProperty(name)) {\n        callback.call(thisArg, this.map[name], name, this);\n      }\n    }\n  };\n\n  Headers.prototype.keys = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push(name);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.values = function() {\n    var items = [];\n    this.forEach(function(value) {\n      items.push(value);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.entries = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push([name, value]);\n    });\n    return iteratorFor(items)\n  };\n\n  if (support.iterable) {\n    Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n  }\n\n  function consumed(body) {\n    if (body.bodyUsed) {\n      return Promise.reject(new TypeError('Already read'))\n    }\n    body.bodyUsed = true;\n  }\n\n  function fileReaderReady(reader) {\n    return new Promise(function(resolve, reject) {\n      reader.onload = function() {\n        resolve(reader.result);\n      };\n      reader.onerror = function() {\n        reject(reader.error);\n      };\n    })\n  }\n\n  function readBlobAsArrayBuffer(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsArrayBuffer(blob);\n    return promise\n  }\n\n  function readBlobAsText(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsText(blob);\n    return promise\n  }\n\n  function readArrayBufferAsText(buf) {\n    var view = new Uint8Array(buf);\n    var chars = new Array(view.length);\n\n    for (var i = 0; i < view.length; i++) {\n      chars[i] = String.fromCharCode(view[i]);\n    }\n    return chars.join('')\n  }\n\n  function bufferClone(buf) {\n    if (buf.slice) {\n      return buf.slice(0)\n    } else {\n      var view = new Uint8Array(buf.byteLength);\n      view.set(new Uint8Array(buf));\n      return view.buffer\n    }\n  }\n\n  function Body() {\n    this.bodyUsed = false;\n\n    this._initBody = function(body) {\n      /*\n        fetch-mock wraps the Response object in an ES6 Proxy to\n        provide useful test harness features such as flush. However, on\n        ES5 browsers without fetch or Proxy support pollyfills must be used;\n        the proxy-pollyfill is unable to proxy an attribute unless it exists\n        on the object before the Proxy is created. This change ensures\n        Response.bodyUsed exists on the instance, while maintaining the\n        semantic of setting Request.bodyUsed in the constructor before\n        _initBody is called.\n      */\n      this.bodyUsed = this.bodyUsed;\n      this._bodyInit = body;\n      if (!body) {\n        this._bodyText = '';\n      } else if (typeof body === 'string') {\n        this._bodyText = body;\n      } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n        this._bodyBlob = body;\n      } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n        this._bodyFormData = body;\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this._bodyText = body.toString();\n      } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n        this._bodyArrayBuffer = bufferClone(body.buffer);\n        // IE 10-11 can't handle a DataView body.\n        this._bodyInit = new Blob([this._bodyArrayBuffer]);\n      } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n        this._bodyArrayBuffer = bufferClone(body);\n      } else {\n        this._bodyText = body = Object.prototype.toString.call(body);\n      }\n\n      if (!this.headers.get('content-type')) {\n        if (typeof body === 'string') {\n          this.headers.set('content-type', 'text/plain;charset=UTF-8');\n        } else if (this._bodyBlob && this._bodyBlob.type) {\n          this.headers.set('content-type', this._bodyBlob.type);\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        }\n      }\n    };\n\n    if (support.blob) {\n      this.blob = function() {\n        var rejected = consumed(this);\n        if (rejected) {\n          return rejected\n        }\n\n        if (this._bodyBlob) {\n          return Promise.resolve(this._bodyBlob)\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as blob')\n        } else {\n          return Promise.resolve(new Blob([this._bodyText]))\n        }\n      };\n\n      this.arrayBuffer = function() {\n        if (this._bodyArrayBuffer) {\n          var isConsumed = consumed(this);\n          if (isConsumed) {\n            return isConsumed\n          }\n          if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n            return Promise.resolve(\n              this._bodyArrayBuffer.buffer.slice(\n                this._bodyArrayBuffer.byteOffset,\n                this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n              )\n            )\n          } else {\n            return Promise.resolve(this._bodyArrayBuffer)\n          }\n        } else {\n          return this.blob().then(readBlobAsArrayBuffer)\n        }\n      };\n    }\n\n    this.text = function() {\n      var rejected = consumed(this);\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return readBlobAsText(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as text')\n      } else {\n        return Promise.resolve(this._bodyText)\n      }\n    };\n\n    if (support.formData) {\n      this.formData = function() {\n        return this.text().then(decode)\n      };\n    }\n\n    this.json = function() {\n      return this.text().then(JSON.parse)\n    };\n\n    return this\n  }\n\n  // HTTP methods whose capitalization should be normalized\n  var methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT'];\n\n  function normalizeMethod(method) {\n    var upcased = method.toUpperCase();\n    return methods.indexOf(upcased) > -1 ? upcased : method\n  }\n\n  function Request(input, options) {\n    if (!(this instanceof Request)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n\n    options = options || {};\n    var body = options.body;\n\n    if (input instanceof Request) {\n      if (input.bodyUsed) {\n        throw new TypeError('Already read')\n      }\n      this.url = input.url;\n      this.credentials = input.credentials;\n      if (!options.headers) {\n        this.headers = new Headers(input.headers);\n      }\n      this.method = input.method;\n      this.mode = input.mode;\n      this.signal = input.signal;\n      if (!body && input._bodyInit != null) {\n        body = input._bodyInit;\n        input.bodyUsed = true;\n      }\n    } else {\n      this.url = String(input);\n    }\n\n    this.credentials = options.credentials || this.credentials || 'same-origin';\n    if (options.headers || !this.headers) {\n      this.headers = new Headers(options.headers);\n    }\n    this.method = normalizeMethod(options.method || this.method || 'GET');\n    this.mode = options.mode || this.mode || null;\n    this.signal = options.signal || this.signal;\n    this.referrer = null;\n\n    if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n      throw new TypeError('Body not allowed for GET or HEAD requests')\n    }\n    this._initBody(body);\n\n    if (this.method === 'GET' || this.method === 'HEAD') {\n      if (options.cache === 'no-store' || options.cache === 'no-cache') {\n        // Search for a '_' parameter in the query string\n        var reParamSearch = /([?&])_=[^&]*/;\n        if (reParamSearch.test(this.url)) {\n          // If it already exists then set the value with the current time\n          this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime());\n        } else {\n          // Otherwise add a new '_' parameter to the end with the current time\n          var reQueryString = /\\?/;\n          this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime();\n        }\n      }\n    }\n  }\n\n  Request.prototype.clone = function() {\n    return new Request(this, {body: this._bodyInit})\n  };\n\n  function decode(body) {\n    var form = new FormData();\n    body\n      .trim()\n      .split('&')\n      .forEach(function(bytes) {\n        if (bytes) {\n          var split = bytes.split('=');\n          var name = split.shift().replace(/\\+/g, ' ');\n          var value = split.join('=').replace(/\\+/g, ' ');\n          form.append(decodeURIComponent(name), decodeURIComponent(value));\n        }\n      });\n    return form\n  }\n\n  function parseHeaders(rawHeaders) {\n    var headers = new Headers();\n    // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n    // https://tools.ietf.org/html/rfc7230#section-3.2\n    var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n    // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n    // https://github.com/github/fetch/issues/748\n    // https://github.com/zloirock/core-js/issues/751\n    preProcessedHeaders\n      .split('\\r')\n      .map(function(header) {\n        return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n      })\n      .forEach(function(line) {\n        var parts = line.split(':');\n        var key = parts.shift().trim();\n        if (key) {\n          var value = parts.join(':').trim();\n          headers.append(key, value);\n        }\n      });\n    return headers\n  }\n\n  Body.call(Request.prototype);\n\n  function Response(bodyInit, options) {\n    if (!(this instanceof Response)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n    if (!options) {\n      options = {};\n    }\n\n    this.type = 'default';\n    this.status = options.status === undefined ? 200 : options.status;\n    this.ok = this.status >= 200 && this.status < 300;\n    this.statusText = options.statusText === undefined ? '' : '' + options.statusText;\n    this.headers = new Headers(options.headers);\n    this.url = options.url || '';\n    this._initBody(bodyInit);\n  }\n\n  Body.call(Response.prototype);\n\n  Response.prototype.clone = function() {\n    return new Response(this._bodyInit, {\n      status: this.status,\n      statusText: this.statusText,\n      headers: new Headers(this.headers),\n      url: this.url\n    })\n  };\n\n  Response.error = function() {\n    var response = new Response(null, {status: 0, statusText: ''});\n    response.type = 'error';\n    return response\n  };\n\n  var redirectStatuses = [301, 302, 303, 307, 308];\n\n  Response.redirect = function(url, status) {\n    if (redirectStatuses.indexOf(status) === -1) {\n      throw new RangeError('Invalid status code')\n    }\n\n    return new Response(null, {status: status, headers: {location: url}})\n  };\n\n  exports.DOMException = global.DOMException;\n  try {\n    new exports.DOMException();\n  } catch (err) {\n    exports.DOMException = function(message, name) {\n      this.message = message;\n      this.name = name;\n      var error = Error(message);\n      this.stack = error.stack;\n    };\n    exports.DOMException.prototype = Object.create(Error.prototype);\n    exports.DOMException.prototype.constructor = exports.DOMException;\n  }\n\n  function fetch(input, init) {\n    return new Promise(function(resolve, reject) {\n      var request = new Request(input, init);\n\n      if (request.signal && request.signal.aborted) {\n        return reject(new exports.DOMException('Aborted', 'AbortError'))\n      }\n\n      var xhr = new XMLHttpRequest();\n\n      function abortXhr() {\n        xhr.abort();\n      }\n\n      xhr.onload = function() {\n        var options = {\n          status: xhr.status,\n          statusText: xhr.statusText,\n          headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n        };\n        options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n        var body = 'response' in xhr ? xhr.response : xhr.responseText;\n        setTimeout(function() {\n          resolve(new Response(body, options));\n        }, 0);\n      };\n\n      xhr.onerror = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.ontimeout = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.onabort = function() {\n        setTimeout(function() {\n          reject(new exports.DOMException('Aborted', 'AbortError'));\n        }, 0);\n      };\n\n      function fixUrl(url) {\n        try {\n          return url === '' && global.location.href ? global.location.href : url\n        } catch (e) {\n          return url\n        }\n      }\n\n      xhr.open(request.method, fixUrl(request.url), true);\n\n      if (request.credentials === 'include') {\n        xhr.withCredentials = true;\n      } else if (request.credentials === 'omit') {\n        xhr.withCredentials = false;\n      }\n\n      if ('responseType' in xhr) {\n        if (support.blob) {\n          xhr.responseType = 'blob';\n        } else if (\n          support.arrayBuffer &&\n          request.headers.get('Content-Type') &&\n          request.headers.get('Content-Type').indexOf('application/octet-stream') !== -1\n        ) {\n          xhr.responseType = 'arraybuffer';\n        }\n      }\n\n      if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers)) {\n        Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n          xhr.setRequestHeader(name, normalizeValue(init.headers[name]));\n        });\n      } else {\n        request.headers.forEach(function(value, name) {\n          xhr.setRequestHeader(name, value);\n        });\n      }\n\n      if (request.signal) {\n        request.signal.addEventListener('abort', abortXhr);\n\n        xhr.onreadystatechange = function() {\n          // DONE (success or failure)\n          if (xhr.readyState === 4) {\n            request.signal.removeEventListener('abort', abortXhr);\n          }\n        };\n      }\n\n      xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n    })\n  }\n\n  fetch.polyfill = true;\n\n  if (!global.fetch) {\n    global.fetch = fetch;\n    global.Headers = Headers;\n    global.Request = Request;\n    global.Response = Response;\n  }\n\n  exports.Headers = Headers;\n  exports.Request = Request;\n  exports.Response = Response;\n  exports.fetch = fetch;\n\n  return exports;\n\n})({});\n})(__globalThis__);\n// This is a ponyfill, so...\n__globalThis__.fetch.ponyfill = true;\ndelete __globalThis__.fetch.polyfill;\n// Choose between native implementation (__global__) or custom implementation (__globalThis__)\nvar ctx = __global__.fetch ? __global__ : __globalThis__;\nexports = ctx.fetch // To enable: import fetch from 'cross-fetch'\nexports.default = ctx.fetch // For TypeScript consumers without esModuleInterop.\nexports.fetch = ctx.fetch // To enable: import {fetch} from 'cross-fetch'\nexports.Headers = ctx.Headers\nexports.Request = ctx.Request\nexports.Response = ctx.Response\nmodule.exports = exports\n", "var fetchApi = typeof fetch === 'function' ? fetch : undefined\nif (typeof global !== 'undefined' && global.fetch) {\n  fetchApi = global.fetch\n} else if (typeof window !== 'undefined' && window.fetch) {\n  fetchApi = window.fetch\n}\n\nif (typeof require !== 'undefined' && typeof window === 'undefined') {\n  var f = fetchApi || require('cross-fetch')\n  if (f.default) f = f.default\n  exports.default = f\n  module.exports = exports.default\n}\n", "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nimport { hasXMLHttpRequest } from './utils.js';\nimport * as fetchNode from './getFetch.cjs';\nvar fetchApi = typeof fetch === 'function' ? fetch : undefined;\nif (typeof global !== 'undefined' && global.fetch) {\n  fetchApi = global.fetch;\n} else if (typeof window !== 'undefined' && window.fetch) {\n  fetchApi = window.fetch;\n}\nvar XmlHttpRequestApi;\nif (hasXMLHttpRequest()) {\n  if (typeof global !== 'undefined' && global.XMLHttpRequest) {\n    XmlHttpRequestApi = global.XMLHttpRequest;\n  } else if (typeof window !== 'undefined' && window.XMLHttpRequest) {\n    XmlHttpRequestApi = window.XMLHttpRequest;\n  }\n}\nvar ActiveXObjectApi;\nif (typeof ActiveXObject === 'function') {\n  if (typeof global !== 'undefined' && global.ActiveXObject) {\n    ActiveXObjectApi = global.ActiveXObject;\n  } else if (typeof window !== 'undefined' && window.ActiveXObject) {\n    ActiveXObjectApi = window.ActiveXObject;\n  }\n}\nif (!fetchApi && fetchNode && !XmlHttpRequestApi && !ActiveXObjectApi) fetchApi = fetchNode.default || fetchNode;\nif (typeof fetchApi !== 'function') fetchApi = undefined;\nvar addQueryString = function addQueryString(url, params) {\n  if (params && _typeof(params) === 'object') {\n    var queryString = '';\n    for (var paramName in params) {\n      queryString += '&' + encodeURIComponent(paramName) + '=' + encodeURIComponent(params[paramName]);\n    }\n    if (!queryString) return url;\n    url = url + (url.indexOf('?') !== -1 ? '&' : '?') + queryString.slice(1);\n  }\n  return url;\n};\nvar fetchIt = function fetchIt(url, fetchOptions, callback, altFetch) {\n  var resolver = function resolver(response) {\n    if (!response.ok) return callback(response.statusText || 'Error', {\n      status: response.status\n    });\n    response.text().then(function (data) {\n      callback(null, {\n        status: response.status,\n        data: data\n      });\n    }).catch(callback);\n  };\n  if (altFetch) {\n    var altResponse = altFetch(url, fetchOptions);\n    if (altResponse instanceof Promise) {\n      altResponse.then(resolver).catch(callback);\n      return;\n    }\n  }\n  if (typeof fetch === 'function') {\n    fetch(url, fetchOptions).then(resolver).catch(callback);\n  } else {\n    fetchApi(url, fetchOptions).then(resolver).catch(callback);\n  }\n};\nvar omitFetchOptions = false;\nvar requestWithFetch = function requestWithFetch(options, url, payload, callback) {\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  var headers = _objectSpread({}, typeof options.customHeaders === 'function' ? options.customHeaders() : options.customHeaders);\n  if (typeof window === 'undefined' && typeof global !== 'undefined' && typeof global.process !== 'undefined' && global.process.versions && global.process.versions.node) {\n    headers['User-Agent'] = \"i18next-http-backend (node/\".concat(global.process.version, \"; \").concat(global.process.platform, \" \").concat(global.process.arch, \")\");\n  }\n  if (payload) headers['Content-Type'] = 'application/json';\n  var reqOptions = typeof options.requestOptions === 'function' ? options.requestOptions(payload) : options.requestOptions;\n  var fetchOptions = _objectSpread({\n    method: payload ? 'POST' : 'GET',\n    body: payload ? options.stringify(payload) : undefined,\n    headers: headers\n  }, omitFetchOptions ? {} : reqOptions);\n  var altFetch = typeof options.alternateFetch === 'function' && options.alternateFetch.length >= 1 ? options.alternateFetch : undefined;\n  try {\n    fetchIt(url, fetchOptions, callback, altFetch);\n  } catch (e) {\n    if (!reqOptions || Object.keys(reqOptions).length === 0 || !e.message || e.message.indexOf('not implemented') < 0) {\n      return callback(e);\n    }\n    try {\n      Object.keys(reqOptions).forEach(function (opt) {\n        delete fetchOptions[opt];\n      });\n      fetchIt(url, fetchOptions, callback, altFetch);\n      omitFetchOptions = true;\n    } catch (err) {\n      callback(err);\n    }\n  }\n};\nvar requestWithXmlHttpRequest = function requestWithXmlHttpRequest(options, url, payload, callback) {\n  if (payload && _typeof(payload) === 'object') {\n    payload = addQueryString('', payload).slice(1);\n  }\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  try {\n    var x;\n    if (XmlHttpRequestApi) {\n      x = new XmlHttpRequestApi();\n    } else {\n      x = new ActiveXObjectApi('MSXML2.XMLHTTP.3.0');\n    }\n    x.open(payload ? 'POST' : 'GET', url, 1);\n    if (!options.crossDomain) {\n      x.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n    }\n    x.withCredentials = !!options.withCredentials;\n    if (payload) {\n      x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n    }\n    if (x.overrideMimeType) {\n      x.overrideMimeType('application/json');\n    }\n    var h = options.customHeaders;\n    h = typeof h === 'function' ? h() : h;\n    if (h) {\n      for (var i in h) {\n        x.setRequestHeader(i, h[i]);\n      }\n    }\n    x.onreadystatechange = function () {\n      x.readyState > 3 && callback(x.status >= 400 ? x.statusText : null, {\n        status: x.status,\n        data: x.responseText\n      });\n    };\n    x.send(payload);\n  } catch (e) {\n    console && console.log(e);\n  }\n};\nvar request = function request(options, url, payload, callback) {\n  if (typeof payload === 'function') {\n    callback = payload;\n    payload = undefined;\n  }\n  callback = callback || function () {};\n  if (fetchApi && url.indexOf('file:') !== 0) {\n    return requestWithFetch(options, url, payload, callback);\n  }\n  if (hasXMLHttpRequest() || typeof ActiveXObject === 'function') {\n    return requestWithXmlHttpRequest(options, url, payload, callback);\n  }\n  callback(new Error('No fetch and no xhr implementation found!'));\n};\nexport default request;", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { makePromise } from './utils.js';\nimport request from './request.js';\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/add/{{lng}}/{{ns}}',\n    parse: function parse(data) {\n      return JSON.parse(data);\n    },\n    stringify: JSON.stringify,\n    parsePayload: function parsePayload(namespace, key, fallbackValue) {\n      return _defineProperty({}, key, fallbackValue || '');\n    },\n    parseLoadPayload: function parseLoadPayload(languages, namespaces) {\n      return undefined;\n    },\n    request: request,\n    reloadInterval: typeof window !== 'undefined' ? false : 60 * 60 * 1000,\n    customHeaders: {},\n    queryStringParams: {},\n    crossDomain: false,\n    withCredentials: false,\n    overrideMimeType: false,\n    requestOptions: {\n      mode: 'cors',\n      credentials: 'same-origin',\n      cache: 'default'\n    }\n  };\n};\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    _classCallCheck(this, Backend);\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n  return _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var _this = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), this.options || {}), options);\n      this.allOptions = allOptions;\n      if (this.services && this.options.reloadInterval) {\n        var timer = setInterval(function () {\n          return _this.reload();\n        }, this.options.reloadInterval);\n        if (_typeof(timer) === 'object' && typeof timer.unref === 'function') timer.unref();\n      }\n    }\n  }, {\n    key: \"readMulti\",\n    value: function readMulti(languages, namespaces, callback) {\n      this._readAny(languages, languages, namespaces, namespaces, callback);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      this._readAny([language], language, [namespace], namespace, callback);\n    }\n  }, {\n    key: \"_readAny\",\n    value: function _readAny(languages, loadUrlLanguages, namespaces, loadUrlNamespaces, callback) {\n      var _this2 = this;\n      var loadPath = this.options.loadPath;\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(languages, namespaces);\n      }\n      loadPath = makePromise(loadPath);\n      loadPath.then(function (resolvedLoadPath) {\n        if (!resolvedLoadPath) return callback(null, {});\n        var url = _this2.services.interpolator.interpolate(resolvedLoadPath, {\n          lng: languages.join('+'),\n          ns: namespaces.join('+')\n        });\n        _this2.loadUrl(url, callback, loadUrlLanguages, loadUrlNamespaces);\n      });\n    }\n  }, {\n    key: \"loadUrl\",\n    value: function loadUrl(url, callback, languages, namespaces) {\n      var _this3 = this;\n      var lng = typeof languages === 'string' ? [languages] : languages;\n      var ns = typeof namespaces === 'string' ? [namespaces] : namespaces;\n      var payload = this.options.parseLoadPayload(lng, ns);\n      this.options.request(this.options, url, payload, function (err, res) {\n        if (res && (res.status >= 500 && res.status < 600 || !res.status)) return callback('failed loading ' + url + '; status code: ' + res.status, true);\n        if (res && res.status >= 400 && res.status < 500) return callback('failed loading ' + url + '; status code: ' + res.status, false);\n        if (!res && err && err.message) {\n          var errorMessage = err.message.toLowerCase();\n          var isNetworkError = ['failed', 'fetch', 'network', 'load'].find(function (term) {\n            return errorMessage.indexOf(term) > -1;\n          });\n          if (isNetworkError) {\n            return callback('failed loading ' + url + ': ' + err.message, true);\n          }\n        }\n        if (err) return callback(err, false);\n        var ret, parseErr;\n        try {\n          if (typeof res.data === 'string') {\n            ret = _this3.options.parse(res.data, languages, namespaces);\n          } else {\n            ret = res.data;\n          }\n        } catch (e) {\n          parseErr = 'failed parsing ' + url + ' to json';\n        }\n        if (parseErr) return callback(parseErr, false);\n        callback(null, ret);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this4 = this;\n      if (!this.options.addPath) return;\n      if (typeof languages === 'string') languages = [languages];\n      var payload = this.options.parsePayload(namespace, key, fallbackValue);\n      var finished = 0;\n      var dataArray = [];\n      var resArray = [];\n      languages.forEach(function (lng) {\n        var addPath = _this4.options.addPath;\n        if (typeof _this4.options.addPath === 'function') {\n          addPath = _this4.options.addPath(lng, namespace);\n        }\n        var url = _this4.services.interpolator.interpolate(addPath, {\n          lng: lng,\n          ns: namespace\n        });\n        _this4.options.request(_this4.options, url, payload, function (data, res) {\n          finished += 1;\n          dataArray.push(data);\n          resArray.push(res);\n          if (finished === languages.length) {\n            if (typeof callback === 'function') callback(dataArray, resArray);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reload\",\n    value: function reload() {\n      var _this5 = this;\n      var _this$services = this.services,\n        backendConnector = _this$services.backendConnector,\n        languageUtils = _this$services.languageUtils,\n        logger = _this$services.logger;\n      var currentLanguage = backendConnector.language;\n      if (currentLanguage && currentLanguage.toLowerCase() === 'cimode') return;\n      var toLoad = [];\n      var append = function append(lng) {\n        var lngs = languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(function (l) {\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      append(currentLanguage);\n      if (this.allOptions.preload) this.allOptions.preload.forEach(function (l) {\n        return append(l);\n      });\n      toLoad.forEach(function (lng) {\n        _this5.allOptions.ns.forEach(function (ns) {\n          backendConnector.read(lng, ns, 'read', null, null, function (err, data) {\n            if (err) logger.warn(\"loading namespace \".concat(ns, \" for language \").concat(lng, \" failed\"), err);\n            if (!err && data) logger.log(\"loaded namespace \".concat(ns, \" for language \").concat(lng), data);\n            backendConnector.loaded(\"\".concat(lng, \"|\").concat(ns), err, data);\n          });\n        });\n      });\n    }\n  }]);\n}();\nBackend.type = 'backend';\nexport default Backend;"], "names": ["isString", "obj", "defer", "res", "rej", "promise", "Promise", "resolve", "reject", "makeString", "object", "lastOfPathSeparatorRegExp", "<PERSON><PERSON><PERSON>", "key", "indexOf", "replace", "canNotTraverseDeeper", "getLastOfPath", "path", "Empty", "stack", "split", "stackIndex", "length", "Object", "prototype", "hasOwnProperty", "call", "k", "set<PERSON>ath", "newValue", "e", "p", "slice", "last", "<PERSON><PERSON><PERSON>", "deepExtend", "target", "source", "overwrite", "prop", "String", "regexEscape", "str", "_entityMap", "escape", "data", "s", "chars", "looksLikeObjectPathRegExpCache", "constructor", "capacity", "this", "regExpMap", "Map", "regExpQueue", "getRegExp", "pattern", "regExpFromCache", "get", "regExpNew", "RegExp", "delete", "shift", "set", "push", "deepFind", "keySeparator", "arguments", "tokens", "current", "i", "next", "nextPath", "j", "getCleanedCode", "code", "consoleLogger", "type", "log", "args", "output", "warn", "error", "console", "<PERSON><PERSON>", "concreteLogger", "options", "init", "prefix", "logger", "debug", "_len", "Array", "_key", "forward", "_len2", "_key2", "_len3", "_key3", "deprecate", "_len4", "_key4", "lvl", "debugOnly", "create", "moduleName", "clone", "baseLogger", "EventEmitter", "observers", "on", "events", "listener", "for<PERSON>ach", "event", "numListeners", "off", "emit", "from", "entries", "_ref", "observer", "numTimesAdded", "_ref2", "apply", "ResourceStore", "ns", "defaultNS", "super", "ignoreJSONStructure", "addNamespaces", "removeNamespaces", "index", "splice", "getResource", "lng", "isArray", "result", "join", "addResource", "value", "silent", "concat", "addResources", "resources", "m", "addResourceBundle", "deep", "skipCopy", "pack", "JSON", "parse", "stringify", "removeResourceBundle", "hasResourceBundle", "getResourceBundle", "compatibilityAPI", "getDataByLanguage", "hasLanguageSomeTranslations", "keys", "find", "v", "toJSON", "postProcessor", "processors", "addPostProcessor", "module", "name", "handle", "translator", "processor", "process", "checkedLoadedFor", "Translator", "services", "t", "changeLanguage", "language", "exists", "interpolation", "resolved", "extractFromKey", "nsSeparator", "namespaces", "wouldCheckForNsInKey", "seemsNaturalLanguage", "userDefinedKeySeparator", "userDefinedNsSeparator", "possibleChars", "filter", "c", "r", "map", "matched", "test", "ki", "substring", "looksLikeObjectPath", "match", "interpolator", "nestingRegexp", "parts", "translate", "last<PERSON>ey", "overloadTranslationOptionHandler", "returnDetails", "namespace", "appendNamespaceToCIMode", "toLowerCase", "usedKey", "exactUsed<PERSON>ey", "usedLng", "usedNS", "usedParams", "getUsedParamsDetails", "resUsed<PERSON><PERSON>", "resExactUsedKey", "resType", "toString", "joinArrays", "handleAsObjectInI18nFormat", "i18nFormat", "handleAsObject", "extendTranslation", "usedDefault", "needsPluralHandling", "count", "hasDefaultValue", "defaultValueSuffix", "pluralResolver", "getSuffix", "defaultValueSuffixOrdinalFallback", "ordinal", "needsZeroSuffixLookup", "shouldUseIntlApi", "defaultValue", "pluralSeparator", "isValidLookup", "resForMissing", "missingKeyNoValueFallbackToKey", "updateMissing", "fk", "lngs", "fallbackLngs", "languageUtils", "getFallbackCodes", "fallbackLng", "saveMissingTo", "toResolveHierarchy", "send", "l", "specificDefaultValue", "defaultForMissing", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backendConnector", "saveMissing", "saveMissingPlurals", "suffixes", "getSuffixes", "suffix", "appendNamespaceToMissingKey", "parseMissingKeyHandler", "returnObjects", "returnedObjectHandler", "resTypeIsArray", "copy", "newKeyToUse", "<PERSON><PERSON><PERSON>", "_this", "defaultVariables", "skipInterpolation", "skipOnVariables", "nestBef", "nb", "interpolate", "na", "nest", "context", "reset", "postProcess", "postProcessorNames", "applyPostProcessor", "postProcessPassResolved", "i18nResolved", "found", "extracted", "fallbackNS", "needsContextHandling", "codes", "utils", "hasLoadedNamespace", "finalKeys", "addLookupKeys", "pluralSuffix", "zeroSuffix", "ordinalPrefix", "<PERSON><PERSON>ey", "contextSeparator", "<PERSON><PERSON><PERSON>", "pop", "returnNull", "returnEmptyString", "resourceStore", "optionsKeys", "useOptionsReplaceForData", "option", "capitalize", "string", "char<PERSON>t", "toUpperCase", "LanguageUtil", "supportedLngs", "getScriptPartFromCode", "formatLanguageCode", "getLanguagePartFromCode", "Intl", "getCanonicalLocales", "formattedCode", "lowerCaseLng", "specialCases", "part", "cleanCode", "isSupportedCode", "load", "nonExplicitSupportedLngs", "getBestMatchFromCodes", "cleanedLng", "lngOnly", "supportedLng", "fallbacks", "default", "fallbackCode", "fallbackCodes", "addCode", "fc", "sets", "nr", "_rulesPluralsTypes", "n", "Number", "nonIntlVersions", "intlVersions", "suffixesOrder", "zero", "one", "two", "few", "many", "other", "PluralResolver", "compatibilityJSON", "includes", "PluralRules", "rules", "numbers", "plurals", "createRules", "pluralRulesCache", "addRule", "clearCache", "getRule", "cleanedCode", "cache<PERSON>ey", "rule", "err", "lngPart", "needsPlural", "resolvedOptions", "pluralCategories", "getPluralFormsOfKey", "sort", "pluralCategory1", "pluralCategory2", "pluralCategory", "prepend", "number", "select", "getSuffixRetroCompatible", "idx", "noAbs", "Math", "abs", "simplifyPluralSuffix", "returnSuffix", "deepFindWithDefaults", "defaultData", "getPathWithDefaults", "regexSafe", "val", "Interpolator", "format", "escapeValue", "escape$1", "useRawValueToEscape", "prefixEscaped", "suffixEscaped", "formatSeparator", "unescapeSuffix", "unescapePrefix", "nestingPrefix", "nestingPrefixEscaped", "nestingSuffix", "nestingSuffixEscaped", "nestingOptionsSeparator", "maxReplaces", "alwaysFormat", "resetRegExp", "getOrResetRegExp", "existingRegExp", "lastIndex", "regexp", "regexpUnescape", "replaces", "handleFormat", "interpolationkey", "trim", "f", "missingInterpolationHandler", "regex", "safeValue", "todo", "exec", "matchedVar", "temp", "clonedOptions", "handleHasOptions", "inheritedOptions", "sep", "optionsString", "matchedSingleQuotes", "matchedDoubleQuotes", "formatters", "doReduce", "elem", "reduce", "createCachedFormatter", "fn", "cache", "optForCache", "formatParams", "formatter", "<PERSON><PERSON><PERSON>", "formats", "opt", "NumberFormat", "currency", "style", "datetime", "DateTimeFormat", "relativetime", "RelativeTimeFormat", "range", "list", "ListFormat", "add", "addCached", "findIndex", "mem", "formatName", "formatOptions", "formatStr", "optStr", "rest", "<PERSON><PERSON><PERSON>", "isNaN", "parseInt", "parseFormatStr", "formatted", "valOptions", "locale", "Connector", "backend", "store", "waitingReads", "maxP<PERSON>llelReads", "readingCalls", "maxRetries", "retryTimeout", "state", "queue", "queueLoad", "languages", "callback", "toLoad", "pending", "toLoadLanguages", "toLoadNamespaces", "hasAllNamespaces", "reload", "pendingCount", "loaded", "errors", "q", "push<PERSON><PERSON>", "removePending", "done", "loadedKeys", "read", "fcName", "tried", "wait", "resolver", "setTimeout", "bind", "then", "catch", "prepareLoading", "loadOne", "fallback<PERSON><PERSON><PERSON>", "isUpdate", "clb", "opts", "initImmediate", "preload", "partialBundledLanguages", "ret", "tDescription", "transformOptions", "noop", "I18n", "inst", "modules", "external", "getOwnPropertyNames", "getPrototypeOf", "isInitialized", "isClone", "isInitializing", "defOpts", "createClassOnDemand", "ClassOrObject", "lu", "languageDetector", "detection", "deferred", "finish", "initializedStoreOnce", "loadResources", "usedCallback", "append", "resolvedLanguage", "setResolvedLanguage", "reloadResources", "use", "Error", "li", "lngInLngs", "_this2", "isLanguageChangingTo", "setLngProps", "setLng", "cacheUserLanguage", "async", "detect", "getFixedT", "keyPrefix", "_this3", "fixedT", "<PERSON><PERSON><PERSON>", "setDefaultNamespace", "lastLng", "loadNotPending", "loadState", "precheck", "preResult", "loadNamespaces", "loadLanguages", "preloaded", "newLngs", "dir", "createInstance", "cloneInstance", "forkResourceStore", "mergedOptions", "instance", "alreadyWarned", "warnOnce", "Date", "loadedClb", "i18n", "cb", "initialized", "matchHtmlEntity", "htmlEntities", "unescapeHtmlEntity", "i18nInstance", "defaultOptions", "bindI18n", "bindI18nStore", "transEmptyNodeValue", "transSupportBasicHtmlNodes", "transWrapTextNodes", "transKeepBasicHtmlNodesFor", "useSuspense", "unescape", "text", "initReactI18next", "react", "setI18n", "I18nContext", "createContext", "ReportNamespaces", "usedNamespaces", "addUsedNamespaces", "getUsedNamespaces", "useTranslation", "props", "i18nFromProps", "i18nFromContext", "defaultNSFromContext", "useContext", "reportNamespaces", "notReadyT", "optsOrDefaultValue", "retNotReady", "ready", "i18nOptions", "every", "oldI18nextHasLoadedNamespace", "getT", "nsMode", "setT", "useState", "joinedNS", "previousJoinedNS", "ignore", "ref", "useRef", "useEffect", "usePrevious", "isMounted", "boundReset", "isInitial", "_typeof", "o", "Symbol", "iterator", "hasXMLHttpRequest", "XMLHttpRequest", "globalThis", "__global__", "self", "global", "__globalThis__", "F", "fetch", "DOMException", "exports", "support", "Blob", "viewClasses", "isArrayBuffer<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "normalizeName", "TypeError", "normalizeValue", "iteratorFor", "items", "Headers", "headers", "header", "consumed", "body", "bodyUsed", "fileReaderReady", "reader", "onload", "onerror", "readBlobAsArrayBuffer", "blob", "FileReader", "readAsA<PERSON>y<PERSON><PERSON>er", "bufferClone", "buf", "view", "Uint8Array", "byteLength", "buffer", "Body", "_initBody", "_bodyInit", "_bodyText", "isPrototypeOf", "_bodyBlob", "FormData", "_bodyFormData", "URLSearchParams", "DataView", "_body<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rejected", "arrayBuffer", "isConsumed", "byteOffset", "readAsText", "fromCharCode", "readArrayBufferAsText", "formData", "decode", "json", "oldValue", "has", "thisArg", "values", "methods", "Request", "input", "method", "upcased", "url", "credentials", "mode", "signal", "referrer", "reParamSearch", "getTime", "form", "bytes", "decodeURIComponent", "Response", "bodyInit", "status", "ok", "statusText", "response", "redirectStatuses", "redirect", "RangeError", "location", "message", "request", "aborted", "xhr", "abortXhr", "abort", "rawHeaders", "getAllResponseHeaders", "substr", "line", "responseURL", "responseText", "ontimeout", "<PERSON>ab<PERSON>", "open", "href", "fixUrl", "withCredentials", "responseType", "setRequestHeader", "addEventListener", "onreadystatechange", "readyState", "removeEventListener", "polyfill", "ponyfill", "ctx", "fetchApi", "window", "require", "require$$0", "ownKeys", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "enumerable", "_objectSpread", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "toPrimitive", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "XmlHttpRequestApi", "ActiveXObjectApi", "ActiveXObject", "fetchNode", "fetchNode.default", "addQueryString", "params", "queryString", "paramName", "encodeURIComponent", "fetchIt", "fetchOptions", "altFetch", "altResponse", "omitFetchOptions", "payload", "queryStringParams", "customHeaders", "versions", "node", "version", "platform", "arch", "reqOptions", "requestOptions", "<PERSON><PERSON><PERSON>ch", "requestWithFetch", "x", "crossDomain", "overrideMimeType", "h", "requestWithXmlHttpRequest", "_createClass", "_defineProperties", "Backend", "allOptions", "a", "_classCallCheck", "loadPath", "addPath", "parsePayload", "parseLoadPayload", "reloadInterval", "timer", "setInterval", "unref", "_readAny", "loadUrlLanguages", "loadUrlNamespaces", "<PERSON><PERSON><PERSON><PERSON>", "isPromise", "resolvedLoadPath", "loadUrl", "errorMessage", "term", "parseErr", "_this4", "finished", "dataArray", "resArray", "_this5", "_this$services", "currentLanguage"], "mappings": "2YAAA,MAAMA,EAAkBC,GAAe,iBAARA,EACzBC,EAAQ,KACR,IAAAC,EACAC,EACJ,MAAMC,EAAU,IAAIC,SAAQ,CAACC,EAASC,KAC9BL,EAAAI,EACAH,EAAAI,CAAA,IAID,OAFPH,EAAQE,QAAUJ,EAClBE,EAAQG,OAASJ,EACVC,CAAA,EAEHI,EAAuBC,GACb,MAAVA,EAAuB,GACpB,GAAKA,EAORC,EAA4B,OAC5BC,EAAWC,GAAOA,GAAOA,EAAIC,QAAQ,QAAS,EAAKD,EAAIE,QAAQJ,EAA2B,KAAOE,EACjGG,EAAuBN,IAAWA,GAAUV,EAASU,GACrDO,EAAgB,CAACP,EAAQQ,EAAMC,KAC7B,MAAAC,EAASpB,EAASkB,GAAeA,EAAKG,MAAM,KAAlBH,EAChC,IAAII,EAAa,EACV,KAAAA,EAAaF,EAAMG,OAAS,GAAG,CACpC,GAAIP,EAAqBN,GAAS,MAAO,CAAE,EAC3C,MAAMG,EAAMD,EAASQ,EAAME,KACtBZ,EAAOG,IAAQM,IAAcT,EAAAG,GAAO,IAAIM,GAE3CT,EADEc,OAAOC,UAAUC,eAAeC,KAAKjB,EAAQG,GACtCH,EAAOG,GAEP,CAAE,IAEXS,CACN,CACE,OAAIN,EAAqBN,GAAgB,CAAE,EACpC,CACLT,IAAKS,EACLkB,EAAGhB,EAASQ,EAAME,IACnB,EAEGO,EAAU,CAACnB,EAAQQ,EAAMY,KACvB,MAAA7B,IACJA,EAAA2B,EACAA,GACEX,EAAcP,EAAQQ,EAAMM,QAChC,QAAY,IAARvB,GAAqC,IAAhBiB,EAAKK,OAE5B,YADAtB,EAAI2B,GAAKE,GAGX,IAAIC,EAAIb,EAAKA,EAAKK,OAAS,GACvBS,EAAId,EAAKe,MAAM,EAAGf,EAAKK,OAAS,GAChCW,EAAOjB,EAAcP,EAAQsB,EAAGR,QACpC,UAAoB,IAAbU,EAAKjC,KAAqB+B,EAAET,QACjCQ,EAAI,GAAGC,EAAEA,EAAET,OAAS,MAAMQ,IAC1BC,EAAIA,EAAEC,MAAM,EAAGD,EAAET,OAAS,GACnBW,EAAAjB,EAAcP,EAAQsB,EAAGR,QAC5BU,GAAQA,EAAKjC,UAA6C,IAA/BiC,EAAKjC,IAAI,GAAGiC,EAAKN,KAAKG,OACnDG,EAAKjC,SAAM,GAGfiC,EAAKjC,IAAI,GAAGiC,EAAKN,KAAKG,KAAOD,CAAA,EAUzBK,EAAU,CAACzB,EAAQQ,KACjB,MAAAjB,IACJA,EAAA2B,EACAA,GACEX,EAAcP,EAAQQ,GACtB,GAACjB,EACL,OAAOA,EAAI2B,EAAC,EASRQ,EAAa,CAACC,EAAQC,EAAQC,KAClC,IAAA,MAAWC,KAAQF,EACJ,cAATE,GAAiC,gBAATA,IACtBA,KAAQH,EACNrC,EAASqC,EAAOG,KAAUH,EAAOG,aAAiBC,QAAUzC,EAASsC,EAAOE,KAAUF,EAAOE,aAAiBC,OAC5GF,IAAWF,EAAOG,GAAQF,EAAOE,IAErCJ,EAAWC,EAAOG,GAAOF,EAAOE,GAAOD,GAGlCF,EAAAG,GAAQF,EAAOE,IAIrB,OAAAH,CAAA,EAEHK,EAAcC,GAAOA,EAAI5B,QAAQ,sCAAuC,QAC9E,IAAI6B,EAAa,CACf,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,QACL,IAAK,UAEP,MAAMC,EAAiBC,GACjB9C,EAAS8C,GACJA,EAAK/B,QAAQ,cAAmBgC,GAAAH,EAAWG,KAE7CD,EAsBT,MAAME,EAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,KAC7BC,EAAiC,IArBvC,MACE,WAAAC,CAAYC,GACVC,KAAKD,SAAWA,EACXC,KAAAC,cAAgBC,IACrBF,KAAKG,YAAc,EACvB,CACE,SAAAC,CAAUC,GACR,MAAMC,EAAkBN,KAAKC,UAAUM,IAAIF,GAC3C,QAAwB,IAApBC,EACK,OAAAA,EAEH,MAAAE,EAAY,IAAIC,OAAOJ,GAMtB,OALHL,KAAKG,YAAYhC,SAAW6B,KAAKD,UACnCC,KAAKC,UAAUS,OAAOV,KAAKG,YAAYQ,SAEpCX,KAAAC,UAAUW,IAAIP,EAASG,GACvBR,KAAAG,YAAYU,KAAKR,GACfG,CACX,GAGuD,IAgBjDM,EAAW,SAAUjE,EAAKiB,GAC1B,IAAAiD,EAAeC,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,IACnF,IAACnE,EAAY,OACjB,GAAIA,EAAIiB,GAAO,OAAOjB,EAAIiB,GACpB,MAAAmD,EAASnD,EAAKG,MAAM8C,GAC1B,IAAIG,EAAUrE,EACd,IAAA,IAASsE,EAAI,EAAGA,EAAIF,EAAO9C,QAAS,CAClC,IAAK+C,GAA8B,iBAAZA,EACd,OAEL,IAAAE,EACAC,EAAW,GACf,IAAA,IAASC,EAAIH,EAAGG,EAAIL,EAAO9C,SAAUmD,EAMnC,GALIA,IAAMH,IACIE,GAAAN,GAEdM,GAAYJ,EAAOK,GACnBF,EAAOF,EAAQG,QACF,IAATD,EAAoB,CACtB,GAAI,CAAC,SAAU,SAAU,WAAW1D,eAAe0D,IAAQ,GAAME,EAAIL,EAAO9C,OAAS,EACnF,SAEFgD,GAAKG,EAAIH,EAAI,EACb,KACR,CAEcD,EAAAE,CACd,CACS,OAAAF,CACT,EACMK,EAAyBC,GAAAA,GAAQA,EAAK7D,QAAQ,IAAK,KAEnD8D,EAAgB,CACpBC,KAAM,SACN,GAAAC,CAAIC,GACG5B,KAAA6B,OAAO,MAAOD,EACpB,EACD,IAAAE,CAAKF,GACE5B,KAAA6B,OAAO,OAAQD,EACrB,EACD,KAAAG,CAAMH,GACC5B,KAAA6B,OAAO,QAASD,EACtB,EACD,MAAAC,CAAOH,EAAME,GACPI,SAAWA,QAAQN,EAC3B,GAEA,MAAMO,EACJ,WAAAnC,CAAYoC,GACN,IAAAC,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAC/EhB,KAAAoC,KAAKF,EAAgBC,EAC9B,CACE,IAAAC,CAAKF,GACC,IAAAC,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAC/EhB,KAAAqC,OAASF,EAAQE,QAAU,WAChCrC,KAAKsC,OAASJ,GAAkBT,EAChCzB,KAAKmC,QAAUA,EACfnC,KAAKuC,MAAQJ,EAAQI,KACzB,CACE,GAAAZ,GACE,IAAA,IAASa,EAAOxB,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMD,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC1Ed,EAAAc,GAAQ1B,UAAU0B,GAEzB,OAAO1C,KAAK2C,QAAQf,EAAM,MAAO,IAAI,EACzC,CACE,IAAAE,GACE,IAAA,IAASc,EAAQ5B,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMG,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC/EjB,EAAAiB,GAAS7B,UAAU6B,GAE1B,OAAO7C,KAAK2C,QAAQf,EAAM,OAAQ,IAAI,EAC1C,CACE,KAAAG,GACE,IAAA,IAASe,EAAQ9B,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMK,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC/EnB,EAAAmB,GAAS/B,UAAU+B,GAE1B,OAAO/C,KAAK2C,QAAQf,EAAM,QAAS,GACvC,CACE,SAAAoB,GACE,IAAA,IAASC,EAAQjC,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMQ,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC/EtB,EAAAsB,GAASlC,UAAUkC,GAE1B,OAAOlD,KAAK2C,QAAQf,EAAM,OAAQ,wBAAwB,EAC9D,CACE,OAAAe,CAAQf,EAAMuB,EAAKd,EAAQe,GACzB,OAAIA,IAAcpD,KAAKuC,MAAc,MACjC3F,EAASgF,EAAK,QAAU,GAAK,GAAGS,IAASrC,KAAKqC,UAAUT,EAAK,MAC1D5B,KAAKsC,OAAOa,GAAKvB,GAC5B,CACE,MAAAyB,CAAOC,GACE,OAAA,IAAIrB,EAAOjC,KAAKsC,OAAQ,CAE3BD,OAAQ,GAAGrC,KAAKqC,UAAUiB,QAEzBtD,KAAKmC,SAEd,CACE,KAAAoB,CAAMpB,GAGJ,OAFAA,EAAUA,GAAWnC,KAAKmC,SAClBE,OAASF,EAAQE,QAAUrC,KAAKqC,OACjC,IAAIJ,EAAOjC,KAAKsC,OAAQH,EACnC,EAEA,IAAIqB,EAAa,IAAIvB,EAErB,MAAMwB,EACJ,WAAA3D,GACEE,KAAK0D,UAAY,CAAE,CACvB,CACE,EAAAC,CAAGC,EAAQC,GAMF,OALPD,EAAO3F,MAAM,KAAK6F,SAAiBC,IAC5B/D,KAAK0D,UAAUK,UAAaL,UAAUK,GAAS,IAAI7D,KACxD,MAAM8D,EAAehE,KAAK0D,UAAUK,GAAOxD,IAAIsD,IAAa,EAC5D7D,KAAK0D,UAAUK,GAAOnD,IAAIiD,EAAUG,EAAe,EAAC,IAE/ChE,IACX,CACE,GAAAiE,CAAIF,EAAOF,GACJ7D,KAAK0D,UAAUK,KACfF,EAIL7D,KAAK0D,UAAUK,GAAOrD,OAAOmD,UAHpB7D,KAAK0D,UAAUK,GAI5B,CACE,IAAAG,CAAKH,GACH,IAAA,IAASvB,EAAOxB,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMD,EAAO,EAAIA,EAAO,EAAI,GAAIE,EAAO,EAAGA,EAAOF,EAAME,IAClGd,EAAKc,EAAO,GAAK1B,UAAU0B,GAEzB,GAAA1C,KAAK0D,UAAUK,GAAQ,CACVtB,MAAM0B,KAAKnE,KAAK0D,UAAUK,GAAOK,WACzCN,SAAgBO,IACjB,IAACC,EAAUC,GAAiBF,EAChC,IAAA,IAASlD,EAAI,EAAGA,EAAIoD,EAAepD,IACjCmD,KAAY1C,EACtB,GAEA,CACQ,GAAA5B,KAAK0D,UAAU,KAAM,CACRjB,MAAM0B,KAAKnE,KAAK0D,UAAU,KAAKU,WACvCN,SAAiBU,IAClB,IAACF,EAAUC,GAAiBC,EAChC,IAAA,IAASrD,EAAI,EAAGA,EAAIoD,EAAepD,IACjCmD,EAASG,MAAMH,EAAU,CAACP,KAAUnC,GAC9C,GAEA,CACA,EAGA,MAAM8C,UAAsBjB,EAC1B,WAAA3D,CAAYJ,GACN,IAAAyC,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAChF2D,GAAI,CAAC,eACLC,UAAW,eAENC,QACF7E,KAAAN,KAAOA,GAAQ,CAAE,EACtBM,KAAKmC,QAAUA,OACmB,IAA9BnC,KAAKmC,QAAQpB,eACff,KAAKmC,QAAQpB,aAAe,UAEW,IAArCf,KAAKmC,QAAQ2C,sBACf9E,KAAKmC,QAAQ2C,qBAAsB,EAEzC,CACE,aAAAC,CAAcJ,GACR3E,KAAKmC,QAAQwC,GAAGjH,QAAQiH,GAAM,GAC3B3E,KAAAmC,QAAQwC,GAAG9D,KAAK8D,EAE3B,CACE,gBAAAK,CAAiBL,GACf,MAAMM,EAAQjF,KAAKmC,QAAQwC,GAAGjH,QAAQiH,GAClCM,GAAY,GACdjF,KAAKmC,QAAQwC,GAAGO,OAAOD,EAAO,EAEpC,CACE,WAAAE,CAAYC,EAAKT,EAAIlH,GACf,IAAA0E,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpF,MAAMD,OAAwC,IAAzBoB,EAAQpB,aAA6BoB,EAAQpB,aAAef,KAAKmC,QAAQpB,aACxF+D,OAAsD,IAAhC3C,EAAQ2C,oBAAoC3C,EAAQ2C,oBAAsB9E,KAAKmC,QAAQ2C,oBAC/G,IAAAhH,EACAsH,EAAI1H,QAAQ,MAAW,EAClBI,EAAAsH,EAAInH,MAAM,MAEVH,EAAA,CAACsH,EAAKT,GACTlH,IACEgF,MAAM4C,QAAQ5H,GACXK,EAAA+C,QAAQpD,GACJb,EAASa,IAAQsD,EAC1BjD,EAAK+C,QAAQpD,EAAIQ,MAAM8C,IAEvBjD,EAAK+C,KAAKpD,KAIhB,MAAM6H,EAASvG,EAAQiB,KAAKN,KAAM5B,GAMlC,OALKwH,IAAWX,IAAOlH,GAAO2H,EAAI1H,QAAQ,MAAW,IACnD0H,EAAMtH,EAAK,GACX6G,EAAK7G,EAAK,GACVL,EAAMK,EAAKe,MAAM,GAAG0G,KAAK,OAEvBD,GAAWR,GAAwBlI,EAASa,GACzCqD,EAASd,KAAKN,MAAQM,KAAKN,KAAK0F,IAAQpF,KAAKN,KAAK0F,GAAKT,GAAKlH,EAAKsD,GADXuE,CAEjE,CACE,WAAAE,CAAYJ,EAAKT,EAAIlH,EAAKgI,GACpB,IAAAtD,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAChF0E,QAAQ,GAEV,MAAM3E,OAAwC,IAAzBoB,EAAQpB,aAA6BoB,EAAQpB,aAAef,KAAKmC,QAAQpB,aAC1F,IAAAjD,EAAO,CAACsH,EAAKT,GACblH,MAAYK,EAAK6H,OAAO5E,EAAetD,EAAIQ,MAAM8C,GAAgBtD,IACjE2H,EAAI1H,QAAQ,MAAW,IAClBI,EAAAsH,EAAInH,MAAM,KACTwH,EAAAd,EACRA,EAAK7G,EAAK,IAEZkC,KAAK+E,cAAcJ,GACXlG,EAAAuB,KAAKN,KAAM5B,EAAM2H,GACpBtD,EAAQuD,QAAQ1F,KAAKkE,KAAK,QAASkB,EAAKT,EAAIlH,EAAKgI,EAC1D,CACE,YAAAG,CAAaR,EAAKT,EAAIkB,GAChB,IAAA1D,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAChF0E,QAAQ,GAEV,IAAA,MAAWI,KAAKD,GACVjJ,EAASiJ,EAAUC,KAAOrD,MAAM4C,QAAQQ,EAAUC,WAAUN,YAAYJ,EAAKT,EAAImB,EAAGD,EAAUC,GAAI,CACpGJ,QAAQ,IAGPvD,EAAQuD,QAAQ1F,KAAKkE,KAAK,QAASkB,EAAKT,EAAIkB,EACrD,CACE,iBAAAE,CAAkBX,EAAKT,EAAIkB,EAAWG,EAAM7G,GACtC,IAAAgD,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAChF0E,QAAQ,EACRO,UAAU,GAERnI,EAAO,CAACsH,EAAKT,GACbS,EAAI1H,QAAQ,MAAW,IAClBI,EAAAsH,EAAInH,MAAM,KACV+H,EAAAH,EACKA,EAAAlB,EACZA,EAAK7G,EAAK,IAEZkC,KAAK+E,cAAcJ,GACnB,IAAIuB,EAAOnH,EAAQiB,KAAKN,KAAM5B,IAAS,CAAE,EACpCqE,EAAQ8D,WAAUJ,EAAYM,KAAKC,MAAMD,KAAKE,UAAUR,KACzDG,EACShH,EAAAkH,EAAML,EAAW1G,GAErB+G,EAAA,IACFA,KACAL,GAGCpH,EAAAuB,KAAKN,KAAM5B,EAAMoI,GACpB/D,EAAQuD,QAAQ1F,KAAKkE,KAAK,QAASkB,EAAKT,EAAIkB,EACrD,CACE,oBAAAS,CAAqBlB,EAAKT,GACpB3E,KAAKuG,kBAAkBnB,EAAKT,WACvB3E,KAAKN,KAAK0F,GAAKT,GAExB3E,KAAKgF,iBAAiBL,GACjB3E,KAAAkE,KAAK,UAAWkB,EAAKT,EAC9B,CACE,iBAAA4B,CAAkBnB,EAAKT,GACrB,YAAqC,IAA9B3E,KAAKmF,YAAYC,EAAKT,EACjC,CACE,iBAAA6B,CAAkBpB,EAAKT,GAErB,OADKA,IAASA,EAAA3E,KAAKmC,QAAQyC,WACW,OAAlC5E,KAAKmC,QAAQsE,iBAAkC,IAE9CzG,KAAKmF,YAAYC,EAAKT,IAEpB3E,KAAKmF,YAAYC,EAAKT,EACjC,CACE,iBAAA+B,CAAkBtB,GACT,OAAApF,KAAKN,KAAK0F,EACrB,CACE,2BAAAuB,CAA4BvB,GACpB,MAAA1F,EAAOM,KAAK0G,kBAAkBtB,GAEpC,SADU1F,GAAQtB,OAAOwI,KAAKlH,IAAS,IAC5BmH,SAAUnH,EAAKoH,IAAM1I,OAAOwI,KAAKlH,EAAKoH,IAAI3I,OAAS,GAClE,CACE,MAAA4I,GACE,OAAO/G,KAAKN,IAChB,EAGA,IAAIsH,EAAgB,CAClBC,WAAY,CAAE,EACd,gBAAAC,CAAiBC,GACVnH,KAAAiH,WAAWE,EAAOC,MAAQD,CAChC,EACD,MAAAE,CAAOJ,EAAYxB,EAAOhI,EAAK0E,EAASmF,GAI/B,OAHPL,EAAWnD,SAAqByD,IAC1BvH,KAAKiH,WAAWM,KAAoB9B,EAAAzF,KAAKiH,WAAWM,GAAWC,QAAQ/B,EAAOhI,EAAK0E,EAASmF,GAAU,IAErG7B,CACX,GAGA,MAAMgC,EAAmB,CAAE,EAC3B,MAAMC,UAAmBjE,EACvB,WAAA3D,CAAY6H,GACN,IAAAxF,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EA7b3E,IAAIrB,EAAGiI,EA8bT/C,QA9bMlF,EA+byGgI,EA/btGC,EA+bgH5H,KAA3H,CAAC,gBAAiB,gBAAiB,iBAAkB,eAAgB,mBAAoB,aAAc,SA9b5G8D,SAAagC,IACTnG,EAAEmG,OAAMA,GAAKnG,EAAEmG,GAAC,IA8bpB9F,KAAKmC,QAAUA,OACmB,IAA9BnC,KAAKmC,QAAQpB,eACff,KAAKmC,QAAQpB,aAAe,KAEzBf,KAAAsC,OAASkB,EAAWH,OAAO,aACpC,CACE,cAAAwE,CAAezC,GACTA,SAAU0C,SAAW1C,EAC7B,CACE,MAAA2C,CAAOtK,GACD,IAAA0E,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAChFgH,cAAe,CAAA,GAEb,GAAAvK,QACK,OAAA,EAET,MAAMwK,EAAWjI,KAAK7C,QAAQM,EAAK0E,GAC5B,OAAA8F,QAA6B,IAAjBA,EAASlL,GAChC,CACE,cAAAmL,CAAezK,EAAK0E,GAClB,IAAIgG,OAAsC,IAAxBhG,EAAQgG,YAA4BhG,EAAQgG,YAAcnI,KAAKmC,QAAQgG,iBACrE,IAAhBA,IAAyCA,EAAA,KAC7C,MAAMpH,OAAwC,IAAzBoB,EAAQpB,aAA6BoB,EAAQpB,aAAef,KAAKmC,QAAQpB,aAC9F,IAAIqH,EAAajG,EAAQwC,IAAM3E,KAAKmC,QAAQyC,WAAa,GACzD,MAAMyD,EAAuBF,GAAe1K,EAAIC,QAAQyK,IAAe,EACjEG,IAAwBtI,KAAKmC,QAAQoG,yBAA4BpG,EAAQpB,cAAiBf,KAAKmC,QAAQqG,wBAA2BrG,EAAQgG,aA3VxH,EAAC1K,EAAK0K,EAAapH,KAC7CoH,EAAcA,GAAe,GAC7BpH,EAAeA,GAAgB,GAC/B,MAAM0H,EAAgB7I,EAAM8I,QAAOC,GAAKR,EAAYzK,QAAQiL,GAAK,GAAK5H,EAAarD,QAAQiL,GAAK,IAC5F,GAAyB,IAAzBF,EAActK,OAAqB,OAAA,EACvC,MAAMyK,EAAI/I,EAA+BO,UAAU,IAAIqI,EAAcI,KAAIF,GAAW,MAANA,EAAY,MAAQA,IAAGpD,KAAK,SAC1G,IAAIuD,GAAWF,EAAEG,KAAKtL,GACtB,IAAKqL,EAAS,CACN,MAAAE,EAAKvL,EAAIC,QAAQqD,GACnBiI,EAAK,IAAMJ,EAAEG,KAAKtL,EAAIwL,UAAU,EAAGD,MAC3BF,GAAA,EAEhB,CACS,OAAAA,CAAA,EA8U2JI,CAAoBzL,EAAK0K,EAAapH,IAClM,GAAAsH,IAAyBC,EAAsB,CACjD,MAAMxC,EAAIrI,EAAI0L,MAAMnJ,KAAKoJ,aAAaC,eAClC,GAAAvD,GAAKA,EAAE3H,OAAS,EACX,MAAA,CACLV,MACA2K,WAAYxL,EAASwL,GAAc,CAACA,GAAcA,GAGhD,MAAAkB,EAAQ7L,EAAIQ,MAAMkK,IACpBA,IAAgBpH,GAAgBoH,IAAgBpH,GAAgBf,KAAKmC,QAAQwC,GAAGjH,QAAQ4L,EAAM,KAAU,KAAAlB,EAAakB,EAAM3I,SACzHlD,EAAA6L,EAAM/D,KAAKxE,EACvB,CACW,MAAA,CACLtD,MACA2K,WAAYxL,EAASwL,GAAc,CAACA,GAAcA,EAExD,CACE,SAAAmB,CAAU3C,EAAMzE,EAASqH,GAQvB,GAPuB,iBAAZrH,GAAwBnC,KAAKmC,QAAQsH,mCACpCtH,EAAAnC,KAAKmC,QAAQsH,iCAAiCzI,YAEnC,iBAAZmB,IAAgCA,EAAA,IACtCA,IAEAA,IAASA,EAAU,CAAE,GACtByE,QAA4C,MAAA,GAC3CnE,MAAM4C,QAAQuB,KAAcA,EAAA,CAACvH,OAAOuH,KACzC,MAAM8C,OAA0C,IAA1BvH,EAAQuH,cAA8BvH,EAAQuH,cAAgB1J,KAAKmC,QAAQuH,cAC3F3I,OAAwC,IAAzBoB,EAAQpB,aAA6BoB,EAAQpB,aAAef,KAAKmC,QAAQpB,cACxFtD,IACJA,EAAA2K,WACAA,GACEpI,KAAKkI,eAAetB,EAAKA,EAAKzI,OAAS,GAAIgE,GACzCwH,EAAYvB,EAAWA,EAAWjK,OAAS,GAC3CiH,EAAMjD,EAAQiD,KAAOpF,KAAK8H,SAC1B8B,EAA0BzH,EAAQyH,yBAA2B5J,KAAKmC,QAAQyH,wBAChF,GAAIxE,GAA6B,WAAtBA,EAAIyE,cAA4B,CACzC,GAAID,EAAyB,CAC3B,MAAMzB,EAAchG,EAAQgG,aAAenI,KAAKmC,QAAQgG,YACxD,OAAIuB,EACK,CACL3M,IAAK,GAAG4M,IAAYxB,IAAc1K,IAClCqM,QAASrM,EACTsM,aAActM,EACduM,QAAS5E,EACT6E,OAAQN,EACRO,WAAYlK,KAAKmK,qBAAqBhI,IAGnC,GAAGwH,IAAYxB,IAAc1K,GAC5C,CACM,OAAIiM,EACK,CACL3M,IAAKU,EACLqM,QAASrM,EACTsM,aAActM,EACduM,QAAS5E,EACT6E,OAAQN,EACRO,WAAYlK,KAAKmK,qBAAqBhI,IAGnC1E,CACb,CACI,MAAMwK,EAAWjI,KAAK7C,QAAQyJ,EAAMzE,GAChC,IAAApF,EAAMkL,GAAYA,EAASlL,IACzB,MAAAqN,EAAanC,GAAYA,EAAS6B,SAAWrM,EAC7C4M,EAAkBpC,GAAYA,EAAS8B,cAAgBtM,EACvD6M,EAAUlM,OAAOC,UAAUkM,SAAS9F,MAAM1H,GAE1CyN,OAAoC,IAAvBrI,EAAQqI,WAA2BrI,EAAQqI,WAAaxK,KAAKmC,QAAQqI,WAClFC,GAA8BzK,KAAK0K,YAAc1K,KAAK0K,WAAWC,eACjEA,GAAkB/N,EAASG,IAAuB,kBAARA,GAAoC,iBAARA,EAC5E,KAAI0N,GAA8B1N,GAAO4N,GAJxB,CAAC,kBAAmB,oBAAqB,mBAIUjN,QAAQ4M,GAAW,IAAO1N,EAAS4N,IAAe/H,MAAM4C,QAAQtI,GAmCxI,GAAe0N,GAA8B7N,EAAS4N,IAAe/H,MAAM4C,QAAQtI,GACvEA,EAAAA,EAAIwI,KAAKiF,GACXzN,IAAWA,EAAAiD,KAAK4K,kBAAkB7N,EAAK6J,EAAMzE,EAASqH,QACrD,CACL,IAAIqB,GAAc,EACdf,GAAU,EACd,MAAMgB,OAAwC,IAAlB3I,EAAQ4I,QAAwBnO,EAASuF,EAAQ4I,OACvEC,EAAkBtD,EAAWsD,gBAAgB7I,GAC7C8I,EAAqBH,EAAsB9K,KAAKkL,eAAeC,UAAU/F,EAAKjD,EAAQ4I,MAAO5I,GAAW,GACxGiJ,EAAoCjJ,EAAQkJ,SAAWP,EAAsB9K,KAAKkL,eAAeC,UAAU/F,EAAKjD,EAAQ4I,MAAO,CACnIM,SAAS,IACN,GACCC,EAAwBR,IAAwB3I,EAAQkJ,SAA6B,IAAlBlJ,EAAQ4I,OAAe/K,KAAKkL,eAAeK,mBAC9GC,EAAeF,GAAyBnJ,EAAQ,eAAenC,KAAKmC,QAAQsJ,wBAA0BtJ,EAAQ,eAAe8I,MAAyB9I,EAAQ,eAAeiJ,MAAwCjJ,EAAQqJ,cAC9NxL,KAAK0L,cAAc3O,IAAQiO,IAChBH,GAAA,EACR9N,EAAAyO,GAEHxL,KAAK0L,cAAc3O,KACZ+M,GAAA,EACJ/M,EAAAU,GAER,MACMkO,GADiCxJ,EAAQyJ,gCAAkC5L,KAAKmC,QAAQyJ,iCACtC9B,OAAU,EAAY/M,EACxE8O,EAAgBb,GAAmBQ,IAAiBzO,GAAOiD,KAAKmC,QAAQ0J,cAC1E,GAAA/B,GAAWe,GAAegB,EAAe,CAE3C,GADK7L,KAAAsC,OAAOX,IAAIkK,EAAgB,YAAc,aAAczG,EAAKuE,EAAWlM,EAAKoO,EAAgBL,EAAezO,GAC5GgE,EAAc,CACV,MAAA+K,EAAK9L,KAAK7C,QAAQM,EAAK,IACxB0E,EACHpB,cAAc,IAEZ+K,GAAMA,EAAG/O,KAAUiD,KAAAsC,OAAOR,KAAK,kLAC7C,CACQ,IAAIiK,EAAO,GACL,MAAAC,EAAehM,KAAKiM,cAAcC,iBAAiBlM,KAAKmC,QAAQgK,YAAahK,EAAQiD,KAAOpF,KAAK8H,UACvG,GAAmC,aAA/B9H,KAAKmC,QAAQiK,eAAgCJ,GAAgBA,EAAa,GAC5E,IAAA,IAAS7K,EAAI,EAAGA,EAAI6K,EAAa7N,OAAQgD,IAClC4K,EAAAlL,KAAKmL,EAAa7K,QAEe,QAA/BnB,KAAKmC,QAAQiK,cACtBL,EAAO/L,KAAKiM,cAAcI,mBAAmBlK,EAAQiD,KAAOpF,KAAK8H,UAEjEiE,EAAKlL,KAAKsB,EAAQiD,KAAOpF,KAAK8H,UAEhC,MAAMwE,EAAO,CAACC,EAAG/N,EAAGgO,KAClB,MAAMC,EAAoBzB,GAAmBwB,IAAyBzP,EAAMyP,EAAuBb,EAC/F3L,KAAKmC,QAAQuK,kBACf1M,KAAKmC,QAAQuK,kBAAkBH,EAAG5C,EAAWnL,EAAGiO,EAAmBZ,EAAe1J,GACzEnC,KAAK2M,kBAAoB3M,KAAK2M,iBAAiBC,aACxD5M,KAAK2M,iBAAiBC,YAAYL,EAAG5C,EAAWnL,EAAGiO,EAAmBZ,EAAe1J,GAEvFnC,KAAKkE,KAAK,aAAcqI,EAAG5C,EAAWnL,EAAGzB,EAAG,EAE1CiD,KAAKmC,QAAQyK,cACX5M,KAAKmC,QAAQ0K,oBAAsB/B,EACrCiB,EAAKjI,SAAoBgE,IACvB,MAAMgF,EAAW9M,KAAKkL,eAAe6B,YAAYjF,EAAU3F,GACvDmJ,GAAyBnJ,EAAQ,eAAenC,KAAKmC,QAAQsJ,wBAA0BqB,EAASpP,QAAQ,GAAGsC,KAAKmC,QAAQsJ,uBAAyB,GACnJqB,EAASjM,KAAK,GAAGb,KAAKmC,QAAQsJ,uBAEhCqB,EAAShJ,SAAkBkJ,IACpBV,EAAA,CAACxE,GAAWrK,EAAMuP,EAAQ7K,EAAQ,eAAe6K,MAAaxB,EAAY,GAChF,IAGEc,EAAAP,EAAMtO,EAAK+N,GAG5B,CACMzO,EAAMiD,KAAK4K,kBAAkB7N,EAAK6J,EAAMzE,EAAS8F,EAAUuB,GACvDM,GAAW/M,IAAQU,GAAOuC,KAAKmC,QAAQ8K,8BAAmClQ,EAAA,GAAG4M,KAAalM,MACzFqM,GAAWe,IAAgB7K,KAAKmC,QAAQ+K,yBAEzCnQ,EADoC,OAAlCiD,KAAKmC,QAAQsE,iBACTzG,KAAKmC,QAAQ+K,uBAAuBlN,KAAKmC,QAAQ8K,4BAA8B,GAAGtD,KAAalM,IAAQA,EAAKoN,EAAc9N,OAAM,GAEhIiD,KAAKmC,QAAQ+K,uBAAuBnQ,GAGpD,KAlH+I,CACzI,IAAKoF,EAAQgL,gBAAkBnN,KAAKmC,QAAQgL,cAAe,CACpDnN,KAAKmC,QAAQiL,uBACXpN,KAAAsC,OAAOR,KAAK,mEAEb,MAAA8G,EAAI5I,KAAKmC,QAAQiL,sBAAwBpN,KAAKmC,QAAQiL,sBAAsBhD,EAAYrN,EAAK,IAC9FoF,EACHwC,GAAIyD,IACD,QAAQ3K,MAAQuC,KAAK8H,mDAC1B,OAAI4B,GACFzB,EAASlL,IAAM6L,EACNX,EAAAiC,WAAalK,KAAKmK,qBAAqBhI,GACzC8F,GAEFW,CACf,CACM,GAAI7H,EAAc,CACV,MAAAsM,EAAiB5K,MAAM4C,QAAQtI,GAC/BuQ,EAAOD,EAAiB,GAAK,CAAE,EAC/BE,EAAcF,EAAiBhD,EAAkBD,EACvD,IAAA,MAAWtE,KAAK/I,EACd,GAAIqB,OAAOC,UAAUC,eAAeC,KAAKxB,EAAK+I,GAAI,CAChD,MAAM0H,EAAU,GAAGD,IAAcxM,IAAe+E,IAChDwH,EAAKxH,GAAK9F,KAAKuJ,UAAUiE,EAAS,IAC7BrL,EAEDqI,YAAY,EACZ7F,GAAIyD,IAGJkF,EAAKxH,KAAO0H,IAASF,EAAKxH,GAAK/I,EAAI+I,GACnD,CAEcwH,EAAAA,CACd,CACA,CAgFI,OAAI5D,GACFzB,EAASlL,IAAMA,EACNkL,EAAAiC,WAAalK,KAAKmK,qBAAqBhI,GACzC8F,GAEFlL,CACX,CACE,iBAAA6N,CAAkB7N,EAAKU,EAAK0E,EAAS8F,EAAUuB,GAC7C,IAAIiE,EAAQzN,KACZ,GAAIA,KAAK0K,YAAc1K,KAAK0K,WAAWtE,MAC/BrJ,EAAAiD,KAAK0K,WAAWtE,MAAMrJ,EAAK,IAC5BiD,KAAKmC,QAAQ6F,cAAc0F,oBAC3BvL,GACFA,EAAQiD,KAAOpF,KAAK8H,UAAYG,EAAS+B,QAAS/B,EAASgC,OAAQhC,EAAS6B,QAAS,CACtF7B,kBAER,IAAgB9F,EAAQwL,kBAAmB,CACjCxL,EAAQ6F,eAAoBhI,KAAAoJ,aAAahH,KAAK,IAC7CD,EAED6F,cAAe,IACVhI,KAAKmC,QAAQ6F,iBACb7F,EAAQ6F,iBAIjB,MAAM4F,EAAkBhR,EAASG,KAASoF,GAAWA,EAAQ6F,oBAA2D,IAA1C7F,EAAQ6F,cAAc4F,gBAAgCzL,EAAQ6F,cAAc4F,gBAAkB5N,KAAKmC,QAAQ6F,cAAc4F,iBACnM,IAAAC,EACJ,GAAID,EAAiB,CACnB,MAAME,EAAK/Q,EAAIoM,MAAMnJ,KAAKoJ,aAAaC,eACvCwE,EAAUC,GAAMA,EAAG3P,MAC3B,CACU,IAAAuB,EAAOyC,EAAQxE,UAAYf,EAASuF,EAAQxE,SAAWwE,EAAQxE,QAAUwE,EAM7E,GALInC,KAAKmC,QAAQ6F,cAAc0F,mBAAyBhO,EAAA,IACnDM,KAAKmC,QAAQ6F,cAAc0F,oBAC3BhO,IAEC3C,EAAAiD,KAAKoJ,aAAa2E,YAAYhR,EAAK2C,EAAMyC,EAAQiD,KAAOpF,KAAK8H,UAAYG,EAAS+B,QAAS7H,GAC7FyL,EAAiB,CACnB,MAAMI,EAAKjR,EAAIoM,MAAMnJ,KAAKoJ,aAAaC,eAEnCwE,GADYG,GAAMA,EAAG7P,UACFgE,EAAQ8L,MAAO,EAC9C,EACW9L,EAAQiD,KAAyC,OAAlCpF,KAAKmC,QAAQsE,kBAA6BwB,GAAYA,EAASlL,MAAKoF,EAAQiD,IAAMpF,KAAK8H,UAAYG,EAAS+B,UAC3G,IAAjB7H,EAAQ8L,OAAgBlR,EAAMiD,KAAKoJ,aAAa6E,KAAKlR,GAAK,WAC5D,IAAA,IAASyF,EAAOxB,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMD,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC1Ed,EAAAc,GAAQ1B,UAAU0B,GAErB,OAAA8G,GAAWA,EAAQ,KAAO5H,EAAK,KAAOO,EAAQ+L,SAC1CT,EAAAnL,OAAOR,KAAK,6CAA6CF,EAAK,cAAcnE,EAAI,MAC/E,MAEFgQ,EAAMlE,aAAa3H,EAAMnE,EACjC,GAAE0E,IACCA,EAAQ6F,eAAoBhI,KAAAoJ,aAAa+E,OACnD,CACI,MAAMC,EAAcjM,EAAQiM,aAAepO,KAAKmC,QAAQiM,YAClDC,EAAqBzR,EAASwR,GAAe,CAACA,GAAeA,EAU5D,OATHrR,SAAqCsR,GAAsBA,EAAmBlQ,SAAyC,IAA/BgE,EAAQmM,qBAC5FvR,EAAAiK,EAAcK,OAAOgH,EAAoBtR,EAAKU,EAAKuC,KAAKmC,SAAWnC,KAAKmC,QAAQoM,wBAA0B,CAC9GC,aAAc,IACTvG,EACHiC,WAAYlK,KAAKmK,qBAAqBhI,OAErCA,GACDA,EAASnC,OAERjD,CACX,CACE,OAAAI,CAAQyJ,GACF,IACA6H,EACA3E,EACAC,EACAC,EACAC,EALA9H,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAqE7E,OA/DHpE,EAASgK,KAAOA,EAAO,CAACA,IAC5BA,EAAK9C,SAAatF,IACZ,GAAAwB,KAAK0L,cAAc+C,GAAQ,OAC/B,MAAMC,EAAY1O,KAAKkI,eAAe1J,EAAG2D,GACnC1E,EAAMiR,EAAUjR,IACZqM,EAAArM,EACV,IAAI2K,EAAasG,EAAUtG,WACvBpI,KAAKmC,QAAQwM,aAAYvG,EAAaA,EAAWzC,OAAO3F,KAAKmC,QAAQwM,aACzE,MAAM7D,OAAwC,IAAlB3I,EAAQ4I,QAAwBnO,EAASuF,EAAQ4I,OACvEO,EAAwBR,IAAwB3I,EAAQkJ,SAA6B,IAAlBlJ,EAAQ4I,OAAe/K,KAAKkL,eAAeK,mBAC9GqD,OAA2C,IAApBzM,EAAQ+L,UAA0BtR,EAASuF,EAAQ+L,UAAuC,iBAApB/L,EAAQ+L,UAA6C,KAApB/L,EAAQ+L,QACtIW,EAAQ1M,EAAQ4J,KAAO5J,EAAQ4J,KAAO/L,KAAKiM,cAAcI,mBAAmBlK,EAAQiD,KAAOpF,KAAK8H,SAAU3F,EAAQgK,aACxH/D,EAAWtE,SAAca,IACnB3E,KAAK0L,cAAc+C,KACdxE,EAAAtF,GACJ8C,EAAiB,GAAGoH,EAAM,MAAMlK,MAAS3E,KAAK8O,OAAS9O,KAAK8O,MAAMC,qBAAuB/O,KAAK8O,MAAMC,mBAAmB9E,KAC1HxC,EAAiB,GAAGoH,EAAM,MAAMlK,MAAQ,EACxC3E,KAAKsC,OAAOR,KAAK,QAAQgI,qBAA2B+E,EAAMtJ,KAAK,2CAA2C0E,wBAA8B,6NAE1I4E,EAAM/K,SAAgBtC,IAChB,GAAAxB,KAAK0L,cAAc+C,GAAQ,OACrBzE,EAAAxI,EACJ,MAAAwN,EAAY,CAACvR,GACnB,GAAIuC,KAAK0K,YAAc1K,KAAK0K,WAAWuE,cACrCjP,KAAK0K,WAAWuE,cAAcD,EAAWvR,EAAK+D,EAAMmD,EAAIxC,OACnD,CACD,IAAA+M,EACApE,MAAoC9K,KAAKkL,eAAeC,UAAU3J,EAAMW,EAAQ4I,MAAO5I,IAC3F,MAAMgN,EAAa,GAAGnP,KAAKmC,QAAQsJ,sBAC7B2D,EAAgB,GAAGpP,KAAKmC,QAAQsJ,yBAAyBzL,KAAKmC,QAAQsJ,kBAU5E,GATIX,IACQkE,EAAAnO,KAAKpD,EAAMyR,GACjB/M,EAAQkJ,SAAmD,IAAxC6D,EAAaxR,QAAQ0R,IAChCJ,EAAAnO,KAAKpD,EAAMyR,EAAavR,QAAQyR,EAAepP,KAAKmC,QAAQsJ,kBAEpEH,GACQ0D,EAAAnO,KAAKpD,EAAM0R,IAGrBP,EAAsB,CAClB,MAAAS,EAAa,GAAG5R,IAAMuC,KAAKmC,QAAQmN,mBAAmBnN,EAAQ+L,UACpEc,EAAUnO,KAAKwO,GACXvE,IACQkE,EAAAnO,KAAKwO,EAAaH,GACxB/M,EAAQkJ,SAAmD,IAAxC6D,EAAaxR,QAAQ0R,IAChCJ,EAAAnO,KAAKwO,EAAaH,EAAavR,QAAQyR,EAAepP,KAAKmC,QAAQsJ,kBAE3EH,GACQ0D,EAAAnO,KAAKwO,EAAaF,GAG9C,CACA,CACc,IAAAI,EACG,KAAAA,EAAcP,EAAUQ,OACxBxP,KAAK0L,cAAc+C,KACP1E,EAAAwF,EACfd,EAAQzO,KAAKmF,YAAY3D,EAAMmD,EAAI4K,EAAapN,GAE9D,IACS,GACF,IAEI,CACLpF,IAAK0R,EACL3E,UACAC,eACAC,UACAC,SAEN,CACE,aAAAyB,CAAc3O,GACZ,aAAe,IAARA,IAAwBiD,KAAKmC,QAAQsN,YAAsB,OAAR1S,IAAoBiD,KAAKmC,QAAQuN,mBAA6B,KAAR3S,EACpH,CACE,WAAAoI,CAAY3D,EAAMmD,EAAIlH,GAChB,IAAA0E,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpF,OAAIhB,KAAK0K,YAAc1K,KAAK0K,WAAWvF,YAAoBnF,KAAK0K,WAAWvF,YAAY3D,EAAMmD,EAAIlH,EAAK0E,GAC/FnC,KAAK2P,cAAcxK,YAAY3D,EAAMmD,EAAIlH,EAAK0E,EACzD,CACE,oBAAAgI,GACM,IAAAhI,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpF,MAAM4O,EAAc,CAAC,eAAgB,UAAW,UAAW,UAAW,MAAO,OAAQ,cAAe,KAAM,eAAgB,cAAe,gBAAiB,gBAAiB,aAAc,cAAe,iBAClMC,EAA2B1N,EAAQxE,UAAYf,EAASuF,EAAQxE,SAClE,IAAA+B,EAAOmQ,EAA2B1N,EAAQxE,QAAUwE,EAUxD,GATI0N,QAAqD,IAAlB1N,EAAQ4I,QAC7CrL,EAAKqL,MAAQ5I,EAAQ4I,OAEnB/K,KAAKmC,QAAQ6F,cAAc0F,mBACtBhO,EAAA,IACFM,KAAKmC,QAAQ6F,cAAc0F,oBAC3BhO,KAGFmQ,EAA0B,CACtBnQ,EAAA,IACFA,GAEL,IAAA,MAAWjC,KAAOmS,SACTlQ,EAAKjC,EAEpB,CACW,OAAAiC,CACX,CACE,sBAAOsL,CAAgB7I,GACrB,MAAME,EAAS,eACf,IAAA,MAAWyN,KAAU3N,EACnB,GAAI/D,OAAOC,UAAUC,eAAeC,KAAK4D,EAAS2N,IAAWzN,IAAWyN,EAAO7G,UAAU,EAAG5G,UAAkB,IAAcF,EAAQ2N,GAC3H,OAAA,EAGJ,OAAA,CACX,EAGA,MAAMC,EAAuBC,GAAAA,EAAOC,OAAO,GAAGC,cAAgBF,EAAOnR,MAAM,GAC3E,MAAMsR,EACJ,WAAArQ,CAAYqC,GACVnC,KAAKmC,QAAUA,EACVnC,KAAAoQ,cAAgBpQ,KAAKmC,QAAQiO,gBAAiB,EAC9CpQ,KAAAsC,OAASkB,EAAWH,OAAO,gBACpC,CACE,qBAAAgN,CAAsB7O,GAEpB,KADAA,EAAOD,EAAeC,KACTA,EAAK9D,QAAQ,KAAO,EAAU,OAAA,KACrC,MAAAkB,EAAI4C,EAAKvD,MAAM,KACjB,OAAa,IAAbW,EAAET,OAAqB,MAC3BS,EAAE4Q,MACoC,MAAlC5Q,EAAEA,EAAET,OAAS,GAAG0L,cAA8B,KAC3C7J,KAAKsQ,mBAAmB1R,EAAE2G,KAAK,MAC1C,CACE,uBAAAgL,CAAwB/O,GAEtB,KADAA,EAAOD,EAAeC,KACTA,EAAK9D,QAAQ,KAAO,EAAU,OAAA8D,EACrC,MAAA5C,EAAI4C,EAAKvD,MAAM,KACrB,OAAO+B,KAAKsQ,mBAAmB1R,EAAE,GACrC,CACE,kBAAA0R,CAAmB9O,GACjB,GAAI5E,EAAS4E,IAASA,EAAK9D,QAAQ,MAAW,EAAA,CAC5C,GAAoB,oBAAT8S,WAA4D,IAA7BA,KAAKC,oBACzC,IACF,IAAIC,EAAgBF,KAAKC,oBAAoBjP,GAAM,GAInD,GAHIkP,GAAiB1Q,KAAKmC,QAAQwO,eAChCD,EAAgBA,EAAc7G,eAE5B6G,EAAsB,OAAAA,CAC3B,OAAQ/R,GAAG,CAER,MAAAiS,EAAe,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAClE,IAAAhS,EAAI4C,EAAKvD,MAAM,KAcZ,OAbH+B,KAAKmC,QAAQwO,aACf/R,EAAIA,EAAEiK,KAAYgI,GAAAA,EAAKhH,gBACD,IAAbjL,EAAET,QACXS,EAAE,GAAKA,EAAE,GAAGiL,cACZjL,EAAE,GAAKA,EAAE,GAAGsR,cACRU,EAAalT,QAAQkB,EAAE,GAAGiL,gBAAqB,IAAAjL,EAAE,GAAKmR,EAAWnR,EAAE,GAAGiL,iBACpD,IAAbjL,EAAET,SACXS,EAAE,GAAKA,EAAE,GAAGiL,cACQ,IAAhBjL,EAAE,GAAGT,SAAgBS,EAAA,GAAKA,EAAE,GAAGsR,eACtB,QAATtR,EAAE,IAAgC,IAAhBA,EAAE,GAAGT,WAAgB,GAAKS,EAAE,GAAGsR,eACjDU,EAAalT,QAAQkB,EAAE,GAAGiL,gBAAqB,IAAAjL,EAAE,GAAKmR,EAAWnR,EAAE,GAAGiL,gBACtE+G,EAAalT,QAAQkB,EAAE,GAAGiL,gBAAqB,IAAAjL,EAAE,GAAKmR,EAAWnR,EAAE,GAAGiL,iBAErEjL,EAAE2G,KAAK,IACpB,CACW,OAAAvF,KAAKmC,QAAQ2O,WAAa9Q,KAAKmC,QAAQwO,aAAenP,EAAKqI,cAAgBrI,CACtF,CACE,eAAAuP,CAAgBvP,GAIP,OAHmB,iBAAtBxB,KAAKmC,QAAQ6O,MAA2BhR,KAAKmC,QAAQ8O,4BAChDzP,EAAAxB,KAAKuQ,wBAAwB/O,KAE9BxB,KAAKoQ,gBAAkBpQ,KAAKoQ,cAAcjS,QAAU6B,KAAKoQ,cAAc1S,QAAQ8D,IAAQ,CACnG,CACE,qBAAA0P,CAAsBrC,GAChB,IAACA,EAAc,OAAA,KACf,IAAAJ,EAoBG,OAnBPI,EAAM/K,SAAgBtC,IACpB,GAAIiN,EAAO,OACL,MAAA0C,EAAanR,KAAKsQ,mBAAmB9O,GACtCxB,KAAKmC,QAAQiO,gBAAiBpQ,KAAK+Q,gBAAgBI,KAAqB1C,EAAA0C,EAAA,KAE1E1C,GAASzO,KAAKmC,QAAQiO,eACzBvB,EAAM/K,SAAgBtC,IACpB,GAAIiN,EAAO,OACL,MAAA2C,EAAUpR,KAAKuQ,wBAAwB/O,GAC7C,GAAIxB,KAAK+Q,gBAAgBK,UAAiB3C,EAAQ2C,EAClD3C,EAAQzO,KAAKmC,QAAQiO,cAAcvJ,MAAqBwK,GAClDA,IAAiBD,EAAgBC,EACjCA,EAAa3T,QAAQ,KAAO,GAAK0T,EAAQ1T,QAAQ,KAAO,OAAxD,EACA2T,EAAa3T,QAAQ,KAAO,GAAK0T,EAAQ1T,QAAQ,KAAO,GAAK2T,EAAapI,UAAU,EAAGoI,EAAa3T,QAAQ,QAAU0T,GACpF,IAAlCC,EAAa3T,QAAQ0T,IAAkBA,EAAQjT,OAAS,EAD8EkT,OACtI,GACL,IAGA5C,IAAeA,EAAAzO,KAAKkM,iBAAiBlM,KAAKmC,QAAQgK,aAAa,IAC7DsC,CACX,CACE,gBAAAvC,CAAiBoF,EAAW9P,GACtB,IAAC8P,EAAW,MAAO,GAGvB,GAFyB,mBAAdA,IAA0BA,EAAYA,EAAU9P,IACvD5E,EAAS0U,KAAYA,EAAY,CAACA,IAClC7O,MAAM4C,QAAQiM,GAAmB,OAAAA,EACrC,IAAK9P,EAAa,OAAA8P,EAAUC,SAAW,GACnC,IAAA9C,EAAQ6C,EAAU9P,GAKtB,OAJKiN,IAAOA,EAAQ6C,EAAUtR,KAAKqQ,sBAAsB7O,KACpDiN,IAAOA,EAAQ6C,EAAUtR,KAAKsQ,mBAAmB9O,KACjDiN,IAAOA,EAAQ6C,EAAUtR,KAAKuQ,wBAAwB/O,KACtDiN,IAAOA,EAAQ6C,EAAUC,SACvB9C,GAAS,EACpB,CACE,kBAAApC,CAAmB7K,EAAMgQ,GACjB,MAAAC,EAAgBzR,KAAKkM,iBAAiBsF,GAAgBxR,KAAKmC,QAAQgK,aAAe,GAAI3K,GACtFqN,EAAQ,GACR6C,EAAe/I,IACdA,IACD3I,KAAK+Q,gBAAgBpI,GACvBkG,EAAMhO,KAAK8H,GAEX3I,KAAKsC,OAAOR,KAAK,uDAAuD6G,KAChF,EAYW,OAVH/L,EAAS4E,KAAUA,EAAK9D,QAAQ,MAAO,GAAM8D,EAAK9D,QAAQ,MAAY,IAC9C,iBAAtBsC,KAAKmC,QAAQ6O,QAAiChR,KAAKsQ,mBAAmB9O,IAChD,iBAAtBxB,KAAKmC,QAAQ6O,MAAiD,gBAAtBhR,KAAKmC,QAAQ6O,MAAgCU,EAAA1R,KAAKqQ,sBAAsB7O,IAC1F,gBAAtBxB,KAAKmC,QAAQ6O,QAAgChR,KAAKuQ,wBAAwB/O,KACrE5E,EAAS4E,IACVkQ,EAAA1R,KAAKsQ,mBAAmB9O,IAElCiQ,EAAc3N,SAAc6N,IACtB9C,EAAMnR,QAAQiU,GAAM,GAAWD,EAAA1R,KAAKsQ,mBAAmBqB,GAAG,IAEzD9C,CACX,EAGA,IAAI+C,EAAO,CAAC,CACV7F,KAAM,CAAC,MAAO,KAAM,KAAM,MAAO,KAAM,MAAO,MAAO,KAAM,MAAO,KAAM,KAAM,KAAM,KAAM,QAAS,KAAM,KAAM,KAAM,KAAM,KAAM,MACjI8F,GAAI,CAAC,EAAG,GACRF,GAAI,GACH,CACD5F,KAAM,CAAC,KAAM,KAAM,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,KAAM,KAAM,KAAM,MAAO,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,KAAM,MAAO,MAAO,KAAM,QAAS,KAAM,MAAO,KAAM,KAAM,KAAM,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MACzY8F,GAAI,CAAC,EAAG,GACRF,GAAI,GACH,CACD5F,KAAM,CAAC,KAAM,KAAM,MAAO,KAAM,KAAM,KAAM,KAAM,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MACxI8F,GAAI,CAAC,GACLF,GAAI,GACH,CACD5F,KAAM,CAAC,KAAM,KAAM,MAAO,KAAM,KAAM,KAAM,KAAM,MAClD8F,GAAI,CAAC,EAAG,EAAG,GACXF,GAAI,GACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,EAAG,EAAG,GAAI,KACrBF,GAAI,GACH,CACD5F,KAAM,CAAC,KAAM,MACb8F,GAAI,CAAC,EAAG,EAAG,GACXF,GAAI,GACH,CACD5F,KAAM,CAAC,MAAO,MACd8F,GAAI,CAAC,EAAG,EAAG,GACXF,GAAI,GACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,EAAG,GACdF,GAAI,GACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,GACRF,GAAI,GACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,EAAG,EAAG,IACjBF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,EAAG,IACdF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,GACRF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,GACRF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,EAAG,GACdF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,IACXF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,GACXF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,GACRF,GAAI,IACH,CACD5F,KAAM,CAAC,OACP8F,GAAI,CAAC,EAAG,EAAG,GACXF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,GAAI,IACfF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,GACRF,GAAI,GACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,IACXF,GAAI,IACH,CACD5F,KAAM,CAAC,MACP8F,GAAI,CAAC,EAAG,EAAG,EAAG,GACdF,GAAI,IACH,CACD5F,KAAM,CAAC,KAAM,MACb8F,GAAI,CAAC,EAAG,EAAG,GAAI,IACfF,GAAI,KAEFG,EAAqB,CACvB,EAAGC,GAAKC,OAAOD,EAAI,GACnB,EAAGA,GAAKC,OAAY,GAALD,GACf,EAAQA,GAAA,EACR,KAAQC,OAAOD,EAAI,IAAM,GAAKA,EAAI,KAAO,GAAK,EAAIA,EAAI,IAAM,GAAKA,EAAI,IAAM,IAAMA,EAAI,IAAM,IAAMA,EAAI,KAAO,IAAM,EAAI,GACtH,KAAQC,OAAY,GAALD,EAAS,EAAS,GAALA,EAAS,EAAS,GAALA,EAAS,EAAIA,EAAI,KAAO,GAAKA,EAAI,KAAO,GAAK,EAAIA,EAAI,KAAO,GAAK,EAAI,GAC9G,EAAQA,GAAAC,OAAY,GAALD,EAAS,EAAIA,GAAK,GAAKA,GAAK,EAAI,EAAI,GACnD,EAAQA,GAAAC,OAAY,GAALD,EAAS,EAAIA,EAAI,IAAM,GAAKA,EAAI,IAAM,IAAMA,EAAI,IAAM,IAAMA,EAAI,KAAO,IAAM,EAAI,GAChG,EAAGA,GAAKC,OAAY,GAALD,EAAS,EAAS,GAALA,EAAS,EAAS,GAALA,GAAe,IAALA,EAAU,EAAI,GACjE,EAAGA,GAAKC,OAAOD,GAAK,GACpB,GAAIA,GAAKC,OAAY,GAALD,EAAS,EAAS,GAALA,EAAS,EAAIA,EAAI,EAAI,EAAIA,EAAI,GAAK,EAAI,GACnE,GAASA,GAAAC,OAAY,GAALD,GAAe,IAALA,EAAU,EAAS,GAALA,GAAe,IAALA,EAAU,EAAIA,EAAI,GAAKA,EAAI,GAAK,EAAI,GACtF,MAASC,OAAOD,EAAI,IAAM,GAAKA,EAAI,KAAO,IAC1C,GAAIA,GAAKC,OAAa,IAAND,GAChB,GAAIA,GAAKC,OAAY,GAALD,EAAS,EAAS,GAALA,EAAS,EAAS,GAALA,EAAS,EAAI,GACvD,MAASC,OAAOD,EAAI,IAAM,GAAKA,EAAI,KAAO,GAAK,EAAIA,EAAI,IAAM,IAAMA,EAAI,IAAM,IAAMA,EAAI,KAAO,IAAM,EAAI,GACxG,GAAIA,GAAKC,OAAOD,EAAI,IAAM,GAAKA,EAAI,KAAO,GAAK,EAAU,IAANA,EAAU,EAAI,GACjE,GAAIA,GAAKC,OAAY,GAALD,GAAUA,EAAI,IAAM,GAAKA,EAAI,KAAO,GAAK,EAAI,GAC7D,MAASC,OAAY,GAALD,EAAS,EAAS,GAALA,EAAS,EAAI,GAC1C,MAASC,OAAY,GAALD,EAAS,EAAS,GAALA,GAAUA,EAAI,IAAM,GAAKA,EAAI,IAAM,GAAK,EAAIA,EAAI,IAAM,IAAMA,EAAI,IAAM,GAAK,EAAI,GAC5G,GAAIA,GAAKC,OAAY,GAALD,EAAS,EAAS,GAALA,GAAUA,EAAI,IAAM,GAAKA,EAAI,IAAM,GAAK,EAAI,GACzE,GAASA,GAAAC,OAAOD,EAAI,KAAO,EAAI,EAAIA,EAAI,KAAO,EAAI,EAAIA,EAAI,KAAO,GAAKA,EAAI,KAAO,EAAI,EAAI,GACzF,GAASA,GAAAC,OAAY,GAALD,EAAS,EAAS,GAALA,EAAS,GAAKA,EAAI,GAAKA,EAAI,KAAOA,EAAI,IAAM,EAAI,EAAI,IAEnF,MAAME,EAAkB,CAAC,KAAM,KAAM,MAC/BC,EAAe,CAAC,MAChBC,EAAgB,CACpBC,KAAM,EACNC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,KAAM,EACNC,MAAO,GAcT,MAAMC,EACJ,WAAA5S,CAAYmM,GACN,IAAA9J,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpFhB,KAAKiM,cAAgBA,EACrBjM,KAAKmC,QAAUA,EACVnC,KAAAsC,OAASkB,EAAWH,OAAO,kBAC1BrD,KAAKmC,QAAQwQ,oBAAqBT,EAAaU,SAAS5S,KAAKmC,QAAQwQ,oBAAwC,oBAATnC,MAAyBA,KAAKqC,cACtI7S,KAAKmC,QAAQwQ,kBAAoB,KAC5B3S,KAAAsC,OAAOP,MAAM,uJAEpB/B,KAAK8S,MAtBW,MAClB,MAAMA,EAAQ,CAAE,EAST,OARPlB,EAAK9N,SAAelD,IACdA,EAAAmL,KAAKjI,SAAayI,IACpBuG,EAAMvG,GAAK,CACTwG,QAASnS,EAAIiR,GACbmB,QAASlB,EAAmBlR,EAAI+Q,IACjC,GACF,IAEImB,CAAA,EAYQG,GACbjT,KAAKkT,iBAAmB,CAAE,CAC9B,CACE,OAAAC,CAAQ/N,EAAKvI,GACNmD,KAAA8S,MAAM1N,GAAOvI,CACtB,CACE,UAAAuW,GACEpT,KAAKkT,iBAAmB,CAAE,CAC9B,CACE,OAAAG,CAAQ7R,GACF,IAAAW,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChF,GAAAhB,KAAKuL,mBAAoB,CAC3B,MAAM+H,EAAc/R,EAAwB,QAATC,EAAiB,KAAOA,GACrDE,EAAOS,EAAQkJ,QAAU,UAAY,WACrCkI,EAAWpN,KAAKE,UAAU,CAC9BiN,cACA5R,SAEE,GAAA6R,KAAYvT,KAAKkT,iBACZ,OAAAlT,KAAKkT,iBAAiBK,GAE3B,IAAAC,EACA,IACKA,EAAA,IAAIhD,KAAKqC,YAAYS,EAAa,CACvC5R,QAEH,OAAQ+R,GACP,IAAKjS,EAAK2H,MAAM,OAAQ,OACxB,MAAMuK,EAAU1T,KAAKiM,cAAcsE,wBAAwB/O,GACpDgS,EAAAxT,KAAKqT,QAAQK,EAASvR,EACrC,CAEa,OADFnC,KAAAkT,iBAAiBK,GAAYC,EAC3BA,CACb,CACW,OAAAxT,KAAK8S,MAAMtR,IAASxB,KAAK8S,MAAM9S,KAAKiM,cAAcsE,wBAAwB/O,GACrF,CACE,WAAAmS,CAAYnS,GACN,IAAAW,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpF,MAAMwS,EAAOxT,KAAKqT,QAAQ7R,EAAMW,GAC5B,OAAAnC,KAAKuL,mBACAiI,GAAQA,EAAKI,kBAAkBC,iBAAiB1V,OAAS,EAE3DqV,GAAQA,EAAKT,QAAQ5U,OAAS,CACzC,CACE,mBAAA2V,CAAoBtS,EAAM/D,GACpB,IAAA0E,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAC7E,OAAAhB,KAAK+M,YAAYvL,EAAMW,GAAS0G,KAAImE,GAAU,GAAGvP,IAAMuP,KAClE,CACE,WAAAD,CAAYvL,GACN,IAAAW,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpF,MAAMwS,EAAOxT,KAAKqT,QAAQ7R,EAAMW,GAChC,OAAKqR,EAGDxT,KAAKuL,mBACAiI,EAAKI,kBAAkBC,iBAAiBE,MAAK,CAACC,EAAiBC,IAAoB9B,EAAc6B,GAAmB7B,EAAc8B,KAAkBpL,KAAsBqL,GAAA,GAAGlU,KAAKmC,QAAQgS,UAAUhS,EAAQkJ,QAAU,UAAUrL,KAAKmC,QAAQgS,UAAY,KAAKD,MAEhQV,EAAKT,QAAQlK,KAAIuL,GAAUpU,KAAKmL,UAAU3J,EAAM4S,EAAQjS,KALtD,EAMb,CACE,SAAAgJ,CAAU3J,EAAMuJ,GACV,IAAA5I,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpF,MAAMwS,EAAOxT,KAAKqT,QAAQ7R,EAAMW,GAChC,OAAIqR,EACExT,KAAKuL,mBACA,GAAGvL,KAAKmC,QAAQgS,UAAUhS,EAAQkJ,QAAU,UAAUrL,KAAKmC,QAAQgS,UAAY,KAAKX,EAAKa,OAAOtJ,KAElG/K,KAAKsU,yBAAyBd,EAAMzI,IAE7C/K,KAAKsC,OAAOR,KAAK,6BAA6BN,KACvC,GACX,CACE,wBAAA8S,CAAyBd,EAAMzI,GAC7B,MAAMwJ,EAAMf,EAAKgB,MAAQhB,EAAKR,QAAQjI,GAASyI,EAAKR,QAAQyB,KAAKC,IAAI3J,IACjE,IAAAiC,EAASwG,EAAKT,QAAQwB,GACtBvU,KAAKmC,QAAQwS,sBAAgD,IAAxBnB,EAAKT,QAAQ5U,QAAoC,IAApBqV,EAAKT,QAAQ,KAClE,IAAX/F,EACOA,EAAA,SACW,IAAXA,IACAA,EAAA,KAGb,MAAM4H,EAAe,IAAM5U,KAAKmC,QAAQgS,SAAWnH,EAAOzC,WAAavK,KAAKmC,QAAQgS,QAAUnH,EAAOzC,WAAayC,EAAOzC,WACrH,MAAmC,OAAnCvK,KAAKmC,QAAQwQ,kBACA,IAAX3F,EAAqB,GACH,iBAAXA,EAA4B,WAAWA,EAAOzC,aAClDqK,IACqC,OAAnC5U,KAAKmC,QAAQwQ,mBAEb3S,KAAKmC,QAAQwS,sBAAgD,IAAxBnB,EAAKT,QAAQ5U,QAAoC,IAApBqV,EAAKT,QAAQ,GADjF6B,IAIF5U,KAAKmC,QAAQgS,SAAWI,EAAIhK,WAAavK,KAAKmC,QAAQgS,QAAUI,EAAIhK,WAAagK,EAAIhK,UAChG,CACE,gBAAAgB,GACE,OAAQ0G,EAAgBW,SAAS5S,KAAKmC,QAAQwQ,kBAClD,EAGA,MAAMkC,EAAuB,SAAUnV,EAAMoV,EAAarX,GACpD,IAAAsD,EAAeC,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,IACnF8D,IAAsB9D,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,KAAmBA,UAAU,GACrFlD,EAnoCsB,EAAC4B,EAAMoV,EAAarX,KACxC,MAAAgI,EAAQ1G,EAAQW,EAAMjC,GAC5B,YAAc,IAAVgI,EACKA,EAEF1G,EAAQ+V,EAAarX,EAAG,EA8nCpBsX,CAAoBrV,EAAMoV,EAAarX,GAK3C,OAJFK,GAAQgH,GAAuBlI,EAASa,KACpCK,EAAAgD,EAASpB,EAAMjC,EAAKsD,QACd,IAATjD,IAAoBA,EAAOgD,EAASgU,EAAarX,EAAKsD,KAErDjD,CACT,EACMkX,EAAYC,GAAOA,EAAItX,QAAQ,MAAO,QAC5C,MAAMuX,EACJ,WAAApV,GACM,IAAAqC,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAC/EhB,KAAAsC,OAASkB,EAAWH,OAAO,gBAChCrD,KAAKmC,QAAUA,EACfnC,KAAKmV,OAAShT,EAAQ6F,eAAiB7F,EAAQ6F,cAAcmN,SAAoB1P,GAAAA,GACjFzF,KAAKoC,KAAKD,EACd,CACE,IAAAC,GACM,IAAAD,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAC/EmB,EAAQ6F,gBAAe7F,EAAQ6F,cAAgB,CAClDoN,aAAa,IAET,MACJ3V,OAAQ4V,EAAAD,YACRA,EAAAE,oBACAA,EAAAjT,OACAA,EAAAkT,cACAA,EAAAvI,OACAA,EAAAwI,cACAA,EAAAC,gBACAA,EAAAC,eACAA,EAAAC,eACAA,EAAAC,cACAA,EAAAC,qBACAA,EAAAC,cACAA,EAAAC,qBACAA,EAAAC,wBACAA,EAAAC,YACAA,EAAAC,aACAA,GACE/T,EAAQ6F,cACPhI,KAAAP,YAAsB,IAAb4V,EAAyBA,EAAW5V,EAC7CO,KAAAoV,iBAA8B,IAAhBA,GAA4BA,EAC1CpV,KAAAsV,yBAA8C,IAAxBA,GAAoCA,EAC/DtV,KAAKqC,OAASA,EAAS/C,EAAY+C,GAAUkT,GAAiB,KAC9DvV,KAAKgN,OAASA,EAAS1N,EAAY0N,GAAUwI,GAAiB,KAC9DxV,KAAKyV,gBAAkBA,GAAmB,IACrCzV,KAAA2V,eAAiBD,EAAiB,GAAKC,GAAkB,IAC9D3V,KAAK0V,eAAiB1V,KAAK2V,eAAiB,GAAKD,GAAkB,GACnE1V,KAAK4V,cAAgBA,EAAgBtW,EAAYsW,GAAiBC,GAAwBvW,EAAY,OACtGU,KAAK8V,cAAgBA,EAAgBxW,EAAYwW,GAAiBC,GAAwBzW,EAAY,KACtGU,KAAKgW,wBAA0BA,GAA2B,IAC1DhW,KAAKiW,YAAcA,GAAe,IAC7BjW,KAAAkW,kBAAgC,IAAjBA,GAA6BA,EACjDlW,KAAKmW,aACT,CACE,KAAAhI,GACMnO,KAAKmC,SAAcnC,KAAAoC,KAAKpC,KAAKmC,QACrC,CACE,WAAAgU,GACQ,MAAAC,EAAmB,CAACC,EAAgBhW,IACpCgW,GAAkBA,EAAenX,SAAWmB,GAC9CgW,EAAeC,UAAY,EACpBD,GAEF,IAAI5V,OAAOJ,EAAS,KAExBL,KAAAuW,OAASH,EAAiBpW,KAAKuW,OAAQ,GAAGvW,KAAKqC,cAAcrC,KAAKgN,UACvEhN,KAAKwW,eAAiBJ,EAAiBpW,KAAKwW,eAAgB,GAAGxW,KAAKqC,SAASrC,KAAK2V,sBAAsB3V,KAAK0V,iBAAiB1V,KAAKgN,UAC9HhN,KAAAqJ,cAAgB+M,EAAiBpW,KAAKqJ,cAAe,GAAGrJ,KAAK4V,qBAAqB5V,KAAK8V,gBAChG,CACE,WAAA/H,CAAYxO,EAAKG,EAAM0F,EAAKjD,GACtB,IAAAgH,EACA1D,EACAgR,EACE,MAAA3B,EAAc9U,KAAKmC,SAAWnC,KAAKmC,QAAQ6F,eAAiBhI,KAAKmC,QAAQ6F,cAAc0F,kBAAoB,CAAE,EAC7GgJ,EAAsBjZ,IAC1B,GAAIA,EAAIC,QAAQsC,KAAKyV,iBAAmB,EAAG,CACnC,MAAA3X,EAAO+W,EAAqBnV,EAAMoV,EAAarX,EAAKuC,KAAKmC,QAAQpB,aAAcf,KAAKmC,QAAQ2C,qBAClG,OAAO9E,KAAKkW,aAAelW,KAAKmV,OAAOrX,OAAM,EAAWsH,EAAK,IACxDjD,KACAzC,EACHiX,iBAAkBlZ,IACfK,CACb,CACM,MAAMc,EAAInB,EAAIQ,MAAM+B,KAAKyV,iBACnBjX,EAAII,EAAE+B,QAAQiW,OACdC,EAAIjY,EAAE2G,KAAKvF,KAAKyV,iBAAiBmB,OACvC,OAAO5W,KAAKmV,OAAON,EAAqBnV,EAAMoV,EAAatW,EAAGwB,KAAKmC,QAAQpB,aAAcf,KAAKmC,QAAQ2C,qBAAsB+R,EAAGzR,EAAK,IAC/HjD,KACAzC,EACHiX,iBAAkBnY,GACnB,EAEHwB,KAAKmW,cACL,MAAMW,EAA8B3U,GAAWA,EAAQ2U,6BAA+B9W,KAAKmC,QAAQ2U,4BAC7FlJ,EAAkBzL,GAAWA,EAAQ6F,oBAA2D,IAA1C7F,EAAQ6F,cAAc4F,gBAAgCzL,EAAQ6F,cAAc4F,gBAAkB5N,KAAKmC,QAAQ6F,cAAc4F,gBA2C9K,MA1CO,CAAC,CACbmJ,MAAO/W,KAAKwW,eACZQ,UAAkB/B,GAAAD,EAAUC,IAC3B,CACD8B,MAAO/W,KAAKuW,OACZS,UAAkB/B,GAAAjV,KAAKoV,YAAcJ,EAAUhV,KAAKP,OAAOwV,IAAQD,EAAUC,KAEzEnR,SAAgBmT,IAEpB,IADWR,EAAA,EACJtN,EAAQ8N,EAAKF,MAAMG,KAAK3X,IAAM,CACnC,MAAM4X,EAAahO,EAAM,GAAGyN,OAE5B,GADAnR,EAAQiR,EAAaS,QACP,IAAV1R,EACE,GAAuC,mBAAhCqR,EAA4C,CACrD,MAAMM,EAAON,EAA4BvX,EAAK4J,EAAOhH,GAC7CsD,EAAA7I,EAASwa,GAAQA,EAAO,EAC5C,MAAA,GAAqBjV,GAAW/D,OAAOC,UAAUC,eAAeC,KAAK4D,EAASgV,GAC1D1R,EAAA,WACCmI,EAAiB,CAC1BnI,EAAQ0D,EAAM,GACd,QACZ,CACYnJ,KAAKsC,OAAOR,KAAK,8BAA8BqV,uBAAgC5X,KACvEkG,EAAA,EACpB,MACoB7I,EAAS6I,IAAWzF,KAAKsV,sBACnC7P,EAAQpI,EAAWoI,IAEf,MAAAuR,EAAYC,EAAKD,UAAUvR,GAS7B,GARJlG,EAAMA,EAAI5B,QAAQwL,EAAM,GAAI6N,GACxBpJ,GACGqJ,EAAAF,MAAMT,WAAa7Q,EAAMtH,OAC9B8Y,EAAKF,MAAMT,WAAanN,EAAM,GAAGhL,QAEjC8Y,EAAKF,MAAMT,UAAY,EAEzBG,IACIA,GAAYzW,KAAKiW,YACnB,KAEV,KAEW1W,CACX,CACE,IAAA0O,CAAK1O,EAAKoS,GACJ,IACAxI,EACA1D,EACA4R,EAHAlV,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAI9E,MAAAsW,EAAmB,CAAC7Z,EAAK8Z,KAC7B,MAAMC,EAAMxX,KAAKgW,wBACjB,GAAIvY,EAAIC,QAAQ8Z,GAAO,EAAU,OAAA/Z,EAC3B,MAAAkL,EAAIlL,EAAIQ,MAAM,IAAIwC,OAAO,GAAG+W,WAClC,IAAIC,EAAgB,IAAI9O,EAAE,KAC1BlL,EAAMkL,EAAE,GACQ8O,EAAAzX,KAAK+N,YAAY0J,EAAeJ,GAC1C,MAAAK,EAAsBD,EAActO,MAAM,MAC1CwO,EAAsBF,EAActO,MAAM,OAC5CuO,GAAuBA,EAAoBvZ,OAAS,GAAM,IAAMwZ,GAAuBA,EAAoBxZ,OAAS,GAAM,KAC5GsZ,EAAAA,EAAc9Z,QAAQ,KAAM,MAE1C,IACc0Z,EAAAlR,KAAKC,MAAMqR,GACvBF,IAAkCF,EAAA,IACjCE,KACAF,GAEN,OAAQ1Y,GAEP,OADAqB,KAAKsC,OAAOR,KAAK,oDAAoDrE,IAAOkB,GACrE,GAAGlB,IAAM+Z,IAAMC,GAC9B,CAEa,OADHJ,EAAc7L,cAAgB6L,EAAc7L,aAAa9N,QAAQsC,KAAKqC,SAAc,UAAOgV,EAAc7L,aACtG/N,CAAA,EAET,KAAO0L,EAAQnJ,KAAKqJ,cAAc6N,KAAK3X,IAAM,CAC3C,IAAIqY,EAAa,GACDP,EAAA,IACXlV,GAEWkV,EAAAA,EAAc1Z,UAAYf,EAASya,EAAc1Z,SAAW0Z,EAAc1Z,QAAU0Z,EACpGA,EAAc/I,oBAAqB,SAC5B+I,EAAc7L,aACrB,IAAIqM,GAAW,EACf,IAAqD,IAAjD1O,EAAM,GAAGzL,QAAQsC,KAAKyV,mBAA4B,OAAO1M,KAAKI,EAAM,IAAK,CAC3E,MAAMP,EAAIO,EAAM,GAAGlL,MAAM+B,KAAKyV,iBAAiB5M,KAAIiP,GAAQA,EAAKlB,SAC1DzN,EAAA,GAAKP,EAAEjI,QACAiX,EAAAhP,EACFiP,GAAA,CACnB,CAEU,GADIpS,EAAAkM,EAAG2F,EAAiB/Y,KAAKyB,KAAMmJ,EAAM,GAAGyN,OAAQS,GAAgBA,GACpE5R,GAAS0D,EAAM,KAAO5J,IAAQ3C,EAAS6I,GAAe,OAAAA,EACrD7I,EAAS6I,KAAQA,EAAQpI,EAAWoI,IACpCA,IACEzF,KAAAsC,OAAOR,KAAK,qBAAqBqH,EAAM,kBAAkB5J,KACtDkG,EAAA,IAENoS,IACMpS,EAAAmS,EAAWG,QAAO,CAACjR,EAAG+P,IAAM7W,KAAKmV,OAAOrO,EAAG+P,EAAG1U,EAAQiD,IAAK,IAC9DjD,EACHwU,iBAAkBxN,EAAM,GAAGyN,UACzBnR,EAAMmR,SAEZrX,EAAMA,EAAI5B,QAAQwL,EAAM,GAAI1D,GAC5BzF,KAAKuW,OAAOD,UAAY,CAC9B,CACW,OAAA/W,CACX,EAGA,MA+BMyY,EAA8BC,IAClC,MAAMC,EAAQ,CAAE,EACT,MAAA,CAACjD,EAAK7P,EAAKjD,KAChB,IAAIgW,EAAchW,EACdA,GAAWA,EAAQwU,kBAAoBxU,EAAQiW,cAAgBjW,EAAQiW,aAAajW,EAAQwU,mBAAqBxU,EAAQA,EAAQwU,oBACrHwB,EAAA,IACTA,EACH,CAAChW,EAAQwU,uBAAmB,IAGhC,MAAMlZ,EAAM2H,EAAMe,KAAKE,UAAU8R,GAC7B,IAAAE,EAAYH,EAAMza,GAKtB,OAJK4a,IACHA,EAAYJ,EAAG1W,EAAe6D,GAAMjD,GACpC+V,EAAMza,GAAO4a,GAERA,EAAUpD,EAAG,CACrB,EAEH,MAAMqD,EACJ,WAAAxY,GACM,IAAAqC,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAC/EhB,KAAAsC,OAASkB,EAAWH,OAAO,aAChCrD,KAAKmC,QAAUA,EACfnC,KAAKuY,QAAU,CACbnE,OAAQ4D,GAAsB,CAAC5S,EAAKoT,KAClC,MAAMH,EAAY,IAAI7H,KAAKiI,aAAarT,EAAK,IACxCoT,IAEE,OAAAvD,GAAOoD,EAAUlD,OAAOF,EAAG,IAEpCyD,SAAUV,GAAsB,CAAC5S,EAAKoT,KACpC,MAAMH,EAAY,IAAI7H,KAAKiI,aAAarT,EAAK,IACxCoT,EACHG,MAAO,aAEF,OAAA1D,GAAOoD,EAAUlD,OAAOF,EAAG,IAEpC2D,SAAUZ,GAAsB,CAAC5S,EAAKoT,KACpC,MAAMH,EAAY,IAAI7H,KAAKqI,eAAezT,EAAK,IAC1CoT,IAEE,OAAAvD,GAAOoD,EAAUlD,OAAOF,EAAG,IAEpC6D,aAAcd,GAAsB,CAAC5S,EAAKoT,KACxC,MAAMH,EAAY,IAAI7H,KAAKuI,mBAAmB3T,EAAK,IAC9CoT,IAEL,UAAcH,EAAUlD,OAAOF,EAAKuD,EAAIQ,OAAS,MAAK,IAExDC,KAAMjB,GAAsB,CAAC5S,EAAKoT,KAChC,MAAMH,EAAY,IAAI7H,KAAK0I,WAAW9T,EAAK,IACtCoT,IAEE,OAAAvD,GAAOoD,EAAUlD,OAAOF,EAAG,KAGtCjV,KAAKoC,KAAKD,EACd,CACE,IAAAC,CAAKuF,GACC,IAAAxF,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAChFgH,cAAe,CAAA,GAEZhI,KAAAyV,gBAAkBtT,EAAQ6F,cAAcyN,iBAAmB,GACpE,CACE,GAAA0D,CAAI/R,EAAMuK,GACR3R,KAAKuY,QAAQnR,EAAKyC,cAAc+M,QAAUjF,CAC9C,CACE,SAAAyH,CAAUhS,EAAMuK,GACT3R,KAAAuY,QAAQnR,EAAKyC,cAAc+M,QAAUoB,EAAsBrG,EACpE,CACE,MAAAwD,CAAO1P,EAAO0P,EAAQ/P,GAChB,IAAAjD,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpF,MAAMuX,EAAUpD,EAAOlX,MAAM+B,KAAKyV,iBAC9B,GAAA8C,EAAQpa,OAAS,GAAKoa,EAAQ,GAAG7a,QAAQ,KAAO,GAAK6a,EAAQ,GAAG7a,QAAQ,KAAO,GAAK6a,EAAQ1R,MAAUgQ,GAAAA,EAAEnZ,QAAQ,MAAO,IAAK,CACxH,MAAA4Y,EAAYiC,EAAQc,WAAUxC,GAAKA,EAAEnZ,QAAQ,MAAS,IAC5D6a,EAAQ,GAAK,CAACA,EAAQ,MAAOA,EAAQrT,OAAO,EAAGoR,IAAY/Q,KAAKvF,KAAKyV,gBAC3E,CAyBW,OAxBQ8C,EAAQR,QAAO,CAACuB,EAAKzC,KAC5B,MAAA0C,WACJA,EAAAC,cACAA,GAhHe,CAAaC,IAClC,IAAIF,EAAaE,EAAU5P,cAAc+M,OACzC,MAAM4C,EAAgB,CAAE,EACxB,GAAIC,EAAU/b,QAAQ,MAAW,EAAA,CACzB,MAAAkB,EAAI6a,EAAUxb,MAAM,KAC1Bsb,EAAa3a,EAAE,GAAGiL,cAAc+M,OAC1B,MAAA8C,EAAS9a,EAAE,GAAGqK,UAAU,EAAGrK,EAAE,GAAGT,OAAS,GAC5B,aAAfob,GAA6BG,EAAOhc,QAAQ,KAAO,EAChD8b,EAAcd,WAAwBc,EAAAd,SAAWgB,EAAO9C,QACrC,iBAAf2C,GAAiCG,EAAOhc,QAAQ,KAAO,EAC3D8b,EAAcR,QAAqBQ,EAAAR,MAAQU,EAAO9C,QAE1C8C,EAAOzb,MAAM,KACrB6F,SAAe0U,IAClB,GAAIA,EAAK,CACP,MAAO/a,KAAQkc,GAAQnB,EAAIva,MAAM,KAC3BgX,EAAM0E,EAAKpU,KAAK,KAAKqR,OAAOjZ,QAAQ,WAAY,IAChDic,EAAanc,EAAImZ,OAClB4C,EAAcI,KAAaJ,EAAcI,GAAc3E,GAChD,UAARA,IAA+BuE,EAAAI,IAAc,GACrC,SAAR3E,IAA8BuE,EAAAI,IAAc,GAC3CC,MAAM5E,OAAoB2E,GAAcE,SAAS7E,EAAK,IACrE,IAGA,CACS,MAAA,CACLsE,aACAC,gBACD,EAoFOO,CAAelD,GACf,GAAA7W,KAAKuY,QAAQgB,GAAa,CAC5B,IAAIS,EAAYV,EACZ,IACI,MAAAW,EAAa9X,GAAWA,EAAQiW,cAAgBjW,EAAQiW,aAAajW,EAAQwU,mBAAqB,CAAE,EACpGpK,EAAI0N,EAAWC,QAAUD,EAAW7U,KAAOjD,EAAQ+X,QAAU/X,EAAQiD,KAAOA,EAClF4U,EAAYha,KAAKuY,QAAQgB,GAAYD,EAAK/M,EAAG,IACxCiN,KACArX,KACA8X,GAEN,OAAQlY,GACF/B,KAAAsC,OAAOR,KAAKC,EAC3B,CACe,OAAAiY,CACf,CAGa,OAFLha,KAAKsC,OAAOR,KAAK,oCAAoCyX,KAEhDD,CAAA,GACN7T,EAEP,EASA,MAAM0U,UAAkB1W,EACtB,WAAA3D,CAAYsa,EAASC,EAAO1S,GACtB,IAAAxF,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAC7E6D,QACP7E,KAAKoa,QAAUA,EACfpa,KAAKqa,MAAQA,EACbra,KAAK2H,SAAWA,EAChB3H,KAAKiM,cAAgBtE,EAASsE,cAC9BjM,KAAKmC,QAAUA,EACVnC,KAAAsC,OAASkB,EAAWH,OAAO,oBAChCrD,KAAKsa,aAAe,GACfta,KAAAua,iBAAmBpY,EAAQoY,kBAAoB,GACpDva,KAAKwa,aAAe,EACpBxa,KAAKya,WAAatY,EAAQsY,YAAc,EAAItY,EAAQsY,WAAa,EACjEza,KAAK0a,aAAevY,EAAQuY,cAAgB,EAAIvY,EAAQuY,aAAe,IACvE1a,KAAK2a,MAAQ,CAAE,EACf3a,KAAK4a,MAAQ,GACT5a,KAAKoa,SAAWpa,KAAKoa,QAAQhY,MAC/BpC,KAAKoa,QAAQhY,KAAKuF,EAAUxF,EAAQiY,QAASjY,EAEnD,CACE,SAAA0Y,CAAUC,EAAW1S,EAAYjG,EAAS4Y,GACxC,MAAMC,EAAS,CAAE,EACXC,EAAU,CAAE,EACZC,EAAkB,CAAE,EACpBC,EAAmB,CAAE,EA4BpB,OA3BPL,EAAUhX,SAAesB,IACvB,IAAIgW,GAAmB,EACvBhT,EAAWtE,SAAca,IACvB,MAAMyC,EAAO,GAAGhC,KAAOT,KAClBxC,EAAQkZ,QAAUrb,KAAKqa,MAAM9T,kBAAkBnB,EAAKT,GAClD3E,KAAA2a,MAAMvT,GAAQ,EACVpH,KAAK2a,MAAMvT,GAAQ,IAAmC,IAArBpH,KAAK2a,MAAMvT,QAC/B,IAAlB6T,EAAQ7T,KAAqB6T,EAAQ7T,IAAQ,IAE5CpH,KAAA2a,MAAMvT,GAAQ,EACAgU,GAAA,OACG,IAAlBH,EAAQ7T,KAAqB6T,EAAQ7T,IAAQ,QAC5B,IAAjB4T,EAAO5T,KAAqB4T,EAAO5T,IAAQ,QAClB,IAAzB+T,EAAiBxW,KAAmBwW,EAAiBxW,IAAM,IACzE,IAEWyW,IAAkCF,EAAA9V,IAAO,EAAA,KAE5ChH,OAAOwI,KAAKoU,GAAQ7c,QAAUC,OAAOwI,KAAKqU,GAAS9c,SACrD6B,KAAK4a,MAAM/Z,KAAK,CACdoa,UACAK,aAAcld,OAAOwI,KAAKqU,GAAS9c,OACnCod,OAAQ,CAAE,EACVC,OAAQ,GACRT,aAGG,CACLC,OAAQ5c,OAAOwI,KAAKoU,GACpBC,QAAS7c,OAAOwI,KAAKqU,GACrBC,gBAAiB9c,OAAOwI,KAAKsU,GAC7BC,iBAAkB/c,OAAOwI,KAAKuU,GAEpC,CACE,MAAAI,CAAOnU,EAAMqM,EAAK/T,GACV,MAAAC,EAAIyH,EAAKnJ,MAAM,KACfmH,EAAMzF,EAAE,GACRgF,EAAKhF,EAAE,GACT8T,GAAUzT,KAAAkE,KAAK,gBAAiBkB,EAAKT,EAAI8O,IACxCA,GAAO/T,GACVM,KAAKqa,MAAMtU,kBAAkBX,EAAKT,EAAIjF,OAAM,OAAW,EAAW,CAChEuG,UAAU,IAGdjG,KAAK2a,MAAMvT,GAAQqM,GAAW,EAAA,EAC1BA,GAAO/T,IAAWM,KAAA2a,MAAMvT,GAAQ,GACpC,MAAMmU,EAAS,CAAE,EACZvb,KAAA4a,MAAM9W,SAAa2X,IAxjDX,EAACne,EAAQQ,EAAMY,KACxB,MAAA7B,IACJA,EAAA2B,EACAA,GACEX,EAAcP,EAAQQ,EAAMM,QAChCvB,EAAI2B,GAAK3B,EAAI2B,IAAM,GACf3B,EAAA2B,GAAGqC,KAAKnC,EAAQ,EAmjDhBgd,CAASD,EAAEF,OAAQ,CAACnW,GAAMT,GAhFV,EAAC8W,EAAGrU,UACA,IAApBqU,EAAER,QAAQ7T,YACLqU,EAAER,QAAQ7T,GACfqU,EAAAH,eACN,EA6EMK,CAAcF,EAAGrU,GACbqM,GAAKgI,EAAED,OAAO3a,KAAK4S,GACA,IAAnBgI,EAAEH,cAAuBG,EAAEG,OAC7Bxd,OAAOwI,KAAK6U,EAAEF,QAAQzX,SAAayI,IAC5BgP,EAAOhP,KAAWgP,EAAAhP,GAAK,CAAE,GACxB,MAAAsP,EAAaJ,EAAEF,OAAOhP,GACxBsP,EAAW1d,QACb0d,EAAW/X,SAAaiO,SACD,IAAjBwJ,EAAOhP,GAAGwF,KAAyBwJ,EAAAhP,GAAGwF,IAAK,EAAA,GAE7D,IAEQ0J,EAAEG,MAAO,EACLH,EAAED,OAAOrd,OACTsd,EAAAV,SAASU,EAAED,QAEbC,EAAEV,WAEZ,IAES/a,KAAAkE,KAAK,SAAUqX,GACpBvb,KAAK4a,MAAQ5a,KAAK4a,MAAMlS,QAAY+S,IAACA,EAAEG,MAC3C,CACE,IAAAE,CAAK1W,EAAKT,EAAIoX,GACR,IAAAC,EAAQhb,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,EAC5Eib,EAAOjb,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAKhB,KAAK0a,aAChFK,EAAW/Z,UAAU7C,OAAS,EAAI6C,UAAU,QAAK,EACrD,IAAKoE,EAAIjH,cAAe4c,EAAS,KAAM,CAAA,GACnC,GAAA/a,KAAKwa,cAAgBxa,KAAKua,iBAS5B,YARAva,KAAKsa,aAAazZ,KAAK,CACrBuE,MACAT,KACAoX,SACAC,QACAC,OACAlB,aAIC/a,KAAAwa,eACC,MAAA0B,EAAW,CAACzI,EAAK/T,KAEjB,GADCM,KAAAwa,eACDxa,KAAKsa,aAAanc,OAAS,EAAG,CAC1B,MAAAiD,EAAOpB,KAAKsa,aAAa3Z,QAC/BX,KAAK8b,KAAK1a,EAAKgE,IAAKhE,EAAKuD,GAAIvD,EAAK2a,OAAQ3a,EAAK4a,MAAO5a,EAAK6a,KAAM7a,EAAK2Z,SAC9E,CACUtH,GAAO/T,GAAQsc,EAAQhc,KAAKya,WAC9B0B,YAAW,KACJnc,KAAA8b,KAAKvd,KAAKyB,KAAMoF,EAAKT,EAAIoX,EAAQC,EAAQ,EAAU,EAAPC,EAAUlB,EAAQ,GAClEkB,GAGLlB,EAAStH,EAAK/T,EAAI,EAEdiS,EAAK3R,KAAKoa,QAAQ2B,GAAQK,KAAKpc,KAAKoa,SACtC,GAAc,IAAdzI,EAAGxT,OAaA,OAAAwT,EAAGvM,EAAKT,EAAIuX,GAZb,IACI,MAAAtT,EAAI+I,EAAGvM,EAAKT,GACdiE,GAAuB,mBAAXA,EAAEyT,KACdzT,EAAAyT,SAAaH,EAAS,KAAMxc,KAAO4c,MAAMJ,GAE3CA,EAAS,KAAMtT,EAElB,OAAQ6K,GACPyI,EAASzI,EACjB,CAIA,CACE,cAAA8I,CAAezB,EAAW1S,GACpB,IAAAjG,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChF+Z,EAAW/Z,UAAU7C,OAAS,EAAI6C,UAAU,QAAK,EACjD,IAAChB,KAAKoa,QAER,OADKpa,KAAAsC,OAAOR,KAAK,kEACViZ,GAAYA,IAEjBne,EAASke,OAAwB9a,KAAKiM,cAAcI,mBAAmByO,IACvEle,EAASwL,KAAaA,EAAa,CAACA,IACxC,MAAM4S,EAAShb,KAAK6a,UAAUC,EAAW1S,EAAYjG,EAAS4Y,GAC1D,IAACC,EAAOA,OAAO7c,OAEV,OADF6c,EAAOC,QAAQ9c,QAAkB4c,IAC/B,KAEFC,EAAAA,OAAOlX,SAAgBsD,IAC5BpH,KAAKwc,QAAQpV,EAAI,GAEvB,CACE,IAAA4J,CAAK8J,EAAW1S,EAAY2S,GAC1B/a,KAAKuc,eAAezB,EAAW1S,EAAY,CAAA,EAAI2S,EACnD,CACE,MAAAM,CAAOP,EAAW1S,EAAY2S,GACvB/a,KAAAuc,eAAezB,EAAW1S,EAAY,CACzCiT,QAAQ,GACPN,EACP,CACE,OAAAyB,CAAQpV,GACF,IAAA/E,EAASrB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,GAC3E,MAAArB,EAAIyH,EAAKnJ,MAAM,KACfmH,EAAMzF,EAAE,GACRgF,EAAKhF,EAAE,GACRK,KAAA8b,KAAK1W,EAAKT,EAAI,YAAQ,OAAW,GAAW,CAAC8O,EAAK/T,KACjD+T,GAAUzT,KAAAsC,OAAOR,KAAK,GAAGO,sBAA2BsC,kBAAmBS,WAAcqO,IACpFA,GAAO/T,GAAMM,KAAKsC,OAAOX,IAAI,GAAGU,qBAA0BsC,kBAAmBS,IAAO1F,GACpFM,KAAAub,OAAOnU,EAAMqM,EAAK/T,EAAI,GAEjC,CACE,WAAAkN,CAAYkO,EAAWnR,EAAWlM,EAAKgf,EAAeC,GAChD,IAAAva,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChF2b,EAAM3b,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,OAC9E,GAAIhB,KAAK2H,SAASmH,OAAS9O,KAAK2H,SAASmH,MAAMC,qBAAuB/O,KAAK2H,SAASmH,MAAMC,mBAAmBpF,GAC3G3J,KAAKsC,OAAOR,KAAK,qBAAqBrE,wBAA0BkM,wBAAiC,iOAGnG,GAAIlM,SAA6C,KAARA,EAAzC,CACA,GAAIuC,KAAKoa,SAAWpa,KAAKoa,QAAQ/W,OAAQ,CACvC,MAAMuZ,EAAO,IACRza,EACHua,YAEI/K,EAAK3R,KAAKoa,QAAQ/W,OAAO+Y,KAAKpc,KAAKoa,SACrC,GAAAzI,EAAGxT,OAAS,EACV,IACE,IAAAyK,EAEFA,EADgB,IAAd+I,EAAGxT,OACDwT,EAAGmJ,EAAWnR,EAAWlM,EAAKgf,EAAeG,GAE7CjL,EAAGmJ,EAAWnR,EAAWlM,EAAKgf,GAEhC7T,GAAuB,mBAAXA,EAAEyT,KACdzT,EAAAyT,SAAaM,EAAI,KAAMjd,KAAO4c,MAAMK,GAEtCA,EAAI,KAAM/T,EAEb,OAAQ6K,GACPkJ,EAAIlJ,EACd,MAEQ9B,EAAGmJ,EAAWnR,EAAWlM,EAAKgf,EAAeE,EAAKC,EAE1D,CACS9B,GAAcA,EAAU,IAC7B9a,KAAKqa,MAAM7U,YAAYsV,EAAU,GAAInR,EAAWlM,EAAKgf,EA5BA,CA6BzD,EAGA,MAAMlc,EAAM,KAAO,CACjBgC,OAAO,EACPsa,eAAe,EACflY,GAAI,CAAC,eACLC,UAAW,CAAC,eACZuH,YAAa,CAAC,OACdwC,YAAY,EACZyB,eAAe,EACfa,0BAA0B,EAC1BD,KAAM,MACN8L,SAAS,EACTnI,sBAAsB,EACtB5T,aAAc,IACdoH,YAAa,IACbsD,gBAAiB,IACjB6D,iBAAkB,IAClByN,yBAAyB,EACzBnQ,aAAa,EACbf,eAAe,EACfO,cAAe,WACfS,oBAAoB,EACpBH,mBAAmB,EACnBoK,6BAA6B,EAC7B1I,aAAa,EACbG,yBAAyB,EACzBkB,YAAY,EACZC,mBAAmB,EACnBvC,eAAe,EACf3C,YAAY,EACZ4C,uBAAuB,EACvBF,wBAAwB,EACxBD,6BAA6B,EAC7BrD,yBAAyB,EACzBH,iCAA0C7H,IACxC,IAAIob,EAAM,CAAE,EAIR,GAHmB,iBAAZpb,EAAK,KAAiBob,EAAMpb,EAAK,IACxChF,EAASgF,EAAK,MAASob,EAAAxR,aAAe5J,EAAK,IAC3ChF,EAASgF,EAAK,MAASob,EAAAC,aAAerb,EAAK,IACxB,iBAAZA,EAAK,IAAsC,iBAAZA,EAAK,GAAiB,CAC9D,MAAMO,EAAUP,EAAK,IAAMA,EAAK,GAChCxD,OAAOwI,KAAKzE,GAAS2B,SAAerG,IAC9Buf,EAAAvf,GAAO0E,EAAQ1E,EAAG,GAE9B,CACW,OAAAuf,CAAA,EAEThV,cAAe,CACboN,aAAa,EACbD,OAAiB1P,GAAAA,EACjBpD,OAAQ,KACR2K,OAAQ,KACRyI,gBAAiB,IACjBE,eAAgB,IAChBC,cAAe,MACfE,cAAe,IACfE,wBAAyB,IACzBC,YAAa,IACbrI,iBAAiB,KAGfsP,EAA8B/a,IAC9BvF,EAASuF,EAAQwC,QAAaA,GAAK,CAACxC,EAAQwC,KAC5C/H,EAASuF,EAAQgK,iBAAsBA,YAAc,CAAChK,EAAQgK,cAC9DvP,EAASuF,EAAQwM,gBAAqBA,WAAa,CAACxM,EAAQwM,aAC5DxM,EAAQiO,eAAiBjO,EAAQiO,cAAc1S,QAAQ,UAAY,IACrEyE,EAAQiO,cAAgBjO,EAAQiO,cAAczK,OAAO,CAAC,YAEjDxD,GAGHgb,EAAO,OASb,MAAMC,UAAa3Z,EACjB,WAAA3D,GACM,IAAAqC,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChF+Z,EAAW/Z,UAAU7C,OAAS,EAAI6C,UAAU,QAAK,EAX7B,IAAQqc,EAoBhC,GAROxY,QACF7E,KAAAmC,QAAU+a,EAAiB/a,GAChCnC,KAAK2H,SAAW,CAAE,EAClB3H,KAAKsC,OAASkB,EACdxD,KAAKsd,QAAU,CACbC,SAAU,IAjBoBF,EAmBZrd,KAlBT5B,OAAOof,oBAAoBpf,OAAOqf,eAAeJ,IACzDvZ,SAAewV,IACO,mBAAd+D,EAAK/D,KACd+D,EAAK/D,GAAO+D,EAAK/D,GAAK8C,KAAKiB,GACjC,IAeQtC,IAAa/a,KAAK0d,gBAAkBvb,EAAQwb,QAAS,CACnD,IAAC3d,KAAKmC,QAAQ0a,cAET,OADF7c,KAAAoC,KAAKD,EAAS4Y,GACZ/a,KAETmc,YAAW,KACJnc,KAAAoC,KAAKD,EAAS4Y,EAAQ,GAC1B,EACT,CACA,CACE,IAAA3Y,GACE,IAAIqL,EAAQzN,KACR,IAAAmC,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChF+Z,EAAW/Z,UAAU7C,OAAS,EAAI6C,UAAU,QAAK,EACrDhB,KAAK4d,gBAAiB,EACC,mBAAZzb,IACE4Y,EAAA5Y,EACXA,EAAU,CAAE,IAETA,EAAQyC,YAAmC,IAAtBzC,EAAQyC,WAAuBzC,EAAQwC,KAC3D/H,EAASuF,EAAQwC,IACnBxC,EAAQyC,UAAYzC,EAAQwC,GACnBxC,EAAQwC,GAAGjH,QAAQ,eAAiB,IACrCyE,EAAAyC,UAAYzC,EAAQwC,GAAG,KAGnC,MAAMkZ,EAAUtd,IAChBP,KAAKmC,QAAU,IACV0b,KACA7d,KAAKmC,WACL+a,EAAiB/a,IAEgB,OAAlCnC,KAAKmC,QAAQsE,mBACfzG,KAAKmC,QAAQ6F,cAAgB,IACxB6V,EAAQ7V,iBACRhI,KAAKmC,QAAQ6F,qBAGS,IAAzB7F,EAAQpB,eACLf,KAAAmC,QAAQoG,wBAA0BpG,EAAQpB,mBAErB,IAAxBoB,EAAQgG,cACLnI,KAAAmC,QAAQqG,uBAAyBrG,EAAQgG,aAEhD,MAAM2V,EAAuCC,GACtCA,EACwB,mBAAlBA,EAAqC,IAAIA,EAC7CA,EAFoB,KAIzB,IAAC/d,KAAKmC,QAAQwb,QAAS,CAMrB,IAAAtF,EALArY,KAAKsd,QAAQhb,OACfkB,EAAWpB,KAAK0b,EAAoB9d,KAAKsd,QAAQhb,QAAStC,KAAKmC,SAEpDqB,EAAApB,KAAK,KAAMpC,KAAKmC,SAGzBnC,KAAKsd,QAAQjF,UACfA,EAAYrY,KAAKsd,QAAQjF,UACA,oBAAT7H,OACJ6H,EAAAC,GAEd,MAAM0F,EAAK,IAAI7N,EAAanQ,KAAKmC,SACjCnC,KAAKqa,MAAQ,IAAI3V,EAAc1E,KAAKmC,QAAQ0D,UAAW7F,KAAKmC,SAC5D,MAAMxC,EAAIK,KAAK2H,SACfhI,EAAE2C,OAASkB,EACX7D,EAAEgQ,cAAgB3P,KAAKqa,MACvB1a,EAAEsM,cAAgB+R,EAChBre,EAAAuL,eAAiB,IAAIwH,EAAesL,EAAI,CACxC7J,QAASnU,KAAKmC,QAAQsJ,gBACtBkH,kBAAmB3S,KAAKmC,QAAQwQ,kBAChCgC,qBAAsB3U,KAAKmC,QAAQwS,wBAEjC0D,GAAerY,KAAKmC,QAAQ6F,cAAcmN,QAAUnV,KAAKmC,QAAQ6F,cAAcmN,SAAW0I,EAAQ7V,cAAcmN,SAChHxV,EAAA0Y,UAAYyF,EAAoBzF,GAClC1Y,EAAE0Y,UAAUjW,KAAKzC,EAAGK,KAAKmC,SACpBnC,KAAAmC,QAAQ6F,cAAcmN,OAASxV,EAAE0Y,UAAUlD,OAAOiH,KAAKzc,EAAE0Y,YAEhE1Y,EAAEyJ,aAAe,IAAI8L,EAAalV,KAAKmC,SACvCxC,EAAEmP,MAAQ,CACRC,mBAAoB/O,KAAK+O,mBAAmBqN,KAAKpc,OAEnDL,EAAEgN,iBAAmB,IAAIwN,EAAU2D,EAAoB9d,KAAKsd,QAAQlD,SAAUza,EAAEgQ,cAAehQ,EAAGK,KAAKmC,SACvGxC,EAAEgN,iBAAiBhJ,GAAG,KAAK,SAAUI,GACnC,IAAA,IAASvB,EAAOxB,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMD,EAAO,EAAIA,EAAO,EAAI,GAAIE,EAAO,EAAGA,EAAOF,EAAME,IAClGd,EAAKc,EAAO,GAAK1B,UAAU0B,GAEvB+K,EAAAvJ,KAAKH,KAAUnC,EAC7B,IACU5B,KAAKsd,QAAQW,mBACfte,EAAEse,iBAAmBH,EAAoB9d,KAAKsd,QAAQW,kBAClDte,EAAEse,iBAAiB7b,MAAQzC,EAAAse,iBAAiB7b,KAAKzC,EAAGK,KAAKmC,QAAQ+b,UAAWle,KAAKmC,UAEnFnC,KAAKsd,QAAQ5S,aACf/K,EAAE+K,WAAaoT,EAAoB9d,KAAKsd,QAAQ5S,YAC5C/K,EAAE+K,WAAWtI,MAAQzC,EAAA+K,WAAWtI,KAAKpC,OAE3CA,KAAKsH,WAAa,IAAII,EAAW1H,KAAK2H,SAAU3H,KAAKmC,SACrDnC,KAAKsH,WAAW3D,GAAG,KAAK,SAAUI,GAChC,IAAA,IAASnB,EAAQ5B,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMG,EAAQ,EAAIA,EAAQ,EAAI,GAAIC,EAAQ,EAAGA,EAAQD,EAAOC,IACxGjB,EAAKiB,EAAQ,GAAK7B,UAAU6B,GAExB4K,EAAAvJ,KAAKH,KAAUnC,EAC7B,IACW5B,KAAAsd,QAAQC,SAASzZ,SAAagC,IAC7BA,EAAE1D,MAAQ0D,EAAA1D,KAAKpC,KAAI,GAE/B,CAGQ,GAFCA,KAAAmV,OAASnV,KAAKmC,QAAQ6F,cAAcmN,OACpC4F,IAAqBA,EAAAoC,GACtBnd,KAAKmC,QAAQgK,cAAgBnM,KAAK2H,SAASsW,mBAAqBje,KAAKmC,QAAQiD,IAAK,CACpF,MAAMyJ,EAAQ7O,KAAK2H,SAASsE,cAAcC,iBAAiBlM,KAAKmC,QAAQgK,aACpE0C,EAAM1Q,OAAS,GAAkB,QAAb0Q,EAAM,KAAmB7O,KAAAmC,QAAQiD,IAAMyJ,EAAM,GAC3E,CACS7O,KAAK2H,SAASsW,kBAAqBje,KAAKmC,QAAQiD,KAC9CpF,KAAAsC,OAAOR,KAAK,2DAEF,CAAC,cAAe,oBAAqB,oBAAqB,qBAClEgC,SAAkBiY,IACpB/b,KAAA+b,GAAU,WACb,OAAOtO,EAAM4M,MAAM0B,MAAW/a,UAC/B,CAAA,IAEqB,CAAC,cAAe,eAAgB,oBAAqB,wBAC7D8C,SAAkBiY,IAC3B/b,KAAA+b,GAAU,WAEN,OADPtO,EAAM4M,MAAM0B,MAAW/a,WAChByM,CACR,CAAA,IAEH,MAAM0Q,EAAWrhB,IACXkU,EAAO,KACL,MAAAoN,EAAS,CAAC3K,EAAK7L,KACnB5H,KAAK4d,gBAAiB,EAClB5d,KAAK0d,gBAAkB1d,KAAKqe,sBAA2Bre,KAAAsC,OAAOR,KAAK,yEACvE9B,KAAK0d,eAAgB,EAChB1d,KAAKmC,QAAQwb,cAAcrb,OAAOX,IAAI,cAAe3B,KAAKmC,SAC1DnC,KAAAkE,KAAK,cAAelE,KAAKmC,SAC9Bgc,EAAShhB,QAAQyK,GACjBmT,EAAStH,EAAK7L,EAAC,EAEjB,GAAI5H,KAAK8a,WAA+C,OAAlC9a,KAAKmC,QAAQsE,mBAA8BzG,KAAK0d,cAAe,OAAOU,EAAO,KAAMpe,KAAK4H,EAAEwU,KAAKpc,OACrHA,KAAK6H,eAAe7H,KAAKmC,QAAQiD,IAAKgZ,EAAM,EAOvC,OALHpe,KAAKmC,QAAQ0D,YAAc7F,KAAKmC,QAAQ0a,cACpC7L,IAENmL,WAAWnL,EAAM,GAEZmN,CACX,CACE,aAAAG,CAAcxW,GACR,IACAyW,EADWvd,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAKmc,EAEnF,MAAMnT,EAAUpN,EAASkL,GAAYA,EAAW9H,KAAK8H,SAErD,GADwB,mBAAbA,IAAwCyW,EAAAzW,IAC9C9H,KAAKmC,QAAQ0D,WAAa7F,KAAKmC,QAAQ4a,wBAAyB,CACnE,GAAI/S,GAAqC,WAA1BA,EAAQH,iBAAgC7J,KAAKmC,QAAQ2a,SAA2C,IAAhC9c,KAAKmC,QAAQ2a,QAAQ3e,eAAsBogB,IAC1H,MAAMvD,EAAS,GACTwD,EAAgBpZ,IACpB,IAAKA,EAAK,OACV,GAAY,WAARA,EAAkB,OACTpF,KAAK2H,SAASsE,cAAcI,mBAAmBjH,GACvDtB,SAAayI,IACN,WAANA,GACAyO,EAAOtd,QAAQ6O,GAAK,GAAGyO,EAAOna,KAAK0L,EAAC,GACzC,EAEH,GAAKvC,EAIHwU,EAAOxU,OAJK,CACMhK,KAAK2H,SAASsE,cAAcC,iBAAiBlM,KAAKmC,QAAQgK,aAClErI,SAAQyI,GAAKiS,EAAOjS,IACtC,CAGUvM,KAAKmC,QAAQ2a,SACf9c,KAAKmC,QAAQ2a,QAAQhZ,SAAayI,GAAAiS,EAAOjS,KAE3CvM,KAAK2H,SAASgF,iBAAiBqE,KAAKgK,EAAQhb,KAAKmC,QAAQwC,IAAShG,IAC3DA,GAAMqB,KAAKye,mBAAoBze,KAAK8H,UAAU9H,KAAK0e,oBAAoB1e,KAAK8H,UACjFyW,EAAa5f,EAAC,GAEtB,MACM4f,EAAa,KAEnB,CACE,eAAAI,CAAgB5S,EAAMpH,EAAIoW,GACxB,MAAMoD,EAAWrhB,IAgBV,MAfa,mBAATiP,IACEgP,EAAAhP,EACJA,OAAA,GAES,mBAAPpH,IACEoW,EAAApW,EACNA,OAAA,GAEFoH,IAAMA,EAAO/L,KAAK8a,WAClBnW,IAASA,EAAA3E,KAAKmC,QAAQwC,IACtBoW,IAAqBA,EAAAoC,GAC1Bnd,KAAK2H,SAASgF,iBAAiB0O,OAAOtP,EAAMpH,GAAW8O,IACrD0K,EAAShhB,UACT4d,EAAStH,EAAG,IAEP0K,CACX,CACE,GAAAS,CAAIzX,GACF,IAAKA,EAAc,MAAA,IAAI0X,MAAM,iGAC7B,IAAK1X,EAAOzF,KAAY,MAAA,IAAImd,MAAM,4FAsB3B,MArBa,YAAhB1X,EAAOzF,OACT1B,KAAKsd,QAAQlD,QAAUjT,IAEL,WAAhBA,EAAOzF,MAAqByF,EAAOxF,KAAOwF,EAAOrF,MAAQqF,EAAOpF,SAClE/B,KAAKsd,QAAQhb,OAAS6E,GAEJ,qBAAhBA,EAAOzF,OACT1B,KAAKsd,QAAQW,iBAAmB9W,GAEd,eAAhBA,EAAOzF,OACT1B,KAAKsd,QAAQ5S,WAAavD,GAER,kBAAhBA,EAAOzF,MACTsF,EAAcE,iBAAiBC,GAEb,cAAhBA,EAAOzF,OACT1B,KAAKsd,QAAQjF,UAAYlR,GAEP,aAAhBA,EAAOzF,MACJ1B,KAAAsd,QAAQC,SAAS1c,KAAKsG,GAEtBnH,IACX,CACE,mBAAA0e,CAAoBnS,GAClB,GAAKA,GAAMvM,KAAK8a,aACZ,CAAC,SAAU,OAAOpd,QAAQ6O,IAAS,GACvC,IAAA,IAASuS,EAAK,EAAGA,EAAK9e,KAAK8a,UAAU3c,OAAQ2gB,IAAM,CAC3C,MAAAC,EAAY/e,KAAK8a,UAAUgE,GACjC,KAAI,CAAC,SAAU,OAAOphB,QAAQqhB,IAAiB,IAC3C/e,KAAKqa,MAAM1T,4BAA4BoY,GAAY,CACrD/e,KAAKye,iBAAmBM,EACxB,KACR,CACA,CACA,CACE,cAAAlX,CAAezC,EAAK2V,GAClB,IAAIiE,EAAShf,KACbA,KAAKif,qBAAuB7Z,EAC5B,MAAM+Y,EAAWrhB,IACZkD,KAAAkE,KAAK,mBAAoBkB,GAC9B,MAAM8Z,EAAmB3S,IACvBvM,KAAK8H,SAAWyE,EAChBvM,KAAK8a,UAAY9a,KAAK2H,SAASsE,cAAcI,mBAAmBE,GAChEvM,KAAKye,sBAAmB,EACxBze,KAAK0e,oBAAoBnS,EAAC,EAEtBqP,EAAO,CAACnI,EAAKlH,KACbA,GACF2S,EAAY3S,GACPvM,KAAAsH,WAAWO,eAAe0E,GAC/BvM,KAAKif,0BAAuB,EACvBjf,KAAAkE,KAAK,kBAAmBqI,GACxBvM,KAAAsC,OAAOX,IAAI,kBAAmB4K,IAEnCvM,KAAKif,0BAAuB,EAE9Bd,EAAShhB,SAAQ,WACR,OAAA6hB,EAAOpX,KAAK5G,UAC3B,IACU+Z,GAAmBA,EAAAtH,GAAK,WACnB,OAAAuL,EAAOpX,KAAK5G,UAC3B,GAAO,EAEGme,EAAiBpT,IAChB3G,GAAQ2G,IAAQ/L,KAAK2H,SAASsW,qBAAyB,IACtD,MAAA1R,EAAI3P,EAASmP,GAAQA,EAAO/L,KAAK2H,SAASsE,cAAciF,sBAAsBnF,GAChFQ,IACGvM,KAAK8H,UACRoX,EAAY3S,GAETvM,KAAKsH,WAAWQ,UAAe9H,KAAAsH,WAAWO,eAAe0E,GAC1DvM,KAAK2H,SAASsW,kBAAoBje,KAAK2H,SAASsW,iBAAiBmB,mBAAwBpf,KAAA2H,SAASsW,iBAAiBmB,kBAAkB7S,IAEtIvM,KAAAse,cAAc/R,GAAUkH,IAC3BmI,EAAKnI,EAAKlH,EAAC,GACZ,EAaI,OAXFnH,IAAOpF,KAAK2H,SAASsW,kBAAqBje,KAAK2H,SAASsW,iBAAiBoB,OAElEja,GAAOpF,KAAK2H,SAASsW,kBAAoBje,KAAK2H,SAASsW,iBAAiBoB,MAC7B,IAAjDrf,KAAK2H,SAASsW,iBAAiBqB,OAAOnhB,OACxC6B,KAAK2H,SAASsW,iBAAiBqB,SAASjD,KAAK8C,GAExCnf,KAAA2H,SAASsW,iBAAiBqB,OAAOH,GAGxCA,EAAO/Z,GARP+Z,EAAOnf,KAAK2H,SAASsW,iBAAiBqB,UAUjCnB,CACX,CACE,SAAAoB,CAAUna,EAAKT,EAAI6a,GACjB,IAAIC,EAASzf,KACP,MAAA0f,EAAS,SAAUjiB,EAAKmf,GACxB,IAAAza,EACA,GAAgB,iBAATya,EAAmB,CAC5B,IAAA,IAAS9Z,EAAQ9B,UAAU7C,OAAQwb,EAAO,IAAIlX,MAAMK,EAAQ,EAAIA,EAAQ,EAAI,GAAIC,EAAQ,EAAGA,EAAQD,EAAOC,IACxG4W,EAAK5W,EAAQ,GAAK/B,UAAU+B,GAEpBZ,EAAAsd,EAAOtd,QAAQsH,iCAAiC,CAAChM,EAAKmf,GAAMjX,OAAOgU,GACrF,MACkBxX,EAAA,IACLya,GAGCza,EAAAiD,IAAMjD,EAAQiD,KAAOsa,EAAOta,IAC5BjD,EAAA4J,KAAO5J,EAAQ4J,MAAQ2T,EAAO3T,KAC9B5J,EAAAwC,GAAKxC,EAAQwC,IAAM+a,EAAO/a,GACR,KAAtBxC,EAAQqd,YAAkBrd,EAAQqd,UAAYrd,EAAQqd,WAAaA,GAAaE,EAAOF,WACrF,MAAAze,EAAe0e,EAAOtd,QAAQpB,cAAgB,IAChD,IAAA4e,EAMG,OAJOA,EADVxd,EAAQqd,WAAa/c,MAAM4C,QAAQ5H,GACzBA,EAAIoL,KAAIrK,GAAK,GAAG2D,EAAQqd,YAAYze,IAAevC,MAEnD2D,EAAQqd,UAAY,GAAGrd,EAAQqd,YAAYze,IAAetD,IAAQA,EAEzEgiB,EAAO7X,EAAE+X,EAAWxd,EAC5B,EAQM,OAPHvF,EAASwI,GACXsa,EAAOta,IAAMA,EAEbsa,EAAO3T,KAAO3G,EAEhBsa,EAAO/a,GAAKA,EACZ+a,EAAOF,UAAYA,EACZE,CACX,CACE,CAAA9X,GACE,OAAO5H,KAAKsH,YAActH,KAAKsH,WAAWiC,aAAavI,UAC3D,CACE,MAAA+G,GACE,OAAO/H,KAAKsH,YAActH,KAAKsH,WAAWS,UAAU/G,UACxD,CACE,mBAAA4e,CAAoBjb,GAClB3E,KAAKmC,QAAQyC,UAAYD,CAC7B,CACE,kBAAAoK,CAAmBpK,GACb,IAAAxC,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChF,IAAChB,KAAK0d,cAED,OADP1d,KAAKsC,OAAOR,KAAK,kDAAmD9B,KAAK8a,YAClE,EAET,IAAK9a,KAAK8a,YAAc9a,KAAK8a,UAAU3c,OAE9B,OADP6B,KAAKsC,OAAOR,KAAK,6DAA8D9B,KAAK8a,YAC7E,EAET,MAAM1V,EAAMjD,EAAQiD,KAAOpF,KAAKye,kBAAoBze,KAAK8a,UAAU,GAC7D3O,IAAcnM,KAAKmC,SAAUnC,KAAKmC,QAAQgK,YAC1C0T,EAAU7f,KAAK8a,UAAU9a,KAAK8a,UAAU3c,OAAS,GACvD,GAA0B,WAAtBiH,EAAIyE,cAAmC,OAAA,EACrC,MAAAiW,EAAiB,CAACvT,EAAGwF,KACnB,MAAAgO,EAAY/f,KAAK2H,SAASgF,iBAAiBgO,MAAM,GAAGpO,KAAKwF,KAC/D,OAAqB,IAAdgO,GAAkC,IAAdA,GAAiC,IAAdA,CAAc,EAE9D,GAAI5d,EAAQ6d,SAAU,CACpB,MAAMC,EAAY9d,EAAQ6d,SAAShgB,KAAM8f,GACrC,QAAc,IAAdG,EAAgC,OAAAA,CAC1C,CACI,QAAIjgB,KAAKuG,kBAAkBnB,EAAKT,OAC3B3E,KAAK2H,SAASgF,iBAAiByN,WAAWpa,KAAKmC,QAAQ0D,WAAc7F,KAAKmC,QAAQ4a,8BACnF+C,EAAe1a,EAAKT,IAASwH,IAAe2T,EAAeD,EAASlb,IAE5E,CACE,cAAAub,CAAevb,EAAIoW,GACjB,MAAMoD,EAAWrhB,IACb,OAACkD,KAAKmC,QAAQwC,IAId/H,EAAS+H,KAAKA,EAAK,CAACA,IACxBA,EAAGb,SAAaiO,IACV/R,KAAKmC,QAAQwC,GAAGjH,QAAQqU,GAAK,GAAQ/R,KAAAmC,QAAQwC,GAAG9D,KAAKkR,EAAC,IAE5D/R,KAAKse,eAAqB7K,IACxB0K,EAAShhB,UACL4d,KAAmBtH,EAAG,IAErB0K,IAXDpD,GAAoBA,IACjB7d,QAAQC,UAWrB,CACE,aAAAgjB,CAAcpU,EAAMgP,GAClB,MAAMoD,EAAWrhB,IACbF,EAASmP,KAAOA,EAAO,CAACA,IAC5B,MAAMqU,EAAYpgB,KAAKmC,QAAQ2a,SAAW,GACpCuD,EAAUtU,EAAKrD,QAAOtD,GAAOgb,EAAU1iB,QAAQ0H,GAAO,GAAKpF,KAAK2H,SAASsE,cAAc8E,gBAAgB3L,KACzG,OAACib,EAAQliB,QAIb6B,KAAKmC,QAAQ2a,QAAUsD,EAAUza,OAAO0a,GACxCrgB,KAAKse,eAAqB7K,IACxB0K,EAAShhB,UACL4d,KAAmBtH,EAAG,IAErB0K,IARDpD,GAAoBA,IACjB7d,QAAQC,UAQrB,CACE,GAAAmjB,CAAIlb,GAEE,GADCA,IAAWA,EAAApF,KAAKye,mBAAqBze,KAAK8a,WAAa9a,KAAK8a,UAAU3c,OAAS,EAAI6B,KAAK8a,UAAU,GAAK9a,KAAK8H,YAC5G1C,EAAY,MAAA,MACjB,MACM6G,EAAgBjM,KAAK2H,UAAY3H,KAAK2H,SAASsE,eAAiB,IAAIkE,EAAa5P,KACvF,MAFgB,CAAC,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,MAAO,MAAO,KAAM,MAAO,OAEna7C,QAAQuO,EAAcsE,wBAAwBnL,KAAc,GAAAA,EAAIyE,cAAcnM,QAAQ,SAAW,EAAI,MAAQ,KAChI,CACE,qBAAO6iB,GAGE,OAAA,IAAInD,EAFGpc,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACrEA,UAAU7C,OAAS,EAAI6C,UAAU,QAAK,EAEzD,CACE,aAAAwf,GACM,IAAAre,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChF+Z,EAAW/Z,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAKmc,EACnF,MAAMsD,EAAoBte,EAAQse,kBAC9BA,UAA0Bte,EAAQse,kBACtC,MAAMC,EAAgB,IACjB1gB,KAAKmC,WACLA,EAEDwb,SAAS,GAGPpa,EAAQ,IAAI6Z,EAAKsD,QACD,IAAlBve,EAAQI,YAA0C,IAAnBJ,EAAQE,SACzCkB,EAAMjB,OAASiB,EAAMjB,OAAOiB,MAAMpB,IA4B7B,MA1Be,CAAC,QAAS,WAAY,YAC9B2B,SAAagC,IACnBvC,EAAAuC,GAAK9F,KAAK8F,EAAC,IAEnBvC,EAAMoE,SAAW,IACZ3H,KAAK2H,UAEVpE,EAAMoE,SAASmH,MAAQ,CACrBC,mBAAoBxL,EAAMwL,mBAAmBqN,KAAK7Y,IAEhDkd,IACFld,EAAM8W,MAAQ,IAAI3V,EAAc1E,KAAKqa,MAAM3a,KAAMghB,GAC3Cnd,EAAAoE,SAASgI,cAAgBpM,EAAM8W,OAEvC9W,EAAM+D,WAAa,IAAII,EAAWnE,EAAMoE,SAAU+Y,GAClDnd,EAAM+D,WAAW3D,GAAG,KAAK,SAAUI,GACjC,IAAA,IAASd,EAAQjC,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMQ,EAAQ,EAAIA,EAAQ,EAAI,GAAIC,EAAQ,EAAGA,EAAQD,EAAOC,IACxGtB,EAAKsB,EAAQ,GAAKlC,UAAUkC,GAExBK,EAAAW,KAAKH,KAAUnC,EAC3B,IACU2B,EAAAnB,KAAKse,EAAe3F,GAC1BxX,EAAM+D,WAAWnF,QAAUue,EACrBnd,EAAA+D,WAAWqF,iBAAiBhF,SAASmH,MAAQ,CACjDC,mBAAoBxL,EAAMwL,mBAAmBqN,KAAK7Y,IAE7CA,CACX,CACE,MAAAwD,GACS,MAAA,CACL5E,QAASnC,KAAKmC,QACdkY,MAAOra,KAAKqa,MACZvS,SAAU9H,KAAK8H,SACfgT,UAAW9a,KAAK8a,UAChB2D,iBAAkBze,KAAKye,iBAE7B,EAEK,MAACkC,EAAWvD,EAAKmD,iBACtBI,EAASJ,eAAiBnD,EAAKmD,eAERI,EAASJ,eACpBI,EAASL,IACRK,EAASve,KACAue,EAASrC,cACPqC,EAAShC,gBACrBgC,EAAS/B,IACE+B,EAAS9Y,eACd8Y,EAASpB,UACjBoB,EAAS/Y,EACJ+Y,EAAS5Y,OACI4Y,EAASf,oBACVe,EAAS5R,mBACb4R,EAAST,eACVS,EAASR,cCl0E/B,MAAMS,EAAgB,CAAE,EACjB,SAASC,IACd,IAAA,IAASje,EAAQ5B,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMG,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC/EjB,EAAAiB,GAAS7B,UAAU6B,GAEH,iBAAZjB,EAAK,IAAmBgf,EAAchf,EAAK,MAC/B,iBAAZA,EAAK,KAA+Bgf,EAAAhf,EAAK,IAAM,IAAIkf,MAfzD,WACD,GAAA9e,SAAWA,QAAQF,KAAM,CAC3B,IAAA,IAASU,EAAOxB,UAAU7C,OAAQyD,EAAO,IAAIa,MAAMD,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC1Ed,EAAAc,GAAQ1B,UAAU0B,GAEF,iBAAZd,EAAK,KAAsBA,EAAA,GAAK,mBAAmBA,EAAK,KAEvE,CACA,CAQEE,IAAQF,GACV,CACA,MAAMmf,EAAY,CAACC,EAAMC,IAAO,KAC9B,GAAID,EAAKtD,cACHuD,QACC,CACL,MAAMC,EAAc,KAClB/E,YAAW,KACJ6E,EAAA/c,IAAI,cAAeid,EAAW,GAClC,GACCD,GAAA,EAEDD,EAAArd,GAAG,cAAeud,EAC3B,GAEO,SAAShB,EAAec,EAAMrc,EAAIsc,GACvCD,EAAKd,eAAevb,EAAIoc,EAAUC,EAAMC,GAC1C,CACO,SAASd,EAAca,EAAM5b,EAAKT,EAAIsc,GACzB,iBAAPtc,IAAiBA,EAAK,CAACA,IAClCA,EAAGb,SAAaiO,IACViP,EAAK7e,QAAQwC,GAAGjH,QAAQqU,GAAK,GAAQiP,EAAA7e,QAAQwC,GAAG9D,KAAKkR,EAAC,IAE5DiP,EAAKb,cAAc/a,EAAK2b,EAAUC,EAAMC,GAC1C,CCxCA,MAAME,EAAkB,oGAClBC,GAAe,CACnB,QAAS,IACT,QAAS,IACT,OAAQ,IACR,QAAS,IACT,OAAQ,IACR,QAAS,IACT,SAAU,IACV,QAAS,IACT,SAAU,IACV,QAAS,IACT,SAAU,IACV,SAAU,IACV,SAAU,IACV,SAAU,IACV,QAAS,IACT,SAAU,IACV,WAAY,IACZ,UAAW,IACX,SAAU,IACV,QAAS,KAELC,GAA0Bvb,GAAAsb,GAAatb,GCtB7C,ICDIwb,GDCAC,GAAiB,CACnBC,SAAU,kBACVC,cAAe,GACfC,oBAAqB,GACrBC,4BAA4B,EAC5BC,mBAAoB,GACpBC,2BAA4B,CAAC,KAAM,SAAU,IAAK,KAClDC,aAAa,EACbC,SDesBC,GAAQA,EAAKrkB,QAAQwjB,EAAiBE,KGtBlD,MAACY,GAAmB,CAC9BvgB,KAAM,WACN,IAAAU,CAAKue,IFOA,WACD,IAAAxe,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACnEugB,GAAA,IACZA,MACApf,EAEP,CEZgBwe,CAAAA,EAASxe,QAAQ+f,ODJ1B,SAAiBvB,GACPA,GAAAA,CACjB,CCGIwB,CAAQxB,EACZ,GCFayB,GAAcC,EAAAA,gBACpB,MAAMC,GACX,WAAAxiB,GACEE,KAAKuiB,eAAiB,CAAE,CAC5B,CACE,iBAAAC,CAAkBpa,GAChBA,EAAWtE,SAAca,IAClB3E,KAAKuiB,eAAe5d,KAAU3E,KAAAuiB,eAAe5d,IAAM,EAAA,GAE9D,CACE,iBAAA8d,GACS,OAAArkB,OAAOwI,KAAK5G,KAAKuiB,eAC5B,ECPO,SAASG,GAAe/d,GACzB,IAAAge,EAAQ3hB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAC5E,MACJggB,KAAM4B,GACJD,GAEF3B,KAAM6B,EACNje,UAAWke,GACTC,EAAUA,WAACX,KAAgB,CAAE,EAC3BpB,EAAO4B,GAAiBC,GHdvBvB,GGgBP,GADIN,IAASA,EAAKgC,mBAAuBhC,EAAAgC,iBAAmB,IAAIV,KAC3DtB,EAAM,CACTH,EAAS,0EACH,MAAAoC,EAAY,CAACzkB,EAAG0kB,IACc,iBAAvBA,EAAwCA,EAC/CA,GAAoD,iBAAvBA,GAA8E,iBAApCA,EAAmB1X,aAAkC0X,EAAmB1X,aAC5I/I,MAAM4C,QAAQ7G,GAAKA,EAAEA,EAAEL,OAAS,GAAKK,EAExC2kB,EAAc,CAACF,EAAW,CAAA,GAAI,GAI7B,OAHPE,EAAYvb,EAAIqb,EAChBE,EAAYnC,KAAO,CAAE,EACrBmC,EAAYC,OAAQ,EACbD,CACX,CACMnC,EAAK7e,QAAQ+f,YAAqC,IAA5BlB,EAAK7e,QAAQ+f,MAAMjG,MAAoB4E,EAAS,uGAC1E,MAAMwC,EAAc,IJhBb9B,MIkBFP,EAAK7e,QAAQ+f,SACbS,IAECb,YACJA,EAAAtC,UACAA,GACE6D,EACJ,IAAIjb,EAAazD,GAAMme,GAAwB9B,EAAK7e,SAAW6e,EAAK7e,QAAQyC,UAC/DwD,EAAsB,iBAAfA,EAA0B,CAACA,GAAcA,GAAc,CAAC,eACxE4Y,EAAKgC,iBAAiBR,mBAAwBxB,EAAAgC,iBAAiBR,kBAAkBpa,GACrF,MAAMgb,GAASpC,EAAKtD,eAAiBsD,EAAK3C,uBAAyBjW,EAAWkb,OAAMvR,GNU/E,SAA4BpN,EAAIqc,GACjC,IAAA7e,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpF,OAAKggB,EAAKlG,WAAckG,EAAKlG,UAAU3c,YAIqB,IAArC6iB,EAAK7e,QAAQ2C,oBAI7Bkc,EAAKjS,mBAAmBpK,EAAI,CACjCS,IAAKjD,EAAQiD,IACb4a,SAAU,CAACsB,EAAcxB,KACnB,GAAA3d,EAAQqf,UAAYrf,EAAQqf,SAAS9jB,QAAQ,wBAA4B4jB,EAAa3Z,SAASgF,iBAAiByN,SAAWkH,EAAarC,uBAAyBa,EAAewB,EAAarC,qBAAsBta,GAAY,OAAA,CAAA,IA7BzO,SAAsCA,EAAIqc,GACpC,IAAA7e,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAC9E,MAAAoE,EAAM4b,EAAKlG,UAAU,GACrB3O,IAAc6U,EAAK7e,SAAU6e,EAAK7e,QAAQgK,YAC1C0T,EAAUmB,EAAKlG,UAAUkG,EAAKlG,UAAU3c,OAAS,GACvD,GAA0B,WAAtBiH,EAAIyE,cAAmC,OAAA,EACrC,MAAAiW,EAAiB,CAACvT,EAAGwF,KACnB,MAAAgO,EAAYiB,EAAKrZ,SAASgF,iBAAiBgO,MAAM,GAAGpO,KAAKwF,KACxD,WAAAgO,GAAkC,IAAdA,CAAc,EAEvC,QAAA5d,EAAQqf,UAAYrf,EAAQqf,SAAS9jB,QAAQ,wBAA4BsjB,EAAKrZ,SAASgF,iBAAiByN,SAAW4G,EAAK/B,uBAAyBa,EAAekB,EAAK/B,qBAAsBta,KAC3Lqc,EAAKza,kBAAkBnB,EAAKT,IAC3Bqc,EAAKrZ,SAASgF,iBAAiByN,WAAW4G,EAAK7e,QAAQ0D,WAAcmb,EAAK7e,QAAQ4a,4BACnF+C,EAAe1a,EAAKT,IAASwH,IAAe2T,EAAeD,EAASlb,IAE1E,CASW4e,CAA6B5e,EAAIqc,EAAM7e,IALrC0e,EAAA,yCAA0CG,EAAKlG,YACjD,EAYX,CM1B2F/L,CAAmBgD,EAAGiP,EAAMqC,KACrH,SAASG,IACP,OAAOxC,EAAKzB,UAAUoD,EAAMvd,KAAO,KAA6B,aAAvBie,EAAYI,OAAwBrb,EAAaA,EAAW,GAAIoX,EAC7G,CACE,MAAO5X,EAAG8b,GAAQC,EAAAA,SAASH,GACvB,IAAAI,EAAWxb,EAAW7C,OACtBod,EAAMvd,MAAKwe,EAAW,GAAGjB,EAAMvd,MAAMwe,KACnC,MAAAC,EAnDY,EAACpe,EAAOqe,KACpB,MAAAC,EAAMC,EAAAA,SAIZ,OAHAC,EAAAA,WAAU,KACRF,EAAI7iB,QAAiCuE,CAAA,GACpC,CAACA,EAAOqe,IACJC,EAAI7iB,OAAA,EA8CcgjB,CAAYN,GAC/BO,EAAYH,EAAMA,QAAC,GACzBC,EAAAA,WAAU,KACF,MAAAzC,SACJA,EAAAC,cACAA,GACE4B,EAgBJ,SAASe,IACHD,EAAUjjB,SAASwiB,EAAKF,EAClC,CAGI,OApBAW,EAAUjjB,SAAU,EACfkiB,GAAUtB,IACTa,EAAMvd,IACR+a,EAAca,EAAM2B,EAAMvd,IAAKgD,GAAY,KACrC+b,EAAUjjB,SAASwiB,EAAKF,EAAI,IAGnBtD,EAAAc,EAAM5Y,GAAY,KAC3B+b,EAAUjjB,SAASwiB,EAAKF,EAAI,KAIlCJ,GAASS,GAAoBA,IAAqBD,GAAYO,EAAUjjB,SAC1EwiB,EAAKF,GAKHhC,GAAYR,GAAWA,EAAArd,GAAG6d,EAAU4C,GACpC3C,GAAiBT,GAAMA,EAAK3G,MAAM1W,GAAG8d,EAAe2C,GACjD,KACLD,EAAUjjB,SAAU,EAChBsgB,GAAYR,GAAeQ,EAAAvjB,MAAM,KAAK6F,SAAQnF,GAAKqiB,EAAK/c,IAAItF,EAAGylB,KAC/D3C,GAAiBT,GAAoBS,EAAAxjB,MAAM,KAAK6F,SAAQnF,GAAKqiB,EAAK3G,MAAMpW,IAAItF,EAAGylB,IAAW,CAC/F,GACA,CAACpD,EAAM4C,IACJ,MAAAS,EAAYL,EAAMA,QAAC,GACzBC,EAAAA,WAAU,KACJE,EAAUjjB,UAAYmjB,EAAUnjB,SAClCwiB,EAAKF,GAEPa,EAAUnjB,SAAU,CAAA,GACnB,CAAC8f,EAAMxB,IACV,MAAMxC,EAAM,CAACpV,EAAGoZ,EAAMoC,GAItB,GAHApG,EAAIpV,EAAIA,EACRoV,EAAIgE,KAAOA,EACXhE,EAAIoG,MAAQA,EACRA,EAAc,OAAApG,EAClB,IAAKoG,IAAUtB,EAAoB,OAAA9E,EAC7B,MAAA,IAAI9f,SAAmBC,IACvBwlB,EAAMvd,IACR+a,EAAca,EAAM2B,EAAMvd,IAAKgD,GAAY,IAAMjL,MAEjD+iB,EAAec,EAAM5Y,GAAY,IAAMjL,KAC7C,GAEA,CC3GA,SAASmnB,GAAQC,GAAuCD,OAAAA,GAAU,mBAAqBE,QAAU,iBAAmBA,OAAOC,SAAW,SAAUF,GAAK,cAAcA,GAAO,SAAUA,GAAYA,OAAAA,GAAK,mBAAqBC,QAAUD,EAAEzkB,cAAgB0kB,QAAUD,IAAMC,OAAOnmB,UAAY,gBAAkBkmB,CAAE,GAAYA,EAAG,CAcrT,SAASG,KACP,MAA0B,mBAAnBC,gBAAqH,YAAzD,oBAAnBA,eAAiC,YAAcL,GAAQK,gBAChH,qTCfI,IAeMC,EAfNC,EACmB,oBAAfD,YAA8BA,YACrB,oBAATE,MAAwBA,WACb,IAAXC,GAA0BA,EAE9BC,EAAkB,WACtB,SAASC,IACTjlB,KAAKklB,OAAQ,EACbllB,KAAKmlB,aAAeN,EAAWM,aAG/B,OADAF,EAAE5mB,UAAYwmB,EACP,IAAII,CACX,CAPsB,GAUZL,EA0mBPI,EAxmBc,SAAWI,GAEtBL,IAAAA,OACqB,IAAfH,GAA8BA,GACrB,oBAATE,MAAwBA,WACb,IAAXC,GAA0BA,EAEhCM,EACY,oBAAqBN,EADjCM,EAEQ,WAAYN,GAAU,aAAcP,OAF5Ca,EAIA,eAAgBN,GAChB,SAAUA,GACT,WACK,IAEK,OADP,IAAIO,MACG,CACR,OAAQ3mB,GACA,OAAA,EAEjB,CAPO,GAND0mB,EAcQ,aAAcN,EAdtBM,EAeW,gBAAiBN,EAOhC,GAAIM,EACF,IAAIE,EAAc,CAChB,qBACA,sBACA,6BACA,sBACA,uBACA,sBACA,uBACA,wBACA,yBAGEC,EACFC,YAAYC,QACZ,SAAS7oB,GACA,OAAAA,GAAO0oB,EAAY7nB,QAAQU,OAAOC,UAAUkM,SAAShM,KAAK1B,KAAQ,CAC1E,EAGL,SAAS8oB,EAAcve,GAIrB,GAHoB,iBAATA,IACTA,EAAO/H,OAAO+H,IAEZ,6BAA6B2B,KAAK3B,IAAkB,KAATA,EAC7C,MAAM,IAAIwe,UAAU,4CAA8Cxe,EAAO,KAE3E,OAAOA,EAAKyC,cAGd,SAASgc,EAAepgB,GAIf,MAHc,iBAAVA,IACTA,EAAQpG,OAAOoG,IAEVA,EAIT,SAASqgB,EAAYC,GACnB,IAAItB,EAAW,CACbrjB,KAAM,WACA,IAAAqE,EAAQsgB,EAAMplB,QAClB,MAAO,CAACib,UAAgB,IAAVnW,EAAqBA,WAUhC,OANH4f,IACOZ,EAAAD,OAAOC,UAAY,WACnB,OAAAA,CACR,GAGIA,EAGT,SAASuB,EAAQC,GACfjmB,KAAK6I,IAAM,CAAE,EAETod,aAAmBD,EACbC,EAAAniB,SAAQ,SAAS2B,EAAO2B,GACzBpH,KAAAwe,OAAOpX,EAAM3B,EACnB,GAAEzF,MACMyC,MAAM4C,QAAQ4gB,GACfA,EAAAniB,SAAQ,SAASoiB,GACvBlmB,KAAKwe,OAAO0H,EAAO,GAAIA,EAAO,GAC/B,GAAElmB,MACMimB,GACT7nB,OAAOof,oBAAoByI,GAASniB,SAAQ,SAASsD,GACnDpH,KAAKwe,OAAOpX,EAAM6e,EAAQ7e,GAC3B,GAAEpH,MAgEP,SAASmmB,EAASC,GAChB,GAAIA,EAAKC,SACP,OAAOnpB,QAAQE,OAAO,IAAIwoB,UAAU,iBAEtCQ,EAAKC,UAAW,EAGlB,SAASC,EAAgBC,GACvB,OAAO,IAAIrpB,SAAQ,SAASC,EAASC,GACnCmpB,EAAOC,OAAS,WACdrpB,EAAQopB,EAAOjhB,OAChB,EACDihB,EAAOE,QAAU,WACfrpB,EAAOmpB,EAAOxkB,MACf,CACF,IAGH,SAAS2kB,EAAsBC,GACzB,IAAAJ,EAAS,IAAIK,WACb3pB,EAAUqpB,EAAgBC,GAEvB,OADPA,EAAOM,kBAAkBF,GAClB1pB,EAoBT,SAAS6pB,EAAYC,GACnB,GAAIA,EAAIloB,MACC,OAAAkoB,EAAIloB,MAAM,GAEjB,IAAImoB,EAAO,IAAIC,WAAWF,EAAIG,YAE9B,OADAF,EAAKpmB,IAAI,IAAIqmB,WAAWF,IACjBC,EAAKG,OAIhB,SAASC,IAkHA,OAjHPpnB,KAAKqmB,UAAW,EAEXrmB,KAAAqnB,UAAY,SAASjB,GAhM5B,IAAoBvpB,EA2MhBmD,KAAKqmB,SAAWrmB,KAAKqmB,SACrBrmB,KAAKsnB,UAAYlB,EACZA,EAEsB,iBAATA,EAChBpmB,KAAKunB,UAAYnB,EACRf,GAAgBC,KAAKjnB,UAAUmpB,cAAcpB,GACtDpmB,KAAKynB,UAAYrB,EACRf,GAAoBqC,SAASrpB,UAAUmpB,cAAcpB,GAC9DpmB,KAAK2nB,cAAgBvB,EACZf,GAAwBuC,gBAAgBvpB,UAAUmpB,cAAcpB,GACpEpmB,KAAAunB,UAAYnB,EAAK7b,WACb8a,GAAuBA,IAvNlBxoB,EAuN6CupB,IAtNjDyB,SAASxpB,UAAUmpB,cAAc3qB,IAuNtCmD,KAAA8nB,iBAAmBhB,EAAYV,EAAKe,QAEzCnnB,KAAKsnB,UAAY,IAAIhC,KAAK,CAACtlB,KAAK8nB,oBACvBzC,IAAwBI,YAAYpnB,UAAUmpB,cAAcpB,IAASZ,EAAkBY,IAC3FpmB,KAAA8nB,iBAAmBhB,EAAYV,GAEpCpmB,KAAKunB,UAAYnB,EAAOhoB,OAAOC,UAAUkM,SAAShM,KAAK6nB,GAhBvDpmB,KAAKunB,UAAY,GAmBdvnB,KAAKimB,QAAQ1lB,IAAI,kBACA,iBAAT6lB,EACJpmB,KAAAimB,QAAQrlB,IAAI,eAAgB,4BACxBZ,KAAKynB,WAAaznB,KAAKynB,UAAU/lB,KAC1C1B,KAAKimB,QAAQrlB,IAAI,eAAgBZ,KAAKynB,UAAU/lB,MACvC2jB,GAAwBuC,gBAAgBvpB,UAAUmpB,cAAcpB,IACpEpmB,KAAAimB,QAAQrlB,IAAI,eAAgB,mDAGtC,EAEGykB,IACFrlB,KAAK2mB,KAAO,WACN,IAAAoB,EAAW5B,EAASnmB,MACxB,GAAI+nB,EACK,OAAAA,EAGT,GAAI/nB,KAAKynB,UACA,OAAAvqB,QAAQC,QAAQ6C,KAAKynB,WACtC,GAAmBznB,KAAK8nB,iBACP,OAAA5qB,QAAQC,QAAQ,IAAImoB,KAAK,CAACtlB,KAAK8nB,oBAChD,GAAmB9nB,KAAK2nB,cACR,MAAA,IAAI9I,MAAM,wCAET,OAAA3hB,QAAQC,QAAQ,IAAImoB,KAAK,CAACtlB,KAAKunB,YAEzC,EAEDvnB,KAAKgoB,YAAc,WACjB,GAAIhoB,KAAK8nB,iBAAkB,CACrB,IAAAG,EAAa9B,EAASnmB,MAC1B,OAAIioB,IAGAxC,YAAYC,OAAO1lB,KAAK8nB,kBACnB5qB,QAAQC,QACb6C,KAAK8nB,iBAAiBX,OAAOtoB,MAC3BmB,KAAK8nB,iBAAiBI,WACtBloB,KAAK8nB,iBAAiBI,WAAaloB,KAAK8nB,iBAAiBZ,aAItDhqB,QAAQC,QAAQ6C,KAAK8nB,kBAExC,CACU,OAAO9nB,KAAK2mB,OAAOtK,KAAKqK,EAE3B,GAGH1mB,KAAKgiB,KAAO,WACN,IAnHgB2E,EAClBJ,EACAtpB,EAiHE8qB,EAAW5B,EAASnmB,MACxB,GAAI+nB,EACK,OAAAA,EAGT,GAAI/nB,KAAKynB,UACA,OAzHWd,EAyHI3mB,KAAKynB,UAxH3BlB,EAAS,IAAIK,WACb3pB,EAAUqpB,EAAgBC,GAC9BA,EAAO4B,WAAWxB,GACX1pB,EAsHX,GAAiB+C,KAAK8nB,iBACd,OAAO5qB,QAAQC,QApHrB,SAA+B4pB,GAI7B,IAHI,IAAAC,EAAO,IAAIC,WAAWF,GACtBnnB,EAAQ,IAAI6C,MAAMukB,EAAK7oB,QAElBgD,EAAI,EAAGA,EAAI6lB,EAAK7oB,OAAQgD,IAC/BvB,EAAMuB,GAAK9B,OAAO+oB,aAAapB,EAAK7lB,IAE/BvB,OAAAA,EAAM2F,KAAK,IA6GS8iB,CAAsBroB,KAAK8nB,mBAC1D,GAAiB9nB,KAAK2nB,cACR,MAAA,IAAI9I,MAAM,wCAET,OAAA3hB,QAAQC,QAAQ6C,KAAKunB,UAE/B,EAEGlC,IACFrlB,KAAKsoB,SAAW,WACd,OAAOtoB,KAAKgiB,OAAO3F,KAAKkM,EACzB,GAGHvoB,KAAKwoB,KAAO,WACV,OAAOxoB,KAAKgiB,OAAO3F,KAAKlW,KAAKC,MAC9B,EAEMpG,KAlOTgmB,EAAQ3nB,UAAUmgB,OAAS,SAASpX,EAAM3B,GACxC2B,EAAOue,EAAcve,GACrB3B,EAAQogB,EAAepgB,GACnB,IAAAgjB,EAAWzoB,KAAK6I,IAAIzB,GACxBpH,KAAK6I,IAAIzB,GAAQqhB,EAAWA,EAAW,KAAOhjB,EAAQA,CACvD,EAEDugB,EAAQ3nB,UAAkB,OAAI,SAAS+I,UAC9BpH,KAAK6I,IAAI8c,EAAcve,GAC/B,EAEO4e,EAAA3nB,UAAUkC,IAAM,SAAS6G,GAE/B,OADAA,EAAOue,EAAcve,GACdpH,KAAK0oB,IAAIthB,GAAQpH,KAAK6I,IAAIzB,GAAQ,IAC1C,EAEO4e,EAAA3nB,UAAUqqB,IAAM,SAASthB,GAC/B,OAAOpH,KAAK6I,IAAIvK,eAAeqnB,EAAcve,GAC9C,EAED4e,EAAQ3nB,UAAUuC,IAAM,SAASwG,EAAM3B,GACrCzF,KAAK6I,IAAI8c,EAAcve,IAASye,EAAepgB,EAChD,EAEDugB,EAAQ3nB,UAAUyF,QAAU,SAASiX,EAAU4N,GACpC,IAAA,IAAAvhB,KAAQpH,KAAK6I,IAChB7I,KAAK6I,IAAIvK,eAAe8I,IAC1B2T,EAASxc,KAAKoqB,EAAS3oB,KAAK6I,IAAIzB,GAAOA,EAAMpH,KAGlD,EAEOgmB,EAAA3nB,UAAUuI,KAAO,WACvB,IAAImf,EAAQ,GAIZ,OAHK/lB,KAAA8D,SAAQ,SAAS2B,EAAO2B,GAC3B2e,EAAMllB,KAAKuG,EACjB,IACW0e,EAAYC,EACpB,EAEOC,EAAA3nB,UAAUuqB,OAAS,WACzB,IAAI7C,EAAQ,GAIZ,OAHK/lB,KAAA8D,SAAQ,SAAS2B,GACpBsgB,EAAMllB,KAAK4E,EACjB,IACWqgB,EAAYC,EACpB,EAEOC,EAAA3nB,UAAU+F,QAAU,WAC1B,IAAI2hB,EAAQ,GAIZ,OAHK/lB,KAAA8D,SAAQ,SAAS2B,EAAO2B,GAC3B2e,EAAMllB,KAAK,CAACuG,EAAM3B,GACxB,IACWqgB,EAAYC,EACpB,EAEGV,IACFW,EAAQ3nB,UAAUmmB,OAAOC,UAAYuB,EAAQ3nB,UAAU+F,SA6KzD,IAAIykB,EAAU,CAAC,SAAU,MAAO,OAAQ,UAAW,OAAQ,OAOlD,SAAAC,EAAQC,EAAO5mB,GAClB,KAAEnC,gBAAgB8oB,GACd,MAAA,IAAIlD,UAAU,8FAItB,IAXuBoD,EACnBC,EAUA7C,GADJjkB,EAAUA,GAAW,CAAE,GACJikB,KAEnB,GAAI2C,aAAiBD,EAAS,CAC5B,GAAIC,EAAM1C,SACF,MAAA,IAAIT,UAAU,gBAEtB5lB,KAAKkpB,IAAMH,EAAMG,IACjBlpB,KAAKmpB,YAAcJ,EAAMI,YACpBhnB,EAAQ8jB,UACXjmB,KAAKimB,QAAU,IAAID,EAAQ+C,EAAM9C,UAEnCjmB,KAAKgpB,OAASD,EAAMC,OACpBhpB,KAAKopB,KAAOL,EAAMK,KAClBppB,KAAKqpB,OAASN,EAAMM,OACfjD,GAA2B,MAAnB2C,EAAMzB,YACjBlB,EAAO2C,EAAMzB,UACbyB,EAAM1C,UAAW,EAEzB,MACWrmB,KAAAkpB,IAAM7pB,OAAO0pB,GAYpB,GATA/oB,KAAKmpB,YAAchnB,EAAQgnB,aAAenpB,KAAKmpB,aAAe,eAC1DhnB,EAAQ8jB,SAAYjmB,KAAKimB,UAC3BjmB,KAAKimB,QAAU,IAAID,EAAQ7jB,EAAQ8jB,UAErCjmB,KAAKgpB,QArCkBA,EAqCO7mB,EAAQ6mB,QAAUhpB,KAAKgpB,QAAU,MApC3DC,EAAUD,EAAO9Y,cACd2Y,EAAQnrB,QAAQurB,MAAgBA,EAAUD,GAoCjDhpB,KAAKopB,KAAOjnB,EAAQinB,MAAQppB,KAAKopB,MAAQ,KACpCppB,KAAAqpB,OAASlnB,EAAQknB,QAAUrpB,KAAKqpB,OACrCrpB,KAAKspB,SAAW,MAEK,QAAhBtpB,KAAKgpB,QAAoC,SAAhBhpB,KAAKgpB,SAAsB5C,EACjD,MAAA,IAAIR,UAAU,6CAItB,GAFA5lB,KAAKqnB,UAAUjB,KAEK,QAAhBpmB,KAAKgpB,QAAoC,SAAhBhpB,KAAKgpB,QACV,aAAlB7mB,EAAQ+V,OAA0C,aAAlB/V,EAAQ+V,OAAsB,CAEhE,IAAIqR,EAAgB,gBAChBA,EAAcxgB,KAAK/I,KAAKkpB,KAErBlpB,KAAAkpB,IAAMlpB,KAAKkpB,IAAIvrB,QAAQ4rB,EAAe,YAAazI,MAAO0I,WAI/DxpB,KAAKkpB,MADe,KACOngB,KAAK/I,KAAKkpB,KAAO,IAAM,KAAO,MAAO,IAAIpI,MAAO0I,WAUnF,SAASjB,EAAOnC,GACV,IAAAqD,EAAO,IAAI/B,SAYR,OAXPtB,EACGxP,OACA3Y,MAAM,KACN6F,SAAQ,SAAS4lB,GAChB,GAAIA,EAAO,CACL,IAAAzrB,EAAQyrB,EAAMzrB,MAAM,KACpBmJ,EAAOnJ,EAAM0C,QAAQhD,QAAQ,MAAO,KACpC8H,EAAQxH,EAAMsH,KAAK,KAAK5H,QAAQ,MAAO,KAC3C8rB,EAAKjL,OAAOmL,mBAAmBviB,GAAOuiB,mBAAmBlkB,IAEnE,IACWgkB,EA6BA,SAAAG,EAASC,EAAU1nB,GACtB,KAAEnC,gBAAgB4pB,GACd,MAAA,IAAIhE,UAAU,8FAEjBzjB,IACHA,EAAU,CAAE,GAGdnC,KAAK0B,KAAO,UACZ1B,KAAK8pB,YAA4B,IAAnB3nB,EAAQ2nB,OAAuB,IAAM3nB,EAAQ2nB,OAC3D9pB,KAAK+pB,GAAK/pB,KAAK8pB,QAAU,KAAO9pB,KAAK8pB,OAAS,IAC9C9pB,KAAKgqB,gBAAoC,IAAvB7nB,EAAQ6nB,WAA2B,GAAK,GAAK7nB,EAAQ6nB,WACvEhqB,KAAKimB,QAAU,IAAID,EAAQ7jB,EAAQ8jB,SAC9BjmB,KAAAkpB,IAAM/mB,EAAQ+mB,KAAO,GAC1BlpB,KAAKqnB,UAAUwC,GA5DTf,EAAAzqB,UAAUkF,MAAQ,WACxB,OAAO,IAAIulB,EAAQ9oB,KAAM,CAAComB,KAAMpmB,KAAKsnB,WACtC,EA0CIF,EAAA7oB,KAAKuqB,EAAQzqB,WAmBb+oB,EAAA7oB,KAAKqrB,EAASvrB,WAEVurB,EAAAvrB,UAAUkF,MAAQ,WAClB,OAAA,IAAIqmB,EAAS5pB,KAAKsnB,UAAW,CAClCwC,OAAQ9pB,KAAK8pB,OACbE,WAAYhqB,KAAKgqB,WACjB/D,QAAS,IAAID,EAAQhmB,KAAKimB,SAC1BiD,IAAKlpB,KAAKkpB,KAEb,EAEDU,EAAS7nB,MAAQ,WACX,IAAAkoB,EAAW,IAAIL,EAAS,KAAM,CAACE,OAAQ,EAAGE,WAAY,KAEnD,OADPC,EAASvoB,KAAO,QACTuoB,CACR,EAED,IAAIC,EAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,KAEnCN,EAAAO,SAAW,SAASjB,EAAKY,GAChC,IAA6C,IAAzCI,EAAiBxsB,QAAQosB,GACrB,MAAA,IAAIM,WAAW,uBAGhB,OAAA,IAAIR,EAAS,KAAM,CAACE,SAAgB7D,QAAS,CAACoE,SAAUnB,IAChE,EAED9D,EAAQD,aAAeJ,EAAOI,aAC1B,IACF,IAAIC,EAAQD,YACb,OAAQ1R,GACP2R,EAAQD,aAAe,SAASmF,EAASljB,GACvCpH,KAAKsqB,QAAUA,EACftqB,KAAKoH,KAAOA,EACR,IAAArF,EAAQ8c,MAAMyL,GAClBtqB,KAAKhC,MAAQ+D,EAAM/D,KACpB,EACDonB,EAAQD,aAAa9mB,UAAYD,OAAOiF,OAAOwb,MAAMxgB,WACrD+mB,EAAQD,aAAa9mB,UAAUyB,YAAcslB,EAAQD,aAG9CD,SAAAA,EAAM6D,EAAO3mB,GACpB,OAAO,IAAIlF,SAAQ,SAASC,EAASC,GACnC,IAAImtB,EAAU,IAAIzB,EAAQC,EAAO3mB,GAEjC,GAAImoB,EAAQlB,QAAUkB,EAAQlB,OAAOmB,QACnC,OAAOptB,EAAO,IAAIgoB,EAAQD,aAAa,UAAW,eAGhD,IAAAsF,EAAM,IAAI9F,eAEd,SAAS+F,IACPD,EAAIE,QAGNF,EAAIjE,OAAS,WACX,IAnGgBoE,EAChB3E,EAkGI9jB,EAAU,CACZ2nB,OAAQW,EAAIX,OACZE,WAAYS,EAAIT,WAChB/D,SAtGc2E,EAsGQH,EAAII,yBAA2B,GArGvD5E,EAAU,IAAID,EAGQ4E,EAAWjtB,QAAQ,eAAgB,KAK1DM,MAAM,MACN4K,KAAI,SAASqd,GACL,OAAyB,IAAzBA,EAAOxoB,QAAQ,MAAcwoB,EAAO4E,OAAO,EAAG5E,EAAO/nB,QAAU+nB,CACvE,IACApiB,SAAQ,SAASinB,GACZ,IAAAzhB,EAAQyhB,EAAK9sB,MAAM,KACnBR,EAAM6L,EAAM3I,QAAQiW,OACxB,GAAInZ,EAAK,CACP,IAAIgI,EAAQ6D,EAAM/D,KAAK,KAAKqR,OACpBqP,EAAAzH,OAAO/gB,EAAKgI,GAE9B,IACWwgB,IAmFK9jB,EAAA+mB,IAAM,gBAAiBuB,EAAMA,EAAIO,YAAc7oB,EAAQ8jB,QAAQ1lB,IAAI,iBAC3E,IAAI6lB,EAAO,aAAcqE,EAAMA,EAAIR,SAAWQ,EAAIQ,aAClD9O,YAAW,WACThf,EAAQ,IAAIysB,EAASxD,EAAMjkB,GAC5B,GAAE,EACJ,EAEDsoB,EAAIhE,QAAU,WACZtK,YAAW,WACF/e,EAAA,IAAIwoB,UAAU,0BACtB,GAAE,EACJ,EAED6E,EAAIS,UAAY,WACd/O,YAAW,WACF/e,EAAA,IAAIwoB,UAAU,0BACtB,GAAE,EACJ,EAED6E,EAAIU,QAAU,WACZhP,YAAW,WACT/e,EAAO,IAAIgoB,EAAQD,aAAa,UAAW,cAC5C,GAAE,EACJ,EAUDsF,EAAIW,KAAKb,EAAQvB,OARjB,SAAgBE,GACV,IACF,MAAe,KAARA,GAAcnE,EAAOsF,SAASgB,KAAOtG,EAAOsF,SAASgB,KAAOnC,CACpE,OAAQvqB,GACA,OAAAuqB,GAIcoC,CAAOf,EAAQrB,MAAM,GAElB,YAAxBqB,EAAQpB,YACVsB,EAAIc,iBAAkB,EACW,SAAxBhB,EAAQpB,cACjBsB,EAAIc,iBAAkB,GAGpB,iBAAkBd,IAChBpF,EACFoF,EAAIe,aAAe,OAEnBnG,GACAkF,EAAQtE,QAAQ1lB,IAAI,kBAEpB,IADAgqB,EAAQtE,QAAQ1lB,IAAI,gBAAgB7C,QAAQ,8BAE5C+sB,EAAIe,aAAe,iBAInBppB,GAAgC,iBAAjBA,EAAK6jB,SAA0B7jB,EAAK6jB,mBAAmBD,EAKxEuE,EAAQtE,QAAQniB,SAAQ,SAAS2B,EAAO2B,GAClCqjB,EAAAgB,iBAAiBrkB,EAAM3B,EACrC,IANQrH,OAAOof,oBAAoBpb,EAAK6jB,SAASniB,SAAQ,SAASsD,GACxDqjB,EAAIgB,iBAAiBrkB,EAAMye,EAAezjB,EAAK6jB,QAAQ7e,IACjE,IAOUmjB,EAAQlB,SACVkB,EAAQlB,OAAOqC,iBAAiB,QAAShB,GAEzCD,EAAIkB,mBAAqB,WAEA,IAAnBlB,EAAImB,YACNrB,EAAQlB,OAAOwC,oBAAoB,QAASnB,EAE/C,GAGHD,EAAIne,UAAkC,IAAtBie,EAAQjD,UAA4B,KAAOiD,EAAQjD,UACpE,IAGHpC,EAAM4G,UAAW,EAEZ/G,EAAOG,QACVH,EAAOG,MAAQA,EACfH,EAAOiB,QAAUA,EACjBjB,EAAO+D,QAAUA,EACjB/D,EAAO6E,SAAWA,GAGpBxE,EAAQY,QAAUA,EAClBZ,EAAQ0D,QAAUA,EAClB1D,EAAQwE,SAAWA,EACnBxE,EAAQF,MAAQA,EAnmBD,CAumBd,CAAE,GAGLF,EAAeE,MAAM6G,UAAW,SACzB/G,EAAeE,MAAM4G,SAExB,IAAAE,EAAMnH,EAAWK,MAAQL,EAAaG,GAC1CI,EAAU4G,EAAI9G,OACI3T,QAAAya,EAAI9G,MACtBE,EAAgBF,MAAA8G,EAAI9G,MACpBE,EAAkBY,QAAAgG,EAAIhG,QACtBZ,EAAkB0D,QAAAkD,EAAIlD,QACtB1D,EAAmBwE,SAAAoC,EAAIpC,SACvBziB,EAAiBie,QAAAA,6CCtoBjB,IAAI6G,EAA4B,mBAAV/G,MAAuBA,WAAQ,EAOrD,QANsB,IAAXH,GAA0BA,EAAOG,MAC1C+G,EAAWlH,EAAOG,MACS,oBAAXgH,QAA0BA,OAAOhH,QACjD+G,EAAWC,OAAOhH,YAGG,IAAZiH,IAA6C,oBAAXD,OAAwB,CAC/D,IAAArV,EAAIoV,GAAYG,KAChBvV,EAAEtF,UAASsF,EAAIA,EAAEtF,SACrB6T,UAAkBvO,EAClB1P,EAAAie,QAAiBA,EAAQ7T,OAC3B,0FCZA,SAAS8a,GAAQ1tB,EAAGiK,GAAS,IAAAhB,EAAIxJ,OAAOwI,KAAKjI,GAAI,GAAIP,OAAOkuB,sBAAuB,CAAM,IAAA/H,EAAInmB,OAAOkuB,sBAAsB3tB,GAAIiK,IAAM2b,EAAIA,EAAE7b,QAAO,SAAUE,GAAK,OAAOxK,OAAOmuB,yBAAyB5tB,EAAGiK,GAAG4jB,UAAa,KAAI5kB,EAAE/G,KAAK4D,MAAMmD,EAAG2c,GAAa,OAAA3c,CAAE,CAC7P,SAAS6kB,GAAc9tB,GAAK,IAAA,IAASiK,EAAI,EAAGA,EAAI5H,UAAU7C,OAAQyK,IAAK,CAAM,IAAAhB,EAAI,MAAQ5G,UAAU4H,GAAK5H,UAAU4H,GAAK,CAAA,EAAQA,EAAA,EAAIyjB,GAAQjuB,OAAOwJ,IAAI,GAAI9D,SAAQ,SAAU8E,GAAK8jB,GAAgB/tB,EAAGiK,EAAGhB,EAAEgB,GAAM,IAAIxK,OAAOuuB,0BAA4BvuB,OAAOwuB,iBAAiBjuB,EAAGP,OAAOuuB,0BAA0B/kB,IAAMykB,GAAQjuB,OAAOwJ,IAAI9D,SAAQ,SAAU8E,GAAKxK,OAAOyuB,eAAeluB,EAAGiK,EAAGxK,OAAOmuB,yBAAyB3kB,EAAGgB,GAAM,GAAE,CAAU,OAAAjK,CAAE,CACrb,SAAS+tB,GAAgB/tB,EAAGiK,EAAGhB,GAAa,OAAAgB,EAC5C,SAAwBhB,GAAS,IAAAzG,EACjC,SAAsByG,EAAGgB,GAAK,GAAI,UAAY0b,GAAQ1c,KAAOA,EAAU,OAAAA,EAAO,IAAAjJ,EAAIiJ,EAAE4c,OAAOsI,aAAc,QAAI,IAAWnuB,EAAG,CAAE,IAAIwC,EAAIxC,EAAEJ,KAAKqJ,EAAGgB,GAAiB,GAAI,UAAY0b,GAAQnjB,GAAW,OAAAA,EAAS,MAAA,IAAIykB,UAAU,+CAAgD,CAAG,OAAQ,WAAahd,EAAIvJ,OAAS2S,QAAQpK,EAAG,CADrRmlB,CAAanlB,EAAG,UAAW,MAAO,UAAY0c,GAAQnjB,GAAKA,EAAIA,EAAI,EAAG,CAD3D6rB,CAAepkB,MAAOjK,EAAIP,OAAOyuB,eAAeluB,EAAGiK,EAAG,CAAEnD,MAAOmC,EAAG4kB,YAAY,EAAIS,cAAc,EAAIC,UAAU,IAAQvuB,EAAEiK,GAAKhB,EAAGjJ,CAAE,CAGlL,SAAS2lB,GAAQC,GAAuCD,OAAAA,GAAU,mBAAqBE,QAAU,iBAAmBA,OAAOC,SAAW,SAAUF,GAAK,cAAcA,GAAO,SAAUA,GAAYA,OAAAA,GAAK,mBAAqBC,QAAUD,EAAEzkB,cAAgB0kB,QAAUD,IAAMC,OAAOnmB,UAAY,gBAAkBkmB,CAAE,GAAYA,EAAG,CAG5T,IAMI4I,GAQAC,GAdAnB,GAA4B,mBAAV/G,MAAuBA,WAAQ,EAC/B,oBAAXH,QAA0BA,OAAOG,MAC1C+G,GAAWlH,OAAOG,MACS,oBAAXgH,QAA0BA,OAAOhH,QACjD+G,GAAWC,OAAOhH,OAGhBR,OACoB,oBAAXK,QAA0BA,OAAOJ,eAC1CwI,GAAoBpI,OAAOJ,eACA,oBAAXuH,QAA0BA,OAAOvH,iBACjDwI,GAAoBjB,OAAOvH,iBAIF,mBAAlB0I,gBACa,oBAAXtI,QAA0BA,OAAOsI,cAC1CD,GAAmBrI,OAAOsI,cACC,oBAAXnB,QAA0BA,OAAOmB,gBACjDD,GAAmBlB,OAAOmB,gBAGzBpB,KAAYqB,IAAcH,IAAsBC,QAA6BG,IAAqBD,IAC/E,mBAAbrB,KAAoCA,QAAA,GAC/C,IAAIuB,GAAiB,SAAwBtE,EAAKuE,GAChD,GAAIA,GAA8B,WAApBnJ,GAAQmJ,GAAsB,CAC1C,IAAIC,EAAc,GAClB,IAAA,IAASC,KAAaF,EACLC,GAAA,IAAME,mBAAmBD,GAAa,IAAMC,mBAAmBH,EAAOE,IAEnF,IAACD,EAAoB,OAAAxE,EACnBA,EAAAA,QAAOA,EAAIxrB,QAAQ,KAAc,IAAM,KAAOgwB,EAAY7uB,MAAM,EAC1E,CACS,OAAAqqB,CACT,EACI2E,GAAU,SAAiB3E,EAAK4E,EAAc/S,EAAUgT,GACtD,IAAA7R,EAAW,SAAkB+N,GAC/B,IAAKA,EAASF,UAAWhP,EAASkP,EAASD,YAAc,QAAS,CAChEF,OAAQG,EAASH,SAEnBG,EAASjI,OAAO3F,MAAK,SAAU3c,GAC7Bqb,EAAS,KAAM,CACb+O,OAAQG,EAASH,OACjBpqB,QAER,IAAO4c,MAAMvB,EACV,EACD,GAAIgT,EAAU,CACR,IAAAC,EAAcD,EAAS7E,EAAK4E,GAChC,GAAIE,aAAuB9wB,QAEzB,YADA8wB,EAAY3R,KAAKH,GAAUI,MAAMvB,EAGvC,CACuB,mBAAVmK,MACTA,MAAMgE,EAAK4E,GAAczR,KAAKH,GAAUI,MAAMvB,GAE9CkR,GAAS/C,EAAK4E,GAAczR,KAAKH,GAAUI,MAAMvB,EAErD,EACIkT,IAAmB,EA6EnB1D,GAAU,SAAiBpoB,EAAS+mB,EAAKgF,EAASnT,GAMpD,MALuB,mBAAZmT,IACEnT,EAAAmT,EACDA,OAAA,GAEZnT,EAAWA,GAAY,WAAc,EACjCkR,IAAqC,IAAzB/C,EAAIxrB,QAAQ,SAlFP,SAA0ByE,EAAS+mB,EAAKgF,EAASnT,GAClE5Y,EAAQgsB,oBACJjF,EAAAsE,GAAetE,EAAK/mB,EAAQgsB,oBAEpC,IAAIlI,EAAUwG,GAAc,CAAE,EAAmC,mBAA1BtqB,EAAQisB,cAA+BjsB,EAAQisB,gBAAkBjsB,EAAQisB,eAC1F,oBAAXlC,QAA4C,oBAAXnH,aAAoD,IAAnBA,OAAOvd,SAA2Bud,OAAOvd,QAAQ6mB,UAAYtJ,OAAOvd,QAAQ6mB,SAASC,OAChKrI,EAAQ,cAAgB,8BAA8BtgB,OAAOof,OAAOvd,QAAQ+mB,QAAS,MAAM5oB,OAAOof,OAAOvd,QAAQgnB,SAAU,KAAK7oB,OAAOof,OAAOvd,QAAQinB,KAAM,MAE1JP,IAAiBjI,EAAA,gBAAkB,oBACnC,IAAAyI,EAA+C,mBAA3BvsB,EAAQwsB,eAAgCxsB,EAAQwsB,eAAeT,GAAW/rB,EAAQwsB,eACtGb,EAAerB,GAAc,CAC/BzD,OAAQkF,EAAU,OAAS,MAC3B9H,KAAM8H,EAAU/rB,EAAQkE,UAAU6nB,QAAW,EAC7CjI,WACCgI,GAAmB,CAAE,EAAGS,GACvBX,EAA6C,mBAA3B5rB,EAAQysB,gBAAiCzsB,EAAQysB,eAAezwB,QAAU,EAAIgE,EAAQysB,oBAAiB,EACzH,IACMf,GAAA3E,EAAK4E,EAAc/S,EAAUgT,EACtC,OAAQpvB,GACP,IAAK+vB,GAAiD,IAAnCtwB,OAAOwI,KAAK8nB,GAAYvwB,SAAiBQ,EAAE2rB,SAAW3rB,EAAE2rB,QAAQ5sB,QAAQ,mBAAqB,EAC9G,OAAOqd,EAASpc,GAEd,IACFP,OAAOwI,KAAK8nB,GAAY5qB,SAAQ,SAAU0U,UACjCsV,EAAatV,EAC5B,IACcqV,GAAA3E,EAAK4E,EAAc/S,EAAUgT,GAClBE,IAAA,CACpB,OAAQxa,GACPsH,EAAStH,EACf,CACA,CACA,CAmDWob,CAAiB1sB,EAAS+mB,EAAKgF,EAASnT,GAE7C2J,MAAgD,mBAAlB2I,cApDJ,SAAmClrB,EAAS+mB,EAAKgF,EAASnT,GACpFmT,GAAgC,WAArB5J,GAAQ4J,KACrBA,EAAUV,GAAe,GAAIU,GAASrvB,MAAM,IAE1CsD,EAAQgsB,oBACJjF,EAAAsE,GAAetE,EAAK/mB,EAAQgsB,oBAEhC,IACE,IAAAW,GAEFA,EADE3B,GACE,IAAIA,GAEJ,IAAIC,GAAiB,uBAEzBhC,KAAK8C,EAAU,OAAS,MAAOhF,EAAK,GACjC/mB,EAAQ4sB,aACTD,EAAArD,iBAAiB,mBAAoB,kBAEvCqD,EAAAvD,kBAAoBppB,EAAQopB,gBAC1B2C,GACAY,EAAArD,iBAAiB,eAAgB,qCAEjCqD,EAAEE,kBACJF,EAAEE,iBAAiB,oBAErB,IAAIC,EAAI9sB,EAAQisB,cAEhB,GADAa,EAAiB,mBAANA,EAAmBA,IAAMA,EAElC,IAAA,IAAS9tB,KAAK8tB,EACZH,EAAErD,iBAAiBtqB,EAAG8tB,EAAE9tB,IAG5B2tB,EAAEnD,mBAAqB,WACnBmD,EAAAlD,WAAa,GAAK7Q,EAAS+T,EAAEhF,QAAU,IAAMgF,EAAE9E,WAAa,KAAM,CAClEF,OAAQgF,EAAEhF,OACVpqB,KAAMovB,EAAE7D,cAEX,EACD6D,EAAExiB,KAAK4hB,EACR,OAAQvvB,GACIqD,OACf,CACA,CAWWktB,CAA0B/sB,EAAS+mB,EAAKgF,EAASnT,QAEjDA,EAAA,IAAI8D,MAAM,6CACrB,EC9JA,SAASyF,GAAQC,GAAuC,OAAAD,GAAU,mBAAqBE,QAAU,iBAAmBA,OAAOC,SAAW,SAAUF,GAAK,cAAcA,GAAO,SAAUA,GAAYA,OAAAA,GAAK,mBAAqBC,QAAUD,EAAEzkB,cAAgB0kB,QAAUD,IAAMC,OAAOnmB,UAAY,gBAAkBkmB,CAAE,GAAYA,EAAG,CAC5T,SAAS8H,GAAQ1tB,EAAGiK,GAAS,IAAAhB,EAAIxJ,OAAOwI,KAAKjI,GAAI,GAAIP,OAAOkuB,sBAAuB,CAAM,IAAA/H,EAAInmB,OAAOkuB,sBAAsB3tB,GAAIiK,IAAM2b,EAAIA,EAAE7b,QAAO,SAAUE,GAAK,OAAOxK,OAAOmuB,yBAAyB5tB,EAAGiK,GAAG4jB,UAAa,KAAI5kB,EAAE/G,KAAK4D,MAAMmD,EAAG2c,GAAa,OAAA3c,CAAE,CAC7P,SAAS6kB,GAAc9tB,GAAK,IAAA,IAASiK,EAAI,EAAGA,EAAI5H,UAAU7C,OAAQyK,IAAK,CAAM,IAAAhB,EAAI,MAAQ5G,UAAU4H,GAAK5H,UAAU4H,GAAK,CAAA,EAAQA,EAAA,EAAIyjB,GAAQjuB,OAAOwJ,IAAI,GAAI9D,SAAQ,SAAU8E,GAAK8jB,GAAgB/tB,EAAGiK,EAAGhB,EAAEgB,GAAM,IAAIxK,OAAOuuB,0BAA4BvuB,OAAOwuB,iBAAiBjuB,EAAGP,OAAOuuB,0BAA0B/kB,IAAMykB,GAAQjuB,OAAOwJ,IAAI9D,SAAQ,SAAU8E,GAAKxK,OAAOyuB,eAAeluB,EAAGiK,EAAGxK,OAAOmuB,yBAAyB3kB,EAAGgB,GAAM,GAAE,CAAU,OAAAjK,CAAE,CAGrb,SAASwwB,GAAaxwB,EAAGiK,EAAGhB,GAAK,OAAOgB,GADxC,SAA2BjK,EAAGiK,GAAK,IAAA,IAAShB,EAAI,EAAGA,EAAIgB,EAAEzK,OAAQyJ,IAAK,CAAM,IAAA2c,EAAI3b,EAAEhB,GAAI2c,EAAEiI,WAAajI,EAAEiI,aAAc,EAAIjI,EAAE0I,cAAe,EAAI,UAAW1I,IAAMA,EAAE2I,UAAW,GAAK9uB,OAAOyuB,eAAeluB,EAAGquB,GAAezI,EAAE9mB,KAAM8mB,EAAK,CAAA,CACzL6K,CAAkBzwB,EAAEN,UAAWuK,GAAkCxK,OAAOyuB,eAAeluB,EAAG,YAAa,CAAEuuB,UAAU,IAAOvuB,CAAE,CACzK,SAAS+tB,GAAgB/tB,EAAGiK,EAAGhB,GAAa,OAAAgB,EAAIokB,GAAepkB,MAAOjK,EAAIP,OAAOyuB,eAAeluB,EAAGiK,EAAG,CAAEnD,MAAOmC,EAAG4kB,YAAY,EAAIS,cAAc,EAAIC,UAAU,IAAQvuB,EAAEiK,GAAKhB,EAAGjJ,CAAE,CAClL,SAASquB,GAAeplB,GAAS,IAAAzG,EACjC,SAAsByG,EAAGgB,GAAK,GAAI,UAAY0b,GAAQ1c,KAAOA,EAAU,OAAAA,EAAO,IAAAjJ,EAAIiJ,EAAE4c,OAAOsI,aAAc,QAAI,IAAWnuB,EAAG,CAAE,IAAIwC,EAAIxC,EAAEJ,KAAKqJ,EAAGgB,GAAiB,GAAI,UAAY0b,GAAQnjB,GAAW,OAAAA,EAAS,MAAA,IAAIykB,UAAU,+CAAkD,CAAC,OAAyBvmB,OAAiBuI,EAAG,CADrRmlB,CAAanlB,EAAG,UAAW,MAAO,UAAY0c,GAAQnjB,GAAKA,EAAIA,EAAI,EAAG,CAI3G,IA4BIkuB,GAWKF,IAVP,SAASE,EAAQ1nB,GACX,IAAAxF,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChFsuB,EAAatuB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,GAvC3F,SAAyBuuB,EAAGxd,GAAK,KAAMwd,aAAaxd,GAAU,MAAA,IAAI6T,UAAU,oCAAqC,CAwC7G4J,CAAgBxvB,KAAMqvB,GACtBrvB,KAAK2H,SAAWA,EAChB3H,KAAKmC,QAAUA,EACfnC,KAAKsvB,WAAaA,EAClBtvB,KAAK0B,KAAO,UACP1B,KAAAoC,KAAKuF,EAAUxF,EAASmtB,EACjC,GAC+B,CAAC,CAC5B7xB,IAAK,OACLgI,MAAO,SAAckC,GACnB,IAAI8F,EAAQzN,KACRmC,EAAUnB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChFsuB,EAAatuB,UAAU7C,OAAS,QAAsB,IAAjB6C,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAIvF,GAHAhB,KAAK2H,SAAWA,EAChB3H,KAAKmC,QAAUsqB,GAAcA,GAAcA,GAAc,CAAE,EA7CxD,CACLgD,SAAU,+BACVC,QAAS,8BACTtpB,MAAO,SAAe1G,GACb,OAAAyG,KAAKC,MAAM1G,EACnB,EACD2G,UAAWF,KAAKE,UAChBspB,aAAc,SAAsBhmB,EAAWlM,EAAKgf,GAClD,OAAOiQ,GAAgB,CAAE,EAAEjvB,EAAKgf,GAAiB,GAClD,EACDmT,iBAAkB,SAA0B9U,EAAW1S,GAEtD,EACDmiB,WACAsF,eAAkC,oBAAX3D,QAAiC,KACxDkC,cAAe,CAAE,EACjBD,kBAAmB,CAAE,EACrBY,aAAa,EACbxD,iBAAiB,EACjByD,kBAAkB,EAClBL,eAAgB,CACdvF,KAAM,OACND,YAAa,cACbjR,MAAO,aAsBsElY,KAAKmC,SAAW,CAAE,GAAGA,GAClGnC,KAAKsvB,WAAaA,EACdtvB,KAAK2H,UAAY3H,KAAKmC,QAAQ0tB,eAAgB,CAC5C,IAAAC,EAAQC,aAAY,WACtB,OAAOtiB,EAAM4N,QACvB,GAAWrb,KAAKmC,QAAQ0tB,gBACO,WAAnBvL,GAAQwL,IAA8C,mBAAhBA,EAAME,OAAsBF,EAAME,OACpF,CACA,GACK,CACDvyB,IAAK,YACLgI,MAAO,SAAmBqV,EAAW1S,EAAY2S,GAC/C/a,KAAKiwB,SAASnV,EAAWA,EAAW1S,EAAYA,EAAY2S,EAClE,GACK,CACDtd,IAAK,OACLgI,MAAO,SAAcqC,EAAU6B,EAAWoR,GACnC/a,KAAAiwB,SAAS,CAACnoB,GAAWA,EAAU,CAAC6B,GAAYA,EAAWoR,EAClE,GACK,CACDtd,IAAK,WACLgI,MAAO,SAAkBqV,EAAWoV,EAAkB9nB,EAAY+nB,EAAmBpV,GACnF,IJ3DsBqV,EI2DlBpR,EAAShf,KACTyvB,EAAWzvB,KAAKmC,QAAQstB,SACS,mBAA1BzvB,KAAKmC,QAAQstB,WACtBA,EAAWzvB,KAAKmC,QAAQstB,SAAS3U,EAAW1S,KAE9CqnB,EJnEN,SAAmBW,GACjB,QAASA,GAA6C,mBAAtBA,EAAa/T,IAC/C,CAEMgU,CADsBD,EIgECX,GJ9DlBW,EAEFlzB,QAAQC,QAAQizB,II6DV/T,MAAK,SAAUiU,GACtB,IAAKA,EAAkB,OAAOvV,EAAS,KAAM,CAAA,GAC7C,IAAImO,EAAMlK,EAAOrX,SAASyB,aAAa2E,YAAYuiB,EAAkB,CACnElrB,IAAK0V,EAAUvV,KAAK,KACpBZ,GAAIyD,EAAW7C,KAAK,OAEtByZ,EAAOuR,QAAQrH,EAAKnO,EAAUmV,EAAkBC,EACxD,GACA,GACK,CACD1yB,IAAK,UACLgI,MAAO,SAAiByjB,EAAKnO,EAAUD,EAAW1S,GAChD,IAAIqX,EAASzf,KACToF,EAA2B,iBAAd0V,EAAyB,CAACA,GAAaA,EACpDnW,EAA2B,iBAAfyD,EAA0B,CAACA,GAAcA,EACrD8lB,EAAUluB,KAAKmC,QAAQytB,iBAAiBxqB,EAAKT,GAC5C3E,KAAAmC,QAAQooB,QAAQvqB,KAAKmC,QAAS+mB,EAAKgF,GAAS,SAAUza,EAAK1W,GAC9D,GAAIA,IAAQA,EAAI+sB,QAAU,KAAO/sB,EAAI+sB,OAAS,MAAQ/sB,EAAI+sB,eAAgB/O,EAAS,kBAAoBmO,EAAM,kBAAoBnsB,EAAI+sB,QAAQ,GAC7I,GAAI/sB,GAAOA,EAAI+sB,QAAU,KAAO/sB,EAAI+sB,OAAS,IAAY,OAAA/O,EAAS,kBAAoBmO,EAAM,kBAAoBnsB,EAAI+sB,QAAQ,GAC5H,IAAK/sB,GAAO0W,GAAOA,EAAI6W,QAAS,CAC1B,IAAAkG,EAAe/c,EAAI6W,QAAQzgB,cAI/B,GAHqB,CAAC,SAAU,QAAS,UAAW,QAAQhD,MAAK,SAAU4pB,GAClE,OAAAD,EAAa9yB,QAAQ+yB,IAAQ,CAChD,IAEY,OAAO1V,EAAS,kBAAoBmO,EAAM,KAAOzV,EAAI6W,SAAS,EAE1E,CACQ,GAAI7W,EAAK,OAAOsH,EAAStH,GAAK,GAC9B,IAAIuJ,EAAK0T,EACL,IAEA1T,EADsB,iBAAbjgB,EAAI2C,KACP+f,EAAOtd,QAAQiE,MAAMrJ,EAAI2C,KAAMob,EAAW1S,GAE1CrL,EAAI2C,IAEb,OAAQf,GACP+xB,EAAW,kBAAoBxH,EAAM,UAC/C,CACQ,GAAIwH,EAAU,OAAO3V,EAAS2V,GAAU,GACxC3V,EAAS,KAAMiC,EACvB,GACA,GACK,CACDvf,IAAK,SACLgI,MAAO,SAAgBqV,EAAWnR,EAAWlM,EAAKgf,EAAe1B,GAC/D,IAAI4V,EAAS3wB,KACT,GAACA,KAAKmC,QAAQutB,QAAd,CACqB,iBAAd5U,IAAwBA,EAAY,CAACA,IAChD,IAAIoT,EAAUluB,KAAKmC,QAAQwtB,aAAahmB,EAAWlM,EAAKgf,GACpDmU,EAAW,EACXC,EAAY,GACZC,EAAW,GACLhW,EAAAhX,SAAQ,SAAUsB,GACtB,IAAAsqB,EAAUiB,EAAOxuB,QAAQutB,QACS,mBAA3BiB,EAAOxuB,QAAQutB,UACxBA,EAAUiB,EAAOxuB,QAAQutB,QAAQtqB,EAAKuE,IAExC,IAAIuf,EAAMyH,EAAOhpB,SAASyB,aAAa2E,YAAY2hB,EAAS,CAC1DtqB,MACAT,GAAIgF,IAECgnB,EAAAxuB,QAAQooB,QAAQoG,EAAOxuB,QAAS+mB,EAAKgF,GAAS,SAAUxuB,EAAM3C,GACvD6zB,GAAA,EACZC,EAAUhwB,KAAKnB,GACfoxB,EAASjwB,KAAK9D,GACV6zB,IAAa9V,EAAU3c,QACD,mBAAb4c,GAAyBA,EAAS8V,EAAWC,EAEpE,GACA,GAvBiC,CAwBjC,GACK,CACDrzB,IAAK,SACLgI,MAAO,WACL,IAAIsrB,EAAS/wB,KACTgxB,EAAiBhxB,KAAK2H,SACxBgF,EAAmBqkB,EAAerkB,iBAClCV,EAAgB+kB,EAAe/kB,cAC/B3J,EAAS0uB,EAAe1uB,OACtB2uB,EAAkBtkB,EAAiB7E,SACvC,IAAImpB,GAAqD,WAAlCA,EAAgBpnB,cAAvC,CACA,IAAImR,EAAS,GACTwD,EAAS,SAAgBpZ,GAChB6G,EAAcI,mBAAmBjH,GACvCtB,SAAQ,SAAUyI,GACjByO,EAAOtd,QAAQ6O,GAAK,GAAGyO,EAAOna,KAAK0L,EACjD,GACO,EACDiS,EAAOyS,GACHjxB,KAAKsvB,WAAWxS,SAAS9c,KAAKsvB,WAAWxS,QAAQhZ,SAAQ,SAAUyI,GACrE,OAAOiS,EAAOjS,EACtB,IACayO,EAAAlX,SAAQ,SAAUsB,GACvB2rB,EAAOzB,WAAW3qB,GAAGb,SAAQ,SAAUa,GACpBgI,EAAAmP,KAAK1W,EAAKT,EAAI,OAAQ,KAAM,MAAM,SAAU8O,EAAK/T,GAC5D+T,GAAKnR,EAAOR,KAAK,qBAAqB6D,OAAOhB,EAAI,kBAAkBgB,OAAOP,EAAK,WAAYqO,IAC1FA,GAAO/T,GAAM4C,EAAOX,IAAI,oBAAoBgE,OAAOhB,EAAI,kBAAkBgB,OAAOP,GAAM1F,GAC1EiN,EAAA4O,OAAO,GAAG5V,OAAOP,EAAK,KAAKO,OAAOhB,GAAK8O,EAAK/T,EACzE,GACA,GACA,GApByE,CAqBzE,KAGA2vB,GAAQ3tB,KAAO", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}