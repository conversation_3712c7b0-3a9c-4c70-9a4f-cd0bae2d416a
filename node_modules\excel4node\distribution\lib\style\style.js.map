{"version": 3, "file": "style.js", "names": ["utils", "require", "deepmerge", "Alignment", "Border", "Fill", "Font", "NumberFormat", "_getFontId", "wb", "font", "arguments", "length", "undefined", "opts", "defaultFont", "thisFont", "lookup<PERSON><PERSON>", "JSON", "stringify", "toObject", "id", "styleDataLookup", "fonts", "styleData", "push", "_getFillId", "fill", "thisFill", "fills", "_getBorderId", "border", "thisBorder", "borders", "_getNumFmt", "val", "fmt", "numFmts", "for<PERSON>ach", "f", "formatCode", "fmtId", "numFmtId", "Style", "_classCallCheck", "styles", "alignment", "borderId", "fillId", "fontId", "numberFormat", "numFmt", "pivotButton", "quotePrefix", "ids", "_createClass", "key", "get", "thisXF", "applyFont", "applyFill", "applyBorder", "applyNumberFormat", "applyAlignment", "value", "obj", "addXFtoXMLele", "ele", "thisEle", "xf", "Object", "keys", "a", "addToXMLele", "att", "addDXFtoXMLele", "module", "exports"], "sources": ["../../../source/lib/style/style.js"], "sourcesContent": ["const utils = require('../utils.js');\nconst deepmerge = require('deepmerge');\n\nconst Alignment = require('./classes/alignment.js');\nconst Border = require('./classes/border.js');\nconst Fill = require('./classes/fill.js');\nconst Font = require('./classes/font.js');\nconst NumberFormat = require('./classes/numberFormat.js');\n\nlet _getFontId = (wb, font = {}) => {\n\n    // Create the Font and lookup key\n    font = deepmerge(wb.opts.defaultFont, font);\n    const thisFont = new Font(font);\n    const lookupKey = JSON.stringify(thisFont.toObject());\n\n    // Find an existing entry, creating a new one if it does not exist\n    let id = wb.styleDataLookup.fonts[lookupKey];\n    if (id === undefined) {\n        id = wb.styleData.fonts.push(thisFont) - 1;\n        wb.styleDataLookup.fonts[lookupKey] = id;\n    }\n\n    return id;\n};\n\nlet _getFillId = (wb, fill) => {\n    if (fill === undefined) {\n        return null;\n    }\n\n    // Create the Fill and lookup key\n    const thisFill = new Fill(fill);\n    const lookupKey = JSON.stringify(thisFill.toObject());\n\n    // Find an existing entry, creating a new one if it does not exist\n    let id = wb.styleDataLookup.fills[lookupKey];\n    if (id === undefined) {\n        id = wb.styleData.fills.push(thisFill) - 1;\n        wb.styleDataLookup.fills[lookupKey] = id;\n    }\n\n    return id;\n};\n\nlet _getBorderId = (wb, border) => {\n    if (border === undefined) {\n        return null;\n    }\n\n    // Create the Border and lookup key\n    const thisBorder = new Border(border);\n    const lookupKey = JSON.stringify(thisBorder.toObject());\n\n    // Find an existing entry, creating a new one if it does not exist\n    let id = wb.styleDataLookup.borders[lookupKey];\n    if (id === undefined) {\n        id = wb.styleData.borders.push(thisBorder) - 1;\n        wb.styleDataLookup.borders[lookupKey] = id;\n    }\n\n    return id;\n};\n\nlet _getNumFmt = (wb, val) => {\n    let fmt;\n    wb.styleData.numFmts.forEach((f) => {\n        if (f.formatCode === val) {\n            fmt = f;\n        }\n    });\n\n    if (fmt === undefined) {\n        let fmtId = wb.styleData.numFmts.length + 164;\n        fmt = new NumberFormat(val);\n        fmt.numFmtId = fmtId;\n        wb.styleData.numFmts.push(fmt);\n    }\n\n    return fmt;\n};\n\n\n/*\n    Style Opts\n    {\n        alignment: { // §18.8.1\n            horizontal: ['center', 'centerContinuous', 'distributed', 'fill', 'general', 'justify', 'left', 'right'],\n            indent: integer, // Number of spaces to indent = indent value * 3\n            justifyLastLine: boolean,\n            readingOrder: ['contextDependent', 'leftToRight', 'rightToLeft'], \n            relativeIndent: integer, // number of additional spaces to indent\n            shrinkToFit: boolean,\n            textRotation: integer, // number of degrees to rotate text counter-clockwise\n            vertical: ['bottom', 'center', 'distributed', 'justify', 'top'],\n            wrapText: boolean\n        },\n        font: { // §18.8.22\n            bold: boolean,\n            charset: integer,\n            color: string,\n            condense: boolean,\n            extend: boolean,\n            family: string,\n            italics: boolean,\n            name: string,\n            outline: boolean,\n            scheme: string, // §18.18.33 ST_FontScheme (Font scheme Styles)\n            shadow: boolean,\n            strike: boolean,\n            size: integer,\n            underline: boolean,\n            vertAlign: string // §22.9.2.17 ST_VerticalAlignRun (Vertical Positioning Location)\n        },\n        border: { // §18.8.4 border (Border)\n            left: {\n                style: string,\n                color: string\n            },\n            right: {\n                style: string,\n                color: string\n            },\n            top: {\n                style: string,\n                color: string\n            },\n            bottom: {\n                style: string,\n                color: string\n            },\n            diagonal: {\n                style: string,\n                color: string\n            },\n            diagonalDown: boolean,\n            diagonalUp: boolean,\n            outline: boolean\n        },\n        fill: { // §18.8.20 fill (Fill)\n            type: 'pattern',\n            patternType: 'solid',\n            color: 'Yellow'\n        },\n        numberFormat: integer or string // §18.8.30 numFmt (Number Format)\n    }\n*/\nclass Style {\n    constructor(wb, opts) {\n        /**\n         * Excel Style object\n         * @class Style\n         * @desc Style object for formatting Excel Cells\n         * @param {Workbook} wb Excel Workbook object\n         * @param {Object} opts Options for style\n         * @param {Object} opts.alignment Options for creating an Alignment instance\n         * @param {Object} opts.font Options for creating a Font instance\n         * @param {Object} opts.border Options for creating a Border instance\n         * @param {Object} opts.fill Options for creating a Fill instance\n         * @param {String} opts.numberFormat\n         * @property {Alignment} alignment Alignment instance associated with Style\n         * @property {Border} border Border instance associated with Style\n         * @property {Number} borderId ID of Border instance in the Workbook\n         * @property {Fill} fill Fill instance associated with Style\n         * @property {Number} fillId ID of Fill instance in the Workbook\n         * @property {Font} font Font instance associated with Style\n         * @property {Number} fontId ID of Font instance in the Workbook\n         * @property {String} numberFormat String represenation of the way a number should be formatted\n         * @property {Number} xf XF id of the Style in the Workbook\n         * @returns {Style} \n         */\n        opts = opts ? opts : {};\n        opts = deepmerge(wb.styles[0] ? wb.styles[0] : {}, opts);\n\n        if (opts.alignment !== undefined) {\n            this.alignment = new Alignment(opts.alignment);\n        }\n\n        if (opts.border !== undefined) {\n            this.borderId = _getBorderId(wb, opts.border); // attribute 0 based index\n            this.border = wb.styleData.borders[this.borderId];\n        }\n        if (opts.fill !== undefined) {\n            this.fillId = _getFillId(wb, opts.fill); // attribute 0 based index\n            this.fill = wb.styleData.fills[this.fillId];\n        }\n\n        if (opts.font !== undefined) {\n            this.fontId = _getFontId(wb, opts.font); // attribute 0 based index\n            this.font = wb.styleData.fonts[this.fontId];\n        }\n\n        if (opts.numberFormat !== undefined) {\n            if (typeof opts.numberFormat === 'number' && opts.numberFormat <= 164) {\n                this.numFmtId = opts.numberFormat;\n            } else if (typeof opts.numberFormat === 'string') {\n                this.numFmt = _getNumFmt(wb, opts.numberFormat);\n            }\n        }\n\n        if (opts.pivotButton !== undefined) {\n            this.pivotButton = null; // attribute boolean\n        }\n\n        if (opts.quotePrefix !== undefined) {\n            this.quotePrefix = null; // attribute boolean\n        }\n\n        this.ids = {};\n    }\n\n    get xf() {\n        let thisXF = {};\n\n        if (typeof this.fontId === 'number') {\n            thisXF.applyFont = 1;\n            thisXF.fontId = this.fontId;\n        }\n\n        if (typeof this.fillId === 'number') {\n            thisXF.applyFill = 1;\n            thisXF.fillId = this.fillId;\n        }\n\n        if (typeof this.borderId === 'number') {\n            thisXF.applyBorder = 1;\n            thisXF.borderId = this.borderId;\n        }\n\n        if (typeof this.numFmtId === 'number') {\n            thisXF.applyNumberFormat = 1;\n            thisXF.numFmtId = this.numFmtId;\n        } else if (this.numFmt !== undefined && this.numFmt !== null) {\n            thisXF.applyNumberFormat = 1;\n            thisXF.numFmtId = this.numFmt.numFmtId;\n        }\n\n        if (this.alignment instanceof Alignment) {\n            thisXF.applyAlignment = 1;\n            thisXF.alignment = this.alignment;\n        }\n\n        return thisXF;\n    }\n\n\n    /** \n     * @func Style.toObject\n     * @desc Converts the Style instance to a javascript object\n     * @returns {Object}\n     */\n    toObject() {\n        let obj = {};\n\n        if (typeof this.fontId === 'number') {\n            obj.font = this.font.toObject();\n        }\n\n        if (typeof this.fillId === 'number') {\n            obj.fill = this.fill.toObject();\n        }\n\n        if (typeof this.borderId === 'number') {\n            obj.border = this.border.toObject();\n        }\n\n        if (typeof this.numFmtId === 'number' && this.numFmtId < 164) {\n            obj.numberFormat = this.numFmtId;\n        } else if (this.numFmt !== undefined && this.numFmt !== null) {\n            obj.numberFormat = this.numFmt.formatCode;\n        }\n\n        if (this.alignment instanceof Alignment) {\n            obj.alignment = this.alignment.toObject();\n        }\n\n        if (this.pivotButton !== undefined) {\n            obj.pivotButton = this.pivotButton;\n        }\n\n        if (this.quotePrefix !== undefined) {\n            obj.quotePrefix = this.quotePrefix;\n        }\n\n        return obj;\n    }\n\n    /**\n     * @alias Style.addToXMLele\n     * @desc When generating Workbook output, attaches style to the styles xml file\n     * @func Style.addToXMLele\n     * @param {xmlbuilder.Element} ele Element object of the xmlbuilder module\n     */\n    addXFtoXMLele(ele) {\n        let thisEle = ele.ele('xf');\n        let thisXF = this.xf;\n        Object.keys(thisXF).forEach((a) => {\n            if (a === 'alignment') {\n                thisXF[a].addToXMLele(thisEle);\n            } else {\n                thisEle.att(a, thisXF[a]);\n            }\n        });\n    }\n\n    /**\n     * @alias Style.addDXFtoXMLele\n     * @desc When generating Workbook output, attaches style to the styles xml file as a dxf for use with conditional formatting rules\n     * @func Style.addDXFtoXMLele\n     * @param {xmlbuilder.Element} ele Element object of the xmlbuilder module\n     */\n    addDXFtoXMLele(ele) {\n        let thisEle = ele.ele('dxf');\n\n        if (this.font instanceof Font) {\n            this.font.addToXMLele(thisEle);\n        }\n\n        if (this.numFmt instanceof NumberFormat) {\n            this.numFmt.addToXMLele(thisEle);\n        }\n\n        if (this.fill instanceof Fill) {\n            this.fill.addToXMLele(thisEle.ele('fill'));\n        }\n\n        if (this.alignment instanceof Alignment) {\n            this.alignment.addToXMLele(thisEle);\n        }\n\n        if (this.border instanceof Border) {\n            this.border.addToXMLele(thisEle);\n        }\n    }\n}\n\nmodule.exports = Style;"], "mappings": ";;;;;AAAA,IAAMA,KAAK,GAAGC,OAAO,CAAC,aAAa,CAAC;AACpC,IAAMC,SAAS,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEtC,IAAME,SAAS,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AACnD,IAAMG,MAAM,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AAC7C,IAAMI,IAAI,GAAGJ,OAAO,CAAC,mBAAmB,CAAC;AACzC,IAAMK,IAAI,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AACzC,IAAMM,YAAY,GAAGN,OAAO,CAAC,2BAA2B,CAAC;AAEzD,IAAIO,UAAU,GAAG,SAAbA,UAAUA,CAAIC,EAAE,EAAgB;EAAA,IAAdC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAE3B;EACAD,IAAI,GAAGR,SAAS,CAACO,EAAE,CAACK,IAAI,CAACC,WAAW,EAAEL,IAAI,CAAC;EAC3C,IAAMM,QAAQ,GAAG,IAAIV,IAAI,CAACI,IAAI,CAAC;EAC/B,IAAMO,SAAS,GAAGC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAACI,QAAQ,CAAC,CAAC,CAAC;;EAErD;EACA,IAAIC,EAAE,GAAGZ,EAAE,CAACa,eAAe,CAACC,KAAK,CAACN,SAAS,CAAC;EAC5C,IAAII,EAAE,KAAKR,SAAS,EAAE;IAClBQ,EAAE,GAAGZ,EAAE,CAACe,SAAS,CAACD,KAAK,CAACE,IAAI,CAACT,QAAQ,CAAC,GAAG,CAAC;IAC1CP,EAAE,CAACa,eAAe,CAACC,KAAK,CAACN,SAAS,CAAC,GAAGI,EAAE;EAC5C;EAEA,OAAOA,EAAE;AACb,CAAC;AAED,IAAIK,UAAU,GAAG,SAAbA,UAAUA,CAAIjB,EAAE,EAAEkB,IAAI,EAAK;EAC3B,IAAIA,IAAI,KAAKd,SAAS,EAAE;IACpB,OAAO,IAAI;EACf;;EAEA;EACA,IAAMe,QAAQ,GAAG,IAAIvB,IAAI,CAACsB,IAAI,CAAC;EAC/B,IAAMV,SAAS,GAAGC,IAAI,CAACC,SAAS,CAACS,QAAQ,CAACR,QAAQ,CAAC,CAAC,CAAC;;EAErD;EACA,IAAIC,EAAE,GAAGZ,EAAE,CAACa,eAAe,CAACO,KAAK,CAACZ,SAAS,CAAC;EAC5C,IAAII,EAAE,KAAKR,SAAS,EAAE;IAClBQ,EAAE,GAAGZ,EAAE,CAACe,SAAS,CAACK,KAAK,CAACJ,IAAI,CAACG,QAAQ,CAAC,GAAG,CAAC;IAC1CnB,EAAE,CAACa,eAAe,CAACO,KAAK,CAACZ,SAAS,CAAC,GAAGI,EAAE;EAC5C;EAEA,OAAOA,EAAE;AACb,CAAC;AAED,IAAIS,YAAY,GAAG,SAAfA,YAAYA,CAAIrB,EAAE,EAAEsB,MAAM,EAAK;EAC/B,IAAIA,MAAM,KAAKlB,SAAS,EAAE;IACtB,OAAO,IAAI;EACf;;EAEA;EACA,IAAMmB,UAAU,GAAG,IAAI5B,MAAM,CAAC2B,MAAM,CAAC;EACrC,IAAMd,SAAS,GAAGC,IAAI,CAACC,SAAS,CAACa,UAAU,CAACZ,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,IAAIC,EAAE,GAAGZ,EAAE,CAACa,eAAe,CAACW,OAAO,CAAChB,SAAS,CAAC;EAC9C,IAAII,EAAE,KAAKR,SAAS,EAAE;IAClBQ,EAAE,GAAGZ,EAAE,CAACe,SAAS,CAACS,OAAO,CAACR,IAAI,CAACO,UAAU,CAAC,GAAG,CAAC;IAC9CvB,EAAE,CAACa,eAAe,CAACW,OAAO,CAAChB,SAAS,CAAC,GAAGI,EAAE;EAC9C;EAEA,OAAOA,EAAE;AACb,CAAC;AAED,IAAIa,UAAU,GAAG,SAAbA,UAAUA,CAAIzB,EAAE,EAAE0B,GAAG,EAAK;EAC1B,IAAIC,GAAG;EACP3B,EAAE,CAACe,SAAS,CAACa,OAAO,CAACC,OAAO,CAAC,UAACC,CAAC,EAAK;IAChC,IAAIA,CAAC,CAACC,UAAU,KAAKL,GAAG,EAAE;MACtBC,GAAG,GAAGG,CAAC;IACX;EACJ,CAAC,CAAC;EAEF,IAAIH,GAAG,KAAKvB,SAAS,EAAE;IACnB,IAAI4B,KAAK,GAAGhC,EAAE,CAACe,SAAS,CAACa,OAAO,CAACzB,MAAM,GAAG,GAAG;IAC7CwB,GAAG,GAAG,IAAI7B,YAAY,CAAC4B,GAAG,CAAC;IAC3BC,GAAG,CAACM,QAAQ,GAAGD,KAAK;IACpBhC,EAAE,CAACe,SAAS,CAACa,OAAO,CAACZ,IAAI,CAACW,GAAG,CAAC;EAClC;EAEA,OAAOA,GAAG;AACd,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/DA,IAgEMO,KAAK;EACP,SAAAA,MAAYlC,EAAE,EAAEK,IAAI,EAAE;IAAA8B,eAAA,OAAAD,KAAA;IAClB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ7B,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC;IACvBA,IAAI,GAAGZ,SAAS,CAACO,EAAE,CAACoC,MAAM,CAAC,CAAC,CAAC,GAAGpC,EAAE,CAACoC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE/B,IAAI,CAAC;IAExD,IAAIA,IAAI,CAACgC,SAAS,KAAKjC,SAAS,EAAE;MAC9B,IAAI,CAACiC,SAAS,GAAG,IAAI3C,SAAS,CAACW,IAAI,CAACgC,SAAS,CAAC;IAClD;IAEA,IAAIhC,IAAI,CAACiB,MAAM,KAAKlB,SAAS,EAAE;MAC3B,IAAI,CAACkC,QAAQ,GAAGjB,YAAY,CAACrB,EAAE,EAAEK,IAAI,CAACiB,MAAM,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACA,MAAM,GAAGtB,EAAE,CAACe,SAAS,CAACS,OAAO,CAAC,IAAI,CAACc,QAAQ,CAAC;IACrD;IACA,IAAIjC,IAAI,CAACa,IAAI,KAAKd,SAAS,EAAE;MACzB,IAAI,CAACmC,MAAM,GAAGtB,UAAU,CAACjB,EAAE,EAAEK,IAAI,CAACa,IAAI,CAAC,CAAC,CAAC;MACzC,IAAI,CAACA,IAAI,GAAGlB,EAAE,CAACe,SAAS,CAACK,KAAK,CAAC,IAAI,CAACmB,MAAM,CAAC;IAC/C;IAEA,IAAIlC,IAAI,CAACJ,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACoC,MAAM,GAAGzC,UAAU,CAACC,EAAE,EAAEK,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC;MACzC,IAAI,CAACA,IAAI,GAAGD,EAAE,CAACe,SAAS,CAACD,KAAK,CAAC,IAAI,CAAC0B,MAAM,CAAC;IAC/C;IAEA,IAAInC,IAAI,CAACoC,YAAY,KAAKrC,SAAS,EAAE;MACjC,IAAI,OAAOC,IAAI,CAACoC,YAAY,KAAK,QAAQ,IAAIpC,IAAI,CAACoC,YAAY,IAAI,GAAG,EAAE;QACnE,IAAI,CAACR,QAAQ,GAAG5B,IAAI,CAACoC,YAAY;MACrC,CAAC,MAAM,IAAI,OAAOpC,IAAI,CAACoC,YAAY,KAAK,QAAQ,EAAE;QAC9C,IAAI,CAACC,MAAM,GAAGjB,UAAU,CAACzB,EAAE,EAAEK,IAAI,CAACoC,YAAY,CAAC;MACnD;IACJ;IAEA,IAAIpC,IAAI,CAACsC,WAAW,KAAKvC,SAAS,EAAE;MAChC,IAAI,CAACuC,WAAW,GAAG,IAAI,CAAC,CAAC;IAC7B;;IAEA,IAAItC,IAAI,CAACuC,WAAW,KAAKxC,SAAS,EAAE;MAChC,IAAI,CAACwC,WAAW,GAAG,IAAI,CAAC,CAAC;IAC7B;;IAEA,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC;EACjB;EAACC,YAAA,CAAAZ,KAAA;IAAAa,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAS;MACL,IAAIC,MAAM,GAAG,CAAC,CAAC;MAEf,IAAI,OAAO,IAAI,CAACT,MAAM,KAAK,QAAQ,EAAE;QACjCS,MAAM,CAACC,SAAS,GAAG,CAAC;QACpBD,MAAM,CAACT,MAAM,GAAG,IAAI,CAACA,MAAM;MAC/B;MAEA,IAAI,OAAO,IAAI,CAACD,MAAM,KAAK,QAAQ,EAAE;QACjCU,MAAM,CAACE,SAAS,GAAG,CAAC;QACpBF,MAAM,CAACV,MAAM,GAAG,IAAI,CAACA,MAAM;MAC/B;MAEA,IAAI,OAAO,IAAI,CAACD,QAAQ,KAAK,QAAQ,EAAE;QACnCW,MAAM,CAACG,WAAW,GAAG,CAAC;QACtBH,MAAM,CAACX,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACnC;MAEA,IAAI,OAAO,IAAI,CAACL,QAAQ,KAAK,QAAQ,EAAE;QACnCgB,MAAM,CAACI,iBAAiB,GAAG,CAAC;QAC5BJ,MAAM,CAAChB,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACnC,CAAC,MAAM,IAAI,IAAI,CAACS,MAAM,KAAKtC,SAAS,IAAI,IAAI,CAACsC,MAAM,KAAK,IAAI,EAAE;QAC1DO,MAAM,CAACI,iBAAiB,GAAG,CAAC;QAC5BJ,MAAM,CAAChB,QAAQ,GAAG,IAAI,CAACS,MAAM,CAACT,QAAQ;MAC1C;MAEA,IAAI,IAAI,CAACI,SAAS,YAAY3C,SAAS,EAAE;QACrCuD,MAAM,CAACK,cAAc,GAAG,CAAC;QACzBL,MAAM,CAACZ,SAAS,GAAG,IAAI,CAACA,SAAS;MACrC;MAEA,OAAOY,MAAM;IACjB;;IAGA;AACJ;AACA;AACA;AACA;EAJI;IAAAF,GAAA;IAAAQ,KAAA,EAKA,SAAA5C,SAAA,EAAW;MACP,IAAI6C,GAAG,GAAG,CAAC,CAAC;MAEZ,IAAI,OAAO,IAAI,CAAChB,MAAM,KAAK,QAAQ,EAAE;QACjCgB,GAAG,CAACvD,IAAI,GAAG,IAAI,CAACA,IAAI,CAACU,QAAQ,CAAC,CAAC;MACnC;MAEA,IAAI,OAAO,IAAI,CAAC4B,MAAM,KAAK,QAAQ,EAAE;QACjCiB,GAAG,CAACtC,IAAI,GAAG,IAAI,CAACA,IAAI,CAACP,QAAQ,CAAC,CAAC;MACnC;MAEA,IAAI,OAAO,IAAI,CAAC2B,QAAQ,KAAK,QAAQ,EAAE;QACnCkB,GAAG,CAAClC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACX,QAAQ,CAAC,CAAC;MACvC;MAEA,IAAI,OAAO,IAAI,CAACsB,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAACA,QAAQ,GAAG,GAAG,EAAE;QAC1DuB,GAAG,CAACf,YAAY,GAAG,IAAI,CAACR,QAAQ;MACpC,CAAC,MAAM,IAAI,IAAI,CAACS,MAAM,KAAKtC,SAAS,IAAI,IAAI,CAACsC,MAAM,KAAK,IAAI,EAAE;QAC1Dc,GAAG,CAACf,YAAY,GAAG,IAAI,CAACC,MAAM,CAACX,UAAU;MAC7C;MAEA,IAAI,IAAI,CAACM,SAAS,YAAY3C,SAAS,EAAE;QACrC8D,GAAG,CAACnB,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC1B,QAAQ,CAAC,CAAC;MAC7C;MAEA,IAAI,IAAI,CAACgC,WAAW,KAAKvC,SAAS,EAAE;QAChCoD,GAAG,CAACb,WAAW,GAAG,IAAI,CAACA,WAAW;MACtC;MAEA,IAAI,IAAI,CAACC,WAAW,KAAKxC,SAAS,EAAE;QAChCoD,GAAG,CAACZ,WAAW,GAAG,IAAI,CAACA,WAAW;MACtC;MAEA,OAAOY,GAAG;IACd;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAT,GAAA;IAAAQ,KAAA,EAMA,SAAAE,cAAcC,GAAG,EAAE;MACf,IAAIC,OAAO,GAAGD,GAAG,CAACA,GAAG,CAAC,IAAI,CAAC;MAC3B,IAAIT,MAAM,GAAG,IAAI,CAACW,EAAE;MACpBC,MAAM,CAACC,IAAI,CAACb,MAAM,CAAC,CAACpB,OAAO,CAAC,UAACkC,CAAC,EAAK;QAC/B,IAAIA,CAAC,KAAK,WAAW,EAAE;UACnBd,MAAM,CAACc,CAAC,CAAC,CAACC,WAAW,CAACL,OAAO,CAAC;QAClC,CAAC,MAAM;UACHA,OAAO,CAACM,GAAG,CAACF,CAAC,EAAEd,MAAM,CAACc,CAAC,CAAC,CAAC;QAC7B;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAhB,GAAA;IAAAQ,KAAA,EAMA,SAAAW,eAAeR,GAAG,EAAE;MAChB,IAAIC,OAAO,GAAGD,GAAG,CAACA,GAAG,CAAC,KAAK,CAAC;MAE5B,IAAI,IAAI,CAACzD,IAAI,YAAYJ,IAAI,EAAE;QAC3B,IAAI,CAACI,IAAI,CAAC+D,WAAW,CAACL,OAAO,CAAC;MAClC;MAEA,IAAI,IAAI,CAACjB,MAAM,YAAY5C,YAAY,EAAE;QACrC,IAAI,CAAC4C,MAAM,CAACsB,WAAW,CAACL,OAAO,CAAC;MACpC;MAEA,IAAI,IAAI,CAACzC,IAAI,YAAYtB,IAAI,EAAE;QAC3B,IAAI,CAACsB,IAAI,CAAC8C,WAAW,CAACL,OAAO,CAACD,GAAG,CAAC,MAAM,CAAC,CAAC;MAC9C;MAEA,IAAI,IAAI,CAACrB,SAAS,YAAY3C,SAAS,EAAE;QACrC,IAAI,CAAC2C,SAAS,CAAC2B,WAAW,CAACL,OAAO,CAAC;MACvC;MAEA,IAAI,IAAI,CAACrC,MAAM,YAAY3B,MAAM,EAAE;QAC/B,IAAI,CAAC2B,MAAM,CAAC0C,WAAW,CAACL,OAAO,CAAC;MACpC;IACJ;EAAC;EAAA,OAAAzB,KAAA;AAAA;AAGLiC,MAAM,CAACC,OAAO,GAAGlC,KAAK"}