{"version": 3, "file": "cf_rule.js", "names": ["_reduce", "require", "_get", "CF_RULE_TYPES", "CfRule", "ruleConfig", "_this", "_classCallCheck", "type", "priority", "formula", "dxfId", "foundType", "TypeError", "supported", "missingProps", "requiredProps", "list", "prop", "push", "length", "join", "_createClass", "key", "value", "addToXMLele", "ele", "thisRule", "undefined", "att", "text", "up", "module", "exports"], "sources": ["../../../../source/lib/worksheet/cf/cf_rule.js"], "sourcesContent": ["const _reduce = require('lodash.reduce');\nconst _get = require('lodash.get');\nconst CF_RULE_TYPES = require('./cf_rule_types');\n\nclass CfRule { // §18.3.1.10 cfRule (Conditional Formatting Rule)\n    constructor(ruleConfig) {\n        this.type = ruleConfig.type;\n        this.priority = ruleConfig.priority;\n        this.formula = ruleConfig.formula;\n        this.dxfId = ruleConfig.dxfId;\n\n        let foundType = CF_RULE_TYPES[this.type];\n\n        if (!foundType) {\n            throw new TypeError('\"' + this.type + '\" is not a valid conditional formatting rule type');\n        }\n\n        if (!foundType.supported) {\n            throw new TypeError('Conditional formatting type \"' + this.type + '\" is not yet supported');\n        }\n\n        let missingProps = _reduce(foundType.requiredProps, (list, prop) => {\n            if (_get(this, prop, null) === null) {\n                list.push(prop);\n            }\n            return list;\n        }, []);\n\n        if (missingProps.length) {\n            throw new TypeError('Conditional formatting rule is missing required properties: ' + missingProps.join(', '));\n        }\n    }\n\n    addToXMLele(ele) {\n        let thisRule = ele.ele('cfRule');\n        if (this.type !== undefined) {\n            thisRule.att('type', this.type);\n        }\n        if (this.dxfId !== undefined) {\n            thisRule.att('dxfId', this.dxfId);\n        }\n        if (this.priority !== undefined) {\n            thisRule.att('priority', this.priority);\n        }\n\n        if (this.formula !== undefined) {\n            thisRule.ele('formula').text(this.formula);\n            thisRule.up();\n        }\n        thisRule.up();\n    }\n}\n\n\nmodule.exports = CfRule;"], "mappings": ";;;;;AAAA,IAAMA,OAAO,GAAGC,OAAO,CAAC,eAAe,CAAC;AACxC,IAAMC,IAAI,GAAGD,OAAO,CAAC,YAAY,CAAC;AAClC,IAAME,aAAa,GAAGF,OAAO,CAAC,iBAAiB,CAAC;AAAC,IAE3CG,MAAM;EAAG;EACX,SAAAA,OAAYC,UAAU,EAAE;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAH,MAAA;IACpB,IAAI,CAACI,IAAI,GAAGH,UAAU,CAACG,IAAI;IAC3B,IAAI,CAACC,QAAQ,GAAGJ,UAAU,CAACI,QAAQ;IACnC,IAAI,CAACC,OAAO,GAAGL,UAAU,CAACK,OAAO;IACjC,IAAI,CAACC,KAAK,GAAGN,UAAU,CAACM,KAAK;IAE7B,IAAIC,SAAS,GAAGT,aAAa,CAAC,IAAI,CAACK,IAAI,CAAC;IAExC,IAAI,CAACI,SAAS,EAAE;MACZ,MAAM,IAAIC,SAAS,CAAC,GAAG,GAAG,IAAI,CAACL,IAAI,GAAG,mDAAmD,CAAC;IAC9F;IAEA,IAAI,CAACI,SAAS,CAACE,SAAS,EAAE;MACtB,MAAM,IAAID,SAAS,CAAC,+BAA+B,GAAG,IAAI,CAACL,IAAI,GAAG,wBAAwB,CAAC;IAC/F;IAEA,IAAIO,YAAY,GAAGf,OAAO,CAACY,SAAS,CAACI,aAAa,EAAE,UAACC,IAAI,EAAEC,IAAI,EAAK;MAChE,IAAIhB,IAAI,CAACI,KAAI,EAAEY,IAAI,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;QACjCD,IAAI,CAACE,IAAI,CAACD,IAAI,CAAC;MACnB;MACA,OAAOD,IAAI;IACf,CAAC,EAAE,EAAE,CAAC;IAEN,IAAIF,YAAY,CAACK,MAAM,EAAE;MACrB,MAAM,IAAIP,SAAS,CAAC,8DAA8D,GAAGE,YAAY,CAACM,IAAI,CAAC,IAAI,CAAC,CAAC;IACjH;EACJ;EAACC,YAAA,CAAAlB,MAAA;IAAAmB,GAAA;IAAAC,KAAA,EAED,SAAAC,YAAYC,GAAG,EAAE;MACb,IAAIC,QAAQ,GAAGD,GAAG,CAACA,GAAG,CAAC,QAAQ,CAAC;MAChC,IAAI,IAAI,CAAClB,IAAI,KAAKoB,SAAS,EAAE;QACzBD,QAAQ,CAACE,GAAG,CAAC,MAAM,EAAE,IAAI,CAACrB,IAAI,CAAC;MACnC;MACA,IAAI,IAAI,CAACG,KAAK,KAAKiB,SAAS,EAAE;QAC1BD,QAAQ,CAACE,GAAG,CAAC,OAAO,EAAE,IAAI,CAAClB,KAAK,CAAC;MACrC;MACA,IAAI,IAAI,CAACF,QAAQ,KAAKmB,SAAS,EAAE;QAC7BD,QAAQ,CAACE,GAAG,CAAC,UAAU,EAAE,IAAI,CAACpB,QAAQ,CAAC;MAC3C;MAEA,IAAI,IAAI,CAACC,OAAO,KAAKkB,SAAS,EAAE;QAC5BD,QAAQ,CAACD,GAAG,CAAC,SAAS,CAAC,CAACI,IAAI,CAAC,IAAI,CAACpB,OAAO,CAAC;QAC1CiB,QAAQ,CAACI,EAAE,CAAC,CAAC;MACjB;MACAJ,QAAQ,CAACI,EAAE,CAAC,CAAC;IACjB;EAAC;EAAA,OAAA3B,MAAA;AAAA;AAIL4B,MAAM,CAACC,OAAO,GAAG7B,MAAM"}