{"version": 3, "file": "ctColor.js", "names": ["types", "require", "xmlbuilder", "CTColor", "color", "_classCallCheck", "type", "rgb", "theme", "colorScheme", "toLowerCase", "undefined", "excelColor", "getColor", "e", "TypeError", "concat", "opts", "join", "_createClass", "key", "value", "toObject", "addToXMLele", "ele", "colorEle", "att", "module", "exports"], "sources": ["../../../../source/lib/style/classes/ctColor.js"], "sourcesContent": ["const types = require('../../types/index.js');\nconst xmlbuilder = require('xmlbuilder');\n\nclass CTColor { //§18.8.3 && §18.8.19\n    /** \n     * @class CTColor\n     * @desc Excel color representation\n     * @param {String} color Excel Color scheme or Excel Color name or HEX value of Color\n     * @properties {String} type Type of color object. defaults to rgb\n     * @properties {String} rgb ARGB representation of Color\n     * @properties {String} theme Excel Color Scheme\n     * @returns {CTColor}\n     */\n    constructor(color) {\n        this.type;\n        this.rgb;\n        this.theme; //§20.1.6.2 clrScheme (Color Scheme) : types.colorSchemes\n\n        if (typeof color === 'string') {\n            if (types.colorScheme[color.toLowerCase()] !== undefined) {\n                this.theme = color;\n                this.type = 'theme';\n            } else {\n                try {\n                    this.rgb = types.excelColor.getColor(color);\n                    this.type = 'rgb';\n                } catch (e) {\n                    throw new TypeError(`Fill color must be an RGB value, Excel color (${types.excelColor.opts.join(', ')}) or Excel theme (${types.colorScheme.opts.join(', ')})`);\n                }\n            }\n        }\n    }\n\n    /** \n     * @func CTColor.toObject\n     * @desc Converts the CTColor instance to a javascript object\n     * @returns {Object}\n     */\n    toObject() {\n        return this[this.type];\n    }\n\n    /**\n     * @alias CTColor.addToXMLele\n     * @desc When generating Workbook output, attaches style to the styles xml file\n     * @func CTColor.addToXMLele\n     * @param {xmlbuilder.Element} ele Element object of the xmlbuilder module\n     */\n    addToXMLele(ele) {\n        let colorEle = ele.ele('color');\n        colorEle.att(this.type, this[this.type]);\n    }\n}\n\nmodule.exports = CTColor;"], "mappings": ";;;;;AAAA,IAAMA,KAAK,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAMC,UAAU,GAAGD,OAAO,CAAC,YAAY,CAAC;AAAC,IAEnCE,OAAO;EAAG;EACZ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,QAAYC,KAAK,EAAE;IAAAC,eAAA,OAAAF,OAAA;IACf,IAAI,CAACG,IAAI;IACT,IAAI,CAACC,GAAG;IACR,IAAI,CAACC,KAAK,CAAC,CAAC;;IAEZ,IAAI,OAAOJ,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAIJ,KAAK,CAACS,WAAW,CAACL,KAAK,CAACM,WAAW,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;QACtD,IAAI,CAACH,KAAK,GAAGJ,KAAK;QAClB,IAAI,CAACE,IAAI,GAAG,OAAO;MACvB,CAAC,MAAM;QACH,IAAI;UACA,IAAI,CAACC,GAAG,GAAGP,KAAK,CAACY,UAAU,CAACC,QAAQ,CAACT,KAAK,CAAC;UAC3C,IAAI,CAACE,IAAI,GAAG,KAAK;QACrB,CAAC,CAAC,OAAOQ,CAAC,EAAE;UACR,MAAM,IAAIC,SAAS,kDAAAC,MAAA,CAAkDhB,KAAK,CAACY,UAAU,CAACK,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC,wBAAAF,MAAA,CAAqBhB,KAAK,CAACS,WAAW,CAACQ,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC,MAAG,CAAC;QACnK;MACJ;IACJ;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EAJIC,YAAA,CAAAhB,OAAA;IAAAiB,GAAA;IAAAC,KAAA,EAKA,SAAAC,SAAA,EAAW;MACP,OAAO,IAAI,CAAC,IAAI,CAAChB,IAAI,CAAC;IAC1B;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAc,GAAA;IAAAC,KAAA,EAMA,SAAAE,YAAYC,GAAG,EAAE;MACb,IAAIC,QAAQ,GAAGD,GAAG,CAACA,GAAG,CAAC,OAAO,CAAC;MAC/BC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAACpB,IAAI,EAAE,IAAI,CAAC,IAAI,CAACA,IAAI,CAAC,CAAC;IAC5C;EAAC;EAAA,OAAAH,OAAA;AAAA;AAGLwB,MAAM,CAACC,OAAO,GAAGzB,OAAO"}